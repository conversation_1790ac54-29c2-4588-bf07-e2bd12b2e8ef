# 用户个人信息页面重构说明

## 重构概述

本次重构将用户个人信息展示和编辑页面从原生 HTML 结构改为使用 Wot Design Uni 的表单组件，提升了代码的可维护性和用户体验。

## 主要变更

### 1. 表单结构重构

**重构前：**

- 使用原生 `<view>` 和自定义样式构建表单
- 手动处理表单验证逻辑
- 样式代码冗余

**重构后：**

- 使用 `wd-form` 作为表单容器
- 使用 `wd-cell-group` 和 `wd-cell` 组织表单项
- 使用 `wd-input` 替代原生输入框
- 集成表单验证规则

### 2. 组件使用

#### 表单组件

```vue
<wd-form ref="profileForm" :model="editForm" :rules="formRules">
  <wd-cell-group border custom-class="form-section">
    <!-- 昵称输入框 -->
    <wd-input
      v-model="editForm.nickname"
      label="昵称"
      label-width="100px"
      prop="nickname"
      placeholder="请输入昵称"
      :maxlength="20"
      show-word-limit
      clearable
      suffix-icon="edit"
      @clicksuffixicon="editNickname"
    />
    
    <!-- 邮箱输入框 -->
    <wd-input
      v-model="editForm.email"
      label="邮箱"
      label-width="100px"
      prop="email"
      placeholder="请输入邮箱地址"
      clearable
      suffix-icon="edit"
      @clicksuffixicon="editEmail"
    />
  </wd-cell-group>
</wd-form>
```

#### 账户信息展示

```vue
<wd-cell-group border custom-class="account-section" title="账户信息">
  <wd-cell title="用户ID" title-width="100px">
    <text class="value-text">{{ userInfo?.id || '-' }}</text>
  </wd-cell>
  
  <wd-cell title="注册时间" title-width="100px">
    <text class="value-text">{{ formatDate(userInfo?.created_at) }}</text>
  </wd-cell>
  
  <wd-cell title="会员等级" title-width="100px">
    <view class="level-badge">
      <wd-icon name="vip" color="#ffd700" size="16" />
      <text class="level-text">{{ userInfo?.level || 'VIP1' }}</text>
    </view>
  </wd-cell>
</wd-cell-group>
```

### 3. 表单验证

**重构前：**

- 手动编写 `validateForm()` 函数
- 在保存时调用验证函数

**重构后：**

- 使用 Wot Design Uni 的表单验证规则
- 集成到表单组件中，自动验证

```typescript
// 表单验证规则
const formRules = {
  nickname: [
    {
      required: false,
      message: '昵称长度必须在1-20个字符之间',
      validator: (value: string) => {
        if (value && (value.length < 1 || value.length > 20)) {
          return Promise.reject('昵称长度必须在1-20个字符之间')
        }
        return Promise.resolve()
      },
    },
  ],
  email: [
    {
      required: false,
      message: '请输入有效的邮箱地址',
      validator: (value: string) => {
        if (value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
          return Promise.reject('请输入有效的邮箱地址')
        }
        return Promise.resolve()
      },
    },
  ],
}
```

### 4. 保存逻辑优化

**重构前：**

```typescript
const saveProfile = async () => {
  if (!validateForm()) {
    return
  }
  // ... 保存逻辑
}
```

**重构后：**

```typescript
const saveProfile = async () => {
  try {
    // 使用表单验证
    const { valid } = await profileForm.value.validate()
    if (!valid) {
      return
    }
    // ... 保存逻辑
  } catch (error) {
    // 错误处理
  }
}
```

### 5. 样式优化

- 删除了大量自定义表单样式
- 使用 Wot Design Uni 的内置样式
- 通过 `:deep()` 选择器自定义必要的样式
- 保持了原有的视觉效果

## 优势

1. **代码简洁性**：减少了大量自定义表单代码
2. **一致性**：与项目中其他使用 Wot Design Uni 的页面保持一致
3. **可维护性**：使用标准组件，更容易维护和扩展
4. **用户体验**：内置的表单验证和交互效果更加流畅
5. **响应式**：Wot Design Uni 组件天然支持响应式设计

## 兼容性

- 保持了所有原有功能
- API 调用逻辑未改变
- 用户数据处理逻辑未改变
- 弹窗交互逻辑保持一致

## 测试建议

1. 测试昵称输入和验证
2. 测试邮箱输入和验证
3. 测试性别选择功能
4. 测试生日选择功能
5. 测试头像上传功能
6. 测试表单保存功能
7. 测试各种错误情况的处理

## 最新更新（2025-01-22）

### 性别选择器优化

- ✅ 将性别选择改为使用 `wd-picker` 组件
- ✅ 配置性别选项：男、女、保密
- ✅ 删除了原有的性别选择弹窗和相关函数

```vue
<!-- 性别选择器 -->
<wd-picker
  v-model="editForm.gender"
  label="性别"
  label-width="100px"
  :columns="genderOptions"
  placeholder="请选择性别"
/>
```

```typescript
// 性别选项
const genderOptions = [
  { value: 1, label: '男' },
  { value: 2, label: '女' },
  { value: 0, label: '保密' },
]
```

### 手机号管理优化

- ✅ 将手机号项从个人信息表单移入账户信息部分
- ✅ 设置独立的修改手机绑定流程
- ✅ 保持手机号脱敏显示功能

```vue
<!-- 账户信息 -->
<wd-cell-group border custom-class="account-section" title="账户信息">
  <wd-cell title="用户ID" title-width="100px">
    <text class="value-text">{{ userInfo?.id || '-' }}</text>
  </wd-cell>

  <!-- 手机号绑定 -->
  <wd-cell title="手机号" title-width="100px" @click="editPhone">
    <view class="form-value">
      <text class="value-text">{{ phoneDisplay }}</text>
      <wd-icon name="arrow-right" color="#999" size="14" />
    </view>
  </wd-cell>

  <wd-cell title="注册时间" title-width="100px">
    <text class="value-text">{{ formatDate(userInfo?.created_at) }}</text>
  </wd-cell>

  <wd-cell title="会员等级" title-width="100px">
    <view class="level-badge">
      <wd-icon name="vip" color="#ffd700" size="16" />
      <text class="level-text">{{ userInfo?.level || 'VIP1' }}</text>
    </view>
  </wd-cell>
</wd-cell-group>
```

### 生日选择器时间范围设置

- ✅ 设置最大时间为3年前（用户至少3岁）
- ✅ 设置最小时间为70年前（用户最多70岁）
- ✅ 添加生日初始化逻辑，确保值在合理范围内
- ✅ 当用户生日超出范围或无效时，自动设置为默认值（20年前）

```vue
<!-- 生日选择器 -->
<wd-datetime-picker
  v-model="birthdayValue"
  type="date"
  label="生日"
  placeholder="请选择生日"
  :min-date="minBirthdayDate"
  :max-date="maxBirthdayDate"
  @confirm="handleBirthdayConfirm"
  :show="showBirthdayPopup"
  @close="showBirthdayPopup = false"
/>
```

```typescript
// 生日日期范围设置
const currentDate = new Date()
// 最大日期：3年前（用户至少3岁）
const maxBirthdayDate = new Date(
  currentDate.getFullYear() - 3,
  currentDate.getMonth(),
  currentDate.getDate(),
).getTime()
// 最小日期：70年前（用户最多70岁）
const minBirthdayDate = new Date(
  currentDate.getFullYear() - 70,
  currentDate.getMonth(),
  currentDate.getDate(),
).getTime()
```

### 头像上传组件重构（最新版本 v1.1.0）

- ✅ 使用项目自定义的 `FileUpload` 组件替代 `wd-upload`
- ✅ 新增 `avatar` 文件类型，专门为头像上传优化
- ✅ 支持图片格式限制和文件大小限制（最大5MB）
- ✅ 支持覆盖上传模式，一次只能上传一张头像
- ✅ 集成项目统一的上传API和错误处理逻辑
- ✅ 保持原有的视觉设计和交互体验
- ✅ 自动压缩头像图片，优化上传性能

```vue
<!-- 头像上传区域 -->
<view class="avatar-section">
  <view class="avatar-title">头像</view>
  <view class="avatar-upload-container" @click="handleAvatarUpload">
    <view class="avatar-wrapper">
      <image
        :src="currentAvatar"
        class="avatar"
        mode="aspectFill"
      />
      <view class="avatar-mask">
        <wd-icon name="camera" color="#fff" size="24" />
        <text class="avatar-tip">更换头像</text>
      </view>
    </view>
  </view>

  <!-- 隐藏的文件上传组件 -->
  <FileUpload
    ref="avatarUploadRef"
    v-model="avatarFileList"
    file-type="avatar"
    :max-count="1"
    :max-size="5"
    upload-url="/api/v1/user/secured/upload"
    :form-data="{ file_usage: 'avatar' }"
    :show-upload-button="false"
    :show-tips="false"
    @success="handleAvatarSuccess"
    @error="handleAvatarError"
  />
</view>
```

```typescript
import FileUpload from '@/components/FileUpload.vue'
import type { FileItem } from '@/components/FileUpload.vue'

// 头像上传相关
const avatarFileList = ref<FileItem[]>([])
const avatarUploadRef = ref()

// 当前头像显示
const currentAvatar = computed(() => {
  if (avatarFileList.value.length > 0 && avatarFileList.value[0].url) {
    return avatarFileList.value[0].url
  }
  return userInfo.value?.avatar || defaultAvatar
})

// 触发头像上传
const handleAvatarUpload = () => {
  if (avatarUploadRef.value) {
    avatarUploadRef.value.upload()
  }
}

// 头像上传成功处理
const handleAvatarSuccess = (file: FileItem, response: any) => {
  // 处理 FileUpload 组件的响应格式
  let avatarUrl = response?.data?.file_url || response?.data?.url || response?.data?.file_path
  if (avatarUrl) {
    editForm.value.avatar = avatarUrl
    uni.showToast({ title: '头像上传成功', icon: 'success' })
  }
}
```

### 代码清理

- ✅ 删除了不再使用的性别选择弹窗相关代码
- ✅ 删除了不再使用的函数：`editGender`、`selectGender`、`editBirthday`、`chooseAvatar`
- ✅ 删除了不再使用的变量：`showGenderPopup`、`genderText`、`birthdayDisplay`
- ✅ 删除了不再使用的样式：`.gender-options`、`.gender-option`
- ✅ 重构了头像相关样式，适配新的上传组件

## 后续优化建议

1. ✅ 性别选择已改为使用 `wd-picker` 组件
2. ✅ 生日选择已直接使用 `wd-datetime-picker` 的内联模式
3. 可以添加更多的表单验证规则
4. 考虑添加表单重置功能
5. 可以考虑为手机号绑定添加更详细的验证流程
