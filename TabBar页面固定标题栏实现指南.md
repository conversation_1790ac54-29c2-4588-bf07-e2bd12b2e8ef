# 📱 TabBar 页面固定标题栏实现指南

## 📋 功能概述

为有TabBar的页面提供固定在顶部的标题栏，确保标题栏不会因为页面滚动而消失，同时保持TabBar固定在底部。

## 🏗️ 组件架构

### 1. **FixedHeader 组件** (`src/components/common/FixedHeader.vue`)

- ✅ 固定在页面顶部的标题栏
- ✅ 支持状态栏适配
- ✅ 支持自定义左中右区域内容
- ✅ 支持返回按钮和自定义操作

### 2. **TabBarLayout 组件** (`src/components/layout/TabBarLayout.vue`)

- ✅ 完整的TabBar页面布局解决方案
- ✅ 集成FixedHeader和TabBar
- ✅ 自动处理间距和安全区域
- ✅ 支持下拉刷新和上拉加载

## 🛠️ 使用方法

### 1. **基础用法**

```vue
<template>
  <TabBarLayout title="页面标题" :show-back="true" :show-header="true" :show-tabbar="true">
    <!-- 页面内容 -->
    <view class="page-content">
      <text>这里是页面内容</text>
    </view>
  </TabBarLayout>
</template>

<script setup lang="ts">
import TabBarLayout from '@/components/layout/TabBarLayout.vue'
</script>
```

### 2. **自定义标题栏**

```vue
<template>
  <TabBarLayout title="自定义标题" :show-back="true">
    <!-- 自定义左侧 -->
    <template #header-left>
      <view class="custom-btn" @click="handleMenu">
        <wd-icon name="menu" size="20" />
      </view>
    </template>

    <!-- 自定义右侧 -->
    <template #header-right>
      <view class="custom-btn" @click="handleSearch">
        <wd-icon name="search" size="20" />
      </view>
      <view class="custom-btn" @click="handleMore">
        <wd-icon name="more" size="20" />
      </view>
    </template>

    <!-- 页面内容 -->
    <view class="content">
      <!-- 内容 -->
    </view>
  </TabBarLayout>
</template>
```

### 3. **透明标题栏**

```vue
<template>
  <TabBarLayout title="透明标题栏" :header-transparent="true" header-text-color="#ffffff">
    <!-- 页面内容 -->
    <view class="content-with-bg">
      <!-- 带背景的内容 -->
    </view>
  </TabBarLayout>
</template>
```

## 📊 组件属性

### TabBarLayout Props

| 属性                | 类型      | 默认值      | 说明             |
| ------------------- | --------- | ----------- | ---------------- |
| `title`             | `string`  | `''`        | 页面标题         |
| `showHeader`        | `boolean` | `true`      | 是否显示标题栏   |
| `showBack`          | `boolean` | `false`     | 是否显示返回按钮 |
| `showTabBar`        | `boolean` | `true`      | 是否显示TabBar   |
| `withStatusBar`     | `boolean` | `true`      | 是否包含状态栏   |
| `headerBgColor`     | `string`  | `'#ffffff'` | 标题栏背景色     |
| `headerTextColor`   | `string`  | `'#333333'` | 标题栏文字颜色   |
| `headerTransparent` | `boolean` | `false`     | 标题栏是否透明   |
| `contentBgColor`    | `string`  | `'#f5f5f5'` | 内容区域背景色   |

### TabBarLayout Events

| 事件   | 参数 | 说明             |
| ------ | ---- | ---------------- |
| `back` | -    | 返回按钮点击事件 |

### TabBarLayout Slots

| 插槽            | 说明                 |
| --------------- | -------------------- |
| `default`       | 页面主要内容         |
| `header-left`   | 标题栏左侧自定义内容 |
| `header-center` | 标题栏中间自定义内容 |
| `header-right`  | 标题栏右侧自定义内容 |

## 🎨 样式定制

### 1. **自定义标题栏样式**

```vue
<template>
  <TabBarLayout title="自定义样式" header-bg-color="#4095e5" header-text-color="#ffffff">
    <!-- 内容 -->
  </TabBarLayout>
</template>
```

### 2. **自定义内容区域样式**

```vue
<template>
  <TabBarLayout content-bg-color="#f0f0f0">
    <view class="custom-content">
      <!-- 自定义样式的内容 -->
    </view>
  </TabBarLayout>
</template>

<style lang="scss" scoped>
.custom-content {
  padding: 32rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
</style>
```

## 🔧 现有页面改造

### 1. **改造前（问题页面）**

```vue
<template>
  <view class="page">
    <!-- 标题会随滚动消失 -->
    <view class="header">
      <text class="title">页面标题</text>
    </view>

    <!-- 内容区域 -->
    <view class="content">
      <!-- 长内容 -->
    </view>
  </view>
</template>
```

### 2. **改造后（使用TabBarLayout）**

```vue
<template>
  <TabBarLayout title="页面标题" :show-back="true">
    <!-- 内容区域 -->
    <view class="content">
      <!-- 长内容 -->
    </view>
  </TabBarLayout>
</template>

<script setup lang="ts">
import TabBarLayout from '@/components/layout/TabBarLayout.vue'
</script>
```

## 📱 平台适配

### 1. **H5 平台**

- ✅ 支持浏览器返回按钮
- ✅ 支持毛玻璃效果
- ✅ 响应式设计

### 2. **小程序平台**

- ✅ 自动适配小程序导航栏
- ✅ 支持小程序安全区域
- ✅ 优化触摸体验

### 3. **APP 平台**

- ✅ 支持状态栏沉浸式
- ✅ 适配刘海屏和安全区域
- ✅ 原生性能优化

## 🧪 测试示例

### 示例页面路径

`src/pages/example/tabbar-layout-demo.vue`

### 测试步骤

1. 访问示例页面
2. 滚动页面内容
3. 确认标题栏固定在顶部
4. 确认TabBar固定在底部
5. 测试返回按钮功能
6. 测试自定义按钮功能

## 🔧 技术实现

### 1. **固定定位实现**

```scss
.fixed-header {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  z-index: 1001 !important;
}
```

### 2. **间距处理**

```scss
/* 顶部间距 */
.header-placeholder {
  height: var(--header-height);
}

/* 底部间距 */
.tabbar-placeholder {
  height: var(--tabbar-height);
}
```

### 3. **安全区域适配**

```scss
.fixed-header {
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
  padding-top: env(safe-area-inset-top);
}
```

## 📝 最佳实践

### 1. **标题栏设计**

- 保持标题简洁明了
- 合理使用左右操作按钮
- 注意颜色对比度

### 2. **内容布局**

- 确保内容不被标题栏遮挡
- 合理使用内边距
- 注意滚动性能

### 3. **交互体验**

- 提供清晰的返回路径
- 合理的加载状态
- 友好的错误处理

## 🎯 总结

通过使用 `TabBarLayout` 组件，可以轻松实现：

- ✅ 固定顶部标题栏
- ✅ 固定底部TabBar
- ✅ 自动间距处理
- ✅ 跨平台适配
- ✅ 良好的用户体验

这个解决方案提供了完整的TabBar页面布局，确保标题栏始终固定在顶部，不会因为滚动而消失。
