/**
 * 地址选择器调试脚本
 * 在订单确认页面的浏览器控制台中运行此脚本
 */

// 检查地址选择器状态
function debugAddressSelector() {
  console.log('=== 地址选择器调试 ===')

  // 1. 检查是否在订单确认页面
  const isOrderConfirmPage =
    window.location.pathname.includes('/order-confirm') ||
    document.querySelector('.order-confirm-container')

  console.log('📍 是否在订单确认页面:', isOrderConfirmPage)

  if (!isOrderConfirmPage) {
    console.warn('⚠️ 请在订单确认页面运行此调试脚本')
    return
  }

  // 2. 检查地址store状态
  const addressStore = window.$pinia?.state?.value?.address
  if (!addressStore) {
    console.error('❌ 无法获取地址store')
    return
  }

  console.log('📦 地址store状态:')
  console.log('  - 地址列表数量:', addressStore.addressList?.length || 0)
  console.log('  - 默认地址:', addressStore.defaultAddress)
  console.log('  - 地址列表:', addressStore.addressList)

  // 3. 检查地址选择器元素
  const addressSection = document.querySelector('.address-section')
  const addressPopup = document.querySelector('.address-popup')
  const addressItems = document.querySelectorAll('.address-item')

  console.log('🔍 DOM元素检查:')
  console.log('  - 地址区域元素:', !!addressSection)
  console.log('  - 地址弹窗元素:', !!addressPopup)
  console.log('  - 地址项数量:', addressItems.length)

  // 4. 检查弹窗状态
  const popup = document.querySelector('.wd-popup')
  if (popup) {
    const popupStyle = window.getComputedStyle(popup)
    console.log('🎭 弹窗样式检查:')
    console.log('  - display:', popupStyle.display)
    console.log('  - z-index:', popupStyle.zIndex)
    console.log('  - position:', popupStyle.position)
  }

  // 5. 模拟点击测试
  if (addressSection) {
    console.log('🧪 模拟点击地址区域...')
    addressSection.click()

    setTimeout(() => {
      const popupVisible =
        document.querySelector('.wd-popup[style*="display: block"]') ||
        document.querySelector('.wd-popup:not([style*="display: none"])')
      console.log('✅ 弹窗是否显示:', !!popupVisible)

      if (popupVisible) {
        const addressItems = document.querySelectorAll('.address-item')
        console.log('📋 地址项数量:', addressItems.length)

        addressItems.forEach((item, index) => {
          const rect = item.getBoundingClientRect()
          console.log(`  地址项[${index}] 位置:`, {
            top: rect.top,
            bottom: rect.bottom,
            height: rect.height,
            visible: rect.top >= 0 && rect.bottom <= window.innerHeight,
          })
        })
      }
    }, 500)
  }
}

// 测试地址选择功能
function testAddressSelection() {
  console.log('=== 测试地址选择功能 ===')

  const addressItems = document.querySelectorAll('.address-item')
  if (addressItems.length === 0) {
    console.warn('⚠️ 没有找到地址项，请先打开地址选择器')
    return
  }

  console.log('📋 找到地址项数量:', addressItems.length)

  // 测试点击第一个地址项
  if (addressItems.length > 0) {
    const firstItem = addressItems[0]
    console.log('🧪 测试点击第一个地址项...')

    // 检查点击区域
    const rect = firstItem.getBoundingClientRect()
    console.log('📐 地址项尺寸:', {
      width: rect.width,
      height: rect.height,
      top: rect.top,
      left: rect.left,
    })

    // 模拟点击
    firstItem.click()

    setTimeout(() => {
      console.log('✅ 点击完成，检查弹窗是否关闭...')
      const popupVisible = document.querySelector('.wd-popup[style*="display: block"]')
      console.log('🎭 弹窗是否仍然显示:', !!popupVisible)
    }, 1000)
  }
}

// 检查地址数据格式
function checkAddressDataFormat() {
  console.log('=== 检查地址数据格式 ===')

  const addressStore = window.$pinia?.state?.value?.address
  if (!addressStore || !addressStore.addressList) {
    console.error('❌ 无法获取地址数据')
    return
  }

  const addresses = addressStore.addressList
  console.log('📊 地址数据分析:')

  addresses.forEach((address, index) => {
    console.log(`地址[${index}]:`, {
      id: address.id,
      receiver_name: address.receiver_name,
      receiver_mobile: address.receiver_mobile,
      province: address.province,
      city: address.city,
      district: address.district,
      detailed_address: address.detailed_address,
      is_default: address.is_default,
      full_address: address.full_address,
    })

    // 检查必要字段
    const requiredFields = ['id', 'receiver_name', 'receiver_mobile', 'province', 'city']
    const missingFields = requiredFields.filter((field) => !address[field])

    if (missingFields.length > 0) {
      console.warn(`⚠️ 地址[${index}] 缺少字段:`, missingFields)
    } else {
      console.log(`✅ 地址[${index}] 数据完整`)
    }
  })
}

// 检查弹窗层级问题
function checkPopupZIndex() {
  console.log('=== 检查弹窗层级 ===')

  const allElements = document.querySelectorAll('*')
  const highZIndexElements = []

  allElements.forEach((el) => {
    const style = window.getComputedStyle(el)
    const zIndex = parseInt(style.zIndex)

    if (zIndex > 9000) {
      highZIndexElements.push({
        element: el,
        zIndex: zIndex,
        className: el.className,
        tagName: el.tagName,
      })
    }
  })

  console.log('🏗️ 高层级元素 (z-index > 9000):')
  highZIndexElements
    .sort((a, b) => b.zIndex - a.zIndex)
    .forEach((item) => {
      console.log(`  z-index: ${item.zIndex}, ${item.tagName}.${item.className}`)
    })

  const popup = document.querySelector('.wd-popup')
  if (popup) {
    const popupZIndex = parseInt(window.getComputedStyle(popup).zIndex)
    console.log(`🎭 地址弹窗 z-index: ${popupZIndex}`)

    const conflictElements = highZIndexElements.filter((item) => item.zIndex >= popupZIndex)
    if (conflictElements.length > 0) {
      console.warn('⚠️ 发现可能遮挡弹窗的元素:', conflictElements)
    } else {
      console.log('✅ 弹窗层级正常')
    }
  }
}

// 运行所有调试测试
function runAllAddressTests() {
  console.log('🚀 开始运行地址选择器调试测试')
  console.log('')

  debugAddressSelector()
  console.log('')

  setTimeout(() => {
    checkAddressDataFormat()
    console.log('')

    checkPopupZIndex()
    console.log('')

    console.log('💡 手动测试提示:')
    console.log('  1. 点击页面顶部的地址区域')
    console.log('  2. 观察弹窗是否正确显示')
    console.log('  3. 尝试点击地址项进行选择')
    console.log('  4. 运行 testAddressSelection() 进行自动测试')
  }, 1000)
}

// 导出函数到全局
if (typeof window !== 'undefined') {
  window.debugAddressSelector = debugAddressSelector
  window.testAddressSelection = testAddressSelection
  window.checkAddressDataFormat = checkAddressDataFormat
  window.checkPopupZIndex = checkPopupZIndex
  window.runAllAddressTests = runAllAddressTests

  console.log('🔧 地址选择器调试工具已加载')
  console.log('💡 使用方法:')
  console.log('   runAllAddressTests() - 运行所有测试')
  console.log('   debugAddressSelector() - 调试地址选择器状态')
  console.log('   testAddressSelection() - 测试地址选择功能')
  console.log('   checkAddressDataFormat() - 检查地址数据格式')
  console.log('   checkPopupZIndex() - 检查弹窗层级问题')

  // 如果在订单确认页面，自动运行测试
  if (window.location.pathname.includes('/order-confirm')) {
    setTimeout(runAllAddressTests, 2000)
  }
}
