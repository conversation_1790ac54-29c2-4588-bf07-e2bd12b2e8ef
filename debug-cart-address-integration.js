/**
 * 购物车地址选择器集成调试脚本
 * 在购物车页面的浏览器控制台中运行此脚本
 */

// 检查购物车地址选择器集成状态
function debugCartAddressIntegration() {
  console.log('=== 购物车地址选择器集成调试 ===')

  // 1. 检查是否在购物车页面
  const isCartPage =
    window.location.pathname.includes('/cart') || document.querySelector('.cart-container')

  console.log('📍 是否在购物车页面:', isCartPage)

  if (!isCartPage) {
    console.warn('⚠️ 请在购物车页面运行此调试脚本')
    return
  }

  // 2. 检查各种store状态
  const stores = {
    cart: window.$pinia?.state?.value?.cart,
    address: window.$pinia?.state?.value?.address,
    takeout: window.$pinia?.state?.value?.takeout,
  }

  console.log('📦 Store状态检查:')
  Object.entries(stores).forEach(([name, store]) => {
    console.log(`  - ${name}Store:`, !!store)
  })

  // 3. 检查地址选择器元素
  const addressSection = document.querySelector('.address-section')
  const addressPopup = document.querySelector('.address-popup')

  console.log('🎨 地址选择器元素检查:')
  console.log('  - 地址选择区域:', !!addressSection)
  console.log('  - 地址选择弹窗:', !!addressPopup)

  if (addressSection) {
    const addressInfo = addressSection.querySelector('.address-info')
    const noAddress = addressSection.querySelector('.no-address')
    console.log('  - 有地址信息:', !!addressInfo)
    console.log('  - 无地址提示:', !!noAddress)
  }

  // 4. 检查地址数据
  if (stores.address) {
    console.log('📍 地址数据检查:')
    console.log('  - 地址列表数量:', stores.address.addressList?.length || 0)
    console.log('  - 默认地址:', stores.address.defaultAddress)
    console.log('  - 地址列表:', stores.address.addressList)
  }

  // 5. 检查购物车数据
  if (stores.cart) {
    console.log('🛒 购物车数据检查:')
    console.log('  - 购物车商品数量:', stores.cart.cartItems?.length || 0)
    console.log('  - 配送费配置:', stores.cart.deliveryConfig)
    console.log('  - 配送费结果:', stores.cart.deliveryFeeResults)

    // 检查各商家的配送费
    const merchantIds = [...new Set(stores.cart.cartItems?.map((item) => item.merchant_id) || [])]
    console.log('  - 商家数量:', merchantIds.length)
    merchantIds.forEach((merchantId) => {
      const deliveryFeeResult = stores.cart.deliveryFeeResults?.[merchantId]
      console.log(`  - 商家[${merchantId}]配送费:`, deliveryFeeResult)
    })
  }
}

// 测试地址选择功能
function testAddressSelection() {
  console.log('=== 测试地址选择功能 ===')

  const addressSection = document.querySelector('.address-section')
  if (!addressSection) {
    console.error('❌ 未找到地址选择区域')
    return
  }

  console.log('🧪 模拟点击地址选择区域...')
  addressSection.click()

  setTimeout(() => {
    const popup = document.querySelector('.address-popup')
    const addressItems = document.querySelectorAll('.address-item')

    console.log('📋 地址选择器状态:')
    console.log('  - 弹窗是否显示:', !!popup)
    console.log('  - 地址项数量:', addressItems.length)

    if (addressItems.length > 0) {
      console.log('🧪 测试点击第一个地址项...')

      // 记录选择前的配送费
      const cartStore = window.$pinia?.state?.value?.cart
      const beforeDeliveryFees = { ...cartStore?.deliveryFeeResults }
      console.log('📊 选择前配送费:', beforeDeliveryFees)

      addressItems[0].click()

      setTimeout(() => {
        console.log('✅ 地址选择测试完成')

        // 检查配送费是否更新
        const afterDeliveryFees = { ...cartStore?.deliveryFeeResults }
        console.log('📊 选择后配送费:', afterDeliveryFees)

        // 比较配送费变化
        const hasChanged = JSON.stringify(beforeDeliveryFees) !== JSON.stringify(afterDeliveryFees)
        console.log('🔄 配送费是否更新:', hasChanged)
      }, 2000)
    }
  }, 500)
}

// 检查配送费计算
function checkDeliveryFeeCalculation() {
  console.log('=== 检查配送费计算 ===')

  const stores = {
    cart: window.$pinia?.state?.value?.cart,
    address: window.$pinia?.state?.value?.address,
  }

  if (!stores.cart || !stores.address) {
    console.error('❌ 无法获取必要的store数据')
    return
  }

  const selectedAddress =
    stores.address.defaultAddress ||
    (stores.address.addressList?.length > 0 ? stores.address.addressList[0] : null)
  const cartItems = stores.cart.cartItems || []

  console.log('📊 配送费计算数据:')
  console.log('  - 选中地址:', selectedAddress)
  console.log('  - 购物车商品:', cartItems)
  console.log('  - 配送费配置:', stores.cart.deliveryConfig)

  if (selectedAddress) {
    console.log('📍 地址坐标信息:')
    console.log('  - 纬度:', selectedAddress.location_latitude || selectedAddress.locationLatitude)
    console.log(
      '  - 经度:',
      selectedAddress.location_longitude || selectedAddress.locationLongitude,
    )
  }

  // 检查各商家的配送费
  const merchantGroups = {}
  cartItems.forEach((item) => {
    if (!merchantGroups[item.merchant_id]) {
      merchantGroups[item.merchant_id] = {
        merchantId: item.merchant_id,
        merchantName: item.merchant_name,
        items: [],
      }
    }
    merchantGroups[item.merchant_id].items.push(item)
  })

  console.log('🏪 商家分组信息:')
  Object.values(merchantGroups).forEach((group) => {
    const deliveryFeeResult = stores.cart.deliveryFeeResults?.[group.merchantId]
    console.log(`  - ${group.merchantName}:`, {
      商品数量: group.items.length,
      配送费结果: deliveryFeeResult,
    })
  })
}

// 测试配送费重新计算
function testDeliveryFeeRecalculation() {
  console.log('=== 测试配送费重新计算 ===')

  const cartStore = window.$pinia?.state?.value?.cart
  if (!cartStore) {
    console.error('❌ 无法获取购物车store')
    return
  }

  console.log('🧪 手动触发配送费重新计算...')

  // 记录计算前的状态
  const beforeResults = { ...cartStore.deliveryFeeResults }
  console.log('📊 计算前配送费:', beforeResults)

  // 触发重新计算
  cartStore
    .calculateAllDeliveryFees()
    .then(() => {
      console.log('✅ 配送费重新计算完成')

      const afterResults = { ...cartStore.deliveryFeeResults }
      console.log('📊 计算后配送费:', afterResults)

      // 检查是否有变化
      const hasChanged = JSON.stringify(beforeResults) !== JSON.stringify(afterResults)
      console.log('🔄 配送费是否有变化:', hasChanged)
    })
    .catch((error) => {
      console.error('❌ 配送费重新计算失败:', error)
    })
}

// 检查页面元素显示
function checkPageElements() {
  console.log('=== 检查页面元素显示 ===')

  // 检查地址选择器
  const addressSection = document.querySelector('.address-section')
  if (addressSection) {
    const rect = addressSection.getBoundingClientRect()
    console.log('📍 地址选择器位置:', {
      top: rect.top,
      left: rect.left,
      width: rect.width,
      height: rect.height,
      visible:
        rect.top >= 0 &&
        rect.left >= 0 &&
        rect.bottom <= window.innerHeight &&
        rect.right <= window.innerWidth,
    })
  }

  // 检查配送费显示
  const deliveryFeeElements = document.querySelectorAll('.delivery-fee')
  console.log('🚚 配送费显示元素数量:', deliveryFeeElements.length)

  deliveryFeeElements.forEach((element, index) => {
    const text = element.textContent
    console.log(`  [${index}] 配送费文本:`, text)
  })

  // 检查商家信息
  const merchantElements = document.querySelectorAll('.merchant-info')
  console.log('🏪 商家信息元素数量:', merchantElements.length)
}

// 运行所有调试测试
function runAllCartAddressTests() {
  console.log('🚀 开始运行购物车地址选择器集成调试测试')
  console.log('')

  debugCartAddressIntegration()
  console.log('')

  setTimeout(() => {
    checkDeliveryFeeCalculation()
    console.log('')

    checkPageElements()
    console.log('')

    console.log('💡 手动测试提示:')
    console.log('  1. 运行 testAddressSelection() 测试地址选择')
    console.log('  2. 运行 testDeliveryFeeRecalculation() 测试配送费计算')
    console.log('  3. 观察地址选择后配送费是否自动更新')
    console.log('  4. 检查购物车页面和订单确认页面的数据一致性')
  }, 1000)
}

// 导出函数到全局
if (typeof window !== 'undefined') {
  window.debugCartAddressIntegration = debugCartAddressIntegration
  window.testAddressSelection = testAddressSelection
  window.checkDeliveryFeeCalculation = checkDeliveryFeeCalculation
  window.testDeliveryFeeRecalculation = testDeliveryFeeRecalculation
  window.checkPageElements = checkPageElements
  window.runAllCartAddressTests = runAllCartAddressTests

  console.log('🔧 购物车地址选择器集成调试工具已加载')
  console.log('💡 使用方法:')
  console.log('   runAllCartAddressTests() - 运行所有测试')
  console.log('   debugCartAddressIntegration() - 调试集成状态')
  console.log('   testAddressSelection() - 测试地址选择功能')
  console.log('   checkDeliveryFeeCalculation() - 检查配送费计算')
  console.log('   testDeliveryFeeRecalculation() - 测试配送费重新计算')
  console.log('   checkPageElements() - 检查页面元素显示')

  // 如果在购物车页面，自动运行测试
  if (window.location.pathname.includes('/cart')) {
    setTimeout(runAllCartAddressTests, 2000)
  }
}
