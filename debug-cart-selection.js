/**
 * 购物车选择功能调试脚本
 * 在浏览器控制台中运行此脚本来调试购物车选择功能
 */

// 调试购物车选择功能
function debugCartSelection() {
  console.log('=== 购物车选择功能调试 ===')

  // 1. 检查购物车数据
  const cartStore = window.$pinia?.state?.value?.cart
  if (!cartStore) {
    console.error('❌ 无法获取购物车store')
    return
  }

  console.log('📦 购物车商品数量:', cartStore.cartItems?.length || 0)
  console.log('📦 购物车商品列表:', cartStore.cartItems)

  // 2. 检查选中状态
  const selectedItems = cartStore.cartItems?.filter((item) => item.selected) || []
  console.log('✅ 已选中商品数量:', selectedItems.length)
  console.log('✅ 已选中商品:', selectedItems)

  // 3. 检查全选状态
  console.log('🔘 全选状态:', cartStore.isAllSelected)

  // 4. 检查选择框元素
  const checkboxes = document.querySelectorAll('.wd-checkbox')
  console.log('🔲 页面中选择框数量:', checkboxes.length)

  // 5. 模拟选择操作
  if (cartStore.cartItems?.length > 0) {
    const firstItem = cartStore.cartItems[0]
    console.log('🧪 测试选择第一个商品:', firstItem.id, '当前状态:', firstItem.selected)

    // 模拟API调用
    const testSelectAPI = async () => {
      try {
        const response = await fetch('/api/v1/user/takeout/cart/select', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${localStorage.getItem('token') || sessionStorage.getItem('token')}`,
          },
          body: JSON.stringify({
            cart_item_ids: [firstItem.id],
            selected: !firstItem.selected,
          }),
        })

        const result = await response.json()
        console.log('🌐 API响应:', result)

        if (response.ok) {
          console.log('✅ API调用成功')
        } else {
          console.error('❌ API调用失败:', result)
        }
      } catch (error) {
        console.error('❌ API调用异常:', error)
      }
    }

    console.log('🧪 点击下面的链接测试API调用:')
    console.log('%c testSelectAPI()', 'color: blue; text-decoration: underline; cursor: pointer;')
    window.testSelectAPI = testSelectAPI
  }

  // 6. 检查事件绑定
  const itemCheckboxes = document.querySelectorAll('.item-checkbox .wd-checkbox')
  console.log('🔲 商品选择框数量:', itemCheckboxes.length)

  itemCheckboxes.forEach((checkbox, index) => {
    const isDisabled =
      checkbox.hasAttribute('disabled') || checkbox.classList.contains('is-disabled')
    console.log(`🔲 选择框[${index}] 是否禁用:`, isDisabled)
  })

  // 7. 检查网络请求
  console.log('🌐 监听网络请求...')
  const originalFetch = window.fetch
  window.fetch = function (...args) {
    if (args[0]?.includes('/cart/select')) {
      console.log('🌐 购物车选择API请求:', args)
    }
    return originalFetch.apply(this, args)
  }

  console.log('=== 调试完成 ===')
  console.log('💡 提示: 尝试点击购物车中的选择框，观察控制台输出')
}

// 检查购物车页面状态
function checkCartPageStatus() {
  console.log('=== 购物车页面状态检查 ===')

  // 检查是否在购物车页面
  const isCartPage =
    window.location.pathname.includes('/cart') ||
    document.title.includes('购物车') ||
    document.querySelector('.cart-content')

  console.log('📍 是否在购物车页面:', isCartPage)

  if (!isCartPage) {
    console.warn('⚠️ 当前不在购物车页面，请先进入购物车页面')
    return false
  }

  // 检查购物车是否为空
  const cartContent = document.querySelector('.cart-content')
  const emptyCart = document.querySelector('.empty-cart')

  if (emptyCart && !cartContent) {
    console.warn('⚠️ 购物车为空，请先添加商品')
    return false
  }

  console.log('✅ 购物车页面状态正常')
  return true
}

// 自动运行检查
if (typeof window !== 'undefined') {
  // 等待页面加载完成
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      setTimeout(() => {
        if (checkCartPageStatus()) {
          debugCartSelection()
        }
      }, 1000)
    })
  } else {
    setTimeout(() => {
      if (checkCartPageStatus()) {
        debugCartSelection()
      }
    }, 1000)
  }

  // 导出调试函数到全局
  window.debugCartSelection = debugCartSelection
  window.checkCartPageStatus = checkCartPageStatus

  console.log('🔧 购物车调试工具已加载')
  console.log('💡 使用方法:')
  console.log('   debugCartSelection() - 调试购物车选择功能')
  console.log('   checkCartPageStatus() - 检查购物车页面状态')
}
