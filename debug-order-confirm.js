/**
 * 订单确认页面调试脚本
 * 在订单确认页面的浏览器控制台中运行此脚本
 */

// 检查订单确认页面状态
function debugOrderConfirm() {
  console.log('=== 订单确认页面调试 ===')

  // 1. 检查是否在订单确认页面
  const isOrderConfirmPage =
    window.location.pathname.includes('/order-confirm') ||
    document.querySelector('.order-confirm-container')

  console.log('📍 是否在订单确认页面:', isOrderConfirmPage)

  if (!isOrderConfirmPage) {
    console.warn('⚠️ 请在订单确认页面运行此调试脚本')
    return
  }

  // 2. 检查各种store状态
  const stores = {
    takeout: window.$pinia?.state?.value?.takeout,
    address: window.$pinia?.state?.value?.address,
    user: window.$pinia?.state?.value?.user,
  }

  console.log('📦 Store状态检查:')
  Object.entries(stores).forEach(([name, store]) => {
    console.log(`  - ${name}Store:`, !!store)
  })

  // 3. 检查订单数据
  if (stores.takeout) {
    console.log('🛒 订单数据:')
    console.log('  - 选中商品数量:', stores.takeout.selectedCartItems?.length || 0)
    console.log('  - 当前商家:', stores.takeout.currentMerchant)
    console.log('  - 选中商品:', stores.takeout.selectedCartItems)
  }

  // 4. 检查地址数据
  if (stores.address) {
    console.log('📍 地址数据:')
    console.log('  - 地址列表数量:', stores.address.addressList?.length || 0)
    console.log('  - 默认地址:', stores.address.defaultAddress)
    console.log('  - 地址列表:', stores.address.addressList)
  }

  // 5. 检查图标组件
  const iconElements = {
    'wd-icon': document.querySelectorAll('wd-icon'),
    'uni-icons': document.querySelectorAll('uni-icons'),
  }

  console.log('🎨 图标组件检查:')
  Object.entries(iconElements).forEach(([type, elements]) => {
    console.log(`  - ${type} 数量:`, elements.length)
    if (elements.length > 0) {
      console.log(
        `    元素:`,
        Array.from(elements).map((el) => ({
          name: el.getAttribute('name') || el.getAttribute('type'),
          size: el.getAttribute('size'),
          color: el.getAttribute('color'),
        })),
      )
    }
  })

  if (iconElements['uni-icons'].length > 0) {
    console.warn('⚠️ 发现未替换的 uni-icons 组件')
  }
}

// 检查配送费计算
function debugDeliveryFee() {
  console.log('=== 配送费计算调试 ===')

  const stores = {
    takeout: window.$pinia?.state?.value?.takeout,
    address: window.$pinia?.state?.value?.address,
  }

  if (!stores.takeout || !stores.address) {
    console.error('❌ 无法获取必要的store数据')
    return
  }

  const selectedAddress =
    stores.address.defaultAddress ||
    (stores.address.addressList?.length > 0 ? stores.address.addressList[0] : null)
  const merchant = stores.takeout.currentMerchant
  const cartItems = stores.takeout.selectedCartItems || []

  console.log('📊 配送费计算数据:')
  console.log('  - 选中地址:', selectedAddress)
  console.log('  - 商家信息:', merchant)
  console.log('  - 购物车商品:', cartItems)

  if (selectedAddress) {
    console.log('📍 地址坐标信息:')
    console.log('  - 纬度:', selectedAddress.location_latitude || selectedAddress.locationLatitude)
    console.log(
      '  - 经度:',
      selectedAddress.location_longitude || selectedAddress.locationLongitude,
    )
  }

  if (merchant) {
    console.log('🏪 商家坐标信息:')
    console.log('  - 纬度:', merchant.latitude)
    console.log('  - 经度:', merchant.longitude)
  }

  // 计算订单总金额
  const totalAmount = cartItems.reduce((total, item) => total + item.price * item.quantity, 0)
  console.log('💰 订单总金额:', totalAmount)

  // 检查配送费显示
  const deliveryFeeElements = document.querySelectorAll('.delivery-item')
  console.log('🚚 配送费显示元素:')
  deliveryFeeElements.forEach((element, index) => {
    const label = element.querySelector('.label')?.textContent
    const value = element.querySelector('.value')?.textContent
    console.log(`  [${index}] ${label}: ${value}`)
  })
}

// 测试地址选择功能
function testAddressSelection() {
  console.log('=== 测试地址选择功能 ===')

  const addressSection = document.querySelector('.address-section')
  if (!addressSection) {
    console.error('❌ 未找到地址选择区域')
    return
  }

  console.log('🧪 模拟点击地址选择区域...')
  addressSection.click()

  setTimeout(() => {
    const popup = document.querySelector('.address-popup')
    const addressItems = document.querySelectorAll('.address-item')

    console.log('📋 地址选择器状态:')
    console.log('  - 弹窗是否显示:', !!popup)
    console.log('  - 地址项数量:', addressItems.length)

    if (addressItems.length > 0) {
      console.log('🧪 测试点击第一个地址项...')
      addressItems[0].click()

      setTimeout(() => {
        console.log('✅ 地址选择测试完成')
        debugDeliveryFee() // 重新检查配送费
      }, 1000)
    }
  }, 500)
}

// 检查配送费配置
function checkDeliveryConfig() {
  console.log('=== 检查配送费配置 ===')

  // 模拟获取配送费配置
  fetch('/api/v1/system/secured/configs/details?page=1&page_size=10&category=deliveryFee')
    .then((response) => response.json())
    .then((data) => {
      console.log('📋 配送费配置响应:', data)

      if (data.data && data.data.list && data.data.list.length > 0) {
        const config = data.data.list[0]
        console.log('⚙️ 配送费配置:', {
          configKey: config.configKey,
          configType: config.configType,
          configValue: config.configValue,
        })

        if (config.configType === 'json') {
          try {
            const parsedConfig = JSON.parse(config.configValue)
            console.log('📊 解析后的配送费配置:', parsedConfig)
          } catch (error) {
            console.error('❌ 配送费配置解析失败:', error)
          }
        }
      } else {
        console.warn('⚠️ 未找到配送费配置')
      }
    })
    .catch((error) => {
      console.error('❌ 获取配送费配置失败:', error)
    })
}

// 运行所有调试测试
function runAllOrderConfirmTests() {
  console.log('🚀 开始运行订单确认页面调试测试')
  console.log('')

  debugOrderConfirm()
  console.log('')

  setTimeout(() => {
    debugDeliveryFee()
    console.log('')

    checkDeliveryConfig()
    console.log('')

    console.log('💡 手动测试提示:')
    console.log('  1. 检查所有图标是否使用 wd-icon')
    console.log('  2. 测试地址选择功能: testAddressSelection()')
    console.log('  3. 观察配送费是否根据地址变化')
    console.log('  4. 检查配送距离是否正确显示')
  }, 1000)
}

// 导出函数到全局
if (typeof window !== 'undefined') {
  window.debugOrderConfirm = debugOrderConfirm
  window.debugDeliveryFee = debugDeliveryFee
  window.testAddressSelection = testAddressSelection
  window.checkDeliveryConfig = checkDeliveryConfig
  window.runAllOrderConfirmTests = runAllOrderConfirmTests

  console.log('🔧 订单确认页面调试工具已加载')
  console.log('💡 使用方法:')
  console.log('   runAllOrderConfirmTests() - 运行所有测试')
  console.log('   debugOrderConfirm() - 调试页面基础状态')
  console.log('   debugDeliveryFee() - 调试配送费计算')
  console.log('   testAddressSelection() - 测试地址选择功能')
  console.log('   checkDeliveryConfig() - 检查配送费配置')

  // 如果在订单确认页面，自动运行测试
  if (window.location.pathname.includes('/order-confirm')) {
    setTimeout(runAllOrderConfirmTests, 2000)
  }
}
