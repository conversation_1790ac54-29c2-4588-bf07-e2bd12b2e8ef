# WebSocket消息广播对接文档

## 概述

本文档详细说明了后端通过WebSocket向前端广播的所有消息类型，按照功能和用户角色进行分类。系统支持多设备同时在线，所有消息都会自动保存到数据库以确保离线用户也能接收到消息。

## 消息基础结构

所有WebSocket消息都遵循统一的结构：

```json
{
  "type": "notification",           // 消息类型：message, notification, system, heartbeat
  "event": "user_order_payment_success", // 具体事件类型
  "session_id": 123,               // 会话ID（可选）
  "timestamp": 1706598000,         // 时间戳
  "data": {                        // 消息数据
    "order_id": 12345,
    "order_no": "ORD20250130001",
    "notification_type": "order_payment_success",
    "priority": 2,
    "action_type": "view_order",
    "action_url": "/user/takeout/order/detail/12345",
    "message": "您的订单已支付成功"
  }
}
```

## 消息类型分类

### 1. 通用消息 (Common Messages)

#### 1.1 文本消息
- **事件**: `text_message`
- **类型**: `message`
- **用途**: 用户间聊天、客服对话

```json
{
  "type": "message",
  "event": "text_message",
  "session_id": 12,
  "timestamp": 1753845215,
  "data": {
    "id": 137,
    "session_id": 12,
    "sender_id": 1,
    "sender_type": "merchant",
    "sender_name": "songda",
    "sender_avatar": "http://omallimg.qwyx.shop/merchant_logo/2025/06/10/1749519350919156000_DZtRIVc0.png",
    "content": "测试文本消息",
    "type": "text",
    "resource_id": "",
    "status": 0,
    "created_at": "2025-07-30T11:13:35+08:00"
  }
}
```

#### 1.2 媒体消息
- **事件**: `media_message`
- **类型**: `message`
- **用途**: 图片、文件、语音、视频消息

```json
{
  "type": "message",
  "event": "media_message",
  "session_id": 12,
  "timestamp": 1753845215,
  "data": {
    "id": 138,
    "session_id": 12,
    "sender_id": 1,
    "sender_type": "merchant",
    "sender_name": "songda",
    "sender_avatar": "http://omallimg.qwyx.shop/merchant_logo/2025/06/10/1749519350919156000_DZtRIVc0.png",
    "content": "http://example.com/image.jpg",
    "type": "image",
    "resource_id": "img_123456",
    "file_name": "产品图片.jpg",
    "file_size": 1024000,
    "file_type": "image/jpeg",
    "file_ext": "jpg",
    "status": 0,
    "created_at": "2025-07-30T11:15:20+08:00"
  }
}
```

#### 1.3 系统消息
- **事件**: `system_message`
- **类型**: `notification`
- **用途**: 系统通知、状态更新

```json
{
  "type": "notification",
  "event": "system_message",
  "data": {
    "title": "系统维护通知",
    "content": "系统将于今晚2:00-4:00进行维护",
    "system_message": true,
    "priority": 3
  }
}
```

### 2. 用户消息 (User Messages)

#### 2.1 订单相关通知

##### 订单状态更新
- **事件**: `user_order_status_update`
- **优先级**: 中 (2)

```json
{
  "type": "notification",
  "event": "user_order_status_update",
  "data": {
    "order_id": 12345,
    "order_no": "ORD20250130001",
    "old_status": "pending",
    "new_status": "confirmed",
    "status_message": "商家已确认订单",
    "notification_type": "order_status_update",
    "priority": 2,
    "action_type": "view_order",
    "action_url": "/user/takeout/order/detail/12345",
    "message": "您的订单状态已更新：商家已确认订单"
  }
}
```

##### 订单支付成功
- **事件**: `user_order_payment_success`
- **优先级**: 高 (3)

```json
{
  "type": "notification",
  "event": "user_order_payment_success",
  "data": {
    "order_id": 12345,
    "order_no": "ORD20250130001",
    "pay_amount": 99.99,
    "notification_type": "order_payment_success",
    "priority": 3,
    "action_type": "view_order",
    "action_url": "/user/takeout/order/detail/12345",
    "message": "订单支付成功，金额：¥99.99"
  }
}
```

##### 订单配送通知
- **事件**: `user_order_delivery_update`
- **优先级**: 中 (2)

```json
{
  "type": "notification",
  "event": "user_order_delivery_update",
  "data": {
    "order_id": 12345,
    "order_no": "ORD20250130001",
    "delivery_status": "delivering",
    "estimated_time": "30分钟",
    "notification_type": "order_delivery_update",
    "priority": 2,
    "action_type": "track_order",
    "action_url": "/user/takeout/order/detail/12345",
    "message": "您的订单正在配送中，预计30分钟送达"
  }
}
```

##### 订单完成通知
- **事件**: `user_order_completed`
- **优先级**: 中 (2)

```json
{
  "type": "notification",
  "event": "user_order_completed",
  "data": {
    "order_id": 12345,
    "order_no": "ORD20250130001",
    "total_amount": 99.99,
    "notification_type": "order_completed",
    "priority": 2,
    "action_type": "review_order",
    "action_url": "/user/review/create/12345",
    "message": "订单已完成，快来评价一下吧！"
  }
}
```

#### 2.2 退款相关通知

##### 退款结果通知
- **事件**: `user_refund_result`
- **优先级**: 高 (3)

```json
{
  "type": "notification",
  "event": "user_refund_result",
  "data": {
    "refund_id": 5001,
    "refund_no": "REF20250130001",
    "order_id": 12345,
    "order_no": "ORD20250130001",
    "refund_amount": 99.99,
    "status": "approved",
    "remark": "退款已批准，将在3-5个工作日内到账",
    "notification_type": "refund_result",
    "priority": 3,
    "action_type": "view_refund",
    "action_url": "/user/refund/detail/5001",
    "message": "退款申请已批准，金额：¥99.99"
  }
}
```

##### 退款进度通知
- **事件**: `user_refund_progress`
- **优先级**: 中 (2)

```json
{
  "type": "notification",
  "event": "user_refund_progress",
  "data": {
    "refund_id": 5001,
    "refund_no": "REF20250130001",
    "progress": "processing",
    "estimated_time": "1-3个工作日",
    "notification_type": "refund_progress",
    "priority": 2,
    "action_type": "view_refund",
    "action_url": "/user/refund/detail/5001",
    "message": "退款正在处理中，预计1-3个工作日完成"
  }
}
```

#### 2.3 优惠券和促销通知

##### 优惠券领取通知
- **事件**: `user_coupon_received`
- **优先级**: 低 (1)

```json
{
  "type": "notification",
  "event": "user_coupon_received",
  "data": {
    "coupon_id": 3001,
    "coupon_name": "新用户专享券",
    "discount_amount": 10.00,
    "expire_time": 1706684400,
    "notification_type": "coupon_received",
    "priority": 1,
    "action_type": "view_coupon",
    "action_url": "/user/coupon/list",
    "message": "恭喜您获得新用户专享券，立减¥10.00"
  }
}
```

##### 优惠券过期提醒
- **事件**: `user_coupon_expire_reminder`
- **优先级**: 中 (2)

```json
{
  "type": "notification",
  "event": "user_coupon_expire_reminder",
  "data": {
    "coupon_id": 3001,
    "coupon_name": "新用户专享券",
    "expire_time": 1706684400,
    "notification_type": "coupon_expire_reminder",
    "priority": 2,
    "action_type": "use_coupon",
    "action_url": "/user/coupon/list",
    "message": "您的新用户专享券即将过期，请尽快使用"
  }
}
```

##### 促销活动通知
- **事件**: `user_promotion`
- **优先级**: 低 (1)

```json
{
  "type": "notification",
  "event": "user_promotion",
  "data": {
    "promotion_id": 4001,
    "promotion_title": "周末特惠",
    "promotion_content": "全场8折，满100减20",
    "notification_type": "promotion",
    "priority": 1,
    "action_type": "promotion_detail",
    "action_url": "/promotion/detail/4001",
    "message": "周末特惠活动开始啦！全场8折，满100减20"
  }
}
```

#### 2.4 跑腿员任务相关通知

##### 任务分配通知
- **事件**: `runner_task_assigned`
- **优先级**: 高 (3)

```json
{
  "type": "notification",
  "event": "runner_task_assigned",
  "data": {
    "task_id": 8001,
    "task_no": "RUN20250130001",
    "task_type": "delivery",
    "pickup_address": "北京市朝阳区xxx街道xxx号",
    "delivery_address": "北京市海淀区xxx路xxx号",
    "reward": 25.00,
    "notification_type": "runner_task_assigned",
    "priority": 3,
    "action_type": "accept_task",
    "action_url": "/runner/task/detail/8001",
    "message": "新任务分配：配送任务，奖励¥25.00"
  }
}
```

##### 任务状态更新
- **事件**: `runner_task_status_update`
- **优先级**: 中 (2)

```json
{
  "type": "notification",
  "event": "runner_task_status_update",
  "data": {
    "task_id": 8001,
    "task_no": "RUN20250130001",
    "old_status": "assigned",
    "new_status": "accepted",
    "notification_type": "runner_task_status_update",
    "priority": 2,
    "action_type": "view_task",
    "action_url": "/runner/task/detail/8001",
    "message": "任务状态已更新：已接受任务"
  }
}
```

##### 任务取消通知
- **事件**: `runner_task_cancelled`
- **优先级**: 中 (2)

```json
{
  "type": "notification",
  "event": "runner_task_cancelled",
  "data": {
    "task_id": 8001,
    "task_no": "RUN20250130001",
    "cancel_reason": "用户取消订单",
    "notification_type": "runner_task_cancelled",
    "priority": 2,
    "action_type": "view_task",
    "action_url": "/runner/task/list",
    "message": "任务已取消：用户取消订单"
  }
}
```

#### 2.5 跑腿员收益相关通知

##### 收益通知
- **事件**: `runner_earnings`
- **优先级**: 中 (2)

```json
{
  "type": "notification",
  "event": "runner_earnings",
  "data": {
    "task_id": 8001,
    "task_no": "RUN20250130001",
    "earnings": 20.00,
    "bonus_amount": 5.00,
    "total_earnings": 25.00,
    "notification_type": "runner_earnings",
    "priority": 2,
    "action_type": "view_earnings",
    "action_url": "/runner/earnings",
    "message": "任务完成，获得收益¥25.00（基础¥20.00 + 奖励¥5.00）"
  }
}
```

##### 提现通知
- **事件**: `runner_withdrawal`
- **优先级**: 高 (3)

```json
{
  "type": "notification",
  "event": "runner_withdrawal",
  "data": {
    "withdrawal_id": 6001,
    "amount": 200.00,
    "status": "approved",
    "notification_type": "runner_withdrawal",
    "priority": 3,
    "action_type": "view_withdrawal",
    "action_url": "/runner/withdrawal/detail/6001",
    "message": "提现申请已批准，金额¥200.00，预计1-3个工作日到账"
  }
}
```

##### 日收益通知
- **事件**: `runner_daily_earnings`
- **优先级**: 低 (1)

```json
{
  "type": "notification",
  "event": "runner_daily_earnings",
  "data": {
    "date": "2025-01-30",
    "total_earnings": 150.00,
    "task_count": 6,
    "notification_type": "runner_daily_earnings",
    "priority": 1,
    "action_type": "view_earnings",
    "action_url": "/runner/earnings",
    "message": "今日收益统计：完成6个任务，总收益¥150.00"
  }
}
```

#### 2.6 跑腿员状态相关通知

##### 状态变更通知
- **事件**: `runner_status_change`
- **优先级**: 中 (2)

```json
{
  "type": "notification",
  "event": "runner_status_change",
  "data": {
    "old_status": "offline",
    "new_status": "online",
    "reason": "手动上线",
    "notification_type": "runner_status_change",
    "priority": 2,
    "action_type": "view_status",
    "action_url": "/runner/profile",
    "message": "状态已更新：已上线接单"
  }
}
```

##### 位置更新通知
- **事件**: `runner_location_update`
- **优先级**: 低 (1)

```json
{
  "type": "notification",
  "event": "runner_location_update",
  "data": {
    "latitude": 39.9042,
    "longitude": 116.4074,
    "address": "北京市朝阳区xxx街道",
    "notification_type": "runner_location_update",
    "priority": 1,
    "action_type": "view_location",
    "action_url": "/runner/location",
    "message": "位置已更新：北京市朝阳区xxx街道"
  }
}
```

#### 2.7 评价和投诉通知

##### 评价提醒通知
- **事件**: `user_review_reminder`
- **优先级**: 低 (1)

```json
{
  "type": "notification",
  "event": "user_review_reminder",
  "data": {
    "order_id": 12345,
    "order_no": "ORD20250130001",
    "notification_type": "review_reminder",
    "priority": 1,
    "action_type": "create_review",
    "action_url": "/user/review/create/12345",
    "message": "您的订单已完成，快来评价一下吧！"
  }
}
```

##### 评价回复通知
- **事件**: `user_review_reply`
- **优先级**: 中 (2)

```json
{
  "type": "notification",
  "event": "user_review_reply",
  "data": {
    "review_id": 7001,
    "order_id": 12345,
    "reply_content": "感谢您的评价，我们会继续努力！",
    "notification_type": "review_reply",
    "priority": 2,
    "action_type": "view_review",
    "action_url": "/user/review/detail/7001",
    "message": "商家回复了您的评价"
  }
}
```

##### 投诉状态通知
- **事件**: `user_complaint_status`
- **优先级**: 高 (3)

```json
{
  "type": "notification",
  "event": "user_complaint_status",
  "data": {
    "complaint_id": 9001,
    "status": "resolved",
    "handle_result": "已协调解决，商家将重新制作",
    "notification_type": "complaint_status",
    "priority": 3,
    "action_type": "view_complaint",
    "action_url": "/user/complaint/detail/9001",
    "message": "您的投诉已处理：已协调解决，商家将重新制作"
  }
}
```

#### 2.8 系统通知

##### 系统通知
- **事件**: `user_system_notification`
- **优先级**: 中 (2)

```json
{
  "type": "notification",
  "event": "user_system_notification",
  "data": {
    "title": "系统升级通知",
    "content": "系统将于今晚进行升级维护",
    "notification_type": "system_notification",
    "priority": 2,
    "action_type": "system_notification",
    "message": "系统将于今晚进行升级维护"
  }
}
```

##### 账户安全通知
- **事件**: `user_account_security`
- **优先级**: 高 (3)

```json
{
  "type": "notification",
  "event": "user_account_security",
  "data": {
    "security_type": "login",
    "description": "检测到异地登录",
    "ip_address": "*************",
    "notification_type": "account_security",
    "priority": 3,
    "action_type": "security_check",
    "action_url": "/user/security",
    "message": "检测到异地登录，请确认是否为本人操作"
  }
}
```

##### 余额变动通知
- **事件**: `user_balance_change`
- **优先级**: 中 (2)

```json
{
  "type": "notification",
  "event": "user_balance_change",
  "data": {
    "change_type": "recharge",
    "amount": 100.00,
    "balance": 250.00,
    "description": "在线充值",
    "notification_type": "balance_change",
    "priority": 2,
    "action_type": "balance_detail",
    "action_url": "/user/balance",
    "message": "余额变动：充值 ¥100.00 元，当前余额：¥250.00 元"
  }
}
```

### 3. 商家消息 (Merchant Messages)

#### 3.1 订单相关通知

##### 新订单通知
- **事件**: `merchant_new_order`
- **优先级**: 高 (3)

```json
{
  "type": "notification",
  "event": "merchant_new_order",
  "data": {
    "order_id": 12345,
    "order_no": "ORD20250130001",
    "order_amount": 99.99,
    "customer_info": {
      "customer_name": "张三",
      "customer_phone": "13800138000"
    },
    "notification_type": "new_order",
    "priority": 3,
    "action_type": "process_order",
    "action_url": "/merchant/order/detail/12345",
    "message": "您有新订单，订单金额：¥99.99"
  }
}
```

##### 订单取消通知
- **事件**: `merchant_order_cancel`
- **优先级**: 中 (2)

```json
{
  "type": "notification",
  "event": "merchant_order_cancel",
  "data": {
    "order_id": 12345,
    "order_no": "ORD20250130001",
    "cancel_reason": "用户主动取消",
    "notification_type": "order_cancel",
    "priority": 2,
    "action_type": "view_order",
    "action_url": "/merchant/order/detail/12345",
    "message": "订单已取消：用户主动取消"
  }
}
```

##### 订单状态更新通知
- **事件**: `merchant_order_status_update`
- **优先级**: 中 (2)

```json
{
  "type": "notification",
  "event": "merchant_order_status_update",
  "data": {
    "order_id": 12345,
    "order_no": "ORD20250130001",
    "old_status": "confirmed",
    "new_status": "preparing",
    "notification_type": "order_status_update",
    "priority": 2,
    "action_type": "view_order",
    "action_url": "/merchant/order/detail/12345",
    "message": "订单状态已更新：正在制作中"
  }
}
```

#### 3.2 退款相关通知

##### 退款申请通知
- **事件**: `merchant_refund_request`
- **优先级**: 高 (3)

```json
{
  "type": "notification",
  "event": "merchant_refund_request",
  "data": {
    "refund_id": 5001,
    "refund_no": "REF20250130001",
    "order_id": 12345,
    "order_no": "ORD20250130001",
    "refund_amount": 99.99,
    "refund_reason": "商品质量问题",
    "notification_type": "refund_request",
    "priority": 3,
    "action_type": "handle_refund",
    "action_url": "/merchant/refund/detail/5001",
    "message": "收到退款申请，金额：¥99.99，原因：商品质量问题"
  }
}
```

##### 退款状态更新通知
- **事件**: `merchant_refund_status_update`
- **优先级**: 中 (2)

```json
{
  "type": "notification",
  "event": "merchant_refund_status_update",
  "data": {
    "refund_id": 5001,
    "refund_no": "REF20250130001",
    "status": "approved",
    "remark": "同意退款申请",
    "notification_type": "refund_status_update",
    "priority": 2,
    "action_type": "view_refund",
    "action_url": "/merchant/refund/detail/5001",
    "message": "退款状态已更新：已批准"
  }
}
```

#### 3.3 营业状态通知

##### 营业状态通知
- **事件**: `merchant_business_status`
- **优先级**: 中 (2)

```json
{
  "type": "notification",
  "event": "merchant_business_status",
  "data": {
    "business_status": "open",
    "reason": "正常营业时间",
    "notification_type": "business_status",
    "priority": 2,
    "action_type": "view_status",
    "action_url": "/merchant/business/status",
    "message": "店铺状态已更新：正在营业"
  }
}
```

##### 店铺关闭提醒
- **事件**: `merchant_store_closing_reminder`
- **优先级**: 低 (1)

```json
{
  "type": "notification",
  "event": "merchant_store_closing_reminder",
  "data": {
    "closing_time": 1706684400,
    "notification_type": "store_closing_reminder",
    "priority": 1,
    "action_type": "view_status",
    "action_url": "/merchant/business/status",
    "message": "店铺即将关闭，请注意营业时间"
  }
}
```

##### 店铺开业提醒
- **事件**: `merchant_store_opening_reminder`
- **优先级**: 低 (1)

```json
{
  "type": "notification",
  "event": "merchant_store_opening_reminder",
  "data": {
    "opening_time": 1706598000,
    "notification_type": "store_opening_reminder",
    "priority": 1,
    "action_type": "view_status",
    "action_url": "/merchant/business/status",
    "message": "店铺即将开业，请做好准备"
  }
}
```

#### 3.4 商品管理通知

##### 商品审核通知
- **事件**: `merchant_product_audit`
- **优先级**: 中 (2)

```json
{
  "type": "notification",
  "event": "merchant_product_audit",
  "data": {
    "product_id": 10001,
    "product_name": "麻辣烫",
    "audit_status": "approved",
    "audit_remark": "商品信息完整，审核通过",
    "notification_type": "product_audit",
    "priority": 2,
    "action_type": "view_product",
    "action_url": "/merchant/product/detail/10001",
    "message": "商品审核结果：麻辣烫 审核通过"
  }
}
```

##### 商品库存预警
- **事件**: `merchant_product_stock_alert`
- **优先级**: 中 (2)

```json
{
  "type": "notification",
  "event": "merchant_product_stock_alert",
  "data": {
    "product_id": 10001,
    "product_name": "麻辣烫",
    "current_stock": 5,
    "min_stock": 10,
    "notification_type": "product_stock_alert",
    "priority": 2,
    "action_type": "manage_stock",
    "action_url": "/merchant/product/stock/10001",
    "message": "商品库存不足：麻辣烫 当前库存5，低于最低库存10"
  }
}
```

##### 商品下架通知
- **事件**: `merchant_product_offline`
- **优先级**: 中 (2)

```json
{
  "type": "notification",
  "event": "merchant_product_offline",
  "data": {
    "product_id": 10001,
    "product_name": "麻辣烫",
    "reason": "违规内容",
    "notification_type": "product_offline",
    "priority": 2,
    "action_type": "view_product",
    "action_url": "/merchant/product/detail/10001",
    "message": "商品已下架：麻辣烫，原因：违规内容"
  }
}
```

#### 3.5 评价和投诉通知

##### 新评价通知
- **事件**: `merchant_new_review`
- **优先级**: 低 (1)

```json
{
  "type": "notification",
  "event": "merchant_new_review",
  "data": {
    "review_id": 7001,
    "order_id": 12345,
    "rating": 5,
    "review_content": "味道很好，服务态度也不错",
    "notification_type": "new_review",
    "priority": 1,
    "action_type": "view_review",
    "action_url": "/merchant/review/detail/7001",
    "message": "收到新评价：5星好评"
  }
}
```

##### 投诉通知
- **事件**: `merchant_complaint`
- **优先级**: 高 (3)

```json
{
  "type": "notification",
  "event": "merchant_complaint",
  "data": {
    "complaint_id": 9001,
    "order_id": 12345,
    "complaint_type": "food_quality",
    "complaint_content": "食物有异味",
    "notification_type": "complaint",
    "priority": 3,
    "action_type": "handle_complaint",
    "action_url": "/merchant/complaint/detail/9001",
    "message": "收到用户投诉：食物有异味"
  }
}
```

#### 3.6 促销活动通知

##### 促销活动开始通知
- **事件**: `merchant_promotion_start`
- **优先级**: 低 (1)

```json
{
  "type": "notification",
  "event": "merchant_promotion_start",
  "data": {
    "promotion_id": 4001,
    "promotion_name": "周末特惠",
    "start_time": 1706598000,
    "notification_type": "promotion_start",
    "priority": 1,
    "action_type": "view_promotion",
    "action_url": "/merchant/promotion/detail/4001",
    "message": "促销活动已开始：周末特惠"
  }
}
```

##### 促销活动结束通知
- **事件**: `merchant_promotion_end`
- **优先级**: 低 (1)

```json
{
  "type": "notification",
  "event": "merchant_promotion_end",
  "data": {
    "promotion_id": 4001,
    "promotion_name": "周末特惠",
    "end_time": 1706684400,
    "notification_type": "promotion_end",
    "priority": 1,
    "action_type": "view_promotion",
    "action_url": "/merchant/promotion/detail/4001",
    "message": "促销活动已结束：周末特惠"
  }
}
```

##### 优惠券使用通知
- **事件**: `merchant_coupon_usage`
- **优先级**: 低 (1)

```json
{
  "type": "notification",
  "event": "merchant_coupon_usage",
  "data": {
    "coupon_id": 3001,
    "coupon_name": "新用户专享券",
    "user_id": 1001,
    "order_id": 12345,
    "notification_type": "coupon_usage",
    "priority": 1,
    "action_type": "view_order",
    "action_url": "/merchant/order/detail/12345",
    "message": "用户使用了优惠券：新用户专享券"
  }
}
```

#### 3.7 财务相关通知

##### 结算通知
- **事件**: `merchant_settlement`
- **优先级**: 中 (2)

```json
{
  "type": "notification",
  "event": "merchant_settlement",
  "data": {
    "settlement_id": 11001,
    "settlement_amount": 1500.00,
    "settlement_date": 1706598000,
    "notification_type": "settlement",
    "priority": 2,
    "action_type": "view_settlement",
    "action_url": "/merchant/finance/settlement/11001",
    "message": "结算完成，金额：¥1500.00"
  }
}
```

##### 提现状态通知
- **事件**: `merchant_withdrawal_status`
- **优先级**: 高 (3)

```json
{
  "type": "notification",
  "event": "merchant_withdrawal_status",
  "data": {
    "withdrawal_id": 6001,
    "amount": 1000.00,
    "status": "approved",
    "notification_type": "withdrawal_status",
    "priority": 3,
    "action_type": "view_withdrawal",
    "action_url": "/merchant/finance/withdrawal/6001",
    "message": "提现申请已批准，金额：¥1000.00"
  }
}
```

#### 3.8 系统通知

##### 系统通知
- **事件**: `merchant_system_notification`
- **优先级**: 中 (2)

```json
{
  "type": "notification",
  "event": "merchant_system_notification",
  "data": {
    "title": "平台政策更新",
    "content": "配送费用调整通知",
    "notification_type": "system_notification",
    "priority": 2,
    "action_type": "system_notification",
    "message": "平台政策更新：配送费用调整通知"
  }
}
```

##### 政策更新通知
- **事件**: `merchant_policy_update`
- **优先级**: 中 (2)

```json
{
  "type": "notification",
  "event": "merchant_policy_update",
  "data": {
    "policy_type": "delivery_fee",
    "update_content": "配送费用调整为每单3元",
    "effective_date": 1706684400,
    "notification_type": "policy_update",
    "priority": 2,
    "action_type": "view_policy",
    "action_url": "/merchant/policy",
    "message": "政策更新：配送费用调整为每单3元"
  }
}
```

### 4. 管理员消息 (Admin Messages)

#### 4.1 系统通知

##### 系统维护通知
- **事件**: `system_maintenance`
- **优先级**: 高 (3)

```json
{
  "type": "notification",
  "event": "system_maintenance",
  "data": {
    "title": "系统维护通知",
    "content": "系统将于今晚2:00-4:00进行维护",
    "start_time": 1706652000,
    "end_time": 1706659200,
    "notification_type": "system_maintenance",
    "priority": 3,
    "action_type": "view_maintenance",
    "action_url": "/admin/system/maintenance",
    "message": "系统维护通知：今晚2:00-4:00进行维护"
  }
}
```

##### 系统公告通知
- **事件**: `system_announcement`
- **优先级**: 中 (2)

```json
{
  "type": "notification",
  "event": "system_announcement",
  "data": {
    "title": "平台升级公告",
    "content": "平台将新增多项功能",
    "priority": 2,
    "notification_type": "system_announcement",
    "action_type": "view_announcement",
    "action_url": "/admin/system/announcement",
    "message": "平台升级公告：平台将新增多项功能"
  }
}
```

#### 4.2 异常告警

##### 系统错误告警
- **事件**: `system_error_alert`
- **优先级**: 高 (3)

```json
{
  "type": "notification",
  "event": "system_error_alert",
  "data": {
    "error_type": "database_connection",
    "error_message": "数据库连接超时",
    "error_details": {
      "server": "db-server-01",
      "error_code": "TIMEOUT_ERROR"
    },
    "notification_type": "system_error_alert",
    "priority": 3,
    "action_type": "handle_error",
    "action_url": "/admin/system/errors",
    "message": "系统错误告警：数据库连接超时"
  }
}
```

##### 性能告警
- **事件**: `performance_alert`
- **优先级**: 中 (2)

```json
{
  "type": "notification",
  "event": "performance_alert",
  "data": {
    "metric_name": "cpu_usage",
    "current_value": 85.5,
    "threshold": 80.0,
    "notification_type": "performance_alert",
    "priority": 2,
    "action_type": "view_metrics",
    "action_url": "/admin/system/metrics",
    "message": "性能告警：CPU使用率85.5%，超过阈值80%"
  }
}
```

##### 安全告警
- **事件**: `security_alert`
- **优先级**: 高 (3)

```json
{
  "type": "notification",
  "event": "security_alert",
  "data": {
    "alert_type": "suspicious_login",
    "description": "检测到异常登录行为",
    "source_ip": "*************",
    "notification_type": "security_alert",
    "priority": 3,
    "action_type": "handle_security",
    "action_url": "/admin/security/alerts",
    "message": "安全告警：检测到异常登录行为"
  }
}
```

#### 4.3 业务异常通知

##### 订单异常通知
- **事件**: `order_exception`
- **优先级**: 中 (2)

```json
{
  "type": "notification",
  "event": "order_exception",
  "data": {
    "order_id": 12345,
    "order_no": "ORD20250130001",
    "exception_type": "payment_timeout",
    "description": "订单支付超时",
    "notification_type": "order_exception",
    "priority": 2,
    "action_type": "handle_exception",
    "action_url": "/admin/order/exception/12345",
    "message": "订单异常：ORD20250130001 支付超时"
  }
}
```

##### 支付异常通知
- **事件**: `payment_exception`
- **优先级**: 高 (3)

```json
{
  "type": "notification",
  "event": "payment_exception",
  "data": {
    "payment_id": 20001,
    "payment_no": "PAY20250130001",
    "exception_type": "refund_failed",
    "amount": 99.99,
    "notification_type": "payment_exception",
    "priority": 3,
    "action_type": "handle_payment",
    "action_url": "/admin/payment/exception/20001",
    "message": "支付异常：PAY20250130001 退款失败，金额¥99.99"
  }
}
```

##### 退款异常通知
- **事件**: `refund_exception`
- **优先级**: 高 (3)

```json
{
  "type": "notification",
  "event": "refund_exception",
  "data": {
    "refund_id": 5001,
    "refund_no": "REF20250130001",
    "exception_type": "refund_timeout",
    "amount": 99.99,
    "notification_type": "refund_exception",
    "priority": 3,
    "action_type": "handle_refund",
    "action_url": "/admin/refund/exception/5001",
    "message": "退款异常：REF20250130001 退款超时，金额¥99.99"
  }
}
```

#### 4.4 用户管理通知

##### 用户举报通知
- **事件**: `user_report`
- **优先级**: 中 (2)

```json
{
  "type": "notification",
  "event": "user_report",
  "data": {
    "report_id": 30001,
    "report_type": "spam",
    "reporter_id": 1001,
    "target_id": 2001,
    "notification_type": "user_report",
    "priority": 2,
    "action_type": "handle_report",
    "action_url": "/admin/user/report/30001",
    "message": "用户举报：用户1001举报用户2001涉嫌垃圾信息"
  }
}
```

##### 用户封禁通知
- **事件**: `user_ban`
- **优先级**: 中 (2)

```json
{
  "type": "notification",
  "event": "user_ban",
  "data": {
    "user_id": 2001,
    "ban_reason": "发布违规内容",
    "ban_duration": 86400,
    "notification_type": "user_ban",
    "priority": 2,
    "action_type": "view_user",
    "action_url": "/admin/user/detail/2001",
    "message": "用户封禁：用户2001因发布违规内容被封禁24小时"
  }
}
```

#### 4.5 商家管理通知

##### 商家审核通知
- **事件**: `merchant_audit`
- **优先级**: 中 (2)

```json
{
  "type": "notification",
  "event": "merchant_audit",
  "data": {
    "merchant_id": 3001,
    "audit_type": "registration",
    "status": "pending",
    "notification_type": "merchant_audit",
    "priority": 2,
    "action_type": "handle_audit",
    "action_url": "/admin/merchant/audit/3001",
    "message": "商家审核：商家3001注册审核待处理"
  }
}
```

##### 商家违规通知
- **事件**: `merchant_violation`
- **优先级**: 高 (3)

```json
{
  "type": "notification",
  "event": "merchant_violation",
  "data": {
    "merchant_id": 3001,
    "violation_type": "fake_product",
    "description": "销售虚假商品",
    "notification_type": "merchant_violation",
    "priority": 3,
    "action_type": "handle_violation",
    "action_url": "/admin/merchant/violation/3001",
    "message": "商家违规：商家3001涉嫌销售虚假商品"
  }
}
```

#### 4.6 数据统计通知

##### 日报通知
- **事件**: `daily_report`
- **优先级**: 低 (1)

```json
{
  "type": "notification",
  "event": "daily_report",
  "data": {
    "report_date": "2025-01-30",
    "report_data": {
      "total_orders": 1250,
      "total_revenue": 25000.00,
      "new_users": 45,
      "active_merchants": 120
    },
    "notification_type": "daily_report",
    "priority": 1,
    "action_type": "view_report",
    "action_url": "/admin/report/daily/2025-01-30",
    "message": "日报生成：2025-01-30，订单1250笔，收入¥25000.00"
  }
}
```

##### 阈值告警
- **事件**: `threshold_alert`
- **优先级**: 中 (2)

```json
{
  "type": "notification",
  "event": "threshold_alert",
  "data": {
    "metric_name": "daily_orders",
    "current_value": 800,
    "threshold": 1000,
    "notification_type": "threshold_alert",
    "priority": 2,
    "action_type": "view_metrics",
    "action_url": "/admin/metrics/daily_orders",
    "message": "阈值告警：今日订单量800笔，低于预期阈值1000笔"
  }
}
```

## 优先级说明

| 优先级 | 数值 | 说明 | 前端处理建议 |
|--------|------|------|-------------|
| 低 | 1 | 一般信息通知 | 普通样式显示，可延迟处理 |
| 中 | 2 | 重要业务通知 | 突出显示，及时提醒用户 |
| 高 | 3 | 紧急或重要通知 | 强制弹窗或声音提醒 |

## 前端对接指南

### 1. WebSocket连接

```javascript
// 建立WebSocket连接
const ws = new WebSocket('ws://localhost:8181/ws');

// 连接成功
ws.onopen = function(event) {
    console.log('WebSocket连接成功');
    // 发送认证信息
    ws.send(JSON.stringify({
        type: 'auth',
        token: 'your_jwt_token'
    }));
};

// 接收消息
ws.onmessage = function(event) {
    const message = JSON.parse(event.data);
    handleWebSocketMessage(message);
};
```

### 2. 消息处理

```javascript
function handleWebSocketMessage(message) {
    const { type, event, data } = message;

    switch (type) {
        case 'notification':
            handleNotification(event, data);
            break;
        case 'message':
            handleChatMessage(event, data);
            break;
        case 'system':
            handleSystemMessage(event, data);
            break;
    }
}

function handleNotification(event, data) {
    // 根据优先级显示不同样式
    const priority = data.priority || 1;

    switch (priority) {
        case 3: // 高优先级
            showUrgentNotification(data);
            break;
        case 2: // 中优先级
            showImportantNotification(data);
            break;
        case 1: // 低优先级
        default:
            showNormalNotification(data);
            break;
    }

    // 根据action_type处理跳转
    if (data.action_type && data.action_url) {
        setupNotificationClick(data.action_type, data.action_url);
    }
}
```

### 3. 业务跳转处理

```javascript
function setupNotificationClick(actionType, actionUrl) {
    // 根据不同的action_type执行不同的跳转逻辑
    const actionHandlers = {
        'view_order': () => router.push(actionUrl),
        'process_order': () => router.push(actionUrl),
        'handle_refund': () => router.push(actionUrl),
        'accept_task': () => router.push(actionUrl),
        'view_earnings': () => router.push(actionUrl),
        'system_notification': () => showSystemDialog(actionUrl),
        // ... 其他action_type处理
    };

    const handler = actionHandlers[actionType];
    if (handler) {
        handler();
    } else {
        // 默认跳转
        if (actionUrl) {
            router.push(actionUrl);
        }
    }
}
```

### 4. 消息存储和同步

```javascript
// 将接收到的消息存储到本地
function storeMessage(message) {
    const messages = JSON.parse(localStorage.getItem('notifications') || '[]');
    messages.unshift(message);

    // 只保留最近100条消息
    if (messages.length > 100) {
        messages.splice(100);
    }

    localStorage.setItem('notifications', JSON.stringify(messages));
}

// 同步服务器消息
async function syncMessages() {
    try {
        const response = await fetch('/api/v1/chat/sessions/14/messages?page=1&page_size=50');
        const data = await response.json();

        if (data.code === 200) {
            // 处理服务器返回的消息，包含notification_data
            data.data.list.forEach(message => {
                if (message.type === 'notification' && message.notification_data) {
                    // 处理通知数据
                    handleNotificationData(message.notification_data);
                }
            });
        }
    } catch (error) {
        console.error('同步消息失败:', error);
    }
}
```

## 技术特点

### 1. 多设备支持
- 同一用户可在多个设备同时在线
- 消息会推送到用户的所有在线设备
- 支持设备间消息状态同步

### 2. 消息持久化
- 所有WebSocket消息都会异步保存到数据库
- 离线用户上线后可获取历史消息
- 支持消息分页查询和搜索

### 3. 可靠性保证
- 消息发送失败时有重试机制
- 支持消息确认和状态追踪
- 异步处理不阻塞主业务流程

### 4. 扩展性设计
- 支持新增消息类型和事件
- 灵活的数据结构设计
- 支持自定义通知模板

## 消息类型汇总表

### 用户消息 (21种)

| 事件名称 | 优先级 | 功能分类 | 描述 |
|---------|--------|----------|------|
| user_order_status_update | 中 | 订单管理 | 订单状态更新通知 |
| user_order_payment_success | 高 | 订单管理 | 订单支付成功通知 |
| user_order_delivery_update | 中 | 订单管理 | 订单配送状态更新 |
| user_order_completed | 中 | 订单管理 | 订单完成通知 |
| user_refund_result | 高 | 退款管理 | 退款结果通知 |
| user_refund_progress | 中 | 退款管理 | 退款进度通知 |
| user_coupon_received | 低 | 优惠活动 | 优惠券领取通知 |
| user_coupon_expire_reminder | 中 | 优惠活动 | 优惠券过期提醒 |
| user_promotion | 低 | 优惠活动 | 促销活动通知 |
| runner_task_assigned | 高 | 跑腿任务 | 任务分配通知 |
| runner_task_status_update | 中 | 跑腿任务 | 任务状态更新 |
| runner_task_cancelled | 中 | 跑腿任务 | 任务取消通知 |
| runner_earnings | 中 | 跑腿收益 | 收益通知 |
| runner_withdrawal | 高 | 跑腿收益 | 提现通知 |
| runner_daily_earnings | 低 | 跑腿收益 | 日收益通知 |
| runner_status_change | 中 | 跑腿状态 | 状态变更通知 |
| runner_location_update | 低 | 跑腿状态 | 位置更新通知 |
| user_review_reminder | 低 | 评价投诉 | 评价提醒通知 |
| user_review_reply | 中 | 评价投诉 | 评价回复通知 |
| user_complaint_status | 高 | 评价投诉 | 投诉状态通知 |
| user_system_notification | 中 | 系统通知 | 系统通知 |
| user_account_security | 高 | 系统通知 | 账户安全通知 |
| user_balance_change | 中 | 系统通知 | 余额变动通知 |

### 商家消息 (17种)

| 事件名称 | 优先级 | 功能分类 | 描述 |
|---------|--------|----------|------|
| merchant_new_order | 高 | 订单管理 | 新订单通知 |
| merchant_order_cancel | 中 | 订单管理 | 订单取消通知 |
| merchant_order_status_update | 中 | 订单管理 | 订单状态更新通知 |
| merchant_refund_request | 高 | 退款管理 | 退款申请通知 |
| merchant_refund_status_update | 中 | 退款管理 | 退款状态更新通知 |
| merchant_business_status | 中 | 营业状态 | 营业状态通知 |
| merchant_store_closing_reminder | 低 | 营业状态 | 店铺关闭提醒 |
| merchant_store_opening_reminder | 低 | 营业状态 | 店铺开业提醒 |
| merchant_product_audit | 中 | 商品管理 | 商品审核通知 |
| merchant_product_stock_alert | 中 | 商品管理 | 商品库存预警 |
| merchant_product_offline | 中 | 商品管理 | 商品下架通知 |
| merchant_new_review | 低 | 评价投诉 | 新评价通知 |
| merchant_complaint | 高 | 评价投诉 | 投诉通知 |
| merchant_promotion_start | 低 | 促销活动 | 促销活动开始通知 |
| merchant_promotion_end | 低 | 促销活动 | 促销活动结束通知 |
| merchant_coupon_usage | 低 | 促销活动 | 优惠券使用通知 |
| merchant_settlement | 中 | 财务管理 | 结算通知 |
| merchant_withdrawal_status | 高 | 财务管理 | 提现状态通知 |
| merchant_system_notification | 中 | 系统通知 | 系统通知 |
| merchant_policy_update | 中 | 系统通知 | 政策更新通知 |

### 管理员消息 (14种)

| 事件名称 | 优先级 | 功能分类 | 描述 |
|---------|--------|----------|------|
| system_maintenance | 高 | 系统通知 | 系统维护通知 |
| system_announcement | 中 | 系统通知 | 系统公告通知 |
| system_error_alert | 高 | 异常告警 | 系统错误告警 |
| performance_alert | 中 | 异常告警 | 性能告警 |
| security_alert | 高 | 异常告警 | 安全告警 |
| order_exception | 中 | 业务异常 | 订单异常通知 |
| payment_exception | 高 | 业务异常 | 支付异常通知 |
| refund_exception | 高 | 业务异常 | 退款异常通知 |
| user_report | 中 | 用户管理 | 用户举报通知 |
| user_ban | 中 | 用户管理 | 用户封禁通知 |
| merchant_audit | 中 | 商家管理 | 商家审核通知 |
| merchant_violation | 高 | 商家管理 | 商家违规通知 |
| daily_report | 低 | 数据统计 | 日报通知 |
| threshold_alert | 中 | 数据统计 | 阈值告警 |

## 总结

本文档涵盖了系统中所有WebSocket消息类型的详细说明，包括：

- **通用消息**：文本、媒体、系统消息
- **用户消息**：23种通知类型，涵盖订单、退款、优惠券、跑腿员任务、评价投诉、系统通知等
- **商家消息**：17种通知类型，涵盖订单管理、退款处理、营业状态、商品管理、评价投诉、促销活动、财务结算等
- **管理员消息**：14种通知类型，涵盖系统维护、异常告警、业务异常、用户管理、商家管理、数据统计等

前端开发者可以根据此文档实现完整的WebSocket消息处理逻辑，确保用户能够及时收到各类业务通知并进行相应的操作。系统支持多设备在线、消息持久化、可靠性保证和扩展性设计，为业务发展提供了坚实的技术基础。
