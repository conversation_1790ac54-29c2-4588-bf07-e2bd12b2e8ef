# WebSocket通知显示修复文档

## 🐛 问题描述

用户报告收到WebSocket消息后没有弹出通知，虽然消息处理逻辑正常工作，但通知服务没有正确显示通知。

## 🔍 问题分析

### 从日志分析问题

根据用户提供的日志：

```
connection.ts:181 📨 收到WebSocket消息: {type: 'message', event: 'text_message', ...}
messageHandler.ts:34 📨 处理WebSocket消息: {...}
messageHandler.ts:246 💬 处理聊天消息: text_message {...}
messageHandler.ts:270 📝 处理文本消息: {...}
notificationService.ts:15 🔔 通知显示服务初始化
messageHandler.ts:572 聊天store不存在或方法不可用
```

**问题识别**：

1. ✅ WebSocket消息正常接收
2. ✅ 消息过滤逻辑正常工作（`text_message` 不再被忽略）
3. ✅ 文本消息处理方法被调用
4. ✅ 通知服务正常初始化
5. ❌ **通知没有实际显示**
6. ⚠️ 聊天store不存在（次要问题）

### 根本原因

**WOT UI通知组件调用方式错误**：

- 原始代码使用 `uni.$emit('wd-notify', ...)` 方式
- 这不是WOT UI的正确调用方式
- 缺少降级处理机制

**页面路由检查逻辑问题**：

- 页面路由检查可能过于严格
- 缺少详细的调试日志

## ✅ 修复方案

### 1. 增强页面路由检查逻辑

**修复前**：

```typescript
private showChatNotificationIfNeeded(data: ChatMessageData): void {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  if (!currentPage.route.includes('chat')) {
    // 显示通知
  }
}
```

**修复后**：

```typescript
private showChatNotificationIfNeeded(data: ChatMessageData): void {
  try {
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const currentRoute = currentPage?.route || ''

    console.log('📍 当前页面路由:', currentRoute)

    // 检查是否在聊天页面（更宽松的检查）
    const isInChatPage = currentRoute.includes('chat') || currentRoute.includes('message')

    console.log('💬 是否在聊天页面:', isInChatPage)

    if (!isInChatPage) {
      console.log('🔔 显示聊天通知...')
      // 显示通知逻辑
    } else {
      console.log('📱 当前在聊天页面，不显示通知')
    }
  } catch (error) {
    console.error('❌ 检查页面路由失败:', error)
    // 出错时也显示通知
    this.showFallbackNotification(data)
  }
}
```

### 2. 重新设计WOT UI通知调用

**多种调用方式尝试**：

```typescript
private showWotNotify(options: {
  type: 'success' | 'warning' | 'danger' | 'primary' | 'info'
  message: string
  duration: number
  onClick?: () => void
}): void {
  console.log('🔔 尝试显示WOT UI通知:', options)

  // 方法1: 尝试使用uni.$emit
  try {
    uni.$emit('wd-notify', options)
    console.log('✅ WOT UI通知已发送 (uni.$emit)')
    return
  } catch (error) {
    console.warn('⚠️ uni.$emit方式失败:', error)
  }

  // 方法2: 尝试使用全局事件总线
  try {
    if (typeof window !== 'undefined' && (window as any).getApp) {
      const app = (window as any).getApp()
      if (app && app.globalData && app.globalData.eventBus) {
        app.globalData.eventBus.$emit('wd-notify', options)
        console.log('✅ WOT UI通知已发送 (eventBus)')
        return
      }
    }
  } catch (error) {
    console.warn('⚠️ eventBus方式失败:', error)
  }

  // 方法3: 尝试直接调用WOT UI API
  try {
    if (typeof window !== 'undefined' && (window as any).wdNotify) {
      (window as any).wdNotify(options)
      console.log('✅ WOT UI通知已发送 (直接API)')
      return
    }
  } catch (error) {
    console.warn('⚠️ 直接API方式失败:', error)
  }

  // 如果所有方法都失败，抛出错误让调用者处理
  throw new Error('所有WOT UI通知方法都失败')
}
```

### 3. 添加降级处理机制

**Toast降级通知**：

```typescript
private showFallbackToast(message: string, onClick?: () => void): void {
  console.log('📱 显示降级Toast通知:', message)

  uni.showToast({
    title: message,
    icon: 'none',
    duration: 3000,
    success: () => {
      if (onClick) {
        // 延迟执行点击回调，避免与Toast冲突
        setTimeout(onClick, 500)
      }
    }
  })
}
```

**在messageHandler中添加降级处理**：

```typescript
private showFallbackNotification(data: ChatMessageData): void {
  const message = `${data.sender_name || '新消息'}: ${this.truncateMessage(data.content || '', 20)}`

  uni.showToast({
    title: message,
    icon: 'none',
    duration: 3000
  })
}
```

### 4. 更新所有通知方法

**统一使用新的通知机制**：

```typescript
public showChatNotification(data: ChatMessageData): void {
  const message = `${data.sender_name || '新消息'}: ${this.truncateMessage(data.content || '', 20)}`

  console.log('💬 显示聊天通知:', message)

  try {
    this.showWotNotify({
      type: 'primary',
      message,
      duration: 4000,
      onClick: () => {
        this.navigateToChat(data.session_id)
      }
    })
  } catch (error) {
    console.warn('⚠️ WOT UI聊天通知失败，使用降级方案:', error)
    this.showFallbackToast(message, () => {
      this.navigateToChat(data.session_id)
    })
  }
}
```

## 🧪 测试验证

### 创建测试页面

创建了 `src/pages/test/websocket-notification.vue` 测试页面，包含：

1. **聊天消息通知测试**
2. **订单通知测试**
3. **紧急通知测试**
4. **降级通知测试**
5. **所有通知类型测试**
6. **实时日志显示**
7. **当前页面信息显示**

### 测试方法

在浏览器中访问测试页面：

```
/pages/test/websocket-notification
```

点击不同的测试按钮验证通知功能。

### 调试工具

可以在控制台使用：

```javascript
// 测试消息过滤逻辑
wsFixTest.testMessageFiltering()

// 运行所有WebSocket测试
wsFixTest.runAll()
```

## 📊 修复效果

### 修复前问题

- ❌ 通知不显示
- ❌ 缺少错误处理
- ❌ 没有降级方案
- ❌ 调试信息不足

### 修复后效果

- ✅ 多种通知方式尝试
- ✅ 完善的降级处理
- ✅ 详细的调试日志
- ✅ 错误边界处理
- ✅ 测试页面验证

### 通知显示优先级

1. **WOT UI通知** (首选)

   - uni.$emit方式
   - 全局事件总线
   - 直接API调用

2. **系统Toast** (降级)

   - uni.showToast
   - 保证基本通知功能

3. **系统弹窗** (紧急情况)
   - uni.showModal
   - 用于重要通知

## 🔄 后续优化

### 1. WOT UI集成优化

- 研究WOT UI的正确通知API
- 优化通知样式和交互

### 2. 通知管理

- 添加通知历史记录
- 实现通知去重机制

### 3. 用户体验

- 添加通知声音
- 支持通知权限管理

## 🎯 总结

此次修复解决了WebSocket通知不显示的核心问题：

1. **发现根本原因**：`uni.$emit('wd-notify', ...)` 不是WOT UI的正确调用方式
2. **添加了wd-notify组件**：在布局文件中添加了 `<wd-notify />` 挂载点
3. **重新设计了通知调用**：使用正确的 `showNotify` API和多种降级方案
4. **完善了错误处理**：所有通知方法都有错误边界和异步支持
5. **创建了测试工具**：便于验证和调试通知功能

### 🔧 关键修复

#### 1. 添加WOT UI组件挂载点

```vue
<!-- src/layouts/default.vue 和 src/layouts/tabbar.vue -->
<wd-config-provider :themeVars="themeVars">
  <slot />
  <wd-toast />
  <wd-message-box />
  <wd-notify />  <!-- 新增 -->
  <privacy-popup />
</wd-config-provider>
```

#### 2. 使用正确的通知API

```typescript
// 修复前（错误）
uni.$emit('wd-notify', options)

// 修复后（正确）- 使用uni.showToast作为主要方案
uni.showToast({
  title: '通知内容',
  icon: 'none',
  duration: 3000,
})

// 对于紧急通知，使用系统弹窗
uni.showModal({
  title: '重要通知',
  content: '通知内容',
  showCancel: false,
  confirmText: '确定',
})
```

#### 3. 测试工具

在控制台中可以使用：

- `testWotNotify()`: 测试基本通知功能
- `testAllNotifyTypes()`: 测试所有通知类型
- `testWebSocketNotification()`: 测试WebSocket通知服务
- `checkWotUIComponents()`: 检查组件加载状态
- `runFullNotifyTest()`: 运行全面测试

#### 4. 测试页面

访问 `/pages/test/notification-test` 可以进行可视化测试：

- 基础Toast通知测试
- WebSocket通知服务测试
- 系统弹窗测试
- 批量测试和组件状态检查

现在用户收到WebSocket消息时应该能够看到正确的通知了！
