# 外卖购物车统计API需求文档

## API概述

**接口名称**: 获取用户购物车统计信息  
**接口路径**: `/api/v1/user/takeout/cart/count`  
**请求方法**: GET  
**接口描述**: 获取当前用户购物车中商品的统计信息，用于页面显示购物车徽章和快速统计

## 请求参数

### Headers

| 参数名        | 类型   | 必填 | 说明                                |
| ------------- | ------ | ---- | ----------------------------------- |
| Authorization | string | 是   | 用户认证token，格式：Bearer {token} |
| Content-Type  | string | 是   | application/json                    |

### Query Parameters

无

### Request Body

无

## 响应参数

### 成功响应 (200)

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total_count": 8,
    "total_price": 135.0,
    "merchant_count": 2
  }
}
```

### 响应字段说明

| 字段名              | 类型    | 必填 | 说明                                           |
| ------------------- | ------- | ---- | ---------------------------------------------- |
| code                | integer | 是   | 响应状态码，200表示成功                        |
| message             | string  | 是   | 响应消息                                       |
| data                | object  | 是   | 统计数据对象                                   |
| data.total_count    | integer | 是   | 购物车中商品总数量（所有商品的数量之和）       |
| data.total_price    | number  | 是   | 购物车中商品总价格（包含规格、套餐等附加费用） |
| data.merchant_count | integer | 是   | 购物车中涉及的商家数量                         |

### 字段详细说明

#### total_count (商品总数量)

- **含义**: 购物车中所有商品的数量总和
- **计算方式**: sum(quantity) for all cart items
- **用途**: 显示在购物车图标上的红色徽章数字
- **示例**: 如果购物车中有3个商品A，2个商品B，5个商品C，则total_count = 10

#### total_price (商品总价格)

- **含义**: 购物车中所有商品的价格总和
- **计算方式**: sum(subtotal) for all cart items
- **包含内容**:
  - 商品基础价格
  - 规格附加费用
  - 套餐选项附加费用
  - 包装费
- **不包含内容**: 配送费、优惠券折扣
- **用途**: 在购物车栏显示总价，判断是否满足起送条件
- **数据类型**: 保留2位小数的浮点数

#### merchant_count (商家数量)

- **含义**: 购物车中商品来源的不同商家数量
- **计算方式**: count(distinct merchant_id) for all cart items
- **用途**:
  - 提醒用户购物车中有多个商家的商品
  - 用于订单拆分逻辑判断
  - 多商家时可能需要分别计算配送费
- **业务规则**: 通常外卖平台不允许跨商家下单，此字段用于前端验证

## 错误响应

### 401 未授权

```json
{
  "code": 401,
  "message": "未授权访问",
  "data": null
}
```

### 500 服务器错误

```json
{
  "code": 500,
  "message": "服务器内部错误",
  "data": null
}
```

## 业务逻辑

### 数据来源

- 从用户购物车表(takeout_cart_items)中查询当前用户的所有有效购物车项
- 关联商品表获取商品信息
- 关联商家表获取商家信息

### 计算规则

1. **total_count计算**:

   ```sql
   SELECT SUM(quantity) as total_count
   FROM takeout_cart_items
   WHERE user_id = ? AND deleted_at IS NULL
   ```

2. **total_price计算**:

   ```sql
   SELECT SUM(subtotal) as total_price
   FROM takeout_cart_items
   WHERE user_id = ? AND deleted_at IS NULL
   ```

3. **merchant_count计算**:
   ```sql
   SELECT COUNT(DISTINCT merchant_id) as merchant_count
   FROM takeout_cart_items
   WHERE user_id = ? AND deleted_at IS NULL
   ```

### 性能要求

- 响应时间: < 200ms
- 支持高并发访问
- 建议对user_id建立索引

### 缓存策略

- 可考虑使用Redis缓存用户购物车统计信息
- 缓存时间: 5分钟
- 缓存key格式: `takeout:cart:count:{user_id}`
- 购物车变更时需要清除对应缓存

## 前端使用场景

1. **页面加载时**: 在商家详情页、商品列表页等页面加载时调用，显示购物车状态
2. **购物车徽章**: 在底部导航栏的购物车图标上显示商品数量
3. **起送判断**: 结合商家的起送金额判断是否可以下单
4. **多商家提醒**: 当merchant_count > 1时，提醒用户购物车中有多个商家商品

## 注意事项

1. **用户身份验证**: 必须验证用户登录状态，只能查询当前用户的购物车
2. **数据一致性**: 确保统计数据与购物车详情接口数据一致
3. **软删除**: 只统计未删除的购物车项
4. **商品状态**: 可考虑过滤掉已下架或不可用的商品
5. **精度处理**: 价格计算需要注意浮点数精度问题，建议使用decimal类型

## 测试用例

### 正常场景

1. 用户购物车为空时，返回全0统计
2. 用户购物车有商品时，返回正确统计
3. 多商家商品时，merchant_count正确

### 异常场景

1. 未登录用户访问，返回401
2. 用户token过期，返回401
3. 数据库连接异常，返回500

### 边界场景

1. 购物车商品数量很大时的性能表现
2. 商品价格为0时的计算正确性
3. 包含已下架商品的购物车处理
