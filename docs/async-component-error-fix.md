# 异步组件加载错误修复指南

## 🚨 错误信息分析

### 主要错误

```
[Vue warn]: Unhandled error during execution of async component loader
SyntaxError: The requested module does not provide an export named 'onReachBottom'
```

### 错误原因

1. **错误的导入方式**: `onReachBottom` 从 Vue 导入，但它是 UniApp 的生命周期钩子
2. **异步组件加载失败**: 由于导入错误导致组件无法正常加载
3. **模块解析错误**: Vue 模块不提供 `onReachBottom` 导出

## ✅ 修复方案

### 1. 修复 onReachBottom 导入

#### 错误的导入方式

```typescript
// ❌ 错误 - 从 Vue 导入 UniApp 生命周期钩子
import { ref, computed, onMounted, onReachBottom } from 'vue'
```

#### 正确的导入方式

```typescript
// ✅ 正确 - 分别从对应模块导入
import { ref, computed, onMounted } from 'vue'
import { onReachBottom } from '@dcloudio/uni-app'
```

### 2. 修复的文件列表

#### 页面文件

1. **src/pages/coupon/center.vue**

   - 修复 `onReachBottom` 导入
   - 简化错误处理组件
   - 增强初始化错误处理

2. **src/pages/coupon/my-coupons.vue**

   - 修复 `onReachBottom` 导入
   - 保持其他功能不变

3. **src/pages/coupon/expiring-soon.vue**

   - 修复 `onReachBottom` 导入

4. **src/pages/coupon/usage-history.vue**
   - 修复 `onReachBottom` 导入

### 3. 创建测试页面

创建了 `src/pages/coupon/simple-test.vue` 用于基础功能测试：

- 测试 Store 初始化
- 测试 API 调用
- 提供调试日志
- 简化的错误处理

## 🔧 UniApp 生命周期钩子正确用法

### 常用生命周期钩子导入

```typescript
// Vue 生命周期钩子
import { onMounted, onUnmounted, ref, computed } from 'vue'

// UniApp 页面生命周期钩子
import {
  onLoad, // 页面加载
  onShow, // 页面显示
  onHide, // 页面隐藏
  onUnload, // 页面卸载
  onReachBottom, // 触底事件
  onPullDownRefresh, // 下拉刷新
} from '@dcloudio/uni-app'
```

### 使用示例

```typescript
<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { onLoad, onReachBottom } from '@dcloudio/uni-app'

const data = ref([])

// Vue 生命周期
onMounted(() => {
  console.log('组件挂载完成')
})

// UniApp 页面生命周期
onLoad((options) => {
  console.log('页面加载', options)
})

// UniApp 触底事件
onReachBottom(() => {
  console.log('触底加载更多')
  loadMore()
})
</script>
```

## 🧪 测试步骤

### 1. 基础功能测试

访问测试页面验证基础功能：

```
/pages/coupon/simple-test
```

### 2. 完整功能测试

依次测试各个页面：

```
/pages/coupon/center          # 优惠券中心
/pages/coupon/my-coupons      # 我的优惠券
/pages/coupon/expiring-soon   # 即将过期
/pages/coupon/usage-history   # 使用记录
```

### 3. 控制台检查

- 检查是否还有导入错误
- 检查是否有其他 JavaScript 错误
- 验证 API 调用是否正常

## 🚀 预防措施

### 1. 使用正确的导入规范

```typescript
// Vue 相关
import { ref, computed, onMounted } from 'vue'

// UniApp 相关
import { onLoad, onReachBottom } from '@dcloudio/uni-app'

// 项目相关
import { useSomeStore } from '@/store/some'
import SomeComponent from '@/components/SomeComponent.vue'
```

### 2. 启用 TypeScript 严格检查

在 `tsconfig.json` 中启用：

```json
{
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true
  }
}
```

### 3. 使用 ESLint 规则

添加导入检查规则：

```json
{
  "rules": {
    "import/no-unresolved": "error",
    "import/named": "error"
  }
}
```

## 🔍 调试技巧

### 1. 检查模块导出

```typescript
// 检查模块是否提供特定导出
console.log(Object.keys(await import('vue')))
console.log(Object.keys(await import('@dcloudio/uni-app')))
```

### 2. 使用动态导入

```typescript
// 动态导入可以捕获导入错误
try {
  const { onReachBottom } = await import('@dcloudio/uni-app')
  // 使用 onReachBottom
} catch (error) {
  console.error('导入失败:', error)
}
```

### 3. 检查构建输出

查看构建日志中的模块解析信息，确认导入路径正确。

## 📋 检查清单

- [ ] 所有 `onReachBottom` 都从 `@dcloudio/uni-app` 导入
- [ ] 所有 Vue 生命周期钩子都从 `vue` 导入
- [ ] 没有循环依赖问题
- [ ] 组件导入路径正确
- [ ] TypeScript 类型检查通过
- [ ] 页面可以正常加载
- [ ] 控制台无错误信息

## 🎯 修复结果

修复后应该实现：

- ✅ 页面可以正常加载
- ✅ 组件可以正常渲染
- ✅ 生命周期钩子正常工作
- ✅ 无异步组件加载错误
- ✅ 控制台无导入相关错误

通过这些修复，优惠券功能现在应该可以正常工作了！
