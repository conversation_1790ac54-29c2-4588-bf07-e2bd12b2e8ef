# 购物车优惠券和促销活动显示逻辑

## 概述

本文档描述了购物车页面中优惠券选择器和促销活动选择器的显示/隐藏逻辑，以及无优惠信息提示的实现。

## 功能需求

### 核心需求

- 当商家没有可用优惠券时，隐藏优惠券选择器
- 当商家没有可用促销活动时，隐藏促销活动选择器
- 当商家既没有优惠券也没有促销活动时，显示友好的提示信息
- 只有在有选中商品的情况下才显示相关组件

### 用户体验优化

- 减少界面冗余，提升页面简洁性
- 提供明确的状态反馈
- 避免用户困惑

## 实现逻辑

### 1. 显示条件判断

#### 优惠券选择器显示条件

```typescript
const hasAvailableCoupons = (merchantId: number) => {
  // 1. 检查是否有选中商品
  const merchantGroup = merchantGroups.value.find((g) => g.merchantId === merchantId)
  const hasSelectedItems = merchantGroup?.items.some((item) => item.selected) || false

  if (!hasSelectedItems) {
    return false // 没有选中商品，不显示
  }

  // 2. 检查是否有可用优惠券
  const availableCoupons = couponStore.getAvailableCouponsForMerchant(merchantId)
  return availableCoupons.length > 0
}
```

#### 促销活动选择器显示条件

```typescript
const hasAvailablePromotions = (merchantId: number) => {
  // 1. 检查是否有选中商品
  const merchantGroup = merchantGroups.value.find((g) => g.merchantId === merchantId)
  const hasSelectedItems = merchantGroup?.items.some((item) => item.selected) || false

  if (!hasSelectedItems) {
    return false // 没有选中商品，不显示
  }

  // 2. 检查是否有适用的促销活动
  const applicablePromotions = promotionStore.getApplicablePromotions(merchantId)
  const availablePromotions = applicablePromotions.filter((p) => p.applicable)
  return availablePromotions.length > 0
}
```

### 2. Vue模板实现

```vue
<template>
  <!-- 优惠券选择器 - 只在有可用优惠券时显示 -->
  <view v-if="hasAvailableCoupons(group.merchantId)" class="coupon-section">
    <CouponSelector
      :merchant-id="group.merchantId"
      :total-amount="calculateMerchantSelectedAmount(group)"
      :food-ids="getSelectedFoodIds(group)"
      @select="handleCouponSelect"
    />
  </view>

  <!-- 促销活动选择器 - 只在有可用促销活动时显示 -->
  <view v-if="hasAvailablePromotions(group.merchantId)" class="promotion-section">
    <MerchantPromotionSelector
      :merchant-id="group.merchantId"
      :total-amount="calculateMerchantSelectedAmount(group)"
      :food-ids="getSelectedFoodIds(group)"
      @select="handlePromotionSelect"
    />
  </view>

  <!-- 无优惠信息提示 -->
  <view
    v-if="
      group.items.some((item) => item.selected) &&
      !hasAvailableCoupons(group.merchantId) &&
      !hasAvailablePromotions(group.merchantId)
    "
    class="no-offers-tip"
  >
    <view class="tip-content">
      <text class="tip-icon">💡</text>
      <text class="tip-text">暂无可用的优惠券和促销活动</text>
    </view>
  </view>
</template>
```

### 3. 样式实现

```scss
/* 无优惠信息提示样式 */
.no-offers-tip {
  margin: 20rpx 0;
  padding: 24rpx 30rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  border-left: 6rpx solid #e9ecef;

  .tip-content {
    display: flex;
    align-items: center;
    justify-content: center;

    .tip-icon {
      font-size: 32rpx;
      margin-right: 16rpx;
    }

    .tip-text {
      font-size: 28rpx;
      color: #6c757d;
      line-height: 1.4;
    }
  }
}
```

## 显示场景分析

### 场景1: 有优惠券和促销活动

- **条件**: 有选中商品 + 有可用优惠券 + 有适用促销活动
- **显示**: ✅ 优惠券选择器 + ✅ 促销活动选择器
- **隐藏**: ❌ 无优惠提示

### 场景2: 只有优惠券

- **条件**: 有选中商品 + 有可用优惠券 + 无适用促销活动
- **显示**: ✅ 优惠券选择器
- **隐藏**: ❌ 促销活动选择器 + ❌ 无优惠提示

### 场景3: 只有促销活动

- **条件**: 有选中商品 + 无可用优惠券 + 有适用促销活动
- **显示**: ✅ 促销活动选择器
- **隐藏**: ❌ 优惠券选择器 + ❌ 无优惠提示

### 场景4: 既没有优惠券也没有促销活动

- **条件**: 有选中商品 + 无可用优惠券 + 无适用促销活动
- **显示**: ✅ 无优惠提示
- **隐藏**: ❌ 优惠券选择器 + ❌ 促销活动选择器

### 场景5: 没有选中商品

- **条件**: 无选中商品
- **显示**: 无
- **隐藏**: ❌ 优惠券选择器 + ❌ 促销活动选择器 + ❌ 无优惠提示

### 场景6: 有促销活动但不适用

- **条件**: 有选中商品 + 无可用优惠券 + 有促销活动但不适用
- **显示**: ✅ 无优惠提示
- **隐藏**: ❌ 优惠券选择器 + ❌ 促销活动选择器

## 调试信息

### 控制台日志

系统会输出详细的调试信息，包括：

```javascript
// 优惠券可用性检查
🎫 商家1优惠券可用性检查: {
  hasSelectedItems: true,
  availableCount: 2,
  hasAvailable: true,
  coupons: [
    { id: 1, name: '满30减5' },
    { id: 2, name: '满50减10' }
  ]
}

// 促销活动可用性检查
🎉 商家1促销活动可用性检查: {
  hasSelectedItems: true,
  totalCount: 3,
  availableCount: 2,
  hasAvailable: true,
  promotions: [
    { id: 1, name: '首单优惠' },
    { id: 2, name: '满减活动' }
  ]
}
```

## 性能考虑

### 1. 计算优化

- 使用计算属性缓存结果
- 避免重复计算
- 响应式更新

### 2. 渲染优化

- 条件渲染减少DOM节点
- 组件懒加载
- 避免不必要的重渲染

## 测试验证

### 功能测试

- ✅ 各种显示场景正确
- ✅ 选中状态变化响应
- ✅ 数据更新后界面同步

### 用户体验测试

- ✅ 界面简洁清晰
- ✅ 状态反馈及时
- ✅ 操作流程顺畅

## 后续优化

1. **动画效果**: 添加显示/隐藏的过渡动画
2. **个性化提示**: 根据用户行为定制提示内容
3. **A/B测试**: 测试不同提示文案的效果
4. **数据埋点**: 收集用户交互数据进行优化
