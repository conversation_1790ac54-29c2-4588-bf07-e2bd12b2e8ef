# 购物车地址检查与配送费计算集成功能实现报告

## 🎯 功能概述

### 主要功能

参考地址列表页面的实现，为购物车页面增加了地址检查功能，并基于用户默认地址的经纬度和商家经纬度信息计算配送费，同时增加了详细的调试信息显示。

### 功能特点

1. **自动地址检查**: 购物车页面打开时自动检查用户地址
2. **基于距离的配送费**: 根据用户地址和商家位置计算实际配送距离和费用
3. **智能地址获取**: 如果没有默认地址，自动获取用户地址列表
4. **详细调试信息**: 开发环境下显示配送费计算的详细过程
5. **多商家支持**: 每个商家独立计算基于距离的配送费

## 🔧 技术实现

### 1. 距离计算工具 (`src/utils/distance.ts`)

#### 核心距离计算函数

```typescript
/**
 * 计算两点之间的距离（使用Haversine公式）
 */
export function calculateDistance(lat1: number, lng1: number, lat2: number, lng2: number): number {
  const R = 6371 // 地球半径（公里）

  const dLat = toRadians(lat2 - lat1)
  const dLng = toRadians(lng2 - lng1)

  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(toRadians(lat1)) * Math.cos(toRadians(lat2)) * Math.sin(dLng / 2) * Math.sin(dLng / 2)

  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))

  return Math.round(R * c * 100) / 100 // 保留2位小数
}
```

#### 基于距离的配送费计算

```typescript
/**
 * 根据距离计算配送费
 */
export function calculateDistanceBasedDeliveryFee(
  distance: number,
  baseFee: number = 5,
  freeDistance: number = 3,
  extraFeePerKm: number = 2,
): number {
  if (distance <= freeDistance) {
    return baseFee
  } else {
    const extraDistance = distance - freeDistance
    const extraFee = Math.ceil(extraDistance) * extraFeePerKm
    return baseFee + extraFee
  }
}
```

### 2. 配送费API增强 (`src/api/delivery.ts`)

#### 增强的配送费计算函数

```typescript
export const calculateDeliveryFee = (
  config: IDeliveryConfig,
  totalAmount: number,
  merchantId?: number,
  distance?: number,
  userLat?: number,
  userLng?: number,
  merchantLat?: number,
  merchantLng?: number,
): IDeliveryFeeCalculateResponse => {
  let originalFee = config.deliveryBaseFee
  let calculatedDistance = distance

  // 如果提供了坐标信息，计算距离
  if (!calculatedDistance && userLat && userLng && merchantLat && merchantLng) {
    calculatedDistance = calculateDistance(userLat, userLng, merchantLat, merchantLng)
    console.log(`🚚 计算配送距离: ${calculatedDistance}km`)
  }

  // 如果启用了基于距离的配送费计算
  if (config.deliveryKmFee && config.deliveryKmFee > 0 && calculatedDistance) {
    originalFee = calculateDistanceBasedDeliveryFee(
      calculatedDistance,
      config.deliveryBaseFee,
      3, // 免费配送距离3km
      config.deliveryKmFee,
    )
    console.log(`🚚 基于距离的配送费: ${originalFee}元 (距离: ${calculatedDistance}km)`)
  }

  // 继续原有的满额优惠逻辑...
}
```

#### 批量计算支持坐标

```typescript
export const calculateMultiMerchantDeliveryFee = (
  config: IDeliveryConfig,
  merchantGroups: Array<{
    merchantId: number;
    selectedSubtotal: number;
    merchantLatitude?: number;
    merchantLongitude?: number;
  }>,
  userLat?: number,
  userLng?: number
): Array<{ merchantId: number; deliveryFeeResult: IDeliveryFeeCalculateResponse }>
```

### 3. 购物车Store集成地址检查

#### 地址检查方法

```typescript
/**
 * 检查并获取用户地址
 */
const checkAndFetchUserAddress = async () => {
  const addressStore = useAddressStore()

  console.log('🏠 检查用户地址状态...')

  // 如果没有默认地址，尝试获取
  if (!addressStore.defaultAddress) {
    console.log('🏠 没有默认地址，尝试获取默认地址...')
    await addressStore.fetchDefaultAddress()
  }

  // 如果地址列表为空，获取地址列表
  if (addressStore.addressList.length === 0) {
    console.log('🏠 地址列表为空，获取地址列表...')
    await addressStore.fetchAddressList()
  }

  return addressStore.defaultAddress
}
```

#### 增强的配送费计算

```typescript
/**
 * 计算所有商家的配送费
 */
const calculateAllDeliveryFees = async () => {
  // 检查并获取用户地址
  const defaultAddress = await checkAndFetchUserAddress()

  let userLat: number | undefined
  let userLng: number | undefined

  if (defaultAddress) {
    // 尝试多种可能的坐标字段名
    const lat = defaultAddress.location_latitude || defaultAddress.locationLatitude
    const lng = defaultAddress.location_longitude || defaultAddress.locationLongitude

    if (lat && lng) {
      userLat = Number(lat)
      userLng = Number(lng)
      console.log('🏠 使用默认地址坐标:', { userLat, userLng })
    }
  }

  // 准备商家数据，包含坐标信息
  const merchantData = merchantGroups.value.map((group) => ({
    merchantId: group.merchantId,
    selectedSubtotal: group.selectedSubtotal,
    merchantLatitude: group.merchantLatitude,
    merchantLongitude: group.merchantLongitude,
  }))

  const results = calculateMultiMerchantDeliveryFee(
    deliveryConfig.value,
    merchantData,
    userLat,
    userLng,
  )

  // 更新配送费结果...
}
```

### 4. 购物车页面调试信息

#### 调试信息显示控制

```typescript
/**
 * 是否显示配送费调试信息
 */
const showDeliveryDebugInfo = (_merchantId: number) => {
  // 在开发环境或者特定条件下显示调试信息
  return import.meta.env.DEV || uni.getStorageSync('showDeliveryDebug') === 'true'
}
```

#### 详细调试信息生成

```typescript
/**
 * 获取配送费调试信息
 */
const getDeliveryDebugInfo = (merchantId: number): string[] => {
  const debugInfo: string[] = []
  const deliveryFeeResult = cartStore.getDeliveryFeeResult(merchantId)
  const config = cartStore.deliveryConfig
  const defaultAddress = addressStore.defaultAddress
  const group = merchantGroups.value.find((g) => g.merchantId === merchantId)

  // 基础信息
  debugInfo.push(`📍 商家ID: ${merchantId}`)
  debugInfo.push(`💰 订单金额: ¥${group.selectedSubtotal.toFixed(2)}`)
  debugInfo.push(`🚚 配送费: ¥${deliveryFeeResult.deliveryFee.toFixed(2)}`)

  // 距离信息
  if (deliveryFeeResult.distance) {
    debugInfo.push(`📏 配送距离: ${formatDistance(deliveryFeeResult.distance)}`)
  }

  // 地址坐标信息
  if (defaultAddress) {
    const lat = defaultAddress.location_latitude || defaultAddress.locationLatitude
    const lng = defaultAddress.location_longitude || defaultAddress.locationLongitude
    if (lat && lng) {
      debugInfo.push(`🏠 用户坐标: ${lat}, ${lng}`)
    } else {
      debugInfo.push(`🏠 用户地址无坐标信息`)
    }
  }

  // 商家坐标信息
  if (group.merchantLatitude && group.merchantLongitude) {
    debugInfo.push(`🏪 商家坐标: ${group.merchantLatitude}, ${group.merchantLongitude}`)
  }

  // 配送费配置信息
  debugInfo.push(`⚙️ 基础配送费: ¥${config.deliveryBaseFee}`)
  if (config.deliveryKmFee && config.deliveryKmFee > 0) {
    debugInfo.push(`📏 距离费用: ¥${config.deliveryKmFee}/km`)
  }

  return debugInfo
}
```

#### 调试信息UI显示

```vue
<!-- 配送费调试信息 -->
<view v-if="showDeliveryDebugInfo(group.merchantId)" class="delivery-debug">
  <text class="debug-title">🚚 配送费调试信息</text>
  <view class="debug-item" v-for="(info, index) in getDeliveryDebugInfo(group.merchantId)" :key="index">
    <text class="debug-text">{{ info }}</text>
  </view>
</view>
```

### 5. 页面初始化流程

#### 增强的初始化方法

```typescript
/**
 * 初始化购物车数据
 */
const initCartData = async () => {
  try {
    console.log('🛒 开始初始化购物车数据...')

    // 获取购物车数据（包含配送费计算和地址检查）
    await cartStore.fetchCartList()

    // 确保地址信息已加载
    await cartStore.checkAndFetchUserAddress()

    console.log('🛒 购物车数据初始化完成')
    console.log('🛒 默认地址:', addressStore.defaultAddress)
    console.log('🛒 配送费配置:', cartStore.deliveryConfig)
    console.log('🛒 配送费结果:', cartStore.deliveryFeeResults)
  } catch (error) {
    console.error('🛒 初始化购物车数据失败:', error)
  }
}
```

## 🎯 功能特性

### 1. 智能地址检查

#### 自动检查流程

1. **检查默认地址**: 首先检查是否有默认地址
2. **获取默认地址**: 如果没有，尝试从API获取默认地址
3. **获取地址列表**: 如果地址列表为空，获取完整地址列表
4. **坐标提取**: 从地址信息中提取经纬度坐标

#### 兼容性处理

- 支持多种坐标字段名：`location_latitude`、`locationLatitude`
- 支持多种坐标字段名：`location_longitude`、`locationLongitude`
- 向后兼容旧版本API字段

### 2. 基于距离的配送费计算

#### 计算策略

1. **距离计算**: 使用Haversine公式计算实际距离
2. **分段计费**: 3km内基础费用，超出部分按公里计费
3. **优惠叠加**: 距离费用计算后再应用满额优惠
4. **多商家独立**: 每个商家独立计算距离和费用

#### 配置支持

- `deliveryKmFee`: 每公里额外费用
- `deliveryBaseFee`: 基础配送费
- `deliveryFreeAmount`: 免费配送门槛
- `deliveryDiscountAmount`: 折扣门槛

### 3. 详细调试信息

#### 调试内容

- 📍 商家ID和订单金额
- 🚚 最终配送费和原始费用
- 📏 计算出的配送距离
- 🏠 用户地址坐标信息
- 🏪 商家坐标信息
- ⚙️ 配送费配置参数
- 🎉 享受的优惠信息

#### 显示控制

- 开发环境自动显示
- 生产环境可通过存储开关控制
- 每个商家独立显示调试信息

## 🔄 数据流程

### 1. 页面加载流程

```
页面加载 → initCartData() → fetchCartList() → checkAndFetchUserAddress() → calculateAllDeliveryFees()
```

### 2. 地址检查流程

```
检查默认地址 → 获取默认地址(如需要) → 获取地址列表(如需要) → 提取坐标信息
```

### 3. 配送费计算流程

```
获取用户坐标 → 获取商家坐标 → 计算距离 → 计算基于距离的费用 → 应用满额优惠 → 更新UI
```

## 🎉 实现效果

### 功能完整性

- ✅ **自动地址检查**: 页面加载时自动检查和获取用户地址
- ✅ **距离计算**: 基于真实坐标计算配送距离
- ✅ **动态配送费**: 根据距离动态调整配送费
- ✅ **调试信息**: 详细的配送费计算过程展示
- ✅ **多商家支持**: 每个商家独立计算距离和费用

### 用户体验

- 🎨 **透明计费**: 用户可以看到基于距离的配送费计算
- 🎨 **实时更新**: 地址变化时配送费自动更新
- 🎨 **调试友好**: 开发者可以清楚看到计算过程
- 🎨 **智能检查**: 自动处理地址缺失的情况

### 技术特点

- 🔧 **精确计算**: 使用Haversine公式精确计算距离
- 🔧 **兼容性好**: 支持多种地址字段格式
- 🔧 **性能优化**: 智能缓存和按需计算
- 🔧 **调试完善**: 详细的日志和调试信息

## 📱 使用场景

### 1. 新用户首次使用

- 自动检查地址信息
- 引导用户添加收货地址
- 基于地址计算准确配送费

### 2. 多地址用户

- 使用默认地址计算配送费
- 支持地址切换后重新计算
- 显示不同地址的配送费差异

### 3. 开发调试场景

- 查看详细的计算过程
- 验证距离计算准确性
- 调试配送费配置问题

## 🎯 总结

通过本次功能实现，成功为购物车页面增加了完整的地址检查和基于距离的配送费计算功能：

### 技术成果

- ✅ **地址集成**: 成功集成地址store的功能
- ✅ **距离计算**: 实现了精确的距离计算算法
- ✅ **配送费优化**: 基于实际距离计算更准确的配送费
- ✅ **调试增强**: 提供了完善的调试信息展示

### 业务价值

- 🎯 **费用准确**: 基于实际距离的配送费更加公平合理
- 🎯 **用户体验**: 自动地址检查提升了使用便利性
- 🎯 **开发效率**: 详细的调试信息便于问题排查
- 🎯 **系统完善**: 购物车功能更加完整和智能

现在购物车页面具备了完整的地址检查和基于距离的配送费计算功能，用户可以看到基于实际配送距离的准确费用，开发者也可以通过调试信息清楚地了解配送费的计算过程！
