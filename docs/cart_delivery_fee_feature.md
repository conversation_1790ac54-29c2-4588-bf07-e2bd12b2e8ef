# 购物车配送费计算功能实现报告

## 🎯 功能概述

### 主要功能

为购物车页面增加配送费计算功能，根据后端配置动态计算每个商家的配送费，并提供优惠提示和费用明细显示。

### 功能特点

1. **动态配送费计算**: 根据系统配置实时计算配送费
2. **多商家支持**: 每个商家独立计算配送费
3. **优惠策略**: 支持满额免配送费和配送费折扣
4. **用户提示**: 显示配送费优惠提示和距离优惠的差额
5. **费用明细**: 清晰显示配送费构成和总计

## 🔧 技术实现

### 1. 配送费API模块

#### 配送费API (`src/api/delivery.ts`)

```typescript
/**
 * 配送费配置接口
 */
export interface IDeliveryConfig {
  deliveryBaseFee: number          // 基础配送费
  deliveryFreeEnabled: boolean     // 是否启用免配送费
  deliveryFreeAmount: number       // 免配送费门槛金额
  deliveryDiscountEnabled: boolean // 是否启用配送费折扣
  deliveryDiscountAmount: number   // 配送费折扣门槛金额
  deliveryDiscountRate: number     // 配送费折扣率
  deliveryKmFee?: number          // 基于距离的配送费（预留）
  deliveryMinOrderAmount?: number  // 最低起送金额（预留）
}

/**
 * 获取配送费配置
 */
export const getDeliveryConfig = async (): Promise<IDeliveryConfig>

/**
 * 计算配送费
 */
export const calculateDeliveryFee = (
  config: IDeliveryConfig,
  totalAmount: number,
  merchantId?: number,
  distance?: number
): IDeliveryFeeCalculateResponse

/**
 * 批量计算多商家配送费
 */
export const calculateMultiMerchantDeliveryFee = (
  config: IDeliveryConfig,
  merchantGroups: Array<{ merchantId: number; selectedSubtotal: number }>
): Array<{ merchantId: number; deliveryFeeResult: IDeliveryFeeCalculateResponse }>
```

#### 配送费计算逻辑

```typescript
// 计算优先级：
// 1. 满额免配送费 (最高优先级)
// 2. 满额配送费折扣
// 3. 基础配送费

if (config.deliveryFreeEnabled && totalAmount >= config.deliveryFreeAmount) {
  deliveryFee = 0 // 免配送费
} else if (config.deliveryDiscountEnabled && totalAmount >= config.deliveryDiscountAmount) {
  deliveryFee = originalFee * config.deliveryDiscountRate // 折扣配送费
} else {
  deliveryFee = originalFee // 基础配送费
}
```

### 2. 购物车Store集成

#### 状态扩展

```typescript
// 配送费相关状态
const deliveryConfig = ref<IDeliveryConfig | null>(null)
const deliveryFeeResults = ref<Map<number, IDeliveryFeeCalculateResponse>>(new Map())
```

#### 方法扩展

```typescript
/**
 * 获取配送费配置
 */
const fetchDeliveryConfig = async () => {
  const config = await getDeliveryConfig()
  deliveryConfig.value = config
  return config
}

/**
 * 计算所有商家的配送费
 */
const calculateAllDeliveryFees = async () => {
  if (!deliveryConfig.value) {
    await fetchDeliveryConfig()
  }

  const results = calculateMultiMerchantDeliveryFee(
    deliveryConfig.value,
    merchantGroups.value.map((group) => ({
      merchantId: group.merchantId,
      selectedSubtotal: group.selectedSubtotal,
    })),
  )

  // 更新配送费结果
  deliveryFeeResults.value.clear()
  results.forEach((result) => {
    deliveryFeeResults.value.set(result.merchantId, result.deliveryFeeResult)
  })
}

/**
 * 获取指定商家的配送费结果
 */
const getDeliveryFeeResult = (merchantId: number): IDeliveryFeeCalculateResponse | null => {
  return deliveryFeeResults.value.get(merchantId) || null
}
```

#### 自动计算触发

```typescript
// 在fetchCartList方法中自动计算配送费
const fetchCartList = async (forceRefresh = false) => {
  // ... 获取购物车数据

  // 验证商品有效性
  await validateCart()

  // 计算配送费
  await calculateAllDeliveryFees()
}
```

### 3. 购物车页面UI集成

#### 商家小计显示

```vue
<view class="merchant-summary">
  <view class="summary-row">
    <text class="summary-label">商品小计</text>
    <text class="summary-value">¥{{ group.selectedSubtotal.toFixed(2) }}</text>
  </view>
  <view v-if="group.selectedPackagingFee > 0" class="summary-row">
    <text class="summary-label">包装费</text>
    <text class="summary-value">¥{{ group.selectedPackagingFee.toFixed(2) }}</text>
  </view>
  <view class="summary-row">
    <text class="summary-label">配送费</text>
    <text class="summary-value" :class="{ 'free-delivery': getDeliveryFeeResult(group.merchantId)?.isFree }">
      {{ formatDeliveryFeeTextForMerchant(group.merchantId) }}
    </text>
  </view>
  <!-- 配送费优惠提示 -->
  <view v-if="getDeliveryFeeTipForMerchant(group.merchantId)" class="delivery-tip">
    <text class="tip-text">{{ getDeliveryFeeTipForMerchant(group.merchantId) }}</text>
  </view>
  <view class="summary-row total">
    <text class="summary-label">小计</text>
    <text class="summary-value">¥{{ getMerchantTotal(group).toFixed(2) }}</text>
  </view>
</view>
```

#### 底部结算栏

```vue
<view class="bar-right">
  <view class="price-info">
    <view class="total-info">
      <text class="total-label">合计:</text>
      <text class="total-price">¥{{ totalAmountWithDelivery.toFixed(2) }}</text>
    </view>
    <view v-if="selectedCount > 0" class="selected-count">
      已选{{ selectedCount }}件
      <text v-if="totalDeliveryFee > 0" class="delivery-fee-info">
        (含配送费¥{{ totalDeliveryFee.toFixed(2) }})
      </text>
    </view>
  </view>
  <wd-button type="primary" :disabled="selectedCount === 0" @click="handleCheckout">
    结算({{ selectedCount }})
  </wd-button>
</view>
```

#### 计算属性

```typescript
// 总配送费
const totalDeliveryFee = computed(() => {
  return merchantGroups.value.reduce((total, group) => {
    const deliveryFeeResult = cartStore.getDeliveryFeeResult(group.merchantId)
    return total + (deliveryFeeResult ? deliveryFeeResult.deliveryFee : 3.0)
  }, 0)
})

// 包含配送费的总金额
const totalAmountWithDelivery = computed(() => {
  return selectedTotalAmount.value + totalDeliveryFee.value
})
```

#### 辅助方法

```typescript
/**
 * 格式化配送费显示文本
 */
const formatDeliveryFeeTextForMerchant = (merchantId: number) => {
  const result = cartStore.getDeliveryFeeResult(merchantId)
  if (!result) {
    return '¥3.00' // 默认配送费
  }
  return formatDeliveryFeeText(result)
}

/**
 * 获取配送费优惠提示
 */
const getDeliveryFeeTipForMerchant = (merchantId: number) => {
  const config = cartStore.deliveryConfig
  const group = merchantGroups.value.find((g) => g.merchantId === merchantId)
  if (!config || !group) {
    return ''
  }
  return getDeliveryFeeTip(config, group.selectedSubtotal)
}

/**
 * 计算商家总计
 */
const getMerchantTotal = (group: any) => {
  const deliveryFeeResult = cartStore.getDeliveryFeeResult(group.merchantId)
  const deliveryFee = deliveryFeeResult ? deliveryFeeResult.deliveryFee : 3.0
  return group.selectedSubtotal + group.selectedPackagingFee + deliveryFee
}
```

### 4. 样式设计

#### 配送费样式

```scss
.summary-value {
  font-size: 28rpx;
  color: #ff5500;

  &.free-delivery {
    color: #52c41a;
    font-weight: bold;
  }
}

.delivery-tip {
  padding: 16rpx 0;
  border-top: 1px solid #f0f0f0;
  margin-top: 16rpx;
}

.tip-text {
  font-size: 24rpx;
  color: #fa8c16;
  background-color: #fff7e6;
  padding: 8rpx 12rpx;
  border-radius: 4rpx;
  border-left: 3px solid #fa8c16;
}

.delivery-fee-info {
  font-size: 22rpx;
  color: #666;
  margin-left: 8rpx;
}
```

## 🎯 功能特性

### 1. 配送费计算策略

#### 基础配送费

- 每个商家都有基础配送费（默认从配置获取）
- 支持不同商家设置不同的配送费（预留功能）

#### 满额免配送费

- 当订单金额达到免配送费门槛时，配送费为0
- 优先级最高，覆盖其他优惠

#### 满额配送费折扣

- 当订单金额达到折扣门槛时，配送费享受折扣
- 只有在不满足免配送费条件时才生效

### 2. 用户体验优化

#### 实时计算

- 购物车数据变化时自动重新计算配送费
- 选择/取消选择商品时实时更新费用

#### 优惠提示

- 显示距离免配送费还差多少金额
- 显示距离配送费折扣还差多少金额
- 已享受优惠时显示优惠信息

#### 费用明细

- 清晰显示商品小计、包装费、配送费
- 底部显示总计金额和配送费明细
- 免配送费时特殊标识

### 3. 多商家支持

#### 独立计算

- 每个商家的配送费独立计算
- 基于每个商家的选中商品金额计算

#### 分别显示

- 每个商家分组显示各自的配送费
- 总计中汇总所有商家的配送费

## 🔄 数据流程

### 1. 配置获取流程

```
页面加载 → fetchCartList() → calculateAllDeliveryFees() → fetchDeliveryConfig() → 获取系统配置
```

### 2. 配送费计算流程

```
商家分组数据 → calculateMultiMerchantDeliveryFee() → 逐个商家计算 → 更新deliveryFeeResults
```

### 3. UI更新流程

```
配送费结果更新 → 计算属性重新计算 → UI自动更新 → 用户看到最新费用
```

## 🎉 实现效果

### 功能完整性

- ✅ **配送费计算**: 根据配置动态计算配送费
- ✅ **多商家支持**: 每个商家独立计算和显示
- ✅ **优惠策略**: 支持免配送费和配送费折扣
- ✅ **用户提示**: 提供优惠提示和费用明细
- ✅ **实时更新**: 购物车变化时自动更新

### 用户体验

- 🎨 **费用透明**: 清晰显示各项费用构成
- 🎨 **优惠明确**: 明确显示享受的优惠和距离优惠的差额
- 🎨 **操作便捷**: 无需额外操作，自动计算和显示
- 🎨 **视觉友好**: 免配送费等特殊状态有明显标识

### 技术特点

- 🔧 **模块化设计**: 配送费功能独立封装
- 🔧 **可扩展性**: 预留距离配送费等扩展功能
- 🔧 **性能优化**: 使用计算属性和缓存机制
- 🔧 **错误处理**: 完善的异常处理和默认值

## 📱 使用场景

### 1. 普通购物场景

- 用户添加商品到购物车
- 系统自动计算并显示配送费
- 用户可以看到总费用包含配送费

### 2. 优惠触发场景

- 用户继续添加商品
- 达到优惠门槛时显示优惠信息
- 配送费自动调整并更新总计

### 3. 多商家购物场景

- 用户从多个商家添加商品
- 每个商家独立显示配送费
- 总计汇总所有商家的费用

## 🔮 扩展功能

### 预留功能

1. **基于距离的配送费**: 根据配送距离计算费用
2. **最低起送金额**: 设置商家最低起送门槛
3. **时段配送费**: 不同时段不同配送费
4. **会员配送费优惠**: VIP用户享受配送费优惠

### 配置扩展

- 支持更复杂的配送费计算规则
- 支持商家自定义配送费策略
- 支持地区差异化配送费

## 🎯 总结

通过本次功能实现，成功为购物车页面增加了完整的配送费计算功能：

### 技术成果

- ✅ **API集成**: 成功对接后端配送费配置API
- ✅ **计算逻辑**: 实现了完整的配送费计算逻辑
- ✅ **UI集成**: 在购物车页面完美集成配送费显示
- ✅ **用户体验**: 提供了清晰的费用明细和优惠提示

### 业务价值

- 🎯 **费用透明**: 用户可以清楚了解配送费构成
- 🎯 **促进消费**: 优惠提示鼓励用户增加购买
- 🎯 **提升体验**: 实时计算和明确显示提升用户体验
- 🎯 **运营支持**: 为运营活动提供配送费优惠工具

现在购物车页面具备了完整的配送费计算和显示功能，用户可以清楚地看到每个商家的配送费、享受的优惠以及总费用，大大提升了购物体验的透明度和便利性！
