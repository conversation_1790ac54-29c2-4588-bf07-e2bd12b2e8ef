# 聊天房间导航修复总结

## 修改内容

将"联系商家"功能的跳转路径从 `/pages/chat/detail` 修改为 `/pages/chat/room/index`。

## 修改前后对比

### 修改前

```typescript
// 跳转到聊天详情页面
uni.navigateTo({
  url: `/pages/chat/detail?sessionId=${conversation.id}&merchantId=${merchant.value.id}&merchantName=${encodeURIComponent(merchant.value.name)}`,
})
```

### 修改后

```typescript
// 跳转到聊天房间页面
uni.navigateTo({
  url: `/pages/chat/room/index?id=${conversation.id}&type=merchant&name=${encodeURIComponent(merchant.value.name)}`,
})
```

## 参数映射

| 参数名   | 修改前                                                    | 修改后                                            | 说明                     |
| -------- | --------------------------------------------------------- | ------------------------------------------------- | ------------------------ |
| 会话ID   | `sessionId=${conversation.id}`                            | `id=${conversation.id}`                           | 聊天房间页面期望的参数名 |
| 聊天类型 | 无                                                        | `type=merchant`                                   | 标识这是与商家的聊天     |
| 显示名称 | `merchantName=${encodeURIComponent(merchant.value.name)}` | `name=${encodeURIComponent(merchant.value.name)}` | 聊天房间页面期望的参数名 |
| 商家ID   | `merchantId=${merchant.value.id}`                         | 移除                                              | 聊天房间页面不需要此参数 |

## 聊天房间页面参数处理

聊天房间页面在 `onLoad` 中处理参数：

```typescript
onLoad((option) => {
  // 获取路由参数
  chatId.value = option.id || '' // 会话ID
  chatType.value = option.type || 'user' // 聊天类型
  chatTitle.value = decodeURIComponent(option.name || '') // 聊天标题

  // 设置导航栏标题
  uni.setNavigationBarTitle({
    title: chatTitle.value,
  })

  // 初始化WebSocket连接
  initWebSocket()
})
```

## 功能验证

### 1. 页面配置检查

- ✅ `/pages/chat/room/index` 页面存在
- ✅ 页面已在 `pages.json` 中正确配置
- ✅ 页面支持所需的路由参数

### 2. 参数传递检查

- ✅ `id`: 会话ID，用于标识聊天会话
- ✅ `type`: 聊天类型，设置为 `merchant` 表示商家聊天
- ✅ `name`: 商家名称，用于显示聊天标题

### 3. 预期行为

1. 用户点击"联系商家"
2. 创建与商家的聊天会话
3. 跳转到 `/pages/chat/room/index?id=12&type=merchant&name=商家名称`
4. 聊天房间页面加载，显示商家名称作为标题
5. 初始化WebSocket连接，准备聊天功能

## 优势

### 1. 路径标准化

- 使用统一的聊天房间页面
- 符合预期的URL格式：`/pages/chat/room/index?id=12`

### 2. 参数简化

- 移除不必要的 `merchantId` 参数
- 使用标准的参数名称（`id`、`type`、`name`）

### 3. 功能完整性

- 聊天房间页面已经实现了完整的聊天功能
- 支持WebSocket实时通信
- 包含消息历史记录加载

## 测试步骤

### 1. 基本功能测试

1. 进入商家详情页面
2. 点击"联系商家"按钮
3. 验证页面跳转到 `/pages/chat/room/index?id=12&type=merchant&name=商家名称`
4. 确认聊天页面正常加载

### 2. 聊天功能测试

1. 验证页面标题显示为商家名称
2. 测试消息发送功能
3. 验证WebSocket连接状态
4. 测试消息历史记录加载

### 3. 参数传递测试

1. 检查控制台日志，确认参数正确传递
2. 验证会话ID正确获取
3. 确认聊天类型设置为 `merchant`

## 注意事项

### 1. 兼容性

- 确保聊天房间页面支持 `merchant` 类型的聊天
- 验证WebSocket服务支持商家聊天功能

### 2. 错误处理

- 如果会话创建失败，不会进行页面跳转
- 聊天房间页面应该处理无效的会话ID

### 3. 用户体验

- 页面跳转应该流畅无卡顿
- 聊天界面应该快速加载
- 错误情况下提供友好提示

通过以上修改，"联系商家"功能现在会跳转到正确的聊天房间页面，使用标准的URL格式和参数。
