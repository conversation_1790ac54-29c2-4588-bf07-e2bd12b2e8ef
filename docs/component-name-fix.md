# UI组件名称修复记录

## 🚨 错误信息

```
[Vue warn]: Failed to resolve component: wot-icon
[Vue warn]: Failed to resolve component: wot-loading
```

## 🔍 问题分析

### 根本原因

项目使用的是 **wd-ui** (WotDesign) 组件库，但代码中错误地使用了 `wot-` 前缀的组件名称。

### 正确的组件名称映射

```
❌ 错误使用          ✅ 正确使用
wot-icon       →    wd-icon
wot-loading    →    wd-loading
wot-popup      →    wd-popup
wot-button     →    wd-button
```

## ✅ 修复内容

### 1. 页面文件修复

#### src/pages/coupon/center.vue

- 修复所有 `wot-icon` → `wd-icon`
- 修复所有 `wot-loading` → `wd-loading`
- 涉及组件：搜索图标、统计卡片图标、加载状态、空状态图标

#### src/pages/coupon/my-coupons.vue

- 修复快捷操作区域的图标
- 修复加载状态和空状态组件
- 修复加载更多组件

#### src/pages/coupon/expiring-soon.vue

- 修复加载状态和空状态
- 修复温馨提示图标
- 修复加载更多组件

#### src/pages/coupon/detail.vue

- 修复加载状态和错误状态图标

#### src/pages/coupon/usage-history.vue

- 修复所有状态图标
- 修复箭头图标和加载组件

### 2. 组件文件修复

#### src/components/coupon/CouponSelector.vue

- 修复弹窗组件 `wot-popup` → `wd-popup`
- 修复所有图标组件
- 修复加载状态和空状态组件

## 🔧 WotDesign UI 正确用法

### 常用组件导入和使用

```vue
<template>
  <!-- 图标组件 -->
  <wd-icon name="search" size="16" color="#999" />

  <!-- 加载组件 -->
  <wd-loading size="30" />

  <!-- 弹窗组件 -->
  <wd-popup v-model="visible" position="bottom">
    <view>弹窗内容</view>
  </wd-popup>

  <!-- 按钮组件 -->
  <wd-button type="primary">按钮</wd-button>
</template>
```

### 全局注册检查

确保在 `main.ts` 中正确注册了 WotDesign UI：

```typescript
import { createSSRApp } from 'vue'
import App from './App.vue'
import WotUI from 'wot-design-uni'

export function createApp() {
  const app = createSSRApp(App)
  app.use(WotUI)
  return {
    app,
  }
}
```

## 📋 修复的文件清单

### 页面文件 (5个)

- [x] `src/pages/coupon/center.vue`
- [x] `src/pages/coupon/my-coupons.vue`
- [x] `src/pages/coupon/expiring-soon.vue`
- [x] `src/pages/coupon/detail.vue`
- [x] `src/pages/coupon/usage-history.vue`

### 组件文件 (1个)

- [x] `src/components/coupon/CouponSelector.vue`

### 修复统计

- **总计修复**: 50+ 个组件名称错误
- **涉及组件类型**: `wd-icon`, `wd-loading`, `wd-popup`
- **修复范围**: 所有优惠券相关页面和组件

## 🧪 验证步骤

### 1. 检查控制台

确认没有 "Failed to resolve component" 错误

### 2. 功能测试

- [ ] 图标正常显示
- [ ] 加载动画正常工作
- [ ] 弹窗正常弹出和关闭
- [ ] 所有交互功能正常

### 3. 页面测试

依次测试所有优惠券页面：

- [ ] `/pages/coupon/center` - 优惠券中心
- [ ] `/pages/coupon/my-coupons` - 我的优惠券
- [ ] `/pages/coupon/expiring-soon` - 即将过期
- [ ] `/pages/coupon/detail` - 优惠券详情
- [ ] `/pages/coupon/usage-history` - 使用记录

## 🚀 预防措施

### 1. 使用IDE自动补全

配置IDE以支持WotDesign UI组件的自动补全

### 2. 创建组件使用规范

```typescript
// 推荐的组件使用方式
const WOTUI_COMPONENTS = {
  icon: 'wd-icon',
  loading: 'wd-loading',
  popup: 'wd-popup',
  button: 'wd-button',
  // ... 其他组件
}
```

### 3. ESLint规则

添加自定义规则检测错误的组件名称：

```json
{
  "rules": {
    "vue/no-unused-components": "error",
    "vue/component-name-in-template-casing": ["error", "kebab-case"]
  }
}
```

### 4. 代码审查检查点

- 确认组件名称前缀正确 (`wd-` 而不是 `wot-`)
- 检查组件属性是否符合文档规范
- 验证组件导入和注册是否正确

## 📖 WotDesign UI 文档参考

- 官方文档: [WotDesign Uni 文档](https://wot-design-uni.cn/)
- 组件列表: 查看所有可用组件及其正确名称
- 使用示例: 参考官方示例代码

## 🎯 修复结果

修复后应该实现：

- ✅ 所有UI组件正常渲染
- ✅ 图标正确显示
- ✅ 加载动画正常工作
- ✅ 弹窗交互正常
- ✅ 控制台无组件解析错误

通过这次修复，所有优惠券功能的UI组件现在都使用了正确的WotDesign UI组件名称！
