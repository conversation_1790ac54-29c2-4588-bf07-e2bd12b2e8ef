# 优惠券日期格式显示修复记录

## 🚨 问题描述

优惠券详情页面的有效期和领取时间显示为 `nan`，无法正确显示日期信息。

## 🔍 问题分析

### 根本原因

代码中使用的字段名与API返回的实际字段名不匹配：

**API实际返回的数据结构**：

```json
{
  "id": 1,
  "user_id": 2,
  "coupon_id": 1,
  "coupon": {
    "start_time": "2025-06-20T11:45:49+08:00",
    "end_time": "2025-07-20T11:37:50+08:00",
    "created_at": "2025-06-20T11:40:00+08:00"
  },
  "status": 1,
  "used_time": "0001-01-01T00:00:00Z",
  "created_at": "2025-07-01T23:17:17+08:00"
}
```

**代码中错误使用的字段**：

```javascript
// ❌ 错误的字段名
coupon.expire_time // 不存在
coupon.claimed_at // 不存在
coupon.used_at // 不存在
```

**正确的字段名**：

```javascript
// ✅ 正确的字段名
coupon.coupon.end_time // 优惠券结束时间
coupon.created_at // 用户领取时间
coupon.used_time // 使用时间
```

## ✅ 修复方案

### 1. 修复优惠券详情页面字段名

#### src/pages/coupon/detail.vue

```vue
<!-- ❌ 修复前 -->
<text class="info-value">{{ formatExpireTime(coupon.expire_time) }}</text>
<text class="info-value">{{ formatDate(coupon.claimed_at) }}</text>
<text class="info-value">{{ formatDate(coupon.used_at) }}</text>

<!-- ✅ 修复后 -->
<text class="info-value">{{ formatExpireTime(coupon.coupon.end_time) }}</text>
<text class="info-value">{{ formatDate(coupon.created_at) }}</text>
<text class="info-value">{{ formatDate(coupon.used_time) }}</text>
```

### 2. 增强日期格式化函数

#### formatDate 函数增强

```javascript
const formatDate = (dateStr: string) => {
  // 处理未使用的情况
  if (!dateStr || dateStr === '0001-01-01T00:00:00Z') {
    return '未使用'
  }

  const date = new Date(dateStr)
  // 处理无效日期
  if (isNaN(date.getTime())) {
    return '无效日期'
  }

  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
}
```

#### formatExpireTime 函数增强

```javascript
export const formatExpireTime = (expireTime: string) => {
  // 处理空值
  if (!expireTime) {
    return '无有效期信息'
  }

  const date = new Date(expireTime)
  // 处理无效日期
  if (isNaN(date.getTime())) {
    return '无效日期'
  }

  // 原有逻辑...
}
```

### 3. 添加使用状态判断函数

```javascript
const isUsed = (coupon: IUserCoupon) => {
  return coupon.status === CouponStatus.USED &&
         coupon.used_time &&
         coupon.used_time !== '0001-01-01T00:00:00Z'
}
```

### 4. 修复优惠券卡片组件

#### src/components/coupon/CouponCard.vue

```javascript
// ❌ 修复前
const expireTime = userCoupon.value?.expire_time || actualCoupon.value.end_time

// ✅ 修复后
const expireTime = userCoupon.value?.coupon?.end_time || actualCoupon.value.end_time
```

## 🔧 修复的文件列表

### 页面文件

- **src/pages/coupon/detail.vue**
  - 修复有效期字段名：`coupon.expire_time` → `coupon.coupon.end_time`
  - 修复领取时间字段名：`coupon.claimed_at` → `coupon.created_at`
  - 修复使用时间字段名：`coupon.used_at` → `coupon.used_time`
  - 增强 `formatDate` 函数处理无效日期
  - 添加 `isUsed` 函数判断使用状态

### 组件文件

- **src/components/coupon/CouponCard.vue**
  - 修复过期时间字段名获取逻辑

### 工具文件

- **src/utils/coupon.ts**
  - 增强 `formatExpireTime` 函数处理无效日期和空值

## 📋 API字段映射表

| 显示内容 | 错误字段名           | 正确字段名               | 说明                 |
| -------- | -------------------- | ------------------------ | -------------------- |
| 有效期   | `coupon.expire_time` | `coupon.coupon.end_time` | 优惠券模板的结束时间 |
| 领取时间 | `coupon.claimed_at`  | `coupon.created_at`      | 用户优惠券的创建时间 |
| 使用时间 | `coupon.used_at`     | `coupon.used_time`       | 用户优惠券的使用时间 |

## 🧪 测试验证

### 1. 优惠券详情页面测试

访问优惠券详情页面，验证：

- [ ] 有效期正确显示（如：有效期至2025-07-20）
- [ ] 领取时间正确显示（如：2025-07-01 23:17）
- [ ] 使用时间正确处理（未使用显示"未使用"）

### 2. 日期格式测试

测试各种日期格式：

- [ ] 正常日期：`2025-07-20T11:37:50+08:00`
- [ ] 未使用时间：`0001-01-01T00:00:00Z`
- [ ] 空值或undefined
- [ ] 无效日期字符串

### 3. 优惠券卡片测试

在列表页面验证：

- [ ] 优惠券卡片的过期时间正确显示
- [ ] 不同状态的优惠券显示正确

## 🚀 预期修复结果

修复后应该实现：

- ✅ 有效期正确显示为具体日期或相对时间
- ✅ 领取时间正确显示为格式化的日期时间
- ✅ 使用时间根据状态正确显示
- ✅ 无效日期和空值得到妥善处理
- ✅ 不再出现 `nan` 或 `Invalid Date` 错误

## 🔍 调试技巧

### 1. 控制台调试

```javascript
// 检查优惠券数据结构
console.log('优惠券数据:', coupon.value)
console.log('结束时间:', coupon.value?.coupon?.end_time)
console.log('创建时间:', coupon.value?.created_at)
console.log('使用时间:', coupon.value?.used_time)
```

### 2. 日期解析测试

```javascript
// 测试日期解析
const testDate = '2025-07-20T11:37:50+08:00'
console.log('原始日期:', testDate)
console.log('解析结果:', new Date(testDate))
console.log('是否有效:', !isNaN(new Date(testDate).getTime()))
```

## 📝 注意事项

1. **时区处理**: API返回的日期包含时区信息 `+08:00`，JavaScript能正确解析
2. **特殊值处理**: `0001-01-01T00:00:00Z` 表示未使用，需要特殊处理
3. **向后兼容**: 修复保持了对旧数据格式的兼容性
4. **错误处理**: 增加了对无效日期的处理，避免显示错误信息

通过这些修复，优惠券详情页面现在应该能够正确显示所有日期信息了！
