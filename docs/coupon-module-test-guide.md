# 优惠券模块测试指南

## 🎯 模块概述

优惠券模块是外卖系统的重要组成部分，提供完整的优惠券功能，包括优惠券中心、我的优惠券、优惠券选择器等功能。

## 📁 文件结构

```
src/
├── api/
│   ├── coupon.ts                    # 优惠券API接口
│   └── coupon.typings.ts           # 优惠券类型定义
├── store/
│   └── coupon.ts                   # 优惠券状态管理
├── components/
│   └── coupon/
│       ├── CouponSelector.vue      # 优惠券选择器组件
│       └── CouponCard.vue          # 优惠券卡片组件
├── pages/
│   └── coupon/
│       ├── center.vue              # 优惠券中心页面
│       └── my-coupons.vue          # 我的优惠券页面
└── utils/
    └── coupon.ts                   # 优惠券工具函数
```

## 🧪 测试内容

### 1. API接口测试

#### 1.1 获取我的优惠券列表

```javascript
// 测试API调用
import { getMyCoupons } from '@/api/coupon'

// 获取未使用的优惠券
const response = await getMyCoupons({
  status: 1, // 1:未使用 2:已使用 3:已过期
  page: 1,
  page_size: 20,
})

console.log('我的优惠券:', response.data)
```

#### 1.2 获取订单可用优惠券

```javascript
import { getAvailableCouponsForOrder } from '@/api/coupon'

const response = await getAvailableCouponsForOrder({
  merchant_id: 1,
  total_amount: 50.0,
  food_ids: '1,2,3',
})

console.log('可用优惠券:', response.data.available_coupons)
console.log('不可用优惠券:', response.data.unavailable_coupons)
```

#### 1.3 领取优惠券

```javascript
import { claimCoupon } from '@/api/coupon'

const response = await claimCoupon({
  coupon_id: 1,
})

console.log('领取结果:', response.data)
```

### 2. Store状态管理测试

#### 2.1 优惠券Store基础功能

```javascript
import { useCouponStore } from '@/store/coupon'

const couponStore = useCouponStore()

// 加载我的优惠券
await couponStore.fetchMyCoupons({ refresh: true })
console.log('我的优惠券:', couponStore.myCoupons)

// 加载优惠券中心
await couponStore.fetchCouponCenter({ refresh: true })
console.log('优惠券中心:', couponStore.centerCoupons)

// 领取优惠券
await couponStore.claimCoupon(1)

// 选择优惠券
couponStore.selectCouponForMerchant(1, couponStore.myCoupons[0])
console.log('选中的优惠券:', couponStore.getSelectedCouponForMerchant(1))
```

#### 2.2 计算属性测试

```javascript
// 测试各种计算属性
console.log('未使用优惠券:', couponStore.unusedCoupons)
console.log('已使用优惠券:', couponStore.usedCoupons)
console.log('已过期优惠券:', couponStore.expiredCoupons)
console.log('按类型分组:', couponStore.couponsByType)
console.log('按商家分组:', couponStore.couponsByMerchant)
console.log('总优惠金额:', couponStore.totalDiscountAmount)
```

### 3. 组件功能测试

#### 3.1 优惠券选择器组件

```vue
<template>
  <CouponSelector
    :merchant-id="1"
    :total-amount="50.0"
    :food-ids="'1,2,3'"
    @select="handleCouponSelect"
  />
</template>

<script setup>
import CouponSelector from '@/components/coupon/CouponSelector.vue'

const handleCouponSelect = (merchantId, coupon) => {
  console.log('选择优惠券:', { merchantId, coupon })
}
</script>
```

#### 3.2 优惠券卡片组件

```vue
<template>
  <CouponCard
    :coupon="coupon"
    :can-claim="true"
    :show-progress="true"
    mode="claim"
    @claim="handleClaimCoupon"
  />
</template>

<script setup>
import CouponCard from '@/components/coupon/CouponCard.vue'

const coupon = {
  id: 1,
  name: '满50减10优惠券',
  type: 1,
  amount: 10,
  min_order_amount: 50,
  // ... 其他属性
}

const handleClaimCoupon = (coupon) => {
  console.log('领取优惠券:', coupon)
}
</script>
```

### 4. 页面功能测试

#### 4.1 优惠券中心页面

- **页面路径**: `/pages/coupon/center`
- **测试要点**:
  - 横幅轮播显示
  - 分类筛选功能
  - 优惠券列表加载
  - 优惠券领取功能
  - 批量领取功能
  - 分页加载更多

#### 4.2 我的优惠券页面

- **页面路径**: `/pages/coupon/my-coupons`
- **测试要点**:
  - 统计信息显示
  - 状态筛选功能
  - 优惠券列表显示
  - 优惠券使用功能
  - 过期提醒功能

### 5. 工具函数测试

#### 5.1 优惠券格式化函数

```javascript
import {
  formatCouponAmount,
  getCouponTypeText,
  getCouponDescription,
  calculateCouponDiscount,
  isCouponAvailable,
} from '@/utils/coupon'

const coupon = {
  type: 1, // 满减券
  amount: 10,
  min_order_amount: 50,
}

console.log('金额格式:', formatCouponAmount(coupon)) // ¥10
console.log('类型文本:', getCouponTypeText(coupon.type)) // 满减券
console.log('描述文本:', getCouponDescription(coupon)) // 满¥50减¥10
console.log('折扣金额:', calculateCouponDiscount(coupon, 60)) // 10
console.log('可用性检查:', isCouponAvailable(coupon, 60)) // { available: true }
```

## 🔍 测试场景

### 场景1：用户浏览优惠券中心

1. 进入优惠券中心页面
2. 查看横幅轮播
3. 切换不同分类
4. 浏览优惠券列表
5. 领取感兴趣的优惠券
6. 批量领取多张优惠券

### 场景2：用户查看我的优惠券

1. 进入我的优惠券页面
2. 查看统计信息
3. 切换不同状态筛选
4. 查看优惠券详情
5. 使用可用优惠券
6. 查看即将过期的优惠券

### 场景3：购物车中选择优惠券

1. 添加商品到购物车
2. 进入购物车页面
3. 查看优惠券选择器
4. 选择合适的优惠券
5. 查看优惠后的价格
6. 进行结算

### 场景4：订单确认页面使用优惠券

1. 从购物车进入订单确认
2. 查看已选择的优惠券
3. 确认优惠金额
4. 提交订单

## ⚠️ 注意事项

### 1. 数据格式

- 确保API返回的数据格式与类型定义一致
- 注意时间格式的处理（ISO 8601格式）
- 金额字段使用数字类型，保留两位小数

### 2. 状态管理

- 优惠券状态变更后及时更新Store
- 注意不同页面间的数据同步
- 合理使用缓存避免重复请求

### 3. 用户体验

- 加载状态的友好提示
- 错误情况的适当处理
- 操作反馈的及时响应

### 4. 性能优化

- 列表数据的分页加载
- 图片资源的懒加载
- 避免不必要的API调用

## 🐛 常见问题

### 1. 优惠券不显示

- 检查API接口是否正常返回数据
- 确认数据格式是否正确
- 检查组件props传递是否正确

### 2. 优惠券无法领取

- 检查用户登录状态
- 确认优惠券是否还有库存
- 检查用户是否已达到领取上限

### 3. 优惠券选择器不工作

- 确认商家ID和订单金额是否正确传递
- 检查优惠券是否满足使用条件
- 确认事件监听是否正确绑定

### 4. 样式显示异常

- 检查CSS样式是否正确加载
- 确认组件嵌套层级是否合理
- 检查响应式布局是否适配

## 📊 测试清单

### 功能测试

- [ ] 优惠券列表加载
- [ ] 优惠券分类筛选
- [ ] 优惠券状态筛选
- [ ] 优惠券领取功能
- [ ] 优惠券选择功能
- [ ] 优惠券使用功能
- [ ] 批量操作功能
- [ ] 分页加载功能

### 界面测试

- [ ] 页面布局正确
- [ ] 组件样式正常
- [ ] 响应式适配
- [ ] 交互动画流畅
- [ ] 图标显示正确
- [ ] 文字内容准确

### 兼容性测试

- [ ] 不同浏览器兼容
- [ ] 不同设备尺寸适配
- [ ] 不同网络环境测试
- [ ] 异常情况处理

### 性能测试

- [ ] 页面加载速度
- [ ] 列表滚动流畅度
- [ ] 内存使用情况
- [ ] 网络请求优化

## 🎯 完善后的功能清单

### 新增页面功能

- **即将过期优惠券页面** (`/pages/coupon/expiring-soon`)

  - 按时间筛选即将过期的优惠券
  - 温馨提示和使用建议
  - 快捷跳转到相关页面

- **优惠券详情页面** (`/pages/coupon/detail`)

  - 详细的优惠券信息展示
  - 使用说明和规则
  - 订单关联信息

- **使用记录页面** (`/pages/coupon/usage-history`)

  - 优惠券使用历史记录
  - 统计概览信息
  - 时间筛选功能

- **功能测试页面** (`/pages/test/coupon-test`)
  - 完整的功能测试界面
  - API接口测试
  - 组件功能测试
  - 实时测试日志

### 个人中心集成

- 修复优惠券跳转路径
- 集成真实优惠券数量显示
- 添加优惠券中心快捷入口
- 优化菜单样式和交互

### 优惠券中心完善

- 添加统计信息卡片
- 集成搜索功能
- 添加筛选功能
- 即将过期优惠券快捷入口
- 实时数据更新

### 我的优惠券页面增强

- 添加快捷操作区域
- 优化统计信息显示
- 增加多个功能入口
- 改进用户体验

## 🔗 页面跳转关系

```
个人中心 (/pages/user/index)
├── 我的优惠券 (/pages/coupon/my-coupons)
│   ├── 优惠券详情 (/pages/coupon/detail)
│   ├── 即将过期 (/pages/coupon/expiring-soon)
│   └── 使用记录 (/pages/coupon/usage-history)
└── 优惠券中心 (/pages/coupon/center)
    ├── 我的优惠券 (/pages/coupon/my-coupons)
    └── 即将过期 (/pages/coupon/expiring-soon)

购物车页面 (/pages/cart/index)
└── 优惠券选择器组件 (CouponSelector)
    └── 优惠券中心 (/pages/coupon/center)

测试页面 (/pages/test/coupon-test)
├── 优惠券中心 (/pages/coupon/center)
├── 我的优惠券 (/pages/coupon/my-coupons)
├── 即将过期 (/pages/coupon/expiring-soon)
└── 使用记录 (/pages/coupon/usage-history)
```

## 🎨 用户体验优化

### 视觉设计

- 统一的颜色主题和视觉风格
- 丰富的图标和状态标识
- 流畅的动画和过渡效果
- 响应式布局适配

### 交互设计

- 直观的操作流程
- 及时的反馈提示
- 便捷的快捷操作
- 智能的推荐功能

### 功能完整性

- 完整的优惠券生命周期管理
- 多维度的数据统计
- 灵活的筛选和搜索
- 详细的使用记录

这个优惠券模块提供了完整的优惠券功能，包括领取、使用、管理等核心功能，可以很好地提升用户的购物体验！
