# 优惠券功能故障排除指南

## 🚨 常见错误及解决方案

### 1. TypeError: Cannot read properties of undefined (reading 'length')

**错误原因：**

- API接口返回的数据结构不符合预期
- 网络请求失败导致数据为undefined
- Store中的数据初始化问题

**解决方案：**

```javascript
// ✅ 正确的写法 - 使用可选链和默认值
const count = couponStore.myCoupons?.length || 0

// ❌ 错误的写法 - 直接访问可能为undefined的属性
const count = couponStore.myCoupons.length
```

**已修复的文件：**

- `src/store/coupon.ts` - 所有getters和actions中的数组访问
- `src/pages/user/index.vue` - 获取优惠券统计时的错误处理
- `src/pages/coupon/center.vue` - 统计数据加载的错误处理

### 2. 组件图标不显示

**错误原因：**

- 使用了错误的图标组件名称
- 项目使用wot-ui但代码中使用了其他UI库的组件

**解决方案：**

```vue
<!-- ✅ 正确的写法 - 使用wot-ui组件 -->
<wot-icon name="coupon" size="20" color="#ff5500" />
<wot-loading size="30" />
<wot-popup v-model="showModal" position="bottom"></wot-popup>
```

**已修复的文件：**

- `src/pages/coupon/center.vue`
- `src/pages/coupon/my-coupons.vue`
- `src/pages/coupon/expiring-soon.vue`
- `src/pages/coupon/detail.vue`
- `src/pages/coupon/usage-history.vue`
- `src/components/coupon/CouponSelector.vue`
- `src/components/coupon/CouponCard.vue`

### 3. 页面无法正常显示

**错误原因：**

- API接口不可用或返回错误
- 数据格式不匹配
- 组件渲染错误

**解决方案：**

1. **添加错误边界组件**

```vue
<ErrorFallback
  v-if="hasError"
  :title="'加载失败'"
  :message="errorMessage"
  :show-retry="true"
  @retry="loadData"
/>
```

2. **完善错误处理**

```javascript
try {
  await couponStore.fetchMyCoupons({ refresh: true })
} catch (error) {
  console.error('加载失败:', error)
  // 设置默认值避免undefined错误
  this.myCoupons = []
  this.updateCouponStats()
}
```

## 🔧 开发环境配置

### 1. 确保正确安装依赖

```bash
# 安装wot-ui依赖
npm install wot-design-uni

# 或使用yarn
yarn add wot-design-uni
```

### 2. 配置wot-ui

在`main.ts`中正确引入wot-ui：

```typescript
import { createSSRApp } from 'vue'
import App from './App.vue'
import WotUI from 'wot-design-uni'

export function createApp() {
  const app = createSSRApp(App)
  app.use(WotUI)
  return {
    app,
  }
}
```

### 3. 配置类型支持

在`types/global.d.ts`中添加：

```typescript
declare module 'wot-design-uni'
```

## 🧪 测试建议

### 1. 使用测试页面

访问 `/pages/test/coupon-test` 页面进行功能测试：

- API接口测试
- Store状态测试
- 组件功能测试
- 工具函数测试

### 2. 模拟数据测试

在API不可用时，可以在Store中添加模拟数据：

```javascript
// 在fetchMyCoupons方法的catch块中添加
catch (error) {
  console.error('API不可用，使用模拟数据')
  this.myCoupons = [
    {
      id: 1,
      coupon: {
        id: 1,
        name: '满50减10优惠券',
        type: CouponType.DISCOUNT,
        amount: 10,
        min_order_amount: 50
        // ... 其他必要字段
      },
      status: CouponStatus.UNUSED,
      // ... 其他必要字段
    }
  ]
}
```

### 3. 网络状态检测

```javascript
// 检测网络状态
const networkType = uni.getNetworkType()
if (networkType.networkType === 'none') {
  uni.showToast({
    title: '网络连接失败',
    icon: 'error',
  })
}
```

## 📱 兼容性说明

### 1. 平台兼容性

- ✅ H5
- ✅ 微信小程序
- ✅ 支付宝小程序
- ✅ App

### 2. 组件兼容性

所有优惠券相关组件都使用了wot-ui，确保在不同平台上的一致性。

### 3. API兼容性

所有API调用都包含了错误处理，在接口不可用时会显示友好的错误提示。

## 🔍 调试技巧

### 1. 开启详细日志

在开发环境中，所有Store操作都会输出详细日志：

```javascript
console.log('✅ 优惠券列表加载成功:', coupons.length)
console.error('❌ 加载优惠券列表失败:', error)
```

### 2. 使用Vue DevTools

安装Vue DevTools浏览器扩展，可以实时查看：

- Pinia Store状态
- 组件props和data
- 事件触发情况

### 3. 网络请求监控

在浏览器开发者工具的Network面板中监控：

- API请求状态
- 响应数据格式
- 请求耗时

## 🚀 性能优化建议

### 1. 数据缓存

```javascript
// 避免重复请求
if (this.myCoupons.length > 0 && !refresh) {
  return
}
```

### 2. 分页加载

```javascript
// 实现分页加载
const loadMore = () => {
  if (!loading.value && hasMore.value) {
    loadCoupons(false)
  }
}
```

### 3. 防抖搜索

```javascript
// 搜索防抖
const handleSearch = () => {
  clearTimeout(searchTimer)
  searchTimer = setTimeout(() => {
    loadCoupons(true)
  }, 500)
}
```

## 📞 技术支持

如果遇到其他问题，请：

1. 查看浏览器控制台错误信息
2. 检查网络请求状态
3. 确认API接口是否正常
4. 验证数据格式是否正确

记住：大多数问题都是由于数据为undefined或API不可用导致的，添加适当的错误处理和默认值可以解决大部分问题。
