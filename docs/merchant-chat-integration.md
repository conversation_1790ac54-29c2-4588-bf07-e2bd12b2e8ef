# 商家聊天功能集成文档

## 功能概述

在外卖商家详情页面中集成了"联系商家"功能，用户可以通过点击按钮直接与商家开启聊天会话，实现实时沟通。

## 功能特性

### 1. 智能会话创建

- 自动检测用户登录状态
- 创建用户与商家的专属聊天会话
- 支持会话标题自定义（显示商家名称）
- 传递商家ID等关键信息

### 2. 用户体验优化

- 一键跳转到聊天页面
- 加载状态提示
- 错误处理和友好提示
- 视觉效果增强（渐变背景、点击动画）

### 3. 安全性保障

- 用户身份验证
- 参数验证和错误处理
- 异常情况的优雅降级

## 技术实现

### 1. 前端实现

#### 1.1 UI界面更新

```vue
<!-- 联系方式 -->
<view class="merchant-contact">
  <view class="contact-item" @click="contactMerchant">
    <wd-icon name="chat" size="16" color="#52c41a" />
    <text>联系商家</text>
  </view>
  <view class="contact-item" @click="callMerchant">
    <wd-icon name="phone" size="16" color="#ff9500" />
    <text>拨打电话</text>
  </view>
  <view class="contact-item">
    <wd-icon name="location" size="16" color="#1890ff" />
    <text>查看位置</text>
  </view>
</view>
```

#### 1.2 核心功能实现

```typescript
/**
 * 联系商家（聊天）
 */
async function contactMerchant() {
  try {
    // 1. 检查用户登录状态
    const userStore = useUserStore()
    if (!userStore.isLoggedIn) {
      uni.showToast({
        title: '请先登录',
        icon: 'none',
      })
      uni.navigateTo({
        url: '/pages/auth/login',
      })
      return
    }

    // 2. 验证商家信息
    if (!merchant.value?.id) {
      uni.showToast({
        title: '商家信息不可用',
        icon: 'none',
      })
      return
    }

    // 3. 显示加载状态
    uni.showLoading({
      title: '正在创建会话...',
    })

    // 4. 创建聊天会话
    const conversation = await createConversation({
      type: ConversationType.MERCHANT,
      participantId: merchant.value.id.toString(),
      title: `与${merchant.value.name}的对话`,
      extra: {
        merchantId: merchant.value.id.toString(),
      },
    })

    uni.hideLoading()

    // 5. 跳转到聊天页面
    uni.navigateTo({
      url: `/pages/chat/detail?conversationId=${conversation.id}&merchantId=${merchant.value.id}&merchantName=${encodeURIComponent(merchant.value.name)}`,
    })
  } catch (error) {
    uni.hideLoading()
    console.error('创建商家聊天会话失败:', error)
    uni.showToast({
      title: '创建会话失败，请重试',
      icon: 'none',
    })
  }
}
```

#### 1.3 样式优化

```scss
.contact-item {
  display: flex;
  align-items: center;
  margin-right: 24px;
  font-size: 14px;
  color: #666;
  padding: 8px 12px;
  border-radius: 16px;
  transition: all 0.2s ease;
  background: #f8f9fa;

  wd-icon {
    margin-right: 4px;
  }

  // 联系商家按钮特殊样式
  &:first-child {
    background: linear-gradient(135deg, #52c41a, #73d13d);
    color: white;
    font-weight: 500;

    &:active {
      transform: scale(0.95);
      background: linear-gradient(135deg, #389e0d, #52c41a);
    }
  }

  &:active {
    transform: scale(0.95);
    background: #e6f7ff;
  }
}
```

### 2. API集成

#### 2.1 聊天API调用

```typescript
import { createConversation } from '@/api/chat'
import { ConversationType } from '@/api/chat.typings'

// 创建会话参数
const conversationParams = {
  type: ConversationType.MERCHANT,
  participantId: merchantId.toString(),
  title: `与${merchantName}的对话`,
  extra: {
    merchantId: merchantId.toString(),
  },
}
```

#### 2.2 后端API支持

- **创建会话**: `POST /chat/conversations`
- **会话类型**: `MERCHANT` (商家类型)
- **参数传递**: 商家ID、会话标题、扩展信息

## 用户使用流程

### 1. 正常流程

1. 用户进入商家详情页面
2. 点击"联系商家"按钮
3. 系统检查登录状态（已登录）
4. 创建与商家的聊天会话
5. 自动跳转到聊天页面
6. 开始与商家对话

### 2. 未登录流程

1. 用户进入商家详情页面
2. 点击"联系商家"按钮
3. 系统检查登录状态（未登录）
4. 提示用户登录
5. 跳转到登录页面
6. 登录后可重新尝试联系商家

### 3. 异常处理流程

1. 网络异常或API错误
2. 显示友好的错误提示
3. 用户可以重新尝试
4. 记录错误日志便于排查

## 测试场景

### 1. 功能测试

- ✅ 已登录用户点击联系商家
- ✅ 未登录用户点击联系商家
- ✅ 网络异常情况处理
- ✅ 商家信息缺失情况处理
- ✅ 会话创建成功后的页面跳转

### 2. UI测试

- ✅ 按钮样式和动画效果
- ✅ 加载状态显示
- ✅ 错误提示显示
- ✅ 不同屏幕尺寸的适配

### 3. 集成测试

- ✅ 与聊天模块的集成
- ✅ 与用户认证系统的集成
- ✅ 与商家数据的集成

## 注意事项

### 1. 依赖要求

- 聊天模块API正常运行
- 用户认证系统正常运行
- 商家数据完整性

### 2. 性能考虑

- 会话创建的响应时间
- 页面跳转的流畅性
- 错误处理的及时性

### 3. 安全考虑

- 用户身份验证
- 商家ID验证
- 参数传递安全性

## 后续优化建议

### 1. 功能增强

- 支持快速消息模板
- 添加商家在线状态显示
- 支持语音和图片消息
- 添加消息推送通知

### 2. 用户体验

- 优化加载动画
- 添加操作引导
- 支持离线消息提醒
- 优化错误提示文案

### 3. 数据分析

- 统计联系商家的使用频率
- 分析用户聊天行为
- 监控会话创建成功率
- 收集用户反馈

通过以上实现，用户可以在商家详情页面便捷地与商家建立聊天联系，提升了用户体验和商家服务效率。
