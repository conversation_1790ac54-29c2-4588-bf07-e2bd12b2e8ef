# 多商家订单API后端开发需求文档

## 概述

当前多商家订单创建API `/api/v1/user/takeout/order/create` 返回的是订单数组格式，前端已适配此格式。本文档整理相关API需求，确保后端与前端保持一致。

## 当前API返回格式

### 请求格式

```typescript
interface CreateMultiMerchantOrderRequest {
  takeoutAddressID: number
  paymentMethod: string
  merchantOrders: MerchantOrderRequest[]
}

interface MerchantOrderRequest {
  merchantID: number
  cartItemIDs: number[]
  remark?: string
  couponID?: number
  promotionID?: number
}
```

### 响应格式

```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "orderID": 76,
      "orderNo": "202507221112146938",
      "userID": 2,
      "merchantID": 1,
      "totalAmount": 21,
      "payAmount": 21,
      "discountAmount": 0,
      "orderStatus": 10,
      "payStatus": 0,
      "deliveryStatus": 0,
      "deliveryFee": 2,
      "packagingFee": 2.5,
      "deliveryInfo": null,
      "items": null,
      "paymentMethod": "",
      "remark": "是淡粉色",
      "couponInfo": null,
      "isRated": false,
      "hasRefund": false,
      "refundNo": "",
      "refundStatus": 0,
      "refundAmount": 0,
      "refundReason": "",
      "refundTime": null,
      "createTime": "2025-07-22T11:12:15+08:00",
      "payTime": null,
      "acceptedTime": null,
      "deliveryTime": null,
      "completeTime": null,
      "cancelTime": null,
      "cancelReason": "",
      "statusText": ""
    },
    {
      "orderID": 77,
      "orderNo": "202507221112153224",
      "userID": 2,
      "merchantID": 5,
      "totalAmount": 9.9,
      "payAmount": 9.9,
      "discountAmount": 0,
      "orderStatus": 10,
      "payStatus": 0,
      "deliveryStatus": 0,
      "deliveryFee": 2,
      "packagingFee": 2,
      "deliveryInfo": null,
      "items": null,
      "paymentMethod": "",
      "remark": "asdfc",
      "couponInfo": null,
      "isRated": false,
      "hasRefund": false,
      "refundNo": "",
      "refundStatus": 0,
      "refundAmount": 0,
      "refundReason": "",
      "refundTime": null,
      "createTime": "2025-07-22T11:12:16+08:00",
      "payTime": null,
      "acceptedTime": null,
      "deliveryTime": null,
      "completeTime": null,
      "cancelTime": null,
      "cancelReason": "",
      "statusText": ""
    }
  ]
}
```

## 前端处理逻辑

前端已实现以下处理逻辑：

1. **数组检测**：检查返回结果是否为数组
2. **单订单处理**：如果数组长度为1，直接跳转到订单详情页
3. **多订单处理**：如果数组长度大于1，跳转到订单列表页
4. **兼容性处理**：如果返回单个对象（旧格式），直接跳转到订单详情页

## 后端API需求

### 1. 订单创建API优化

**API路径**: `/api/v1/user/takeout/order/create`

**需求**:

- 支持多商家订单创建
- 返回格式统一为数组（即使只有一个订单）
- 确保每个订单对象包含完整的订单信息

### 2. 订单详情API

**API路径**: `/api/v1/user/takeout/order/{orderID}`

**需求**:

- 支持单个订单详情查询
- 返回完整的订单信息，包括订单项目列表
- 确保与创建订单返回的格式一致

### 3. 订单列表API

**API路径**: `/api/v1/user/takeout/order`

**需求**:

- 支持多订单列表查询
- 支持按状态、时间等条件筛选
- 分页支持

### 4. 数据完整性要求

**订单项目信息**:

- 当前返回的 `items` 字段为 `null`，建议填充完整的订单项目信息
- 包括商品名称、规格、数量、价格等详细信息

**配送信息**:

- 当前返回的 `deliveryInfo` 字段为 `null`，建议填充配送地址信息
- 包括收货人、联系方式、详细地址等

**支付方式**:

- 当前返回的 `paymentMethod` 字段为空字符串，建议填充实际支付方式

**状态文本**:

- 当前返回的 `statusText` 字段为空字符串，建议填充订单状态的中文描述

## 建议的API响应格式优化

```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "orderID": 76,
      "orderNo": "202507221112146938",
      "userID": 2,
      "merchantID": 1,
      "merchantName": "美味餐厅",
      "totalAmount": 21,
      "payAmount": 21,
      "discountAmount": 0,
      "orderStatus": 10,
      "payStatus": 0,
      "deliveryStatus": 0,
      "deliveryFee": 2,
      "packagingFee": 2.5,
      "deliveryInfo": {
        "receiverName": "张三",
        "receiverPhone": "13800138000",
        "address": "北京市朝阳区xxx街道xxx号",
        "longitude": 116.397128,
        "latitude": 39.916527
      },
      "items": [
        {
          "id": 1,
          "orderID": 76,
          "productID": 101,
          "productName": "宫保鸡丁",
          "productType": "main",
          "price": 18,
          "quantity": 1,
          "amount": 18,
          "specText": "中辣",
          "image": "https://example.com/food.jpg",
          "isCombination": false,
          "comboItems": []
        }
      ],
      "paymentMethod": "wechat_pay",
      "remark": "是淡粉色",
      "couponInfo": null,
      "isRated": false,
      "hasRefund": false,
      "refundNo": "",
      "refundStatus": 0,
      "refundAmount": 0,
      "refundReason": "",
      "refundTime": null,
      "createTime": "2025-07-22T11:12:15+08:00",
      "payTime": null,
      "acceptedTime": null,
      "deliveryTime": null,
      "completeTime": null,
      "cancelTime": null,
      "cancelReason": "",
      "statusText": "待支付"
    }
  ]
}
```

## 前端适配状态

✅ **已完成**:

- 多商家订单数组格式处理
- 单订单/多订单跳转逻辑
- 兼容性处理（支持旧格式）

⏳ **待优化**:

- 订单详情页面可能需要适配新的数据结构
- 订单列表页面展示优化

## 测试建议

1. **单商家订单测试**：确保返回数组格式，长度为1
2. **多商家订单测试**：确保返回数组格式，长度大于1
3. **数据完整性测试**：确保所有字段都有正确的值
4. **前后端联调测试**：确保页面跳转和数据展示正常

## 总结

前端已完成多商家订单API的适配工作，支持数组格式返回值的处理。建议后端按照本文档的要求完善API实现，特别是数据完整性方面的优化，以提供更好的用户体验。
