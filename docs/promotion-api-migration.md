# 促销活动API迁移文档

## 概述

本文档描述了从旧版促销活动API迁移到新版API的详细过程和变更内容。

## API变更对比

### 旧版API

```
POST /api/v1/user/takeout/merchant/{merchantId}/promotions
```

**特点:**

- 单个商家查询
- 需要传递订单相关参数 (`food_ids`, `total_amount`)
- 只返回促销活动信息
- 需要多次调用获取多个商家数据

**请求参数:**

```json
{
  "food_ids": "1,2,3",
  "total_amount": 50.0
}
```

**响应格式:**

```json
{
  "promotion_info": "满减优惠",
  "promotions": [
    {
      "id": 1,
      "name": "满减优惠",
      "description": "满50减10",
      "type": 4,
      "type_text": "满减活动"
    }
  ]
}
```

### 新版API

```
POST /api/v1/user/takeout/merchants/promotions-coupons
```

**特点:**

- 批量商家查询
- 只需要商家ID数组
- 同时返回促销活动和优惠券信息
- 后端自动过滤不符合条件的数据
- 支持游客访问

**请求参数:**

```json
{
  "merchant_ids": [1, 2, 3]
}
```

**响应格式:**

```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "merchant_id": 1,
      "merchant_name": "美味餐厅",
      "has_promotion": true,
      "has_coupon": true,
      "promotions": [
        {
          "id": 1,
          "name": "满减优惠",
          "description": "满50减10",
          "type": 4,
          "type_name": "满减活动",
          "rules": "{\"coupon\":{\"amount\":10,\"min_order_amount\":50}}",
          "start_time": "2025-01-01 00:00:00",
          "end_time": "2025-12-31 23:59:59"
        }
      ],
      "coupons": [
        {
          "id": 1,
          "user_id": 123,
          "coupon_id": 456,
          "coupon": {
            "id": 456,
            "name": "新用户专享券",
            "amount": 5.0,
            "min_order_amount": 30.0
          }
        }
      ]
    }
  ]
}
```

## 代码变更

### 1. API函数更新

**文件:** `src/api/promotion.ts`

```typescript
// 新增API函数
export const getMerchantsPromotionsAndCoupons = (merchantIds: number[]) => {
  return request<IMerchantsPromotionsAndCouponsResponse>(
    `/api/v1/user/takeout/merchants/promotions-coupons`,
    {
      method: 'POST',
      data: {
        merchant_ids: merchantIds,
      },
    },
  )
}
```

### 2. 类型定义更新

**文件:** `src/api/promotion.typings.ts`

```typescript
// 新增类型定义
export interface IMerchantPromotionCouponInfo {
  merchant_id: number
  merchant_name: string
  promotions: IPromotionInfoDTO[]
  coupons: IUserCouponResponse[]
  has_promotion: boolean
  has_coupon: boolean
}

export interface IMerchantsPromotionsAndCouponsResponse {
  data: IMerchantPromotionCouponInfo[]
}
```

### 3. Store更新

**文件:** `src/store/promotion.ts`

```typescript
// 新增批量获取方法
async fetchMerchantsPromotionsAndCoupons(merchantIds: number[]) {
  // 批量获取多个商家的促销活动和优惠券信息
  const response = await getMerchantsPromotionsAndCoupons(merchantIds)

  // 数据转换和存储逻辑
  response.data.forEach((merchantInfo: IMerchantPromotionCouponInfo) => {
    const promotions = merchantInfo.promotions.map(promotionDTO => ({
      // 转换为兼容的IPromotion格式
    }))
    this.merchantPromotions[merchantInfo.merchant_id] = promotions
  })
}
```

### 4. 购物车页面更新

**文件:** `src/pages/cart/index.vue`

```typescript
// 新增促销活动初始化方法
const initializePromotions = async () => {
  const merchantIds = merchantGroups.value
    .filter((group) => group.items.some((item) => item.selected))
    .map((group) => group.merchantId)

  if (merchantIds.length > 0) {
    await promotionStore.fetchMerchantsPromotionsAndCoupons(merchantIds)
  }
}

// 在页面显示时调用
onShow(async () => {
  await cartStore.fetchCartList()
  await addressStore.fetchAddressList()

  if (!isEmpty.value) {
    await Promise.all([
      initializeCoupons(),
      initializePromotions(), // 新增
    ])
  }
})
```

## 迁移优势

### 1. 性能优化

- **减少API调用次数**: 从N次调用减少到1次调用
- **批量处理**: 一次性获取所有商家数据
- **网络开销减少**: 减少HTTP请求数量

### 2. 功能增强

- **统一数据源**: 同时获取促销活动和优惠券
- **后端过滤**: 服务端统一处理筛选逻辑
- **游客支持**: 未登录用户也可查看促销活动

### 3. 维护性提升

- **代码简化**: 减少前端处理逻辑
- **数据一致性**: 后端统一数据格式
- **错误处理**: 集中化错误处理

## 测试验证

### 1. 功能测试

- ✅ 批量获取多商家促销活动
- ✅ 数据格式转换正确
- ✅ 游客访问支持
- ✅ 错误处理机制

### 2. 性能测试

- ✅ API响应时间优化
- ✅ 前端渲染性能提升
- ✅ 内存使用优化

### 3. 兼容性测试

- ✅ 现有功能正常工作
- ✅ 促销活动选择器正常
- ✅ 优惠券功能不受影响

## 注意事项

1. **向后兼容**: 旧版API标记为废弃但仍可使用
2. **数据转换**: 新API数据需要转换为现有格式
3. **错误处理**: 需要处理批量请求中的部分失败情况
4. **缓存策略**: 考虑批量数据的缓存机制

## 后续优化

1. **缓存优化**: 实现更智能的数据缓存策略
2. **增量更新**: 支持部分商家数据更新
3. **实时同步**: 考虑WebSocket实时数据同步
4. **A/B测试**: 对比新旧API的用户体验差异
