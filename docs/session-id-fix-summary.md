# 会话ID无效错误修复总结

## 问题描述

发送消息时出现"会话ID无效"的错误：

```json
{
  "code": 400,
  "message": "参数错误",
  "data": {
    "details": "会话ID无效"
  }
}
```

## 问题根本原因

### 1. 会话ID类型不匹配

- **前端传递**：字符串类型的会话ID（如 `"123"`）
- **后端期望**：数字类型的会话ID（如 `123`）
- **API路径**：`/api/v1/chat/sessions/{session_id}/messages/text`

### 2. 会话ID解析失败

后端在解析会话ID时使用：

```go
sessionID, err := strconv.ParseInt(sessionIDStr, 10, 64)
if err != nil {
    c.responseError(result.CodeInvalidParams, "参数错误", "会话ID无效")
    return
}
```

如果传递的字符串无法解析为数字，就会返回"会话ID无效"错误。

### 3. 可能的数据流问题

1. 创建会话 → 返回会话对象
2. 获取会话ID → 可能是字符串格式
3. 发送消息 → 使用字符串ID构建API路径
4. 后端解析 → 无法解析为数字 → 报错

## 修复方案

### 1. API层面修复 (`H5/o-mall-user/src/api/chat.ts`)

#### 1.1 发送消息API修复

```typescript
export const sendMessage = (params: ISendMessageParams) => {
  // 确保会话ID是数字类型
  const sessionId = parseInt(params.conversationId)
  if (isNaN(sessionId)) {
    throw new Error(`无效的会话ID: ${params.conversationId}`)
  }

  console.log('🎯 [API.sendMessage] 发送消息API调用:', {
    originalConversationId: params.conversationId,
    parsedSessionId: sessionId,
    url: `/api/v1/chat/sessions/${sessionId}/messages/text`,
    content: params.content.text || '',
  })

  return request<IChatMessage>({
    url: `/api/v1/chat/sessions/${sessionId}/messages/text`,
    method: 'POST',
    data: {
      content: params.content.text || '',
    },
  })
}
```

#### 1.2 获取消息列表API修复

```typescript
export const getMessageList = (params: IMessageListParams) => {
  // 确保会话ID是数字类型
  const sessionId = parseInt(params.conversationId)
  if (isNaN(sessionId)) {
    throw new Error(`无效的会话ID: ${params.conversationId}`)
  }

  return request<IMessageListResponse>({
    url: `/api/v1/chat/sessions/${sessionId}/messages`,
    method: 'GET',
    data: {
      page: params.page || 1,
      page_size: params.pageSize || 20,
      before_message_id: params.beforeMessageId,
      after_message_id: params.afterMessageId,
    },
  })
}
```

#### 1.3 标记已读API修复

```typescript
export const markMessageRead = (params: IMarkReadParams) => {
  // 确保会话ID是数字类型
  const sessionId = parseInt(params.conversationId)
  if (isNaN(sessionId)) {
    throw new Error(`无效的会话ID: ${params.conversationId}`)
  }

  return request({
    url: `/api/v1/chat/sessions/${sessionId}/read`,
    method: 'POST',
    data: {
      message_ids: params.messageIds,
    },
  })
}
```

### 2. 调试日志增强

#### 2.1 商家详情页面调试

```typescript
// 创建与商家的聊天会话
const conversation = await createConversation({
  type: ConversationType.MERCHANT,
  participantId: merchant.value.id.toString(),
  title: `与${merchant.value.name}的对话`,
  extra: {
    merchantId: merchant.value.id.toString(),
  },
})

console.log('🎯 [ContactMerchant] 创建会话成功:', conversation)
console.log('🎯 [ContactMerchant] 会话ID:', conversation.id, '类型:', typeof conversation.id)
```

#### 2.2 聊天详情页面调试

```typescript
// 路由参数
const conversationId = (route.query.conversationId || route.query.sessionId) as string
console.log('🎯 [ChatDetail] 获取到的会话ID:', conversationId, '类型:', typeof conversationId)
console.log('🎯 [ChatDetail] 路由参数:', route.query)
```

#### 2.3 Store层面调试

```typescript
const sendMessage = async (
  conversationId: string,
  messageData: {
    type: string
    content: any
  },
) => {
  console.log('🎯 [Store.sendMessage] 接收到发送消息请求:', {
    conversationId,
    conversationIdType: typeof conversationId,
    messageData,
  })

  // ... 处理逻辑
}
```

### 3. 错误处理增强

#### 3.1 会话创建验证

```typescript
// 确保会话ID存在
if (!conversation.id) {
  uni.showToast({
    title: '会话创建失败，请重试',
    icon: 'none',
  })
  return
}
```

#### 3.2 API调用前验证

```typescript
// 确保会话ID是数字类型
const sessionId = parseInt(params.conversationId)
if (isNaN(sessionId)) {
  throw new Error(`无效的会话ID: ${params.conversationId}`)
}
```

## 预期修复效果

### ✅ **类型安全**

- 所有API调用前都会验证会话ID格式
- 无效的会话ID会在前端就被拦截
- 提供清晰的错误信息

### ✅ **调试能力**

- 完整的数据流调试日志
- 可以追踪会话ID在各个环节的变化
- 便于定位具体的问题点

### ✅ **错误处理**

- 优雅的错误处理和用户提示
- 防止无效数据传递到后端
- 提高系统稳定性

## 测试验证步骤

### 1. 创建会话测试

1. 在商家详情页面点击"联系商家"
2. 查看控制台日志，确认会话创建成功
3. 验证会话ID的类型和格式

### 2. 发送消息测试

1. 在聊天页面输入消息并发送
2. 查看控制台日志，确认API调用参数
3. 验证消息发送成功

### 3. 错误处理测试

1. 模拟无效的会话ID
2. 验证错误处理逻辑
3. 确认用户友好的错误提示

## 后续优化建议

### 1. 数据类型统一

- 考虑在前后端统一使用字符串或数字类型的ID
- 更新TypeScript类型定义
- 完善API文档

### 2. 会话管理优化

- 添加会话有效性检查
- 实现会话自动重连机制
- 优化会话状态管理

### 3. 错误监控

- 添加错误上报机制
- 监控API调用成功率
- 收集用户反馈数据

通过以上修复，会话ID无效的问题应该得到解决，用户可以正常发送消息了。
