# 智能优惠算法设计文档

## 🎯 功能概述

智能优惠算法是购物车页面的核心功能之一，能够自动分析用户的购物车内容，为每个商家计算并推荐最优的优惠券和促销活动组合，帮助用户获得最大的优惠金额。

## 🧠 算法设计

### 核心思想

1. **穷举所有可能的优惠组合**
2. **计算每种组合的优惠金额**
3. **选择优惠金额最大的组合**
4. **考虑优惠叠加规则和限制条件**

### 算法流程

```
1. 获取商家的可用优惠券和促销活动
2. 生成所有可能的优惠组合：
   - 只使用优惠券
   - 只使用促销活动
   - 优惠券 + 促销活动（如果可叠加）
3. 计算每种组合的优惠金额
4. 选择优惠金额最大的组合
5. 缓存结果并提供自动选择功能
```

## 🔧 技术实现

### 数据结构

#### 最优优惠组合类型

```typescript
interface BestOfferCombination {
  merchantId: number // 商家ID
  bestCoupon: any | null // 最优优惠券
  bestPromotion: any | null // 最优促销活动
  totalSavings: number // 总优惠金额
  calculatedAt: number // 计算时间戳
}
```

#### 优惠组合类型

```typescript
interface OfferCombination {
  type: 'coupon_only' | 'promotion_only' | 'combined'
  coupon: any | null
  promotion: any | null
  savings: number
}
```

### 核心算法

#### 1. 计算最优优惠组合

```javascript
const calculateBestOfferCombination = async (merchantId: number) => {
  // 1. 获取可用优惠
  const availableCoupons = couponStore.getAvailableCouponsForMerchant(merchantId)
  const availablePromotions = promotionStore.getApplicablePromotions(merchantId)

  // 2. 生成所有可能的组合
  const combinations = []

  // 只使用优惠券
  for (const coupon of availableCoupons) {
    combinations.push({
      coupon,
      promotion: null,
      savings: coupon.discount_amount || 0
    })
  }

  // 只使用促销活动
  for (const promotion of availablePromotions) {
    combinations.push({
      coupon: null,
      promotion,
      savings: promotion.discount_amount || 0
    })
  }

  // 优惠券 + 促销活动组合
  for (const coupon of availableCoupons) {
    for (const promotion of availablePromotions) {
      if (canCombineOffers(coupon, promotion)) {
        combinations.push({
          coupon,
          promotion,
          savings: (coupon.discount_amount || 0) + (promotion.discount_amount || 0)
        })
      }
    }
  }

  // 3. 选择最优组合
  let maxSavings = 0
  let bestCombination = null

  for (const combination of combinations) {
    if (combination.savings > maxSavings) {
      maxSavings = combination.savings
      bestCombination = combination
    }
  }

  return bestCombination
}
```

#### 2. 优惠叠加规则检查

```javascript
const canCombineOffers = (coupon: any, promotion: any): boolean => {
  // 检查优惠券是否允许与促销活动叠加
  if (coupon.coupon?.cannot_combine_with_promotion) {
    return false
  }

  // 检查促销活动是否允许与优惠券叠加
  if (promotion.promotion?.cannot_combine_with_coupon) {
    return false
  }

  // 检查是否是互斥的优惠类型
  if (coupon.coupon?.exclusive || promotion.promotion?.exclusive) {
    return false
  }

  // 默认允许叠加
  return true
}
```

#### 3. 自动选择最优优惠

```javascript
const autoSelectBestOffers = async (merchantId: number) => {
  const bestCombination = await calculateBestOfferCombination(merchantId)

  if (!bestCombination || bestCombination.totalSavings <= 0) {
    return false
  }

  // 应用最优组合
  if (bestCombination.bestCoupon) {
    couponStore.selectCouponForMerchant(merchantId, bestCombination.bestCoupon)
  }

  if (bestCombination.bestPromotion) {
    promotionStore.selectPromotion(merchantId, bestCombination.bestPromotion.promotion)
  }

  return true
}
```

## 🎨 用户界面

### 智能优惠推荐区域

```vue
<view class="smart-offers-section">
  <!-- 标题和一键选择按钮 -->
  <view class="smart-offers-header">
    <text class="smart-offers-title">💡 智能优惠推荐</text>
    <view class="auto-select-btn" @click="autoSelectBestOffers(group.merchantId)">
      <text class="auto-select-text">一键选择最优</text>
    </view>
  </view>

  <!-- 最优优惠组合显示 -->
  <view class="best-offer-display">
    <view class="best-offer-info">
      <text class="best-offer-label">推荐组合:</text>
      <text class="best-offer-savings">可省¥{{ totalSavings.toFixed(2) }}</text>
    </view>

    <!-- 优惠详情 -->
    <view class="best-offer-details">
      <view v-if="bestCoupon" class="offer-item coupon-item">
        <text class="offer-type">优惠券</text>
        <text class="offer-name">{{ bestCoupon.coupon.name }}</text>
        <text class="offer-amount">-¥{{ bestCoupon.discount_amount.toFixed(2) }}</text>
      </view>

      <view v-if="bestPromotion" class="offer-item promotion-item">
        <text class="offer-type">促销活动</text>
        <text class="offer-name">{{ bestPromotion.promotion.name }}</text>
        <text class="offer-amount">-¥{{ bestPromotion.discount_amount.toFixed(2) }}</text>
      </view>
    </view>
  </view>
</view>
```

### 视觉设计特点

1. **渐变背景**: 使用紫色渐变背景突出智能推荐区域
2. **毛玻璃效果**: 使用 `backdrop-filter: blur()` 创建现代感
3. **分类标识**: 优惠券和促销活动使用不同颜色的左边框区分
4. **交互反馈**: 按钮点击有缩放动画和加载状态

## 📊 算法优势

### 1. 智能化

- **自动计算**: 无需用户手动比较，算法自动找出最优组合
- **实时更新**: 购物车内容变化时自动重新计算
- **智能推荐**: 直观显示推荐的优惠组合和节省金额

### 2. 准确性

- **穷举算法**: 考虑所有可能的优惠组合
- **规则检查**: 严格遵循优惠叠加规则
- **金额计算**: 精确计算每种组合的优惠金额

### 3. 用户体验

- **一键操作**: 点击按钮即可自动选择最优优惠
- **可视化展示**: 清晰显示推荐的优惠组合
- **即时反馈**: 显示具体的节省金额

## 🧪 测试用例

### 测试场景1: 单一优惠券

```
商家: 美味餐厅
订单金额: ¥100
可用优惠券:
  - 满80减15元
  - 满100减25元
  - 9折优惠券(减10元)
可用促销: 无

预期结果: 选择"满100减25元"优惠券
```

### 测试场景2: 优惠券+促销活动叠加

```
商家: 美味餐厅
订单金额: ¥100
可用优惠券: 满100减25元
可用促销: 满80减10元

预期结果: 选择优惠券+促销活动组合，总计优惠¥35
```

### 测试场景3: 无可用优惠

```
商家: 快乐餐厅
订单金额: ¥30
可用优惠券: 满50减8元(不满足条件)
可用促销: 无

预期结果: 显示"暂无可用优惠"
```

## 🚀 性能优化

### 1. 缓存机制

- **结果缓存**: 缓存计算结果，避免重复计算
- **失效策略**: 购物车内容变化时清除相关缓存
- **内存管理**: 定期清理过期的缓存数据

### 2. 异步计算

- **非阻塞**: 使用异步方法避免阻塞UI
- **加载状态**: 显示计算中的加载状态
- **错误处理**: 优雅处理计算失败的情况

### 3. 算法优化

- **早期退出**: 在某些条件下提前结束计算
- **复杂度控制**: 限制组合数量避免计算过于复杂
- **并行计算**: 为多个商家并行计算最优组合

## 📈 扩展性

### 1. 新优惠类型支持

- **折扣券**: 支持百分比折扣计算
- **满赠活动**: 支持买赠类型的促销
- **阶梯优惠**: 支持多级满减优惠

### 2. 高级规则

- **用户等级**: 根据用户VIP等级提供不同优惠
- **时间限制**: 考虑优惠的有效时间
- **商品限制**: 支持特定商品的专属优惠

### 3. 个性化推荐

- **历史偏好**: 根据用户历史选择优化推荐
- **使用频率**: 优先推荐用户常用的优惠类型
- **节省目标**: 根据用户的节省偏好调整算法

## 🎯 预期效果

### 1. 用户体验提升

- **操作简化**: 从手动选择变为一键自动选择
- **决策辅助**: 帮助用户做出最优的优惠选择
- **透明度**: 清晰显示优惠组合和节省金额

### 2. 业务价值

- **转化率提升**: 更好的优惠体验提高下单转化率
- **客户满意度**: 帮助用户获得最大优惠提升满意度
- **运营效率**: 自动化优惠选择减少客服咨询

### 3. 技术价值

- **算法积累**: 为后续智能化功能奠定基础
- **数据洞察**: 收集用户优惠偏好数据
- **系统优化**: 提升整体系统的智能化水平

通过智能优惠算法，购物车页面能够为用户提供更加智能、便捷的优惠选择体验，实现用户、商家和平台的三方共赢。
