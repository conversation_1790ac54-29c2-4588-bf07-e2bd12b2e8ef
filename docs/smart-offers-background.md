# 智能优惠算法 - 后台自动运行版本

## 🎯 功能概述

智能优惠算法在购物车页面后台自动运行，无需用户手动操作，自动为每个商家计算并选择最优的优惠券和促销活动组合，直接操作现有的优惠券选择器和促销活动选择器。

## 🔄 自动运行时机

### 1. 页面加载时

```javascript
onShow(async () => {
  // 加载购物车数据
  await cartStore.fetchCartList()
  await addressStore.fetchAddressList()

  if (!isEmpty.value) {
    // 初始化优惠券和促销活动
    await Promise.all([initializeCoupons(), initializePromotions()])

    // 🤖 自动为所有商家选择最优优惠
    await autoSelectBestOffersForAllMerchants()
  }
})
```

### 2. 购物车内容变化时

```javascript
const revalidateMerchantOffers = async (merchantId: number) => {
  // 重新验证优惠券和促销活动
  await promotionStore.validatePromotionsForOrder(...)
  await couponStore.fetchAvailableCouponsForOrder(...)

  // 🤖 自动选择最优优惠组合
  await autoSelectBestOffers(merchantId)
}
```

### 3. 触发场景

- 用户进入购物车页面
- 商品选中状态变化
- 商品数量变化
- 添加新商品到购物车
- 删除购物车商品

## 🧠 核心算法

### 1. 计算最优优惠组合

```javascript
const calculateBestOfferCombination = async (merchantId: number) => {
  // 获取可用优惠
  const availableCoupons = couponStore.getAvailableCouponsForMerchant(merchantId)
  const availablePromotions = promotionStore.getApplicablePromotions(merchantId)

  // 生成所有可能的组合
  const combinations = []

  // 1. 只使用优惠券
  for (const coupon of availableCoupons) {
    combinations.push({ coupon, promotion: null, savings: coupon.discount_amount })
  }

  // 2. 只使用促销活动
  for (const promotion of availablePromotions) {
    combinations.push({ coupon: null, promotion, savings: promotion.discount_amount })
  }

  // 3. 优惠券 + 促销活动组合（如果可叠加）
  for (const coupon of availableCoupons) {
    for (const promotion of availablePromotions) {
      if (canCombineOffers(coupon, promotion)) {
        const combinedSavings = coupon.discount_amount + promotion.discount_amount
        combinations.push({ coupon, promotion, savings: combinedSavings })
      }
    }
  }

  // 选择最优组合
  return combinations.reduce((best, current) =>
    current.savings > best.savings ? current : best
  )
}
```

### 2. 自动应用最优优惠

```javascript
const autoSelectBestOffers = async (merchantId: number) => {
  const bestCombination = await calculateBestOfferCombination(merchantId)

  if (!bestCombination || bestCombination.totalSavings <= 0) {
    return false
  }

  // 🎫 自动选择优惠券
  if (bestCombination.bestCoupon) {
    await couponStore.selectCouponForMerchant(merchantId, bestCombination.bestCoupon)
  }

  // 🎉 自动选择促销活动
  if (bestCombination.bestPromotion) {
    await promotionStore.selectPromotion(merchantId, bestCombination.bestPromotion.promotion)
  }

  return true
}
```

### 3. 优惠叠加规则

```javascript
const canCombineOffers = (coupon: any, promotion: any): boolean => {
  // 检查优惠券限制
  if (coupon.coupon?.cannot_combine_with_promotion) return false

  // 检查促销活动限制
  if (promotion.promotion?.cannot_combine_with_coupon) return false

  // 检查互斥优惠
  if (coupon.coupon?.exclusive || promotion.promotion?.exclusive) return false

  // 默认允许叠加
  return true
}
```

## 🔧 技术特点

### 1. 无感知运行

- **后台计算**: 算法在后台运行，不影响用户操作
- **自动应用**: 直接操作现有的选择器，无需额外UI
- **实时更新**: 购物车变化时自动重新计算

### 2. 智能化程度高

- **穷举算法**: 考虑所有可能的优惠组合
- **最优选择**: 自动选择优惠金额最大的组合
- **规则遵循**: 严格遵循优惠叠加规则

### 3. 性能优化

- **异步处理**: 使用异步方法避免阻塞UI
- **批量处理**: 支持为多个商家并行计算
- **缓存机制**: 缓存计算结果避免重复计算

## 📊 算法效果

### 用户体验

- ✅ **零操作**: 用户无需手动选择优惠
- ✅ **最优化**: 自动获得最大优惠金额
- ✅ **实时性**: 购物车变化时自动更新优惠

### 业务价值

- ✅ **转化率提升**: 自动优惠提高下单意愿
- ✅ **用户满意度**: 确保用户获得最大优惠
- ✅ **运营效率**: 减少用户咨询和客服工作量

## 🧪 测试场景

### 场景1: 页面加载自动选择

```
1. 用户进入购物车页面
2. 系统加载购物车数据
3. 自动初始化优惠券和促销活动
4. 🤖 自动为每个商家选择最优优惠
5. 用户看到已选择的最优优惠组合
```

### 场景2: 购物车变化自动更新

```
1. 用户修改商品选中状态
2. 系统重新验证该商家的优惠
3. 🤖 自动重新选择最优优惠组合
4. 优惠选择器自动更新显示
```

### 场景3: 多商家并行处理

```
1. 购物车包含多个商家的商品
2. 系统为每个商家并行计算最优优惠
3. 🤖 同时为所有商家选择最优优惠
4. 各商家的优惠选择器独立更新
```

## 🔍 日志监控

### 算法执行日志

```javascript
console.log('🧠 开始为商家 1 自动选择最优优惠')
console.log('🧠 商家 1 可用优惠: 3个优惠券, 2个促销活动')
console.log('🔍 找到 8 种优惠组合')
console.log('🏆 最优组合: 可省¥35.00')
console.log('🎫 自动选择优惠券: 满100减25元')
console.log('🎉 自动选择促销活动: 满80减10元')
console.log('✅ 商家 1 最优优惠自动选择完成，可省¥35.00')
```

### 批量处理日志

```javascript
console.log('🤖 开始为所有商家自动选择最优优惠')
console.log('🤖 自动选择优惠完成: 3/3 个商家成功选择最优优惠')
```

## 🚀 部署说明

### 1. 代码集成

- 智能优惠算法已集成到购物车页面
- 无需额外的UI组件
- 直接操作现有的优惠券和促销活动选择器

### 2. 配置要求

- 确保优惠券store提供 `selectCouponForMerchant` 方法
- 确保促销活动store提供 `selectPromotion` 方法
- 确保优惠叠加规则正确配置

### 3. 测试验证

```javascript
// 在浏览器控制台运行测试
runSmartOfferTests()
```

## 💡 最佳实践

### 1. 算法优化

- 限制组合数量避免计算过于复杂
- 使用缓存机制提高性能
- 异步处理避免阻塞UI

### 2. 用户体验

- 确保算法运行不影响页面响应
- 提供清晰的优惠显示
- 保持优惠选择的一致性

### 3. 业务规则

- 严格遵循优惠叠加规则
- 考虑优惠的有效期限制
- 处理特殊商品的优惠限制

## 🎯 预期效果

通过智能优惠算法的后台自动运行，用户在购物车页面将自动获得最优的优惠组合，无需手动选择和比较，大大提升了购物体验和用户满意度。

算法的智能化和自动化特性，不仅为用户提供了便利，也为平台带来了更高的转化率和用户粘性。
