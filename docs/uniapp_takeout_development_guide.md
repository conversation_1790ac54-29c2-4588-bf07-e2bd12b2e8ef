# UniApp外卖模块H5页面开发指导文档

## 1. 项目概述

本文档基于现有Vue3外卖模块代码分析，为UniApp H5页面开发提供详尽的技术指导。涵盖购物车结算、地址选择、优惠券管理、促销活动、支付流程等核心功能。

## 2. 技术架构

### 2.1 技术栈

- **框架**: UniApp + Vue3 + TypeScript
- **状态管理**: Pinia
- **UI组件**: uni-ui 或 uView
- **网络请求**: uni.request 封装
- **地图服务**: 腾讯地图/高德地图

### 2.2 目录结构

```
src/
├── pages/
│   ├── takeout/
│   │   ├── cart/                 # 购物车页面
│   │   ├── checkout/             # 结算页面
│   │   ├── address/              # 地址管理
│   │   ├── coupon/               # 优惠券中心
│   │   └── payment/              # 支付页面
├── components/
│   ├── takeout/
│   │   ├── AddressSelector.vue   # 地址选择组件
│   │   ├── CouponSelector.vue    # 优惠券选择组件
│   │   └── PromotionCard.vue     # 促销活动卡片
├── stores/
│   ├── takeout.js               # 外卖状态管理
│   ├── address.js               # 地址状态管理
│   └── coupon.js                # 优惠券状态管理
├── api/
│   ├── takeout.js               # 外卖相关API
│   ├── address.js               # 地址相关API
│   └── coupon.js                # 优惠券相关API
└── types/
    └── takeout.d.ts             # 类型定义
```

## 3. 核心数据结构

### 3.1 购物车数据结构

```typescript
interface CartItem {
  id: string | number
  merchantId: string | number
  merchantName: string
  foodId: string | number
  foodName: string
  foodImage: string
  price: number
  quantity: number
  totalPrice: number
  variants?: Variant[]
  selected: boolean
  packagingFee?: number
}

interface CartGroup {
  merchantId: string | number
  merchantName: string
  merchantLogo: string
  items: CartItem[]
  totalPrice: number
  deliveryFee: number
  packagingFee: number
  minOrderAmount: number
  distance?: number
}
```

### 3.2 地址数据结构

```typescript
interface UserAddress {
  id: string
  userId: string
  receiver_name: string
  receiver_mobile: string
  province: string
  city: string
  district: string
  detailed_address: string
  is_default: boolean
  location_longitude?: number
  location_latitude?: number
  address_tag?: string
  postal_code?: string
}
```

### 3.3 优惠券数据结构

```typescript
interface UserCoupon {
  id: string | number
  coupon_id: string | number
  name: string
  description: string
  type: CouponType // 1:满减券 2:折扣券 3:免配送费券
  amount: number
  min_order_amount: number
  merchant_id: string | number
  merchant_name: string
  status: CouponStatus // 1:未使用 2:已使用 3:已过期
  expire_time: string
  can_use: boolean
  reason?: string
}
```

### 3.4 促销活动数据结构

```typescript
interface Promotion {
  id: string | number
  name: string
  type: string | number
  condition_amount?: number
  discount_amount?: number
  discount_rate?: number
  description?: string
  start_time?: string
  end_time?: string
  merchant_id?: string | number
  is_available?: boolean
}
```

## 4. API接口设计

### 4.1 购物车相关API

#### 获取购物车列表

```javascript
// GET /api/v1/user/takeout/cart
export function getCartList() {
  return uni.request({
    url: '/api/v1/user/takeout/cart',
    method: 'GET',
  })
}
```

#### 添加商品到购物车

```javascript
// POST /api/v1/user/takeout/cart/add
export function addToCart(data) {
  return uni.request({
    url: '/api/v1/user/takeout/cart/add',
    method: 'POST',
    data: {
      food_id: data.foodId,
      merchant_id: data.merchantId,
      quantity: data.quantity,
      variants: data.variants || [],
    },
  })
}
```

#### 更新购物车商品数量

```javascript
// PUT /api/v1/user/takeout/cart/update
export function updateCartItemQuantity(data) {
  return uni.request({
    url: '/api/v1/user/takeout/cart/update',
    method: 'PUT',
    data: {
      cart_item_id: data.cartItemId,
      quantity: data.quantity,
    },
  })
}
```

#### 选择购物车商品

```javascript
// POST /api/v1/user/takeout/cart/select
export function selectCartItems(data) {
  return uni.request({
    url: '/api/v1/user/takeout/cart/select',
    method: 'POST',
    data: {
      cart_item_ids: data.cartItemIds,
      selected: data.selected,
    },
  })
}
```

#### 结算购物车

```javascript
// POST /api/v1/user/takeout/cart/checkout
export function checkoutCart(data) {
  return uni.request({
    url: '/api/v1/user/takeout/cart/checkout',
    method: 'POST',
    data: {
      cartItemIds: data.cartItemIds,
      addressId: data.addressId,
    },
  })
}
```

### 4.2 地址管理API

#### 获取用户地址列表

```javascript
// GET /api/v1/user/secured/addresses
export function getUserAddresses() {
  return uni.request({
    url: '/api/v1/user/secured/addresses',
    method: 'GET',
  })
}
```

#### 添加新地址

```javascript
// POST /api/v1/user/secured/addresses
export function addUserAddress(data) {
  return uni.request({
    url: '/api/v1/user/secured/addresses',
    method: 'POST',
    data: {
      receiver_name: data.receiverName,
      receiver_mobile: data.receiverMobile,
      province: data.province,
      city: data.city,
      district: data.district,
      detailed_address: data.detailedAddress,
      location_longitude: data.longitude,
      location_latitude: data.latitude,
      is_default: data.isDefault,
    },
  })
}
```

#### 设置默认地址

```javascript
// PUT /api/v1/user/secured/addresses/{id}/default
export function setDefaultAddress(id) {
  return uni.request({
    url: `/api/v1/user/secured/addresses/${id}/default`,
    method: 'PUT',
  })
}
```

### 4.3 优惠券相关API

#### 获取订单可用优惠券

```javascript
// GET /api/v1/user/takeout/coupons/available-for-order
export function getAvailableCouponsForOrder(params) {
  return uni.request({
    url: '/api/v1/user/takeout/coupons/available-for-order',
    method: 'GET',
    data: {
      merchant_id: params.merchantId,
      total_amount: params.totalAmount,
      food_ids: params.foodIds,
    },
  })
}
```

#### 获取我的优惠券列表

```javascript
// GET /api/v1/user/takeout/coupons/my-list
export function getMyCoupons(params) {
  return uni.request({
    url: '/api/v1/user/takeout/coupons/my-list',
    method: 'GET',
    data: {
      status: params.status, // 1:未使用 2:已使用 3:已过期
      merchant_id: params.merchantId,
      page: params.page,
      page_size: params.pageSize,
    },
  })
}
```

#### 领取优惠券

```javascript
// POST /api/v1/user/takeout/coupons/claim
export function claimCoupon(data) {
  return uni.request({
    url: '/api/v1/user/takeout/coupons/claim',
    method: 'POST',
    data: {
      coupon_id: data.couponId,
    },
  })
}
```

### 4.4 促销活动API

#### 获取商家促销活动

```javascript
// GET /api/v1/user/takeout/merchants/{merchantId}/promotions
export function getMerchantPromotions(merchantId) {
  return uni.request({
    url: `/api/v1/user/takeout/merchants/${merchantId}/promotions`,
    method: 'GET',
  })
}
```

### 4.5 订单相关API

#### 创建订单

```javascript
// POST /api/v1/user/takeout/order/create
export function createOrder(data) {
  return uni.request({
    url: '/api/v1/user/takeout/order/create',
    method: 'POST',
    data: {
      takeoutAddressID: data.addressId,
      paymentMethod: data.paymentMethod,
      merchantOrders: data.merchantOrders.map((order) => ({
        merchantID: order.merchantId,
        cartItemIDs: order.cartItemIds,
        couponID: order.couponId,
        deliveryTime: order.deliveryTime,
        remark: order.remark,
      })),
      coupons: data.coupons,
      promotions: data.promotions,
    },
  })
}
```

#### 创建支付

```javascript
// POST /api/v1/user/takeout/order/pay/{orderId}/create
export function createTakeoutPayment(orderId, data) {
  return uni.request({
    url: `/api/v1/user/takeout/order/pay/${orderId}/create`,
    method: 'POST',
    data: {
      payment_method: data.paymentMethod,
      payment_amount: data.paymentAmount,
    },
  })
}
```

## 5. 状态管理设计

### 5.1 外卖Store (stores/takeout.js)

```javascript
import { defineStore } from 'pinia'

export const useTakeoutStore = defineStore('takeout', {
  state: () => ({
    cart: [],
    currentMerchant: null,
    selectedCartItems: [],
    currentOrder: null,
  }),

  getters: {
    cartItemCount: (state) => {
      return state.cart.reduce((sum, item) => sum + item.quantity, 0)
    },

    selectedTotalPrice: (state) => {
      return state.selectedCartItems.reduce((sum, item) => sum + item.totalPrice, 0)
    },

    groupedCartItems: (state) => {
      const groups = {}
      state.cart.forEach((item) => {
        if (!groups[item.merchantId]) {
          groups[item.merchantId] = {
            merchantId: item.merchantId,
            merchantName: item.merchantName,
            items: [],
          }
        }
        groups[item.merchantId].items.push(item)
      })
      return Object.values(groups)
    },
  },

  actions: {
    async loadCart() {
      try {
        const response = await getCartList()
        this.cart = response.data || []
      } catch (error) {
        console.error('加载购物车失败:', error)
      }
    },

    async addToCart(data) {
      try {
        await addToCart(data)
        await this.loadCart()
        uni.showToast({ title: '添加成功', icon: 'success' })
      } catch (error) {
        uni.showToast({ title: '添加失败', icon: 'error' })
      }
    },

    async updateCartItemQuantity(cartItemId, quantity) {
      try {
        await updateCartItemQuantity({ cartItemId, quantity })
        await this.loadCart()
      } catch (error) {
        console.error('更新数量失败:', error)
      }
    },

    async selectCartItems(cartItemIds, selected) {
      try {
        await selectCartItems({ cartItemIds, selected })
        await this.loadCart()
      } catch (error) {
        console.error('选择商品失败:', error)
      }
    },

    async createMultiMerchantOrder(orderData) {
      try {
        const response = await createOrder(orderData)
        this.currentOrder = response.data
        return response.data
      } catch (error) {
        console.error('创建订单失败:', error)
        throw error
      }
    },
  },
})
```

### 5.2 地址Store (stores/address.js)

```javascript
import { defineStore } from 'pinia'

export const useAddressStore = defineStore('address', {
  state: () => ({
    addresses: [],
    defaultAddress: null,
    selectedAddress: null,
  }),

  getters: {
    sortedAddresses: (state) => {
      return [...state.addresses].sort((a, b) => {
        if (a.is_default && !b.is_default) return -1
        if (!a.is_default && b.is_default) return 1
        return 0
      })
    },
  },

  actions: {
    async loadAddresses() {
      try {
        const response = await getUserAddresses()
        this.addresses = response.data || []
        this.defaultAddress = this.addresses.find((addr) => addr.is_default) || null
      } catch (error) {
        console.error('加载地址失败:', error)
      }
    },

    async addAddress(addressData) {
      try {
        await addUserAddress(addressData)
        await this.loadAddresses()
        uni.showToast({ title: '添加成功', icon: 'success' })
      } catch (error) {
        uni.showToast({ title: '添加失败', icon: 'error' })
      }
    },

    async setDefaultAddress(addressId) {
      try {
        await setDefaultAddress(addressId)
        await this.loadAddresses()
        uni.showToast({ title: '设置成功', icon: 'success' })
      } catch (error) {
        uni.showToast({ title: '设置失败', icon: 'error' })
      }
    },

    selectAddress(address) {
      this.selectedAddress = address
    },
  },
})
```

### 5.3 优惠券Store (stores/coupon.js)

```javascript
import { defineStore } from 'pinia'

export const useCouponStore = defineStore('coupon', {
  state: () => ({
    myCoupons: [],
    availableCoupons: [],
    selectedCoupons: {}, // merchantId -> coupon
  }),

  getters: {
    unusedCoupons: (state) => {
      return state.myCoupons.filter((coupon) => coupon.status === 1)
    },

    expiringSoonCoupons: (state) => {
      return state.myCoupons.filter((coupon) => coupon.status === 1 && coupon.days_to_expire <= 3)
    },
  },

  actions: {
    async loadMyCoupons(params = {}) {
      try {
        const response = await getMyCoupons(params)
        this.myCoupons = response.data?.coupons || []
      } catch (error) {
        console.error('加载优惠券失败:', error)
      }
    },

    async loadAvailableCouponsForOrder(merchantId, totalAmount, foodIds) {
      try {
        const response = await getAvailableCouponsForOrder({
          merchantId,
          totalAmount,
          foodIds,
        })
        this.availableCoupons = response.data?.available_coupons || []
      } catch (error) {
        console.error('加载可用优惠券失败:', error)
      }
    },

    async claimCoupon(couponId) {
      try {
        await claimCoupon({ couponId })
        uni.showToast({ title: '领取成功', icon: 'success' })
        await this.loadMyCoupons()
      } catch (error) {
        uni.showToast({ title: '领取失败', icon: 'error' })
      }
    },

    selectCouponForMerchant(merchantId, coupon) {
      this.selectedCoupons[merchantId] = coupon
    },

    clearSelectedCoupon(merchantId) {
      delete this.selectedCoupons[merchantId]
    },
  },
})
```

## 6. 核心页面开发

### 6.1 购物车页面 (pages/takeout/cart/index.vue)

```vue
<template>
  <view class="cart-page">
    <!-- 地址选择区域 -->
    <view class="address-section" @click="selectAddress">
      <view v-if="!selectedAddress" class="no-address">
        <text>请选择收货地址</text>
        <uni-icons type="right" size="16"></uni-icons>
      </view>
      <view v-else class="address-info">
        <view class="address-detail">
          <text class="name">{{ selectedAddress.receiver_name }}</text>
          <text class="phone">{{ selectedAddress.receiver_mobile }}</text>
        </view>
        <text class="address-text">
          {{ selectedAddress.province }}{{ selectedAddress.city }}{{ selectedAddress.district
          }}{{ selectedAddress.detailed_address }}
        </text>
        <uni-icons type="right" size="16"></uni-icons>
      </view>
    </view>

    <!-- 购物车商品列表 -->
    <view class="cart-content">
      <view v-for="group in groupedCartItems" :key="group.merchantId" class="merchant-group">
        <!-- 商家信息 -->
        <view class="merchant-header">
          <checkbox-group @change="selectMerchant">
            <checkbox :value="group.merchantId" :checked="isMerchantSelected(group.merchantId)" />
          </checkbox-group>
          <text class="merchant-name">{{ group.merchantName }}</text>
        </view>

        <!-- 商品列表 -->
        <view class="items-list">
          <view v-for="item in group.items" :key="item.id" class="cart-item">
            <checkbox-group @change="selectItem">
              <checkbox :value="item.id" :checked="item.selected" />
            </checkbox-group>

            <image :src="item.foodImage" class="food-image"></image>

            <view class="item-info">
              <text class="food-name">{{ item.foodName }}</text>
              <view v-if="item.variants" class="variants">
                <text v-for="variant in item.variants" :key="variant.id" class="variant">
                  {{ variant.name }}
                </text>
              </view>
              <text class="price">¥{{ item.price }}</text>
            </view>

            <view class="quantity-control">
              <button @click="decreaseQuantity(item)" class="btn-decrease">-</button>
              <text class="quantity">{{ item.quantity }}</text>
              <button @click="increaseQuantity(item)" class="btn-increase">+</button>
            </view>
          </view>
        </view>

        <!-- 商家小计 -->
        <view class="merchant-summary">
          <text>配送费: ¥{{ group.deliveryFee }}</text>
          <text>包装费: ¥{{ group.packagingFee }}</text>
          <text>小计: ¥{{ group.totalPrice }}</text>
        </view>
      </view>
    </view>

    <!-- 底部结算栏 -->
    <view class="checkout-bar">
      <checkbox-group @change="selectAll">
        <checkbox :checked="allSelected">全选</checkbox>
      </checkbox-group>

      <view class="total-info">
        <text class="total-price">合计: ¥{{ selectedTotalPrice }}</text>
        <text class="delivery-fee">配送费: ¥{{ totalDeliveryFee }}</text>
      </view>

      <button @click="checkout" class="checkout-btn" :disabled="selectedItems.length === 0">
        结算({{ selectedItems.length }})
      </button>
    </view>

    <!-- 地址选择弹窗 -->
    <AddressSelectModal v-model:show="showAddressModal" @select="handleAddressSelect" />
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useTakeoutStore } from '@/stores/takeout'
import { useAddressStore } from '@/stores/address'
import AddressSelectModal from '@/components/takeout/AddressSelectModal.vue'

const takeoutStore = useTakeoutStore()
const addressStore = useAddressStore()

const showAddressModal = ref(false)

// 计算属性
const groupedCartItems = computed(() => takeoutStore.groupedCartItems)
const selectedItems = computed(() => takeoutStore.selectedCartItems)
const selectedTotalPrice = computed(() => takeoutStore.selectedTotalPrice)
const selectedAddress = computed(() => addressStore.selectedAddress || addressStore.defaultAddress)
const allSelected = computed(() => {
  return takeoutStore.cart.length > 0 && takeoutStore.cart.every((item) => item.selected)
})

const totalDeliveryFee = computed(() => {
  return groupedCartItems.value.reduce((sum, group) => {
    const hasSelectedItems = group.items.some((item) => item.selected)
    return hasSelectedItems ? sum + group.deliveryFee : sum
  }, 0)
})

// 方法
const selectAddress = () => {
  showAddressModal.value = true
}

const handleAddressSelect = (address) => {
  addressStore.selectAddress(address)
  showAddressModal.value = false
}

const selectMerchant = (e) => {
  const merchantId = e.detail.value[0]
  const group = groupedCartItems.value.find((g) => g.merchantId === merchantId)
  if (group) {
    const cartItemIds = group.items.map((item) => item.id)
    const selected = !isMerchantSelected(merchantId)
    takeoutStore.selectCartItems(cartItemIds, selected)
  }
}

const selectItem = (e) => {
  const itemId = e.detail.value[0]
  const item = takeoutStore.cart.find((item) => item.id === itemId)
  if (item) {
    takeoutStore.selectCartItems([itemId], !item.selected)
  }
}

const selectAll = (e) => {
  const selected = e.detail.value.length > 0
  const allItemIds = takeoutStore.cart.map((item) => item.id)
  takeoutStore.selectCartItems(allItemIds, selected)
}

const isMerchantSelected = (merchantId) => {
  const group = groupedCartItems.value.find((g) => g.merchantId === merchantId)
  return group && group.items.every((item) => item.selected)
}

const increaseQuantity = (item) => {
  takeoutStore.updateCartItemQuantity(item.id, item.quantity + 1)
}

const decreaseQuantity = (item) => {
  if (item.quantity > 1) {
    takeoutStore.updateCartItemQuantity(item.id, item.quantity - 1)
  } else {
    takeoutStore.removeFromCart(item.id)
  }
}

const checkout = () => {
  if (selectedItems.value.length === 0) {
    uni.showToast({ title: '请选择商品', icon: 'none' })
    return
  }

  if (!selectedAddress.value) {
    uni.showToast({ title: '请选择收货地址', icon: 'none' })
    return
  }

  uni.navigateTo({
    url: '/pages/takeout/checkout/index',
  })
}

onMounted(() => {
  takeoutStore.loadCart()
  addressStore.loadAddresses()
})
</script>

<style scoped>
.cart-page {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

.address-section {
  background: white;
  padding: 30rpx;
  margin-bottom: 20rpx;
  border-bottom: 1px solid #eee;
}

.no-address {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #999;
}

.address-info {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.address-detail {
  display: flex;
  gap: 20rpx;
}

.name {
  font-weight: bold;
}

.phone {
  color: #666;
}

.merchant-group {
  background: white;
  margin-bottom: 20rpx;
}

.merchant-header {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1px solid #eee;
  gap: 20rpx;
}

.merchant-name {
  font-weight: bold;
  font-size: 32rpx;
}

.cart-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1px solid #f5f5f5;
  gap: 20rpx;
}

.food-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 10rpx;
}

.item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.food-name {
  font-size: 30rpx;
  font-weight: 500;
}

.variants {
  display: flex;
  gap: 10rpx;
}

.variant {
  background: #f0f0f0;
  padding: 5rpx 10rpx;
  border-radius: 5rpx;
  font-size: 24rpx;
  color: #666;
}

.price {
  color: #ff4757;
  font-weight: bold;
  font-size: 32rpx;
}

.quantity-control {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.btn-decrease,
.btn-increase {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  border: 1px solid #ddd;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

.quantity {
  min-width: 40rpx;
  text-align: center;
}

.merchant-summary {
  padding: 20rpx 30rpx;
  background: #f9f9f9;
  display: flex;
  justify-content: space-between;
  font-size: 28rpx;
  color: #666;
}

.checkout-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx 30rpx;
  border-top: 1px solid #eee;
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.total-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.total-price {
  font-size: 32rpx;
  font-weight: bold;
  color: #ff4757;
}

.delivery-fee {
  font-size: 24rpx;
  color: #666;
}

.checkout-btn {
  background: #ff4757;
  color: white;
  border: none;
  padding: 20rpx 40rpx;
  border-radius: 50rpx;
  font-size: 30rpx;
}

.checkout-btn:disabled {
  background: #ccc;
}
</style>
```

### 6.2 结算页面 (pages/takeout/checkout/index.vue)

```vue
<template>
  <view class="checkout-page">
    <!-- 地址信息 -->
    <view class="address-section" @click="selectAddress">
      <view class="address-info">
        <text class="receiver">
          {{ selectedAddress.receiver_name }} {{ selectedAddress.receiver_mobile }}
        </text>
        <text class="address">{{ fullAddress }}</text>
      </view>
      <uni-icons type="right" size="16"></uni-icons>
    </view>

    <!-- 商家订单列表 -->
    <view class="merchants-section">
      <view v-for="group in selectedGroups" :key="group.merchantId" class="merchant-order">
        <!-- 商家信息 -->
        <view class="merchant-header">
          <image :src="group.merchantLogo" class="merchant-logo"></image>
          <text class="merchant-name">{{ group.merchantName }}</text>
        </view>

        <!-- 商品列表 -->
        <view class="items-list">
          <view v-for="item in group.items" :key="item.id" class="order-item">
            <image :src="item.foodImage" class="food-image"></image>
            <view class="item-info">
              <text class="food-name">{{ item.foodName }}</text>
              <view v-if="item.variants" class="variants">
                <text v-for="variant in item.variants" :key="variant.id" class="variant">
                  {{ variant.name }}
                </text>
              </view>
              <text class="price">¥{{ item.price }} × {{ item.quantity }}</text>
            </view>
            <text class="total-price">¥{{ item.totalPrice }}</text>
          </view>
        </view>

        <!-- 优惠券选择 -->
        <view class="coupon-section">
          <text class="section-label">优惠券</text>
          <CouponSelector
            :merchant-id="group.merchantId"
            :total-amount="group.totalPrice"
            @select="handleCouponSelect"
          />
        </view>

        <!-- 促销活动 -->
        <view class="promotion-section">
          <text class="section-label">促销活动</text>
          <PromotionList
            :merchant-id="group.merchantId"
            :total-amount="group.totalPrice"
            @select="handlePromotionSelect"
          />
        </view>

        <!-- 配送信息 -->
        <view class="delivery-section">
          <view class="delivery-item">
            <text class="label">配送费</text>
            <text class="value">¥{{ group.deliveryFee }}</text>
          </view>
          <view class="delivery-item">
            <text class="label">包装费</text>
            <text class="value">¥{{ group.packagingFee }}</text>
          </view>
          <view class="delivery-item">
            <text class="label">预计送达</text>
            <text class="value">{{ group.estimatedDeliveryTime }}</text>
          </view>
        </view>

        <!-- 备注 -->
        <view class="remark-section">
          <text class="section-label">备注</text>
          <input
            v-model="merchantRemarks[group.merchantId]"
            placeholder="选填，请输入备注信息"
            class="remark-input"
          />
        </view>

        <!-- 商家小计 -->
        <view class="merchant-total">
          <text class="total-label">商家小计</text>
          <text class="total-amount">¥{{ calculateMerchantTotal(group) }}</text>
        </view>
      </view>
    </view>

    <!-- 支付方式 -->
    <view class="payment-section">
      <text class="section-title">支付方式</text>
      <radio-group @change="handlePaymentMethodChange">
        <label class="payment-option">
          <radio value="wechat" :checked="paymentMethod === 'wechat'" />
          <text>微信支付</text>
        </label>
        <label class="payment-option">
          <radio value="alipay" :checked="paymentMethod === 'alipay'" />
          <text>支付宝</text>
        </label>
        <label class="payment-option">
          <radio value="balance" :checked="paymentMethod === 'balance'" />
          <text>余额支付</text>
        </label>
      </radio-group>
    </view>

    <!-- 订单总计 -->
    <view class="order-summary">
      <view class="summary-item">
        <text class="label">商品总价</text>
        <text class="value">¥{{ totalItemsPrice }}</text>
      </view>
      <view class="summary-item">
        <text class="label">配送费</text>
        <text class="value">¥{{ totalDeliveryFee }}</text>
      </view>
      <view class="summary-item">
        <text class="label">包装费</text>
        <text class="value">¥{{ totalPackagingFee }}</text>
      </view>
      <view class="summary-item discount" v-if="totalDiscount > 0">
        <text class="label">优惠</text>
        <text class="value">-¥{{ totalDiscount }}</text>
      </view>
      <view class="summary-item total">
        <text class="label">实付金额</text>
        <text class="value">¥{{ finalAmount }}</text>
      </view>
    </view>

    <!-- 提交订单按钮 -->
    <view class="submit-section">
      <button @click="submitOrder" class="submit-btn" :loading="submitting">提交订单</button>
    </view>

    <!-- 地址选择弹窗 -->
    <AddressSelectModal v-model:show="showAddressModal" @select="handleAddressSelect" />
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useTakeoutStore } from '@/stores/takeout'
import { useAddressStore } from '@/stores/address'
import { useCouponStore } from '@/stores/coupon'
import CouponSelector from '@/components/takeout/CouponSelector.vue'
import PromotionList from '@/components/takeout/PromotionList.vue'
import AddressSelectModal from '@/components/takeout/AddressSelectModal.vue'

const takeoutStore = useTakeoutStore()
const addressStore = useAddressStore()
const couponStore = useCouponStore()

const showAddressModal = ref(false)
const paymentMethod = ref('wechat')
const merchantRemarks = ref({})
const selectedPromotions = ref({})
const submitting = ref(false)

// 计算属性
const selectedAddress = computed(() => addressStore.selectedAddress || addressStore.defaultAddress)
const selectedGroups = computed(() => {
  return takeoutStore.groupedCartItems.filter((group) => group.items.some((item) => item.selected))
})

const fullAddress = computed(() => {
  if (!selectedAddress.value) return ''
  const addr = selectedAddress.value
  return `${addr.province}${addr.city}${addr.district}${addr.detailed_address}`
})

const totalItemsPrice = computed(() => {
  return selectedGroups.value.reduce(
    (sum, group) =>
      sum +
      group.items
        .filter((item) => item.selected)
        .reduce((itemSum, item) => itemSum + item.totalPrice, 0),
    0,
  )
})

const totalDeliveryFee = computed(() => {
  return selectedGroups.value.reduce((sum, group) => sum + group.deliveryFee, 0)
})

const totalPackagingFee = computed(() => {
  return selectedGroups.value.reduce((sum, group) => sum + group.packagingFee, 0)
})

const totalDiscount = computed(() => {
  let discount = 0

  // 优惠券折扣
  Object.values(couponStore.selectedCoupons).forEach((coupon) => {
    if (coupon && coupon.discount_amount) {
      discount += coupon.discount_amount
    }
  })

  // 促销活动折扣
  Object.values(selectedPromotions.value).forEach((promotion) => {
    if (promotion && promotion.discount_amount) {
      discount += promotion.discount_amount
    }
  })

  return discount
})

const finalAmount = computed(() => {
  return Math.max(
    0,
    totalItemsPrice.value + totalDeliveryFee.value + totalPackagingFee.value - totalDiscount.value,
  )
})

// 方法
const selectAddress = () => {
  showAddressModal.value = true
}

const handleAddressSelect = (address) => {
  addressStore.selectAddress(address)
  showAddressModal.value = false
}

const handlePaymentMethodChange = (e) => {
  paymentMethod.value = e.detail.value
}

const handleCouponSelect = (merchantId, coupon) => {
  couponStore.selectCouponForMerchant(merchantId, coupon)
}

const handlePromotionSelect = (merchantId, promotion) => {
  selectedPromotions.value[merchantId] = promotion
}

const calculateMerchantTotal = (group) => {
  const itemsTotal = group.items
    .filter((item) => item.selected)
    .reduce((sum, item) => sum + item.totalPrice, 0)

  const couponDiscount = couponStore.selectedCoupons[group.merchantId]?.discount_amount || 0
  const promotionDiscount = selectedPromotions.value[group.merchantId]?.discount_amount || 0

  return Math.max(
    0,
    itemsTotal + group.deliveryFee + group.packagingFee - couponDiscount - promotionDiscount,
  )
}

const submitOrder = async () => {
  if (!selectedAddress.value) {
    uni.showToast({ title: '请选择收货地址', icon: 'none' })
    return
  }

  if (selectedGroups.value.length === 0) {
    uni.showToast({ title: '请选择商品', icon: 'none' })
    return
  }

  submitting.value = true

  try {
    const orderData = {
      addressId: selectedAddress.value.id,
      paymentMethod: paymentMethod.value,
      merchantOrders: selectedGroups.value.map((group) => ({
        merchantId: group.merchantId,
        cartItemIds: group.items.filter((item) => item.selected).map((item) => item.id),
        couponId: couponStore.selectedCoupons[group.merchantId]?.id || null,
        deliveryTime: '', // 可以添加配送时间选择
        remark: merchantRemarks.value[group.merchantId] || '',
      })),
      coupons: Object.values(couponStore.selectedCoupons)
        .filter(Boolean)
        .map((coupon) => ({
          couponID: coupon.id,
          merchantID: coupon.merchant_id,
        })),
      promotions: Object.entries(selectedPromotions.value)
        .filter(([_, promotion]) => promotion)
        .map(([merchantId, promotion]) => ({
          promotionID: promotion.id,
          merchantID: merchantId,
        })),
    }

    const order = await takeoutStore.createMultiMerchantOrder(orderData)

    if (order) {
      uni.showToast({ title: '订单创建成功', icon: 'success' })

      // 跳转到支付页面
      uni.redirectTo({
        url: `/pages/takeout/payment/index?orderId=${order.orderID}`,
      })
    }
  } catch (error) {
    console.error('提交订单失败:', error)
    uni.showToast({ title: '提交失败，请重试', icon: 'error' })
  } finally {
    submitting.value = false
  }
}

onMounted(() => {
  // 加载选中的商品组
  selectedGroups.value.forEach((group) => {
    // 加载每个商家的可用优惠券
    couponStore.loadAvailableCouponsForOrder(
      group.merchantId,
      group.totalPrice,
      group.items.map((item) => item.foodId).join(','),
    )
  })
})
</script>

<style scoped>
.checkout-page {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

.address-section {
  background: white;
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.address-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.receiver {
  font-weight: bold;
  font-size: 32rpx;
}

.address {
  color: #666;
  font-size: 28rpx;
}

.merchant-order {
  background: white;
  margin-bottom: 20rpx;
}

.merchant-header {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1px solid #eee;
  gap: 20rpx;
}

.merchant-logo {
  width: 60rpx;
  height: 60rpx;
  border-radius: 10rpx;
}

.merchant-name {
  font-weight: bold;
  font-size: 32rpx;
}

.order-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1px solid #f5f5f5;
  gap: 20rpx;
}

.food-image {
  width: 100rpx;
  height: 100rpx;
  border-radius: 10rpx;
}

.item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.food-name {
  font-size: 30rpx;
  font-weight: 500;
}

.variants {
  display: flex;
  gap: 10rpx;
}

.variant {
  background: #f0f0f0;
  padding: 5rpx 10rpx;
  border-radius: 5rpx;
  font-size: 24rpx;
  color: #666;
}

.price {
  color: #666;
  font-size: 28rpx;
}

.total-price {
  font-weight: bold;
  color: #ff4757;
}

.coupon-section,
.promotion-section,
.delivery-section,
.remark-section {
  padding: 30rpx;
  border-bottom: 1px solid #f5f5f5;
}

.section-label {
  font-size: 30rpx;
  font-weight: 500;
  margin-bottom: 20rpx;
  display: block;
}

.delivery-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
}

.label {
  color: #666;
}

.value {
  color: #333;
}

.remark-input {
  width: 100%;
  padding: 20rpx;
  border: 1px solid #ddd;
  border-radius: 10rpx;
  font-size: 28rpx;
}

.merchant-total {
  padding: 30rpx;
  background: #f9f9f9;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.total-label {
  font-size: 30rpx;
  font-weight: 500;
}

.total-amount {
  font-size: 32rpx;
  font-weight: bold;
  color: #ff4757;
}

.payment-section {
  background: white;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
  display: block;
}

.payment-option {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.order-summary {
  background: white;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.summary-item.discount .value {
  color: #ff4757;
}

.summary-item.total {
  border-top: 1px solid #eee;
  padding-top: 20rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.summary-item.total .value {
  color: #ff4757;
}

.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 30rpx;
  border-top: 1px solid #eee;
}

.submit-btn {
  width: 100%;
  background: #ff4757;
  color: white;
  border: none;
  padding: 30rpx;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: bold;
}
</style>
```

## 7. 核心组件开发

### 7.1 地址选择组件 (components/takeout/AddressSelectModal.vue)

```vue
<template>
  <uni-popup ref="popup" type="bottom" :show="show" @change="handlePopupChange">
    <view class="address-modal">
      <view class="modal-header">
        <text class="title">选择收货地址</text>
        <button @click="addNewAddress" class="add-btn">新增地址</button>
      </view>

      <scroll-view class="address-list" scroll-y>
        <view
          v-for="address in addresses"
          :key="address.id"
          class="address-item"
          :class="{ active: selectedAddressId === address.id }"
          @click="selectAddress(address)"
        >
          <view class="address-info">
            <view class="receiver-info">
              <text class="name">{{ address.receiver_name }}</text>
              <text class="phone">{{ address.receiver_mobile }}</text>
              <text v-if="address.is_default" class="default-tag">默认</text>
            </view>
            <text class="address-detail">
              {{ address.province }}{{ address.city }}{{ address.district
              }}{{ address.detailed_address }}
            </text>
          </view>

          <view class="address-actions">
            <button @click.stop="editAddress(address)" class="edit-btn">编辑</button>
            <button @click.stop="deleteAddress(address)" class="delete-btn">删除</button>
          </view>
        </view>

        <view v-if="addresses.length === 0" class="empty-state">
          <text>暂无收货地址</text>
          <button @click="addNewAddress" class="add-address-btn">添加地址</button>
        </view>
      </scroll-view>
    </view>
  </uni-popup>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useAddressStore } from '@/stores/address'

const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:show', 'select'])

const addressStore = useAddressStore()
const popup = ref(null)
const selectedAddressId = ref(null)

const addresses = computed(() => addressStore.sortedAddresses)

const handlePopupChange = (e) => {
  emit('update:show', e.show)
}

const selectAddress = (address) => {
  selectedAddressId.value = address.id
  emit('select', address)
  emit('update:show', false)
}

const addNewAddress = () => {
  emit('update:show', false)
  uni.navigateTo({
    url: '/pages/address/add/index',
  })
}

const editAddress = (address) => {
  emit('update:show', false)
  uni.navigateTo({
    url: `/pages/address/edit/index?id=${address.id}`,
  })
}

const deleteAddress = async (address) => {
  const result = await uni.showModal({
    title: '确认删除',
    content: '确定要删除这个地址吗？',
  })

  if (result.confirm) {
    try {
      await addressStore.deleteAddress(address.id)
      uni.showToast({ title: '删除成功', icon: 'success' })
    } catch (error) {
      uni.showToast({ title: '删除失败', icon: 'error' })
    }
  }
}

watch(
  () => props.show,
  (newVal) => {
    if (newVal) {
      addressStore.loadAddresses()
    }
  },
)
</script>

<style scoped>
.address-modal {
  background: white;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1px solid #eee;
}

.title {
  font-size: 32rpx;
  font-weight: bold;
}

.add-btn {
  background: #ff4757;
  color: white;
  border: none;
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.address-list {
  flex: 1;
  padding: 20rpx 0;
}

.address-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1px solid #f5f5f5;
}

.address-item.active {
  background: #f0f8ff;
  border-left: 4rpx solid #ff4757;
}

.address-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.receiver-info {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.name {
  font-weight: bold;
  font-size: 30rpx;
}

.phone {
  color: #666;
  font-size: 28rpx;
}

.default-tag {
  background: #ff4757;
  color: white;
  padding: 2rpx 10rpx;
  border-radius: 10rpx;
  font-size: 20rpx;
}

.address-detail {
  color: #666;
  font-size: 26rpx;
  line-height: 1.4;
}

.address-actions {
  display: flex;
  gap: 10rpx;
}

.edit-btn,
.delete-btn {
  padding: 10rpx 20rpx;
  border-radius: 10rpx;
  font-size: 24rpx;
  border: 1px solid #ddd;
  background: white;
}

.delete-btn {
  color: #ff4757;
  border-color: #ff4757;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 30rpx;
  color: #999;
}

.add-address-btn {
  margin-top: 30rpx;
  background: #ff4757;
  color: white;
  border: none;
  padding: 20rpx 40rpx;
  border-radius: 50rpx;
}
</style>
```

### 7.2 优惠券选择组件 (components/takeout/CouponSelector.vue)

```vue
<template>
  <view class="coupon-selector">
    <view class="current-coupon" @click="showCouponModal = true">
      <view v-if="!selectedCoupon" class="no-coupon">
        <text>选择优惠券</text>
        <text class="available-count" v-if="availableCoupons.length > 0">
          {{ availableCoupons.length }}张可用
        </text>
      </view>
      <view v-else class="selected-coupon">
        <text class="coupon-name">{{ selectedCoupon.name }}</text>
        <text class="coupon-discount">-¥{{ selectedCoupon.discount_amount }}</text>
      </view>
      <uni-icons type="right" size="16"></uni-icons>
    </view>

    <!-- 优惠券选择弹窗 -->
    <uni-popup ref="couponPopup" type="bottom" :show="showCouponModal" @change="handlePopupChange">
      <view class="coupon-modal">
        <view class="modal-header">
          <text class="title">选择优惠券</text>
          <button @click="showCouponModal = false" class="close-btn">×</button>
        </view>

        <scroll-view class="coupon-list" scroll-y>
          <!-- 不使用优惠券选项 -->
          <view
            class="coupon-item"
            :class="{ active: !selectedCoupon }"
            @click="selectCoupon(null)"
          >
            <view class="coupon-info">
              <text class="coupon-name">不使用优惠券</text>
            </view>
            <radio :checked="!selectedCoupon" />
          </view>

          <!-- 可用优惠券 -->
          <view class="coupon-section">
            <text class="section-title">可用优惠券 ({{ availableCoupons.length }})</text>
            <view
              v-for="coupon in availableCoupons"
              :key="coupon.id"
              class="coupon-item"
              :class="{ active: selectedCoupon?.id === coupon.id }"
              @click="selectCoupon(coupon)"
            >
              <view class="coupon-card">
                <view class="coupon-amount">
                  <text class="amount">{{ formatCouponAmount(coupon) }}</text>
                  <text class="type">{{ getCouponTypeText(coupon.type) }}</text>
                </view>
                <view class="coupon-info">
                  <text class="coupon-name">{{ coupon.name }}</text>
                  <text class="coupon-desc">{{ getCouponDescription(coupon) }}</text>
                  <text class="expire-time">有效期至: {{ formatDate(coupon.expire_time) }}</text>
                </view>
              </view>
              <radio :checked="selectedCoupon?.id === coupon.id" />
            </view>
          </view>

          <!-- 不可用优惠券 -->
          <view class="coupon-section" v-if="unavailableCoupons.length > 0">
            <text class="section-title">不可用优惠券 ({{ unavailableCoupons.length }})</text>
            <view
              v-for="coupon in unavailableCoupons"
              :key="coupon.id"
              class="coupon-item disabled"
            >
              <view class="coupon-card">
                <view class="coupon-amount">
                  <text class="amount">{{ formatCouponAmount(coupon) }}</text>
                  <text class="type">{{ getCouponTypeText(coupon.type) }}</text>
                </view>
                <view class="coupon-info">
                  <text class="coupon-name">{{ coupon.name }}</text>
                  <text class="coupon-desc">{{ getCouponDescription(coupon) }}</text>
                  <text class="unavailable-reason">{{ coupon.reason }}</text>
                </view>
              </view>
            </view>
          </view>

          <view
            v-if="availableCoupons.length === 0 && unavailableCoupons.length === 0"
            class="empty-state"
          >
            <text>暂无可用优惠券</text>
          </view>
        </scroll-view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { useCouponStore } from '@/stores/coupon'
import { getAvailableCouponsForOrder } from '@/api/coupon'

const props = defineProps({
  merchantId: {
    type: [String, Number],
    required: true,
  },
  totalAmount: {
    type: Number,
    required: true,
  },
  foodIds: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['select'])

const couponStore = useCouponStore()
const showCouponModal = ref(false)
const availableCoupons = ref([])
const unavailableCoupons = ref([])
const selectedCoupon = ref(null)

const handlePopupChange = (e) => {
  showCouponModal.value = e.show
}

const selectCoupon = (coupon) => {
  selectedCoupon.value = coupon
  emit('select', props.merchantId, coupon)
  showCouponModal.value = false
}

const formatCouponAmount = (coupon) => {
  switch (coupon.type) {
    case 1: // 满减券
      return `¥${coupon.amount}`
    case 2: // 折扣券
      return `${coupon.rate || coupon.amount * 10}折`
    case 3: // 免配送费券
      return '免配送费'
    default:
      return `¥${coupon.amount}`
  }
}

const getCouponTypeText = (type) => {
  switch (type) {
    case 1:
      return '满减券'
    case 2:
      return '折扣券'
    case 3:
      return '免配送费'
    default:
      return '优惠券'
  }
}

const getCouponDescription = (coupon) => {
  switch (coupon.type) {
    case 1: // 满减券
      return `满¥${coupon.min_order_amount}减¥${coupon.amount}`
    case 2: // 折扣券
      return `满¥${coupon.min_order_amount}享${coupon.rate || coupon.amount * 10}折`
    case 3: // 免配送费券
      return `满¥${coupon.min_order_amount}免配送费`
    default:
      return coupon.description || ''
  }
}

const formatDate = (dateStr) => {
  const date = new Date(dateStr)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
}

const loadCoupons = async () => {
  try {
    const response = await getAvailableCouponsForOrder({
      merchant_id: props.merchantId,
      total_amount: props.totalAmount,
      food_ids: props.foodIds,
    })

    availableCoupons.value = response.data?.available_coupons || []
    unavailableCoupons.value = response.data?.unavailable_coupons || []
  } catch (error) {
    console.error('加载优惠券失败:', error)
  }
}

watch([() => props.merchantId, () => props.totalAmount], () => {
  loadCoupons()
})

onMounted(() => {
  loadCoupons()
})
</script>

<style scoped>
.coupon-selector {
  width: 100%;
}

.current-coupon {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border: 1px solid #ddd;
  border-radius: 10rpx;
  background: white;
}

.no-coupon {
  display: flex;
  align-items: center;
  gap: 10rpx;
  color: #666;
}

.available-count {
  background: #ff4757;
  color: white;
  padding: 2rpx 8rpx;
  border-radius: 10rpx;
  font-size: 20rpx;
}

.selected-coupon {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.coupon-name {
  font-size: 28rpx;
}

.coupon-discount {
  color: #ff4757;
  font-weight: bold;
}

.coupon-modal {
  background: white;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1px solid #eee;
}

.title {
  font-size: 32rpx;
  font-weight: bold;
}

.close-btn {
  background: none;
  border: none;
  font-size: 40rpx;
  color: #999;
  padding: 0;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.coupon-list {
  flex: 1;
  padding: 20rpx 0;
}

.coupon-section {
  margin-bottom: 30rpx;
}

.section-title {
  padding: 0 30rpx 20rpx;
  font-size: 28rpx;
  color: #666;
  display: block;
}

.coupon-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1px solid #f5f5f5;
}

.coupon-item.active {
  background: #f0f8ff;
}

.coupon-item.disabled {
  opacity: 0.5;
}

.coupon-card {
  flex: 1;
  display: flex;
  gap: 20rpx;
}

.coupon-amount {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 120rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #ff4757, #ff6b7a);
  color: white;
  border-radius: 10rpx;
}

.amount {
  font-size: 24rpx;
  font-weight: bold;
}

.type {
  font-size: 20rpx;
}

.coupon-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 5rpx;
}

.coupon-name {
  font-size: 28rpx;
  font-weight: 500;
}

.coupon-desc {
  font-size: 24rpx;
  color: #666;
}

.expire-time {
  font-size: 22rpx;
  color: #999;
}

.unavailable-reason {
  font-size: 22rpx;
  color: #ff4757;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 100rpx 30rpx;
  color: #999;
}
</style>
```

### 7.3 促销活动组件 (components/takeout/PromotionList.vue)

```vue
<template>
  <view class="promotion-list">
    <view v-if="availablePromotions.length === 0" class="no-promotion">
      <text>暂无促销活动</text>
    </view>

    <view v-else class="promotions">
      <view
        v-for="promotion in availablePromotions"
        :key="promotion.id"
        class="promotion-item"
        :class="{ active: selectedPromotion?.id === promotion.id }"
        @click="selectPromotion(promotion)"
      >
        <radio :checked="selectedPromotion?.id === promotion.id" />
        <view class="promotion-info">
          <text class="promotion-name">{{ promotion.name }}</text>
          <text class="promotion-desc">{{ promotion.description }}</text>
          <text class="promotion-discount">立减¥{{ promotion.discount_amount }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { getMerchantPromotions } from '@/api/takeout'

const props = defineProps({
  merchantId: {
    type: [String, Number],
    required: true,
  },
  totalAmount: {
    type: Number,
    required: true,
  },
})

const emit = defineEmits(['select'])

const availablePromotions = ref([])
const selectedPromotion = ref(null)

const selectPromotion = (promotion) => {
  if (selectedPromotion.value?.id === promotion.id) {
    selectedPromotion.value = null
  } else {
    selectedPromotion.value = promotion
  }
  emit('select', props.merchantId, selectedPromotion.value)
}

const loadPromotions = async () => {
  try {
    const response = await getMerchantPromotions(props.merchantId)
    const promotions = response.data || []

    // 过滤可用的促销活动
    availablePromotions.value = promotions.filter((promotion) => {
      if (!promotion.is_active) return false
      if (promotion.min_amount && props.totalAmount < promotion.min_amount) return false

      const now = new Date()
      if (promotion.start_time && new Date(promotion.start_time) > now) return false
      if (promotion.end_time && new Date(promotion.end_time) < now) return false

      return true
    })
  } catch (error) {
    console.error('加载促销活动失败:', error)
  }
}

watch([() => props.merchantId, () => props.totalAmount], () => {
  loadPromotions()
})

onMounted(() => {
  loadPromotions()
})
</script>

<style scoped>
.promotion-list {
  width: 100%;
}

.no-promotion {
  padding: 30rpx 0;
  text-align: center;
  color: #999;
}

.promotion-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 20rpx;
  border: 1px solid #ddd;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
  background: white;
}

.promotion-item.active {
  border-color: #ff4757;
  background: #fff5f5;
}

.promotion-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 5rpx;
}

.promotion-name {
  font-size: 28rpx;
  font-weight: 500;
}

.promotion-desc {
  font-size: 24rpx;
  color: #666;
}

.promotion-discount {
  font-size: 26rpx;
  color: #ff4757;
  font-weight: bold;
}
</style>
```

## 8. 数据流向分析

### 8.1 购物车到结算流程

```mermaid
graph TD
    A[用户选择商品] --> B[添加到购物车]
    B --> C[购物车页面展示]
    C --> D[选择商品进行结算]
    D --> E[验证地址信息]
    E --> F[跳转结算页面]
    F --> G[加载优惠券和促销]
    G --> H[用户选择优惠]
    H --> I[计算最终金额]
    I --> J[提交订单]
    J --> K[跳转支付页面]
```

### 8.2 地址选择流程

```mermaid
graph TD
    A[进入结算页面] --> B{是否有默认地址}
    B -->|有| C[显示默认地址]
    B -->|无| D[提示选择地址]
    C --> E[用户点击更换地址]
    D --> F[打开地址选择弹窗]
    E --> F
    F --> G[加载用户地址列表]
    G --> H[用户选择地址]
    H --> I[更新选中地址]
    I --> J[重新计算配送费]
```

### 8.3 优惠券使用流程

```mermaid
graph TD
    A[进入结算页面] --> B[加载商家可用优惠券]
    B --> C[API: getAvailableCouponsForOrder]
    C --> D[返回可用/不可用优惠券列表]
    D --> E[用户选择优惠券]
    E --> F[计算优惠金额]
    F --> G[更新订单总价]
    G --> H[提交订单时包含优惠券信息]
```

### 8.4 促销活动应用流程

```mermaid
graph TD
    A[加载商家促销活动] --> B[API: getMerchantPromotions]
    B --> C[过滤可用促销活动]
    C --> D[检查金额条件]
    D --> E[检查时间有效性]
    E --> F[展示可用促销]
    F --> G[用户选择促销]
    G --> H[自动计算优惠]
    H --> I[更新订单金额]
```

## 9. 关键技术实现

### 9.1 配送费计算

```javascript
// 基于距离计算配送费
const calculateDeliveryFee = (distance, baseFee = 3, pricePerKm = 2) => {
  if (distance <= 3) {
    return baseFee
  }
  return baseFee + Math.ceil(distance - 3) * pricePerKm
}

// 获取商家距离
const getMerchantDistance = async (merchantId, userAddress) => {
  if (!userAddress.location_latitude || !userAddress.location_longitude) {
    return 5 // 默认距离
  }

  // 调用地图API计算距离
  const distance = await calculateDistance(merchantLocation, {
    lat: userAddress.location_latitude,
    lng: userAddress.location_longitude,
  })

  return distance
}
```

### 9.2 优惠券折扣计算

```javascript
const calculateCouponDiscount = (coupon, orderAmount) => {
  if (!coupon || !coupon.can_use) return 0

  switch (coupon.type) {
    case 1: // 满减券
      if (orderAmount >= coupon.min_order_amount) {
        return Math.min(coupon.amount, orderAmount)
      }
      return 0

    case 2: // 折扣券
      if (orderAmount >= coupon.min_order_amount) {
        const discount = orderAmount * (1 - coupon.rate / 100)
        return Math.min(discount, coupon.max_discount_amount || discount)
      }
      return 0

    case 3: // 免配送费券
      return deliveryFee

    default:
      return 0
  }
}
```

### 9.3 订单数据组装

```javascript
const buildOrderData = () => {
  return {
    takeoutAddressID: selectedAddress.value.id,
    paymentMethod: paymentMethod.value,
    merchantOrders: selectedGroups.value.map((group) => ({
      merchantID: group.merchantId,
      cartItemIDs: group.items.filter((item) => item.selected).map((item) => item.id),
      couponID: selectedCoupons[group.merchantId]?.id || null,
      deliveryTime: deliveryTimes[group.merchantId] || '',
      remark: merchantRemarks[group.merchantId] || '',
    })),
    coupons: Object.values(selectedCoupons)
      .filter(Boolean)
      .map((coupon) => ({
        couponID: coupon.id,
        merchantID: coupon.merchant_id,
      })),
    promotions: Object.entries(selectedPromotions)
      .filter(([_, promotion]) => promotion)
      .map(([merchantId, promotion]) => ({
        promotionID: promotion.id,
        merchantID: merchantId,
      })),
  }
}
```

## 10. 错误处理和用户体验

### 10.1 网络请求错误处理

```javascript
// 统一错误处理
const handleApiError = (error, defaultMessage = '操作失败') => {
  console.error('API Error:', error)

  let message = defaultMessage
  if (error.response?.data?.message) {
    message = error.response.data.message
  } else if (error.message) {
    message = error.message
  }

  uni.showToast({
    title: message,
    icon: 'none',
    duration: 2000,
  })
}

// 在API调用中使用
try {
  const result = await addToCart(data)
  uni.showToast({ title: '添加成功', icon: 'success' })
} catch (error) {
  handleApiError(error, '添加到购物车失败')
}
```

### 10.2 加载状态管理

```javascript
// 加载状态组合式函数
const useLoading = () => {
  const loading = ref(false)

  const withLoading = async (fn) => {
    loading.value = true
    try {
      return await fn()
    } finally {
      loading.value = false
    }
  }

  return { loading, withLoading }
}

// 使用示例
const { loading, withLoading } = useLoading()

const loadData = () => {
  withLoading(async () => {
    await Promise.all([takeoutStore.loadCart(), addressStore.loadAddresses()])
  })
}
```

### 10.3 表单验证

```javascript
// 地址表单验证
const validateAddress = (address) => {
  const errors = {}

  if (!address.receiver_name?.trim()) {
    errors.receiver_name = '请输入收货人姓名'
  }

  if (!address.receiver_mobile?.trim()) {
    errors.receiver_mobile = '请输入手机号码'
  } else if (!/^1[3-9]\d{9}$/.test(address.receiver_mobile)) {
    errors.receiver_mobile = '手机号码格式不正确'
  }

  if (!address.detailed_address?.trim()) {
    errors.detailed_address = '请输入详细地址'
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  }
}
```

## 11. 性能优化建议

### 11.1 图片懒加载

```vue
<template>
  <image
    :src="imageSrc"
    :lazy-load="true"
    @load="onImageLoad"
    @error="onImageError"
    class="lazy-image"
  />
</template>
```

### 11.2 列表虚拟滚动

```vue
<template>
  <recycle-list :list-data="listData" :template-key="'item'" @scroll="onScroll">
    <template v-slot:item="{ item }">
      <CartItem :item="item" />
    </template>
  </recycle-list>
</template>
```

### 11.3 数据缓存策略

```javascript
// 使用uni.setStorage缓存数据
const cacheData = (key, data, expireTime = 5 * 60 * 1000) => {
  const cacheItem = {
    data,
    timestamp: Date.now(),
    expireTime,
  }
  uni.setStorageSync(key, JSON.stringify(cacheItem))
}

const getCachedData = (key) => {
  try {
    const cached = uni.getStorageSync(key)
    if (!cached) return null

    const cacheItem = JSON.parse(cached)
    if (Date.now() - cacheItem.timestamp > cacheItem.expireTime) {
      uni.removeStorageSync(key)
      return null
    }

    return cacheItem.data
  } catch (error) {
    return null
  }
}
```

## 12. 测试建议

### 12.1 单元测试

```javascript
// 测试优惠券折扣计算
describe('优惠券折扣计算', () => {
  test('满减券计算', () => {
    const coupon = {
      type: 1,
      amount: 10,
      min_order_amount: 50,
      can_use: true,
    }

    expect(calculateCouponDiscount(coupon, 60)).toBe(10)
    expect(calculateCouponDiscount(coupon, 40)).toBe(0)
  })

  test('折扣券计算', () => {
    const coupon = {
      type: 2,
      rate: 20,
      min_order_amount: 50,
      max_discount_amount: 15,
      can_use: true,
    }

    expect(calculateCouponDiscount(coupon, 100)).toBe(15)
  })
})
```

### 12.2 集成测试

```javascript
// 测试完整的结算流程
describe('结算流程测试', () => {
  test('完整结算流程', async () => {
    // 1. 添加商品到购物车
    await addToCart({ foodId: 1, quantity: 2 })

    // 2. 选择地址
    await selectAddress(mockAddress)

    // 3. 选择优惠券
    await selectCoupon(mockCoupon)

    // 4. 提交订单
    const order = await submitOrder(mockOrderData)

    expect(order).toBeDefined()
    expect(order.orderID).toBeTruthy()
  })
})
```

这份文档提供了UniApp外卖模块H5页面开发的完整指导，包括技术架构、数据结构、API设计、核心页面和组件实现、数据流向分析等详细内容。开发团队可以基于此文档进行H5页面的开发工作。

```

```
