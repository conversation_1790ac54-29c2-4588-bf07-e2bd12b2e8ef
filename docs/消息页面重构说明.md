# 消息页面重构说明

## 概述

本次重构根据消息分类API文档，对 `src/pages/message/` 目录下的消息页面及其相关组件、store等内容进行了全面重构，使其与API文档保持一致。

## 重构内容

### 1. 创建专门的消息状态管理 Store

**文件**: `src/store/message.ts`

- 使用 Pinia 创建了专门的消息状态管理 store
- 管理消息分类、未读统计、各类消息列表等全局状态
- 实现了统一的分页和加载状态管理
- 处理了API字段名差异（snake_case ↔ camelCase）

**主要功能**:
- `fetchMessageCategories()` - 获取消息分类
- `fetchSystemNotifications()` - 获取系统通知
- `fetchOrderNotifications()` - 获取订单通知
- `fetchServiceMessages()` - 获取客服消息
- `searchAllMessages()` - 搜索所有消息
- `markXXXRead()` - 标记消息已读
- `loadMoreXXX()` - 分页加载更多

### 2. 更新API接口调用

**文件**: `src/api/message.ts`

根据API文档更新了接口调用：
- 消息列表: `GET /api/v1/chat/messages`
- 系统通知: `GET /api/v1/chat/messages?type=system`
- 订单通知: `GET /api/v1/chat/messages?type=order`
- 客服消息: `GET /api/v1/chat/sessions?category=service`

### 3. 重构页面组件

#### 3.1 消息主页面 (`src/pages/message/index.vue`)
- 使用新的消息 store 管理状态
- 实现了基于 store 的搜索功能
- 简化了组件逻辑，移除了重复的状态管理

#### 3.2 系统通知页面 (`src/pages/message/system.vue`)
- 使用 store 中的系统通知状态
- 通过 store 方法进行数据加载和状态更新
- 实现了统一的分页加载

#### 3.3 订单消息页面 (`src/pages/message/order.vue`)
- 完善了订单消息的API集成
- 使用正确的消息类型枚举
- 实现了订单消息的标记已读功能

#### 3.4 客服消息页面 (`src/pages/message/service.vue`)
- 使用新的客服消息API接口
- 通过 store 管理客服消息状态
- 保持了原有的UI交互逻辑

## 主要改进

### 1. 统一的状态管理
- 所有消息相关状态都通过 Pinia store 管理
- 避免了组件间的状态同步问题
- 提供了统一的数据访问接口

### 2. API接口规范化
- 所有接口调用都符合API文档规范
- 统一了参数传递方式
- 处理了字段名差异问题

### 3. 代码结构优化
- 移除了重复的状态管理逻辑
- 简化了组件代码
- 提高了代码的可维护性

### 4. 类型安全
- 使用 TypeScript 确保类型安全
- 更新了相关的类型定义
- 提供了完整的类型支持

## 字段名转换处理

API返回的数据使用 snake_case 命名，前端使用 camelCase 命名。Store 中自动处理了这种差异：

```typescript
// API 返回: { unread_count: 5 }
// Store 转换为: { unreadCount: 5 }
messageCategories.value = response.data.map(category => ({
  ...category,
  unreadCount: (category as any).unread_count || category.unreadCount || 0,
}))
```

## 测试验证

### 1. 自动化验证脚本
运行 `node scripts/test-message-refactor.js` 可以验证重构是否成功。

### 2. 单元测试
提供了 `src/tests/message-refactor.test.ts` 单元测试文件，覆盖了主要功能。

## 使用方式

### 在组件中使用消息 Store

```vue
<script setup lang="ts">
import { useMessageStore } from '@/store/message'

const messageStore = useMessageStore()

// 获取消息分类
const categories = computed(() => messageStore.messageCategories)

// 获取系统通知
const notifications = computed(() => messageStore.systemNotifications)

// 加载数据
onMounted(async () => {
  await messageStore.initMessageData()
})
</script>
```

## 注意事项

1. **API字段名**: 注意API返回的字段名是 snake_case，Store 会自动转换为 camelCase
2. **分页加载**: 使用 Store 提供的 `loadMoreXXX()` 方法进行分页加载
3. **错误处理**: Store 方法会抛出异常，组件中需要适当处理
4. **状态持久化**: Store 使用了 `persist` 插件，部分状态会持久化到本地存储

## 后续建议

1. 在开发环境中测试所有页面功能
2. 验证API调用是否正确返回数据
3. 测试分页加载和搜索功能
4. 确认消息已读标记功能正常
5. 检查未读消息统计是否准确更新
