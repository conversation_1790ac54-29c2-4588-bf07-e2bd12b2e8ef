# 订单API参数更新测试文档

## 更新内容

### 1. 接口变更

- **请求方式**: GET → POST
- **接口路径**: `/api/v1/orders/list` (保持不变)
- **认证要求**: 需要JWT Token认证

### 2. 参数结构更新

#### 新增参数

```typescript
interface IOrderSearchParams {
  // 新增参数
  user_id?: number // 用户ID
  order_no?: string // 订单号
  pay_status?: number // 支付状态: 0:未支付, 1:已支付, 2:支付失败
  order_type?: number // 订单类型
  takeout?: boolean // 是否外卖订单
  start_time?: string // 开始时间
  end_time?: string // 结束时间
  page_size?: number // 每页数量(新命名)

  // 更新的参数
  status?: number // 订单状态: 10:待付款, 20:已付款, 30:已发货, 40:已收货, 50:已完成, 60:已取消, 70:退款中, 80:已退款

  // 保留的兼容参数(标记为deprecated)
  pageSize?: number // @deprecated 使用 page_size 替代
  keyword?: string // @deprecated 使用 start_time 和 end_time 替代
  startTime?: string // @deprecated 使用 start_time 替代
  endTime?: string // @deprecated 使用 end_time 替代
}
```

### 3. 兼容性处理

在 `store/order.ts` 中实现了参数兼容转换:

```typescript
// 合并搜索参数，处理新旧参数兼容
const searchData: IOrderSearchParams = {
  ...searchParams.value,
  ...params,
  page: currentPage.value,
  page_size: pageSize.value,
  // 兼容处理：如果使用了旧的参数名，转换为新的参数名
  start_time: params?.startTime || searchParams.value.startTime,
  end_time: params?.endTime || searchParams.value.endTime,
}

// 清理undefined值
Object.keys(searchData).forEach((key) => {
  if (
    searchData[key as keyof IOrderSearchParams] === undefined ||
    searchData[key as keyof IOrderSearchParams] === ''
  ) {
    delete searchData[key as keyof IOrderSearchParams]
  }
})
```

### 4. 测试用例

#### 基本查询

```javascript
// 获取第一页订单
const result1 = await getOrderList({
  page: 1,
  page_size: 10,
})

// 按状态查询
const result2 = await getOrderList({
  status: 10, // 待付款
  page: 1,
  page_size: 20,
})

// 按用户ID查询
const result3 = await getOrderList({
  user_id: 123,
  page: 1,
  page_size: 10,
})
```

#### 高级查询

```javascript
// 查询指定时间范围的外卖订单
const result4 = await getOrderList({
  takeout: true,
  start_time: '2024-01-01T00:00:00Z',
  end_time: '2024-01-31T23:59:59Z',
  page: 1,
  page_size: 10,
})

// 按订单号查询
const result5 = await getOrderList({
  order_no: 'ORD20240101001',
  page: 1,
  page_size: 1,
})

// 按支付状态查询
const result6 = await getOrderList({
  pay_status: 1, // 已支付
  status: 20, // 已付款
  page: 1,
  page_size: 10,
})
```

#### 兼容性测试

```javascript
// 使用旧参数名(仍然支持)
const result7 = await getOrderList({
  pageSize: 10,
  startTime: '2024-01-01T00:00:00Z',
  endTime: '2024-01-31T23:59:59Z',
})
```

### 5. 注意事项

1. **请求方式变更**: 从GET改为POST，确保前端调用正确
2. **参数命名**: 新参数使用下划线命名(snake_case)，如`page_size`、`start_time`
3. **状态值变更**: 订单状态从枚举值改为数字值
4. **向后兼容**: 保留了旧参数名的支持，但建议逐步迁移到新参数
5. **参数清理**: 自动清理undefined和空字符串参数

### 6. 迁移建议

1. **立即可用**: 当前修改保持向后兼容，现有代码无需修改
2. **逐步迁移**: 建议在新功能中使用新参数名
3. **测试验证**: 在各个订单相关页面测试功能是否正常
4. **文档更新**: 更新相关API文档和开发文档

## 修改文件列表

1. `src/api/order.typings.ts` - 更新IOrderSearchParams接口
2. `src/api/order.ts` - 修改getOrderList为POST请求
3. `src/store/order.ts` - 更新参数处理逻辑，添加兼容性支持

## 测试状态

- ✅ 编译通过
- ✅ 开发服务器正常运行
- ⏳ 功能测试待验证
- ⏳ 后端接口联调待进行
