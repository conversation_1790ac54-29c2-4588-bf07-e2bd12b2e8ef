import { defineUniPages } from '@uni-helper/vite-plugin-uni-pages'

export default defineUniPages({
  globalStyle: {
    navigationStyle: 'default',
    navigationBarTitleText: 'O-Mall聊天',
    navigationBarBackgroundColor: '#4095e5',
    navigationBarTextStyle: 'white',
    backgroundColor: '#FFFFFF',
  },
  easycom: {
    autoscan: true,
    custom: {
      '^fg-(.*)': '@/components/fg-$1/fg-$1.vue',
      '^wd-(.*)': 'wot-design-uni/components/wd-$1/wd-$1.vue',
      '^(?!z-paging-refresh|z-paging-load-more)z-paging(.*)':
        'z-paging/components/z-paging$1/z-paging$1.vue',
    },
  },
  // 如果不需要tabBar，推荐使用 spa 模板。（pnpm create xxx -t spa）
  tabBar: {
    custom: true,
    color: '#999999',
    selectedColor: '#4095e5',
    backgroundColor: '#F8F8F8',
    borderStyle: 'black',
    height: '50px',
    fontSize: '10px',
    iconWidth: '24px',
    spacing: '3px',
    list: [
      // 注意tabbar路由需要使用 layout:tabbar 布局
      {
        pagePath: 'pages/message/index',
        text: '消息',
        icon: '/static/icons/message.svg',
        iconType: 'local',
      },
      {
        pagePath: 'pages/cart/index',
        text: '购物车',
        icon: '/static/icons/cart.svg',
        iconType: 'local',
      },
      {
        pagePath: 'pages/index/index',
        text: '主页',
        icon: '/static/icons/home.svg',
        iconType: 'local',
        isSpecial: true, // 标记为特殊样式
      },
      {
        pagePath: 'pages/user/favorites',
        text: '收藏',
        icon: '/static/icons/favorite.svg',
        iconType: 'local',
      },
      {
        pagePath: 'pages/user/index',
        text: '个人中心',
        icon: '/static/icons/user.svg',
        iconType: 'local',
      },
    ],
  },
})
