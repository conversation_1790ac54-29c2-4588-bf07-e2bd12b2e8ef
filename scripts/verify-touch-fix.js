/**
 * TouchMove 警告修复验证脚本
 *
 * 用于验证触摸事件优化修复是否生效
 * 可以在浏览器控制台中运行此脚本进行验证
 */

;(function () {
  'use strict'

  console.log('🔧 开始验证 TouchMove 警告修复...')

  // 验证结果
  const results = {
    passiveSupport: false,
    globalFixApplied: false,
    wotDesignOptimized: false,
    componentsFound: [],
    warnings: [],
    errors: [],
  }

  /**
   * 检查浏览器是否支持被动事件监听器
   */
  function checkPassiveSupport() {
    let supportsPassive = false

    try {
      const opts = Object.defineProperty({}, 'passive', {
        get() {
          supportsPassive = true
          return false
        },
      })

      window.addEventListener('testPassive', () => {}, opts)
      window.removeEventListener('testPassive', () => {}, opts)
    } catch (e) {
      // 浏览器不支持被动事件监听器
    }

    results.passiveSupport = supportsPassive
    console.log(`✅ 被动事件监听器支持: ${supportsPassive ? '支持' : '不支持'}`)

    return supportsPassive
  }

  /**
   * 检查全局修复是否已应用
   */
  function checkGlobalFix() {
    // 检查 addEventListener 是否被重写
    const originalToString = EventTarget.prototype.addEventListener.toString()
    const isModified =
      originalToString.includes('touchmove') ||
      originalToString.includes('touchstart') ||
      originalToString.includes('passive')

    results.globalFixApplied = isModified
    console.log(`✅ 全局修复状态: ${isModified ? '已应用' : '未应用'}`)

    return isModified
  }

  /**
   * 检查 wot-design-uni 组件
   */
  function checkWotDesignComponents() {
    const wotSelectors = [
      '.wd-search',
      '.wd-button',
      '.wd-tabs',
      '.wd-tab',
      '.wd-loadmore',
      '.wd-checkbox',
      '.wd-popup',
      '.wd-loading',
      '.wd-icon',
      '.wd-swipe',
      'wd-search',
      'wd-button',
      'wd-tabs',
      'wd-tab',
      'wd-loadmore',
      'wd-checkbox',
      'wd-popup',
      'wd-loading',
      'wd-icon',
      'wd-swipe',
    ]

    const foundComponents = []

    wotSelectors.forEach((selector) => {
      const elements = document.querySelectorAll(selector)
      if (elements.length > 0) {
        foundComponents.push({
          selector,
          count: elements.length,
        })
      }
    })

    results.componentsFound = foundComponents
    console.log(`✅ 发现 wot-design-uni 组件:`, foundComponents)

    return foundComponents.length > 0
  }

  /**
   * 检查优化组件
   */
  function checkOptimizedComponents() {
    const optimizedSelectors = ['.optimized-popup-content', '.optimized-loadmore']

    const foundOptimized = []

    optimizedSelectors.forEach((selector) => {
      const elements = document.querySelectorAll(selector)
      if (elements.length > 0) {
        foundOptimized.push({
          selector,
          count: elements.length,
        })
      }
    })

    console.log(`✅ 发现优化组件:`, foundOptimized)
    return foundOptimized
  }

  /**
   * 监听控制台警告
   */
  function monitorConsoleWarnings() {
    const originalWarn = console.warn
    const originalError = console.error

    let warningCount = 0
    let errorCount = 0

    console.warn = function (...args) {
      const message = args.join(' ')
      if (
        message.includes('touchmove') ||
        message.includes('touchstart') ||
        message.includes('Violation')
      ) {
        warningCount++
        results.warnings.push(message)
      }
      originalWarn.apply(console, args)
    }

    console.error = function (...args) {
      const message = args.join(' ')
      if (
        message.includes('touchmove') ||
        message.includes('touchstart') ||
        message.includes('Violation')
      ) {
        errorCount++
        results.errors.push(message)
      }
      originalError.apply(console, args)
    }

    // 5秒后检查结果
    setTimeout(() => {
      console.log(`✅ 监听结果: ${warningCount} 个警告, ${errorCount} 个错误`)

      if (warningCount === 0 && errorCount === 0) {
        console.log('🎉 恭喜！没有发现触摸事件相关的警告或错误')
      } else {
        console.log('⚠️ 仍有触摸事件相关的警告或错误')
        console.log('警告:', results.warnings)
        console.log('错误:', results.errors)
      }
    }, 5000)
  }

  /**
   * 测试触摸事件
   */
  function testTouchEvents() {
    console.log('🧪 开始测试触摸事件...')

    // 创建测试元素
    const testElement = document.createElement('div')
    testElement.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100px;
      height: 100px;
      background: rgba(255, 0, 0, 0.3);
      z-index: 9999;
      pointer-events: none;
    `
    document.body.appendChild(testElement)

    // 测试被动事件监听器
    let passiveTestPassed = false

    try {
      testElement.addEventListener(
        'touchstart',
        (e) => {
          passiveTestPassed = true
        },
        { passive: true },
      )

      // 模拟触摸事件
      const touchEvent = new TouchEvent('touchstart', {
        touches: [
          new Touch({
            identifier: 0,
            target: testElement,
            clientX: 50,
            clientY: 50,
            radiusX: 0,
            radiusY: 0,
            rotationAngle: 0,
            force: 1,
          }),
        ],
      })

      testElement.dispatchEvent(touchEvent)

      setTimeout(() => {
        console.log(`✅ 被动事件监听器测试: ${passiveTestPassed ? '通过' : '失败'}`)
        document.body.removeChild(testElement)
      }, 1000)
    } catch (e) {
      console.log('❌ 触摸事件测试失败:', e.message)
      document.body.removeChild(testElement)
    }
  }

  /**
   * 生成验证报告
   */
  function generateReport() {
    setTimeout(() => {
      console.log('\n📊 TouchMove 警告修复验证报告')
      console.log('=====================================')
      console.log(`被动事件监听器支持: ${results.passiveSupport ? '✅' : '❌'}`)
      console.log(`全局修复已应用: ${results.globalFixApplied ? '✅' : '❌'}`)
      console.log(`发现组件数量: ${results.componentsFound.length}`)
      console.log(`警告数量: ${results.warnings.length}`)
      console.log(`错误数量: ${results.errors.length}`)

      const score = [
        results.passiveSupport,
        results.globalFixApplied,
        results.warnings.length === 0,
        results.errors.length === 0,
      ].filter(Boolean).length

      console.log(`\n总体评分: ${score}/4 ${score === 4 ? '🎉' : score >= 3 ? '👍' : '⚠️'}`)

      if (score === 4) {
        console.log('🎉 完美！TouchMove 警告修复完全生效！')
      } else if (score >= 3) {
        console.log('👍 很好！TouchMove 警告修复基本生效！')
      } else {
        console.log('⚠️ 需要检查！TouchMove 警告修复可能未完全生效！')
      }

      console.log('=====================================\n')
    }, 6000)
  }

  // 执行验证
  checkPassiveSupport()
  checkGlobalFix()
  checkWotDesignComponents()
  checkOptimizedComponents()
  monitorConsoleWarnings()
  testTouchEvents()
  generateReport()

  console.log('✅ 验证脚本已启动，请等待 6 秒查看完整报告...')
})()
