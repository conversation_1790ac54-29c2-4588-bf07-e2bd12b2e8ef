import { http } from '@/utils/http'
import {
  IAccountInfo,
  IAccountTransactionsResponse,
  IAccountTransactionsParams,
} from './account.typings'

// 定义API响应接口
interface IResData<T> {
  code: number
  message: string
  data: T
}

/**
 * 获取账户信息
 */
export const getAccountInfo = (): Promise<IResData<IAccountInfo>> => {
  return http.get<IAccountInfo>('/api/v1/user/secured/account/info')
}

/**
 * 获取账户变动记录
 */
export const getAccountTransactions = (
  params: IAccountTransactionsParams,
): Promise<IResData<IAccountTransactionsResponse>> => {
  return http.get<IAccountTransactionsResponse>('/api/v1/user/secured/account/transactions', params)
}
