/**
 * 账户信息接口
 */
export interface IAccountInfo {
  id: number
  user_id: number
  balance: string
  frozen_balance: string
  total_recharge: string
  total_consume: string
  status: number
  last_recharge: string
  last_consume: string
  updated_at: string
  created_at: string
}

/**
 * 账户变动记录接口
 */
export interface IAccountTransaction {
  id: number
  transaction_no: string
  related_id: number
  related_type: string
  amount: string
  before_balance: string
  after_balance: string
  type: number
  operation: number
  status: number
  description: string
  remark: string
  client_ip: string
  created_at: string
}

/**
 * 账户变动记录列表响应接口
 */
export interface IAccountTransactionsResponse {
  list: IAccountTransaction[]
  total: number
  page: number
  size: number
}

/**
 * 账户变动记录查询参数接口
 */
export interface IAccountTransactionsParams {
  page?: number
  page_size?: number
  transaction_type?: number
}

/**
 * 交易类型枚举
 */
export enum TransactionType {
  ALL = 0,
  RECHARGE = 1,
  CONSUME = 2,
  REFUND = 3,
  WITHDRAW = 4,
  TRANSFER = 5,
}

/**
 * 操作类型枚举
 */
export enum OperationType {
  INCREASE = 1,
  DECREASE = 2,
}

/**
 * 交易状态枚举
 */
export enum TransactionStatus {
  SUCCESS = 1,
  FAILED = 2,
  PROCESSING = 3,
}

/**
 * 账户状态枚举
 */
export enum AccountStatus {
  FROZEN = 0,
  NORMAL = 1,
}
