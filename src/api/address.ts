/**
 * 地址管理相关API接口
 * 提供用户收货地址的增删改查功能
 */

import { http } from '@/utils/http'
import type {
  IAddress,
  IAddressList,
  ICreateAddressParams,
  IUpdateAddressParams,
} from './address.typings'

/**
 * 获取地址列表
 * @returns 地址列表
 */
export const getAddressList = () => {
  return http.get<IAddressList>('/api/v1/user/secured/addresses')
}

/**
 * 获取地址详情
 * @param addressId 地址ID
 * @returns 地址详情
 */
export const getAddressDetail = (addressId: number) => {
  return http.get<IAddress>(`/api/v1/user/secured/addresses/${addressId}`)
}

/**
 * 创建地址
 * @param params 创建参数
 * @returns 创建的地址信息
 */
export const createAddress = (params: ICreateAddressParams) => {
  return http.post<IAddress>('/api/v1/user/secured/addresses/', params)
}

/**
 * 更新地址
 * @param addressId 地址ID
 * @param params 更新参数
 * @returns 操作结果
 */
export const updateAddress = (addressId: number, params: IUpdateAddressParams) => {
  return http.put<void>(`/api/v1/user/secured/addresses/${addressId}`, params)
}

/**
 * 删除地址
 * @param addressId 地址ID
 * @returns 操作结果
 */
export const deleteAddress = (addressId: number) => {
  return http.delete<void>(`/api/v1/user/secured/addresses/${addressId}`)
}

/**
 * 设置默认地址
 * @param addressId 地址ID
 * @returns 操作结果
 */
export const setDefaultAddress = (addressId: number) => {
  return http.put<void>(`/api/v1/user/secured/addresses/${addressId}/default`)
}

/**
 * 获取默认地址
 * @returns 默认地址
 */
export const getDefaultAddress = () => {
  return http.get<IAddress>('/api/v1/user/secured/addresses/default')
}
