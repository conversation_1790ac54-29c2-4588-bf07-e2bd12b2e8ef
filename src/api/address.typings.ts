/**
 * 地址管理相关类型定义
 * 定义收货地址等相关的数据结构
 */

/**
 * 收货地址信息
 */
export interface IAddress {
  id: number
  user_id?: number
  receiver_name: string
  receiver_mobile: string
  phone?: string // 手机号（与receiver_mobile保持一致）
  province: string
  city: string
  district: string
  detailed_address: string // 社区地址选择器选择的详细地址
  full_address: string
  is_default: boolean
  address_tag?: string // 地址标签
  postal_code?: string // 邮政编码
  location_longitude?: number // 经度
  location_latitude?: number // 纬度
  // 社区相关字段
  community_id?: number | string // 社区ID
  building_id?: number | string // 楼栋ID
  unit_id?: number | string // 单元ID
  create_time?: string
  update_time?: string
  // 兼容旧版本字段（可选）
  receiverName?: string
  receiverPhone?: string
  address?: string
  fullAddress?: string
  isDefault?: boolean
  communityAddress?: string
  communityId?: number | string
  buildingId?: number | string
  unitId?: number | string
  locationLongitude?: number
  locationLatitude?: number
  createTime?: string
  updateTime?: string
}

/**
 * 地址列表响应
 */
export interface IAddressList {
  list: IAddress[]
  total: number
}

/**
 * 创建地址参数
 */
export interface ICreateAddressParams {
  receiver_name: string
  receiver_mobile: string
  phone?: string // 手机号（与receiver_mobile保持一致）
  province: string
  city: string
  district: string
  detailed_address: string // 社区地址选择器选择的详细地址
  is_default?: boolean
  address_tag?: string // 地址标签
  postal_code?: string // 邮政编码
  location_longitude?: number // 经度
  location_latitude?: number // 纬度
  // 社区相关字段
  community_id?: number | string // 社区ID
  building_id?: number | string // 楼栋ID
  unit_id?: number | string // 单元ID
  // 兼容旧版本字段（可选）
  receiverName?: string
  receiverPhone?: string
  address?: string
  isDefault?: boolean
  communityAddress?: string
  communityId?: number | string
  buildingId?: number | string
  unitId?: number | string
  locationLongitude?: number
  locationLatitude?: number
}

/**
 * 更新地址参数
 */
export interface IUpdateAddressParams {
  receiver_name?: string
  receiver_mobile?: string
  phone?: string // 手机号（与receiver_mobile保持一致）
  province?: string
  city?: string
  district?: string
  detailed_address?: string // 社区地址选择器选择的详细地址
  is_default?: boolean
  address_tag?: string // 地址标签
  postal_code?: string // 邮政编码
  location_longitude?: number // 经度
  location_latitude?: number // 纬度
  // 社区相关字段
  community_id?: number | string // 社区ID
  building_id?: number | string // 楼栋ID
  unit_id?: number | string // 单元ID
  // 兼容旧版本字段（可选）
  receiverName?: string
  receiverPhone?: string
  address?: string
  isDefault?: boolean
  communityAddress?: string
  communityId?: number | string
  buildingId?: number | string
  unitId?: number | string
  locationLongitude?: number
  locationLatitude?: number
}
