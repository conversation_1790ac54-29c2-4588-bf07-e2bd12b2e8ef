/**
 * 购物车相关API接口
 * 提供购物车增删改查等功能的API调用
 */

import { http } from '@/utils/http'
import type {
  ICartList,
  IAddToCartParams,
  IUpdateCartParams,
  ICartStats,
  IMerchantCartGroup,
} from './cart.typings'

/**
 * 获取购物车列表
 * @returns 购物车列表
 */
export const getCartList = () => {
  return http.get<ICartList>('/api/v1/user/takeout/cart/list')
}

/**
 * 添加商品到购物车
 * @param params 添加参数
 * @returns 操作结果
 */
export const addToCart = (params: IAddToCartParams) => {
  // 转换参数格式以匹配后端API
  const requestData = {
    food_id: params.productId,
    variant_id: params.variantId || params.specificationId,
    quantity: params.quantity,
    remark: params.remark || '',
    combo_selections: params.comboSelections || [],
  }
  return http.post<void>('/api/v1/user/takeout/cart/add', requestData)
}

/**
 * 更新购物车商品数量
 * @param params 更新参数
 * @returns 操作结果
 */
export const updateCartItem = (params: IUpdateCartParams) => {
  // 转换参数格式以匹配后端API
  const requestData = {
    CartItemId: params.cartItemId,
    Quantity: params.quantity,
    selected: params.selected,
    remark: params.remark,
  }
  return http.post<void>('/api/v1/user/takeout/cart/update', requestData)
}

/**
 * 删除单个购物车商品
 * @param cartItemId 购物车商品ID
 * @returns 操作结果
 */
export const removeCartItem = (cartItemId: number) => {
  return http.post<void>('/api/v1/user/takeout/cart/remove', { CartItemId: cartItemId })
}

/**
 * 批量删除购物车商品
 * @param cartItemIds 购物车商品ID数组
 * @returns 操作结果
 */
export const removeCartItems = (cartItemIds: number[]) => {
  return http.delete<void>('/api/v1/user/takeout/cart/remove', { cartItemIds })
}

/**
 * 清空购物车
 * @returns 操作结果
 */
export const clearCart = () => {
  return http.delete<void>('/api/v1/user/takeout/cart/clear')
}

/**
 * 选择/取消选择购物车商品
 * @param cartItemIds 购物车商品ID数组
 * @param selected 是否选中
 * @returns 操作结果
 */
export const selectCartItems = (cartItemIds: number[], selected: boolean) => {
  return http.post<void>('/api/v1/user/takeout/cart/select', {
    cart_item_ids: cartItemIds,
    selected,
  })
}

/**
 * 全选/取消全选购物车商品
 * @param selected 是否全选
 * @returns 操作结果
 */
export const selectAllCartItems = (selected: boolean) => {
  // 全选功能通过传递空的cart_item_ids数组来实现
  // 后端会自动处理为全选/取消全选
  return http.post<void>('/api/v1/user/takeout/cart/select', { cart_item_ids: [], selected })
}

/**
 * 获取购物车商品数量
 * @returns 商品数量
 */
export const getCartCount = () => {
  return http.get<{ count: number }>('/api/v1/user/takeout/cart/count')
}

/**
 * 获取购物车详细统计
 * @returns 购物车统计信息
 */
export const getCartStats = () => {
  return http.get<ICartStats>('/api/v1/user/takeout/cart/stats')
}

/**
 * 获取按商家分组的购物车
 * @returns 按商家分组的购物车列表
 */
export const getCartByMerchant = () => {
  return http.get<IMerchantCartGroup[]>('/api/v1/user/takeout/cart/merchant-groups')
}

/**
 * 批量删除购物车商品
 * @param cartItemIds 购物车商品ID数组
 * @returns 操作结果
 */
export const batchRemoveCartItems = (cartItemIds: number[]) => {
  return http.delete<void>('/api/v1/user/takeout/cart/batch-remove', { cartItemIds })
}

/**
 * 更新购物车商品备注
 * @param cartItemId 购物车商品ID
 * @param remark 备注内容
 * @returns 操作结果
 */
export const updateCartItemRemark = (cartItemId: number, remark: string) => {
  return http.put<void>(`/api/v1/user/takeout/cart/${cartItemId}/remark`, { remark })
}

/**
 * 检查购物车商品有效性
 * @returns 操作结果
 */
export const validateCartItems = () => {
  return http.post<{ invalidItems: number[] }>('/api/v1/user/takeout/cart/validate')
}

/**
 * 清除无效商品
 * @returns 操作结果
 */
export const clearInvalidItems = () => {
  return http.delete<void>('/api/v1/user/takeout/cart/clear-invalid')
}
