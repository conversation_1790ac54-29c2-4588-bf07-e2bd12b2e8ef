/**
 * 聊天系统相关API接口
 */

import { request } from '@/utils/request'
import { ConversationType } from './chat.typings'
import type {
  IConversationListParams,
  IConversationListResponse,
  ICreateConversationParams,
  IConversation,
  IMessageListParams,
  IMessageListResponse,
  ISendMessageParams,
  IChatMessage,
  IUploadFileParams,
  IUploadFileResponse,
  IMarkReadParams,
  IDeleteMessageParams,
  ISearchMessageParams,
  ISearchMessageResponse,
  ICustomerServiceListResponse,
  IQuickReplyResponse,
} from './chat.typings'

/**
 * 获取会话列表
 */
export const getConversationList = (params: IConversationListParams = {}) => {
  return request<IConversationListResponse>({
    url: '/api/v1/chat/sessions',
    method: 'GET',
    data: {
      page: params.page || 1,
      page_size: params.pageSize || 20,
      type: params.type,
    },
  })
}

/**
 * 获取会话详情
 */
export const getConversationDetail = (conversationId: string) => {
  return request<IConversation>({
    url: `/chat/conversations/${conversationId}`,
    method: 'GET',
  })
}

/**
 * 创建会话
 */
export const createConversation = async (params: ICreateConversationParams) => {
  // 转换前端参数格式为后端期望的格式
  const backendParams = {
    receiver_id: parseInt(params.participantId),
    receiver_type:
      params.type === ConversationType.MERCHANT
        ? 'merchant'
        : params.type === ConversationType.CUSTOMER_SERVICE
          ? 'admin'
          : params.type === ConversationType.DELIVERY
            ? 'delivery'
            : 'user',
  }

  console.log('🎯 [API.createConversation] 创建会话API调用:', {
    originalParams: params,
    backendParams,
  })

  const response = await request<any>({
    url: '/api/v1/chat/sessions',
    method: 'POST',
    data: backendParams,
  })

  console.log('🎯 [API.createConversation] API响应:', response)

  // 处理后端响应格式，提取实际的会话数据
  if (response && response.data) {
    return response.data as IConversation
  } else if (response && response.id) {
    // 如果响应直接是会话数据
    return response as IConversation
  } else {
    throw new Error('创建会话失败：响应格式不正确')
  }
}

/**
 * 删除会话
 */
export const deleteConversation = (conversationId: string) => {
  return request({
    url: `/chat/conversations/${conversationId}`,
    method: 'DELETE',
  })
}

/**
 * 置顶/取消置顶会话
 */
export const toggleConversationTop = (conversationId: string, isTop: boolean) => {
  return request({
    url: `/chat/conversations/${conversationId}/top`,
    method: 'POST',
    data: { isTop },
  })
}

/**
 * 静音/取消静音会话
 */
export const toggleConversationMute = (conversationId: string, isMuted: boolean) => {
  return request({
    url: `/chat/conversations/${conversationId}/mute`,
    method: 'POST',
    data: { isMuted },
  })
}

/**
 * 获取消息列表
 */
export const getMessageList = (params: IMessageListParams) => {
  // 确保会话ID是数字类型
  const sessionId = parseInt(params.conversationId)
  if (isNaN(sessionId)) {
    throw new Error(`无效的会话ID: ${params.conversationId}`)
  }

  return request<IMessageListResponse>({
    url: `/api/v1/chat/sessions/${sessionId}/messages`,
    method: 'GET',
    data: {
      page: params.page || 1,
      page_size: params.pageSize || 20,
      before_message_id: params.beforeMessageId,
      after_message_id: params.afterMessageId,
    },
  })
}

/**
 * 发送消息
 */
export const sendMessage = (params: ISendMessageParams) => {
  // 确保会话ID是数字类型
  const sessionId = parseInt(params.conversationId)
  if (isNaN(sessionId)) {
    throw new Error(`无效的会话ID: ${params.conversationId}`)
  }

  console.log('🎯 [API.sendMessage] 发送消息API调用:', {
    originalConversationId: params.conversationId,
    parsedSessionId: sessionId,
    url: `/api/v1/chat/sessions/${sessionId}/messages/text`,
    content: params.content.text || '',
  })

  return request<IChatMessage>({
    url: `/api/v1/chat/sessions/${sessionId}/messages/text`,
    method: 'POST',
    data: {
      content: params.content.text || '',
    },
  })
}

/**
 * 撤回消息
 */
export const recallMessage = (messageId: string) => {
  return request({
    url: `/chat/messages/${messageId}/recall`,
    method: 'POST',
  })
}

/**
 * 删除消息
 */
export const deleteMessage = (params: IDeleteMessageParams) => {
  return request({
    url: '/chat/messages/delete',
    method: 'POST',
    data: params,
  })
}

/**
 * 标记消息已读
 */
export const markMessageRead = (params: IMarkReadParams) => {
  // 确保会话ID是数字类型
  const sessionId = parseInt(params.conversationId)
  if (isNaN(sessionId)) {
    throw new Error(`无效的会话ID: ${params.conversationId}`)
  }

  return request({
    url: `/api/v1/chat/sessions/${sessionId}/read`,
    method: 'POST',
    data: {
      message_ids: params.messageIds,
    },
  })
}

/**
 * 获取未读消息数量
 */
export const getUnreadCount = () => {
  return request<{ total: number; conversations: Record<string, number> }>({
    url: '/chat/unread-count',
    method: 'GET',
  })
}

/**
 * 上传聊天文件
 */
export const uploadChatFile = (params: IUploadFileParams) => {
  const formData = new FormData()
  formData.append('file', params.file)
  formData.append('type', params.type)

  return request<IUploadFileResponse>({
    url: '/chat/upload',
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}

/**
 * 搜索消息
 */
export const searchMessages = (params: ISearchMessageParams) => {
  return request<ISearchMessageResponse>({
    url: '/chat/messages/search',
    method: 'GET',
    data: params,
  })
}

/**
 * 获取客服列表
 */
export const getCustomerServiceList = () => {
  return request<ICustomerServiceListResponse>({
    url: '/chat/customer-service',
    method: 'GET',
  })
}

/**
 * 分配客服
 */
export const assignCustomerService = (serviceId?: string) => {
  return request<IConversation>({
    url: '/chat/customer-service/assign',
    method: 'POST',
    data: { serviceId },
  })
}

/**
 * 评价客服
 */
export const rateCustomerService = (conversationId: string, rating: number, comment?: string) => {
  return request({
    url: '/chat/customer-service/rate',
    method: 'POST',
    data: {
      conversationId,
      rating,
      comment,
    },
  })
}

/**
 * 获取快捷回复
 */
export const getQuickReplies = () => {
  return request<IQuickReplyResponse>({
    url: '/chat/quick-replies',
    method: 'GET',
  })
}

/**
 * 转接人工客服
 */
export const transferToHuman = (conversationId: string) => {
  return request({
    url: `/chat/conversations/${conversationId}/transfer`,
    method: 'POST',
  })
}

/**
 * 结束会话
 */
export const endConversation = (conversationId: string) => {
  return request({
    url: `/chat/conversations/${conversationId}/end`,
    method: 'POST',
  })
}

/**
 * 获取聊天记录导出链接
 */
export const exportChatHistory = (conversationId: string, startDate?: string, endDate?: string) => {
  return request<{ downloadUrl: string }>({
    url: `/chat/conversations/${conversationId}/export`,
    method: 'POST',
    data: {
      startDate,
      endDate,
    },
  })
}

/**
 * 举报消息
 */
export const reportMessage = (messageId: string, reason: string, description?: string) => {
  return request({
    url: `/chat/messages/${messageId}/report`,
    method: 'POST',
    data: {
      reason,
      description,
    },
  })
}

/**
 * 获取消息详情
 */
export const getMessageDetail = (messageId: string) => {
  return request<IChatMessage>({
    url: `/chat/messages/${messageId}`,
    method: 'GET',
  })
}

/**
 * 发送输入状态
 */
export const sendTypingStatus = (conversationId: string, isTyping: boolean) => {
  return request({
    url: '/chat/typing',
    method: 'POST',
    data: {
      conversationId,
      isTyping,
    },
  })
}

/**
 * 获取在线状态
 */
export const getOnlineStatus = (userIds: string[]) => {
  return request<Record<string, boolean>>({
    url: '/chat/online-status',
    method: 'POST',
    data: { userIds },
  })
}
