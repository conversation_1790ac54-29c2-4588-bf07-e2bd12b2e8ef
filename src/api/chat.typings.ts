/**
 * 聊天系统相关API类型定义
 */

// 消息类型枚举
export enum MessageType {
  TEXT = 'text',
  IMAGE = 'image',
  VOICE = 'voice',
  VIDEO = 'video',
  FILE = 'file',
  LOCATION = 'location',
  SYSTEM = 'system',
  ORDER = 'order',
  GOODS = 'goods',
  ORDER_NOTIFICATION = 'order_notification', // 订单通知消息
}

// 消息状态枚举
export enum MessageStatus {
  SENDING = 'sending',
  SENT = 'sent',
  DELIVERED = 'delivered',
  READ = 'read',
  FAILED = 'failed',
}

// 会话类型枚举
export enum ConversationType {
  CUSTOMER_SERVICE = 'customer_service',
  MERCHANT = 'merchant',
  DELIVERY = 'delivery',
  SYSTEM = 'system',
}

// 订单通知类型枚举
export enum OrderNotificationType {
  PAYMENT_SUCCESS = 'payment_success', // 支付成功
  PAYMENT_FAILED = 'payment_failed', // 支付失败
  ORDER_SHIPPED = 'order_shipped', // 订单发货
  ORDER_DELIVERED = 'order_delivered', // 订单送达
  ORDER_CANCELLED = 'order_cancelled', // 订单取消
  REFUND_APPLIED = 'refund_applied', // 申请退款
  REFUND_APPROVED = 'refund_approved', // 退款通过
  REFUND_REJECTED = 'refund_rejected', // 退款拒绝
  REFUND_SUCCESS = 'refund_success', // 退款成功
  REFUND_FAILED = 'refund_failed', // 退款失败
}

// 用户信息
export interface IChatUser {
  id: string
  nickname: string
  avatar: string
  userType: 'user' | 'service' | 'merchant' | 'delivery' | 'system'
  isOnline: boolean
  lastActiveTime?: string
}

// 消息内容
export interface IMessageContent {
  text?: string
  imageUrl?: string
  voiceUrl?: string
  videoCover?: string
  videoUrl?: string
  fileUrl?: string
  fileName?: string
  fileSize?: number
  location?: {
    latitude: number
    longitude: number
    address: string
  }
  orderInfo?: {
    orderNo: string
    goodsName: string
    goodsImage: string
    amount: number
  }
  goodsInfo?: {
    goodsId: string
    goodsName: string
    goodsImage: string
    price: number
    url: string
  }
  // 订单通知内容
  orderNotification?: {
    type: OrderNotificationType
    title: string
    content: string
    orderId: number
    orderNo: string
    amount?: number
    refundAmount?: number
    actionType?: string
    actionUrl?: string
    data?: any
  }
}

// 消息信息
export interface IChatMessage {
  id: string
  conversationId: string
  senderId: string
  senderInfo: IChatUser
  receiverId: string
  receiverInfo?: IChatUser
  type: MessageType
  content: IMessageContent
  status: MessageStatus
  isRead: boolean
  createdAt: string
  updatedAt: string
  replyToId?: string
  replyToMessage?: IChatMessage
}

// 会话信息
export interface IConversation {
  id: string
  type: ConversationType
  title: string
  avatar: string
  participants: IChatUser[]
  lastMessage?: IChatMessage
  unreadCount: number
  isTop: boolean
  isMuted: boolean
  createdAt: string
  updatedAt: string
  extra?: {
    orderId?: string
    goodsId?: string
    merchantId?: string
    deliveryId?: string
  }
}

// 发送消息参数
export interface ISendMessageParams {
  conversationId: string
  receiverId: string
  type: MessageType
  content: IMessageContent
  replyToId?: string
}

// 创建会话参数
export interface ICreateConversationParams {
  type: ConversationType
  participantId: string
  title?: string
  extra?: {
    orderId?: string
    goodsId?: string
    merchantId?: string
    deliveryId?: string
  }
}

// 会话列表查询参数
export interface IConversationListParams {
  page?: number
  pageSize?: number
  type?: ConversationType
}

// 会话列表响应
export interface IConversationListResponse {
  conversations: IConversation[]
  total: number
  page: number
  pageSize: number
}

// 消息列表查询参数
export interface IMessageListParams {
  conversationId: string
  page?: number
  pageSize?: number
  beforeMessageId?: string
  afterMessageId?: string
}

// 消息列表响应
export interface IMessageListResponse {
  messages: IChatMessage[]
  total: number
  page: number
  pageSize: number
  hasMore: boolean
}

// 上传文件参数
export interface IUploadFileParams {
  file: File
  type: 'image' | 'voice' | 'video' | 'file'
}

// 上传文件响应
export interface IUploadFileResponse {
  url: string
  fileName: string
  fileSize: number
  duration?: number // 音视频时长
  width?: number // 图片/视频宽度
  height?: number // 图片/视频高度
}

// 标记消息已读参数
export interface IMarkReadParams {
  conversationId: string
  messageIds?: string[]
}

// 删除消息参数
export interface IDeleteMessageParams {
  messageIds: string[]
  deleteForAll?: boolean
}

// 搜索消息参数
export interface ISearchMessageParams {
  keyword: string
  conversationId?: string
  type?: MessageType
  startDate?: string
  endDate?: string
  page?: number
  pageSize?: number
}

// 搜索消息响应
export interface ISearchMessageResponse {
  messages: IChatMessage[]
  total: number
  page: number
  pageSize: number
}

// 客服信息
export interface ICustomerService {
  id: string
  name: string
  avatar: string
  isOnline: boolean
  rating: number
  responseTime: number // 平均响应时间（秒）
  introduction: string
  workTime: string
}

// 获取客服列表响应
export interface ICustomerServiceListResponse {
  services: ICustomerService[]
  onlineCount: number
  totalCount: number
}

// 快捷回复
export interface IQuickReply {
  id: string
  content: string
  category: string
  sort: number
  isEnabled: boolean
}

// 获取快捷回复响应
export interface IQuickReplyResponse {
  replies: IQuickReply[]
  categories: string[]
}

// WebSocket消息类型
export enum WSMessageType {
  CONNECT = 'connect',
  DISCONNECT = 'disconnect',
  MESSAGE = 'message',
  READ = 'read',
  TYPING = 'typing',
  ONLINE = 'online',
  OFFLINE = 'offline',
  ERROR = 'error',
  NOTIFICATION = 'notification', // 通知消息
}

// WebSocket通知事件类型
export enum WSNotificationEventType {
  USER_REFUND_RESULT = 'user_refund_result', // 用户退款结果通知
  ORDER_PAYMENT_SUCCESS = 'order_payment_success', // 订单支付成功通知
  ORDER_PAYMENT_FAILED = 'order_payment_failed', // 订单支付失败通知
  ORDER_STATUS_CHANGED = 'order_status_changed', // 订单状态变更通知
  ORDER_SHIPPED = 'order_shipped', // 订单发货通知
  ORDER_DELIVERED = 'order_delivered', // 订单送达通知
  ORDER_CANCELLED = 'order_cancelled', // 订单取消通知
}

// WebSocket消息
export interface IWSMessage {
  type: WSMessageType
  data: any
  timestamp: number
}

// 输入状态
export interface ITypingStatus {
  conversationId: string
  userId: string
  isTyping: boolean
}

// 在线状态
export interface IOnlineStatus {
  userId: string
  isOnline: boolean
  lastActiveTime?: string
}

// 订单通知数据接口
export interface IOrderNotificationData {
  type: WSNotificationEventType
  title: string
  content: string
  orderId: number
  orderNo: string
  userId: number
  merchantId?: number
  amount?: number
  refundAmount?: number
  refundId?: number
  refundNo?: string
  refundStatus?: number
  status?: string
  action?: string
  actionType?: string
  actionUrl?: string
  processTime?: number
  completeTime?: number
  expireTime?: number
  persistent?: boolean
  priority?: number
  data?: any
}
