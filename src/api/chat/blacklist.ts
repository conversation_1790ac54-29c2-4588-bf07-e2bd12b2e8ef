/**
 * 黑名单管理API服务
 *
 * 此文件提供与黑名单管理相关的API接口
 * 包括获取黑名单列表、添加和移除黑名单用户、检查用户是否在黑名单中
 */
import request from '@/utils/request'

// 基础API路径
const BASE_URL = '/api/v1/chat'

/**
 * 黑名单用户信息接口
 */
export interface BlockedUser {
  id: string // 被拉黑用户ID
  username: string // 用户名
  nickname?: string // 昵称
  avatar?: string // 头像
  blockTime: number // 拉黑时间
}

/**
 * 获取黑名单列表
 * @param params 查询参数
 */
export function getBlockedUsers(params?: {
  pageSize?: number // 每页数量
  pageNo?: number // 当前页码
}): Promise<{
  list: BlockedUser[]
  total: number
  pageSize: number
  pageNo: number
}> {
  return request.get(`${BASE_URL}/blacklist`, params, { showLoading: true })
}

/**
 * 添加用户到黑名单
 * @param userId 目标用户ID
 */
export function blockUser(userId: string): Promise<any> {
  return request.post(`${BASE_URL}/blacklist`, { userId }, { showLoading: true })
}

/**
 * 将用户从黑名单中移除
 * @param blockedId 被拉黑的用户ID
 */
export function unblockUser(blockedId: string): Promise<any> {
  return request.del(`${BASE_URL}/blacklist/${blockedId}`, {}, { showLoading: true })
}

/**
 * 检查用户是否在黑名单中
 * @param targetId 目标用户ID
 */
export function checkIsBlocked(targetId: string): Promise<{
  blocked: boolean // 是否已拉黑
  blockTime?: number // 拉黑时间，如果已拉黑
}> {
  return request.get(`${BASE_URL}/blacklist/check/${targetId}`, {}, { showLoading: false })
}
