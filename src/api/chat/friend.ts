/**
 * 好友管理API服务
 *
 * 此文件提供与好友管理相关的API接口
 * 包括获取好友列表、发送好友请求、处理好友请求等功能
 */
import request from '@/utils/request'

// 基础API路径
const BASE_URL = '/api/v1/chat'

/**
 * 好友信息接口
 */
/**
 * 好友接口定义
 * 兼容原有前端结构和后端返回结构
 */
export interface Friend {
  // 前端原有字段
  id: string | number // 好友用户ID
  username?: string // 用户名
  nickname?: string // 昵称
  avatar?: string // 头像
  remark?: string // 备注名
  online?: boolean // 是否在线
  lastSeen?: number // 最后在线时间
  addTime?: number // 添加时间
  category?: string // 分组名称

  // 新增后端字段
  user_id?: number // 用户ID
  user_type?: string // 用户类型
  friend_id?: number // 好友ID
  friend_type?: string // 好友类型
  friend_name?: string // 好友名称
  status?: number // 好友状态
  session_id?: number // 会话ID
  created_at?: string // 创建时间
}

/**
 * 好友请求状态
 */
export enum FriendRequestStatus {
  PENDING = 'pending', // 等待处理
  ACCEPTED = 'accepted', // 已接受
  REJECTED = 'rejected', // 已拒绝
}

/**
 * 好友请求信息接口
 */
export interface FriendRequest {
  id: string // 请求ID
  fromUser: {
    // 发起请求的用户
    id: string // 用户ID
    username: string // 用户名
    nickname?: string // 昵称
    avatar?: string // 头像
  }
  toUser: {
    // 接收请求的用户
    id: string // 用户ID
    username: string // 用户名
    nickname?: string // 昵称
    avatar?: string // 头像
  }
  message?: string // 验证消息
  status: FriendRequestStatus // 请求状态
  createdAt: number // 创建时间
  updatedAt: number // 更新时间
}

/**
 * 获取好友列表
 * @param params 可选参数，支持关键词搜索和分页
 */
export function getFriendList(params?: {
  page?: number
  size?: number
  keyword?: string
}): Promise<{
  code: number
  message: string
  data: {
    list: Array<{
      id: number
      user_id: number
      user_type: string
      friend_id: number
      friend_type: string
      remark: string
      status: number
      session_id: number
      created_at: string
      friend_name: string
    }>
    total: number
    pageSize: number
  }
}> {
  return request.get(`${BASE_URL}/friends`, params, { showLoading: true })
}

/**
 * 发送好友请求
 * @param userId 目标用户ID
 * @param message 验证消息
 */
export function sendFriendRequest(userId: string, message: string = ''): Promise<FriendRequest> {
  return request.post(`${BASE_URL}/friends/requests`, { userId, message }, { showLoading: true })
}

/**
 * 获取好友请求列表
 * @param params 查询参数
 */
export function getFriendRequestList(params?: {
  type?: 'received' | 'sent' // 接收或发送的请求，默认为received
  status?: FriendRequestStatus // 请求状态
  pageSize?: number // 每页数量
  pageNo?: number // 当前页码
}): Promise<{
  list: FriendRequest[]
  total: number
  pageSize: number
  pageNo: number
}> {
  return request.get(`${BASE_URL}/friends/requests`, params, { showLoading: true })
}

/**
 * 处理好友请求
 * @param requestId 请求ID
 * @param action 操作：accept 接受, reject 拒绝
 * @param remark 好友备注名（仅当接受时有效）
 */
export function handleFriendRequest(
  requestId: string,
  action: 'accept' | 'reject',
  remark?: string,
): Promise<any> {
  return request.post(
    `${BASE_URL}/friends/requests/${requestId}`,
    {
      action,
      remark,
    },
    { showLoading: true },
  )
}

/**
 * 更新好友信息
 * @param friendId 好友ID
 * @param data 更新数据
 */
export function updateFriend(
  friendId: string,
  data: {
    remark?: string // 备注名
    category?: string // 分组
  },
): Promise<any> {
  return request.put(`${BASE_URL}/friends/${friendId}`, data, { showLoading: true })
}

/**
 * 删除好友
 * @param friendId 好友ID
 */
export function deleteFriend(friendId: string): Promise<any> {
  return request.del(`${BASE_URL}/friends/${friendId}`, {}, { showLoading: true })
}

/**
 * 搜索用户（通过用户名、手机号等）
 * @param keyword 搜索关键词
 */
export function searchUsers(keyword: string): Promise<
  {
    id: string
    username: string
    nickname?: string
    avatar?: string
    isFriend: boolean // 是否已是好友
  }[]
> {
  return request.get(`${BASE_URL}/users/search`, { keyword }, { showLoading: true })
}

/**
 * 获取用户信息
 * @param userId 用户ID
 */
export function getUserInfo(userId: string): Promise<{
  id: string
  username: string
  nickname?: string
  avatar?: string
  isFriend: boolean // 是否已是好友
}> {
  return request.get(`${BASE_URL}/users/${userId}`, {}, { showLoading: true })
}
