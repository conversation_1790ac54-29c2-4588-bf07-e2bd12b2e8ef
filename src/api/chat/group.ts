/**
 * 群组管理API服务
 *
 * 此文件提供与群组管理相关的API接口
 * 包括创建群组、群组信息管理、群成员管理等功能
 */
import request from '@/utils/request'

// 基础API路径
const BASE_URL = '/api/v1/chat'

/**
 * 群组角色枚举
 */
export enum GroupRole {
  OWNER = 'owner', // 群主
  ADMIN = 'admin', // 管理员
  MEMBER = 'member', // 普通成员
}

/**
 * 群组信息接口 - 适配后端返回数据结构
 */
export interface Group {
  id: number // 群组ID
  name: string // 群组名称
  avatar?: string // 群组头像
  description?: string // 群组描述
  notice?: string // 群公告
  member_count: number // 成员数量
  max_members: number // 最大成员数量
  created_at: string // 创建时间
  updated_at: string // 更新时间
  creator_id: number // 创建者ID
  creator_type: string // 创建者类型
  status: number // 群组状态
  role?: GroupRole // 当前用户在群中的角色
  isMuted?: boolean // 是否已静音

  // 兼容前端使用的字段
  memberCount?: number // 成员数量（兼容字段）
  maxMemberCount?: number // 最大成员数量（兼容字段）
  createTime?: number // 创建时间（兼容字段）
  ownerId?: string // 群主ID（兼容字段）
}

/**
 * 群组成员信息接口
 */
export interface GroupMember {
  userId: string // 用户ID
  groupId: string // 群组ID
  nickname: string // 群内昵称
  role: GroupRole // 角色
  avatar?: string // 头像
  joinTime: number // 加入时间
  username?: string // 用户名
  remarkName?: string // 好友备注名(如果是好友)
}

/**
 * 获取用户的群组列表
 * @param params 查询参数
 */
export async function getUserGroups(params?: {
  keyword?: string // 搜索关键词
  pageSize?: number // 每页数量
  pageNo?: number // 当前页码
}): Promise<
  | {
      list: Group[]
      total: number
      pageSize: number
      pageNo: number
    }
  | Group[]
  | {
      data:
        | {
            list: Group[]
            total: number
          }
        | Group[]
    }
> {
  const response: any = await request.get(`${BASE_URL}/groups`, params, { showLoading: true })

  // 处理后端返回的数据结构
  const transformGroup = (group: any): Group => ({
    ...group,
    // 保持后端原始字段
    id: group.id,
    name: group.name,
    avatar: group.avatar,
    description: group.description,
    notice: group.notice || group.description,
    member_count: group.member_count,
    max_members: group.max_members,
    created_at: group.created_at,
    updated_at: group.updated_at,
    creator_id: group.creator_id,
    creator_type: group.creator_type,
    status: group.status,

    // 添加兼容字段
    memberCount: group.member_count,
    maxMemberCount: group.max_members,
    createTime: group.created_at ? new Date(group.created_at).getTime() : Date.now(),
    ownerId: String(group.creator_id),
    role: group.role || GroupRole.MEMBER,
  })

  // 如果返回的是数组
  if (Array.isArray(response)) {
    return response.map(transformGroup)
  }

  // 如果返回的是分页结构
  if (response && typeof response === 'object') {
    if (response.data) {
      if (Array.isArray(response.data)) {
        return {
          ...response,
          data: response.data.map(transformGroup),
        }
      } else if (response.data.list) {
        return {
          ...response,
          data: {
            ...response.data,
            list: response.data.list.map(transformGroup),
          },
        }
      }
    } else if (response.list) {
      return {
        ...response,
        list: response.list.map(transformGroup),
      }
    }
  }

  return response as any
}

/**
 * 创建群组
 * @param data 群组信息
 */
export function createGroup(data: {
  name: string // 群组名称
  avatar?: string // 群组头像
  notice?: string // 群公告
  initialMembers: string[] // 初始成员ID列表(不包括创建者自己)
}): Promise<Group> {
  return request.post(`${BASE_URL}/groups`, data, { showLoading: true })
}

/**
 * 获取群组信息
 * @param groupId 群组ID
 */
export async function getGroupInfo(groupId: string): Promise<Group> {
  const response = await request.get(`${BASE_URL}/groups/${groupId}`, {}, { showLoading: true })

  // 处理后端返回的数据结构
  let groupData: any
  if (response && typeof response === 'object') {
    // 如果返回的是包装结构 {code, message, data}
    if ((response as any).data && (response as any).data.data) {
      groupData = (response as any).data.data
    } else if ((response as any).data) {
      groupData = (response as any).data
    } else {
      groupData = response
    }
  } else {
    groupData = response
  }

  // 数据转换：将后端字段映射到前端期望的字段
  const transformedData: Group = {
    ...groupData,
    // 保持后端原始字段
    id: groupData.id,
    name: groupData.name,
    avatar: groupData.avatar,
    description: groupData.description,
    notice: groupData.notice || groupData.description, // 如果没有notice，使用description
    member_count: groupData.member_count,
    max_members: groupData.max_members,
    created_at: groupData.created_at,
    updated_at: groupData.updated_at,
    creator_id: groupData.creator_id,
    creator_type: groupData.creator_type,
    status: groupData.status,

    // 添加兼容字段供前端使用
    memberCount: groupData.member_count,
    maxMemberCount: groupData.max_members,
    createTime: groupData.created_at ? new Date(groupData.created_at).getTime() : Date.now(),
    ownerId: String(groupData.creator_id),

    // 默认角色（如果后端没有返回）
    role: groupData.role || GroupRole.MEMBER,
  }

  return transformedData
}

/**
 * 更新群组信息
 * @param groupId 群组ID
 * @param data 更新数据
 */
export function updateGroupInfo(
  groupId: string,
  data: {
    name?: string // 群组名称
    avatar?: string // 群组头像
    notice?: string // 群公告
  },
): Promise<Group> {
  return request.put(`${BASE_URL}/groups/${groupId}`, data, { showLoading: true })
}

/**
 * 解散群组(仅群主可操作)
 * @param groupId 群组ID
 */
export function dismissGroup(groupId: string): Promise<any> {
  return request.post(`${BASE_URL}/groups/${groupId}/dismiss`, {}, { showLoading: true })
}

/**
 * 转让群主身份
 * @param groupId 群组ID
 * @param newOwnerId 新群主用户ID
 */
export function transferOwner(groupId: string, newOwnerId: string): Promise<any> {
  return request.post(
    `${BASE_URL}/groups/${groupId}/transfer`,
    { newOwnerId },
    { showLoading: true },
  )
}

/**
 * 获取群成员列表
 * @param groupId 群组ID
 * @param params 查询参数
 */
export function getGroupMembers(
  groupId: string,
  params?: {
    keyword?: string // 搜索关键词
    pageSize?: number // 每页数量
    pageNo?: number // 当前页码
    roleFilter?: GroupRole // 按角色筛选
  },
): Promise<{
  list: GroupMember[]
  total: number
  pageSize: number
  pageNo: number
}> {
  return request.get(`${BASE_URL}/groups/${groupId}/members`, params, { showLoading: true })
}

/**
 * 添加群成员请求参数接口
 */
export interface AddGroupMemberRequest {
  user_ids: number[] // 用户ID数组
  user_type: string // 用户类型
}

/**
 * 添加群成员
 * @param groupId 群组ID
 * @param requestData 添加群成员请求参数
 */
export function addGroupMember(groupId: string, requestData: AddGroupMemberRequest): Promise<any> {
  return request.post(`${BASE_URL}/groups/${groupId}/members`, requestData, { showLoading: true })
}

/**
 * 移除群成员(群主或管理员可操作)
 * @param groupId 群组ID
 * @param memberId 成员用户ID
 */
export function removeGroupMember(groupId: string, memberId: string): Promise<any> {
  return request.del(`${BASE_URL}/groups/${groupId}/members/${memberId}`, {}, { showLoading: true })
}

/**
 * 更新群成员信息
 * @param groupId 群组ID
 * @param memberId 成员用户ID
 * @param data 更新数据
 */
export function updateGroupMember(
  groupId: string,
  memberId: string,
  data: {
    nickname?: string // 群内昵称
    role?: GroupRole // 角色(仅群主可设置)
  },
): Promise<any> {
  return request.put(`${BASE_URL}/groups/${groupId}/members/${memberId}`, data, {
    showLoading: true,
  })
}

/**
 * 退出群组
 * @param groupId 群组ID
 */
export function quitGroup(groupId: string): Promise<any> {
  // 退出群组实际上是删除自己这个成员
  return request.del(`${BASE_URL}/groups/${groupId}/members/me`, {}, { showLoading: true })
}

/**
 * 邀请用户加入群组
 * @param groupId 群组ID
 * @param userIds 用户ID数组
 */
export function inviteToGroup(groupId: string, userIds: string[]): Promise<any> {
  return request.post(`${BASE_URL}/groups/${groupId}/invites`, { userIds }, { showLoading: true })
}
