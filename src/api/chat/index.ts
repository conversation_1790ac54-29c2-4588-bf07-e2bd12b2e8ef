/**
 * 聊天模块API统一导出
 *
 * 此文件统一导出所有聊天相关的API接口和类型定义
 * 方便其他模块统一引用
 */

// 会话相关 - 类型导出（只在类型检查阶段使用，不会出现在编译结果中）
export type { Session, Message } from './session'

// 会话相关 - 值导出（这些会保留在编译结果中供运行时使用）
export {
  SessionType,
  MessageStatus,
  getSessions,
  getSession,
  createUserSession,
  createGroupSession,
  setSessionSticky,
  setSessionMuted,
  deleteSession,
  getMessages,
  sendTextMessage,
  revokeMessage,
  readSession,
} from './session'

// 消息相关 - 重命名 MessageType 为 MediaMessageType 避免冲突
export {
  MediaType,
  sendMediaMessage,
  uploadTempMedia,
  sendImageMessage,
  sendVoiceMessage,
  sendVideoMessage,
  sendFileMessage,
  previewFile,
} from './message'

// 聊天室相关
export * from './room'

// 好友管理
export * from './friend'

// 群组管理
export * from './group'

// 黑名单管理
export * from './blacklist'

// WebSocket服务
export { WebSocketStatus, WebSocketService } from '@/service/websocket'

// 消息处理器
export * from './messageHandler'

// 类型定义
export * from './types'

// 重新导出 session 中的 MessageType 为 SessionMessageType
export { MessageType as SessionMessageType } from './session'
