/**
 * 聊天消息API服务
 *
 * 此文件提供与聊天消息相关的API接口
 * 主要处理媒体类消息的发送和接收功能
 */
import request from '@/utils/request'
import { Message } from './session'

// 基础API路径
const BASE_URL = '/api/v1/chat'

/**
 * 媒体消息类型
 */
export enum MediaType {
  IMAGE = 'image',
  VOICE = 'voice',
  VIDEO = 'video',
  FILE = 'file',
}

/**
 * 发送媒体消息
 * @param sessionId 会话ID
 * @param filePath 文件本地路径
 * @param type 媒体类型
 * @param options 额外选项
 */
export function sendMediaMessage(
  sessionId: string,
  filePath: string,
  type: MediaType,
  options?: {
    fileName?: string // 文件名（适用于文件类型）
    duration?: number // 语音或视频时长（毫秒）
    width?: number // 图片或视频宽度
    height?: number // 图片或视频高度
    size?: number // 文件大小（字节）
    thumb?: string // 视频缩略图（视频类型）
    replyToId?: string // 回复的消息ID
  },
): Promise<Message> {
  // 构建表单数据
  const formData: Record<string, any> = {
    type,
    ...options,
  }

  // 上传文件并发送消息
  return request.upload(
    `${BASE_URL}/sessions/${sessionId}/messages/media`,
    filePath,
    'file',
    formData,
    {
      showLoading: true,
      loadingText: '发送中...',
    },
  )
}

/**
 * 上传临时文件（不发送消息）
 * 用于先上传文件获取URL，然后再决定是否发送消息
 * @param filePath 文件本地路径
 * @param type 媒体类型
 */
export function uploadTempMedia(
  filePath: string,
  type: MediaType,
): Promise<{
  url: string // 文件URL
  thumbUrl?: string // 缩略图URL（如果有）
  width?: number // 宽度（图片和视频）
  height?: number // 高度（图片和视频）
  duration?: number // 时长（语音和视频）
  size: number // 文件大小
  name: string // 文件名称
}> {
  return request.upload(
    `${BASE_URL}/media/upload`,
    filePath,
    'file',
    { type },
    {
      showLoading: true,
      loadingText: '上传中...',
    },
  )
}

/**
 * 发送图片消息
 * @param sessionId 会话ID
 * @param filePath 图片本地路径
 * @param options 可选参数
 */
export function sendImageMessage(
  sessionId: string,
  filePath: string,
  options?: {
    width?: number // 图片宽度
    height?: number // 图片高度
    size?: number // 文件大小
    replyToId?: string // 回复的消息ID
  },
): Promise<Message> {
  return sendMediaMessage(sessionId, filePath, MediaType.IMAGE, options)
}

/**
 * 发送语音消息
 * @param sessionId 会话ID
 * @param filePath 语音文件本地路径
 * @param duration 语音时长（毫秒）
 * @param replyToId 回复的消息ID（可选）
 */
export function sendVoiceMessage(
  sessionId: string,
  filePath: string,
  duration: number,
  replyToId?: string,
): Promise<Message> {
  return sendMediaMessage(sessionId, filePath, MediaType.VOICE, {
    duration,
    replyToId,
  })
}

/**
 * 发送视频消息
 * @param sessionId 会话ID
 * @param filePath 视频文件本地路径
 * @param options 可选参数
 */
export function sendVideoMessage(
  sessionId: string,
  filePath: string,
  options: {
    duration: number // 视频时长（毫秒）
    width?: number // 视频宽度
    height?: number // 视频高度
    size?: number // 文件大小
    thumb?: string // 视频缩略图
    replyToId?: string // 回复的消息ID
  },
): Promise<Message> {
  return sendMediaMessage(sessionId, filePath, MediaType.VIDEO, options)
}

/**
 * 发送文件消息
 * @param sessionId 会话ID
 * @param filePath 文件本地路径
 * @param fileName 文件名称
 * @param fileSize 文件大小（字节）
 * @param replyToId 回复的消息ID（可选）
 */
export function sendFileMessage(
  sessionId: string,
  filePath: string,
  fileName: string,
  fileSize: number,
  replyToId?: string,
): Promise<Message> {
  return sendMediaMessage(sessionId, filePath, MediaType.FILE, {
    fileName,
    size: fileSize,
    replyToId,
  })
}

/**
 * 预览文件
 * @param url 文件URL
 */
export function previewFile(url: string): void {
  // 判断文件类型
  const ext = url.split('.').pop()?.toLowerCase() || ''

  // 图片类型
  if (['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(ext)) {
    uni.previewImage({
      urls: [url],
      current: url,
      fail: () => {
        uni.showToast({
          title: '预览图片失败',
          icon: 'none',
        })
      },
    })
    return
  }

  // 视频类型
  if (['mp4', 'mov', 'avi', 'mkv', 'flv', 'webm'].includes(ext)) {
    uni.navigateTo({
      url: `/pages-sub/video-player/index?url=${encodeURIComponent(url)}`,
    })
    return
  }

  // 其他文件类型，尝试下载或打开
  uni.showLoading({ title: '打开中...' })

  // 尝试打开文件
  uni.downloadFile({
    url,
    success: (res) => {
      if (res.statusCode === 200) {
        uni.openDocument({
          filePath: res.tempFilePath,
          showMenu: true,
          success: () => {
            console.log('打开文档成功')
          },
          fail: () => {
            uni.showToast({
              title: '无法打开该文件',
              icon: 'none',
            })
          },
        })
      } else {
        uni.showToast({
          title: '下载文件失败',
          icon: 'none',
        })
      }
    },
    fail: () => {
      uni.showToast({
        title: '下载文件失败',
        icon: 'none',
      })
    },
    complete: () => {
      uni.hideLoading()
    },
  })
}
