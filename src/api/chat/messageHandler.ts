/**
 * WebSocket消息处理器
 *
 * 此文件用于处理WebSocket收到的各类消息
 * 包括文本、媒体、系统通知等
 */
import { ref } from 'vue'
import { WebSocketService } from '@/service/websocket'
import { useChatStore } from '@/store/chat'
import { useUserStore } from '@/store/user'
import { MessageType } from '@/api/chat.typings'
import type { IChatMessage } from '@/api/chat.typings'

/**
 * 消息通知事件类型
 */
export enum WebSocketEventType {
  MESSAGE_RECEIVED = 'message_received', // 新消息
  MESSAGE_READ = 'message_read', // 消息已读
  MESSAGE_REVOKED = 'message_revoked', // 消息撤回
  SESSION_UPDATED = 'session_updated', // 会话更新
  FRIEND_REQUEST = 'friend_request', // 好友请求
  FRIEND_UPDATED = 'friend_updated', // 好友状态变更
  GROUP_UPDATED = 'group_updated', // 群组信息变更
  GROUP_MEMBER_UPDATED = 'group_member_updated', // 群成员变更
  USER_KICKED = 'user_kicked', // 被踢出群组
  USER_BLOCKED = 'user_blocked', // 被拉黑
  USER_UNBLOCKED = 'user_unblocked', // 被移出黑名单
}

// 最新未读消息列表
export const unreadMessages = ref<IChatMessage[]>([])

// 最大未读消息缓存数量
const MAX_UNREAD_MESSAGES = 50

/**
 * 初始化WebSocket消息处理
 */
export function initMessageHandler(): void {
  // 注册消息处理回调
  const wsService = WebSocketService.getInstance()
  wsService.addEventListener('message', handleWebSocketMessage)
}

/**
 * 处理WebSocket消息
 * @param data 消息数据
 */
function handleWebSocketMessage(data: any): void {
  if (!data || !data.type) return

  try {
    // 根据消息类型分发处理
    switch (data.type) {
      case WebSocketEventType.MESSAGE_RECEIVED:
        handleNewMessage(data.data)
        break
      case WebSocketEventType.MESSAGE_READ:
        handleMessageRead(data.data)
        break
      case WebSocketEventType.MESSAGE_REVOKED:
        handleMessageRevoked(data.data)
        break
      case WebSocketEventType.SESSION_UPDATED:
        handleSessionUpdated(data.data)
        break
      case WebSocketEventType.FRIEND_REQUEST:
        handleFriendRequest(data.data)
        break
      case WebSocketEventType.FRIEND_UPDATED:
        handleFriendUpdated(data.data)
        break
      case WebSocketEventType.GROUP_UPDATED:
        handleGroupUpdated(data.data)
        break
      case WebSocketEventType.GROUP_MEMBER_UPDATED:
        handleGroupMemberUpdated(data.data)
        break
      case WebSocketEventType.USER_KICKED:
        handleUserKicked(data.data)
        break
      case WebSocketEventType.USER_BLOCKED:
        handleUserBlocked(data.data)
        break
      case WebSocketEventType.USER_UNBLOCKED:
        handleUserUnblocked(data.data)
        break
      default:
        console.warn('未知的WebSocket消息类型', data.type)
    }
  } catch (error) {
    console.error('处理WebSocket消息失败', error)
  }
}

/**
 * 处理新消息
 * @param message 消息数据
 */
function handleNewMessage(message: IChatMessage): void {
  const chatStore = useChatStore()
  const userStore = useUserStore()

  // 如果不是当前用户的消息，则增加未读消息计数
  if (message.senderId !== String(userStore.userInfo?.id)) {
    // 添加到未读消息列表（保持最大限制）
    unreadMessages.value.unshift(message)
    if (unreadMessages.value.length > MAX_UNREAD_MESSAGES) {
      unreadMessages.value.pop() // 移除最老的消息
    }

    // 播放提示音
    playMessageSound()

    // 如果是当前会话，发送已读回执
    if (chatStore.currentConversation?.id === message.conversationId) {
      sendReadReceipt(message.conversationId)
    }
  }

  // 接收新消息到 store
  chatStore.receiveMessage(message)
}

/**
 * 播放消息提示音
 */
function playMessageSound(): void {
  const audio = uni.createInnerAudioContext()
  audio.src = '/static/sounds/message.mp3' // 确保项目中有这个音频文件
  audio.play()
}

/**
 * 发送已读回执
 * @param conversationId 会话ID
 */
function sendReadReceipt(conversationId: string): void {
  if (!conversationId) return

  // 通过WebSocket发送已读回执
  const wsService = WebSocketService.getInstance()
  wsService.sendMessage({
    type: 'read_receipt',
    data: { conversationId },
    timestamp: Date.now(),
  })

  // 同时调用HTTP API更新服务器端状态
  const chatStore = useChatStore()
  chatStore.markMessagesRead(conversationId).catch((err) => {
    console.error('发送已读回执失败', err)
  })
}

/**
 * 处理消息已读事件
 * @param data 已读消息数据
 */
function handleMessageRead(data: {
  conversationId: string
  readerId: string
  timestamp: number
}): void {
  const chatStore = useChatStore()
  // 标记消息为已读
  chatStore.markMessagesRead(data.conversationId)
}

/**
 * 处理消息撤回事件
 * @param data 撤回消息数据
 */
function handleMessageRevoked(data: { conversationId: string; messageId: string }): void {
  const chatStore = useChatStore()
  // 在当前消息列表中找到并标记为撤回
  const message = chatStore.currentMessages.find((msg) => msg.id === data.messageId)
  if (message) {
    message.content = { text: '[消息已撤回]' }
    message.type = MessageType.SYSTEM
  }

  // 显示撤回提示
  uni.showToast({
    title: '对方撤回了一条消息',
    icon: 'none',
  })
}

/**
 * 处理会话更新事件
 * @param data 会话数据
 */
function handleSessionUpdated(data: any): void {
  const chatStore = useChatStore()
  // 重新获取会话列表以更新信息
  chatStore.fetchConversations()
}

/**
 * 处理好友请求事件
 * @param data 好友请求数据
 */
function handleFriendRequest(data: any): void {
  // 显示好友请求通知
  uni.showToast({
    title: `${data.senderName || '有用户'}请求添加你为好友`,
    icon: 'none',
    duration: 3000,
  })
}

/**
 * 处理好友状态更新事件
 * @param data 好友状态数据
 */
function handleFriendUpdated(data: any): void {
  // TODO: 更新好友列表
  console.log('好友状态更新', data)
}

/**
 * 处理群组信息更新事件
 * @param data 群组信息
 */
function handleGroupUpdated(data: any): void {
  // TODO: 更新群组信息
  console.log('群组信息更新', data)
}

/**
 * 处理群成员变更事件
 * @param data 群成员数据
 */
function handleGroupMemberUpdated(data: any): void {
  // TODO: 更新群成员信息
  console.log('群成员变更', data)
}

/**
 * 处理被踢出群组事件
 * @param data 被踢数据
 */
function handleUserKicked(data: { groupId: string; groupName: string }): void {
  // 显示被踢通知
  uni.showToast({
    title: `你已被踢出群组「${data.groupName || data.groupId}」`,
    icon: 'none',
    duration: 3000,
  })

  // 重新获取会话列表
  const chatStore = useChatStore()
  chatStore.fetchConversations()
}

/**
 * 处理被拉黑事件
 * @param data 黑名单数据
 */
function handleUserBlocked(data: { userId: string; userName: string }): void {
  // 显示被拉黑通知
  uni.showToast({
    title: `你已被「${data.userName || data.userId}」拉入黑名单`,
    icon: 'none',
    duration: 3000,
  })

  // 重新获取会话列表
  const chatStore = useChatStore()
  chatStore.fetchConversations()
}

/**
 * 处理被移出黑名单事件
 * @param data 黑名单数据
 */
function handleUserUnblocked(data: { userId: string; userName: string }): void {
  // 显示被移出黑名单通知
  uni.showToast({
    title: `你已被「${data.userName || data.userId}」移出黑名单`,
    icon: 'none',
    duration: 3000,
  })

  // 重新获取会话列表
  const chatStore = useChatStore()
  chatStore.fetchConversations()
}
