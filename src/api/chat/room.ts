/**
 * 聊天室API服务
 *
 * 此文件提供与聊天室相关的API接口
 * 包括加入会话、获取历史消息等功能
 */
import request from '@/utils/request'
import { Message } from './session'

// 基础API路径
const BASE_URL = '/api/v1/chat'

/**
 * 聊天消息接口（UI显示格式）
 */
export interface UIMessage {
  id: string
  type: string
  content: string
  senderId: string | number
  senderName: string
  avatar?: string
  targetId: string | number
  targetType: string
  sendTime: number
  status: string
  fileName?: string
  fileSize?: number
  fileType?: string
  fileExt?: string
}

/**
 * 加入聊天会话
 * @param sessionId 会话ID
 * @param params 可选参数，包含receiver_id等
 */
export function joinSession(
  sessionId: string | number,
  params: Record<string, any> = {},
): Promise<any> {
  return request.post(`${BASE_URL}/sessions/${sessionId}/join`, params, { showLoading: true })
}

/**
 * 加入群聊会话
 * @param groupId 群组ID
 */
export function joinGroupSession(groupId: string | number): Promise<any> {
  return request.post(`${BASE_URL}/groups/${groupId}/join`, {}, { showLoading: true })
}

/**
 * 离开群聊会话
 * @param groupId 群组ID
 */
export function leaveGroupSession(groupId: string | number): Promise<any> {
  return request.post(`${BASE_URL}/groups/${groupId}/leave`, {}, { showLoading: true })
}

/**
 * 获取群聊会话详情
 * @param groupId 群组ID
 */
export function getGroupSessionDetails(groupId: string | number): Promise<any> {
  return request.get(`${BASE_URL}/groups/${groupId}/session`, {}, { showLoading: true })
}

/**
 * 获取聊天历史消息
 * @param sessionId 会话ID
 * @param page 页码
 * @param pageSize 每页数量
 */
export function getHistoryMessages(
  sessionId: string | number,
  page: number = 1,
  pageSize: number = 20,
): Promise<{
  code: number
  message: string
  data: {
    list: Message[]
    total: number
    page_count: number
    page: number
    page_size: number
  }
}> {
  return request.get(
    `${BASE_URL}/sessions/${sessionId}/messages`,
    {
      page,
      page_size: pageSize,
    },
    { showLoading: true },
  )
}

/**
 * 发送消息
 * @param sessionId 会话ID
 * @param content 消息内容
 * @param messageType 消息类型
 * @param fileInfo 文件信息（可选）
 */
export function sendMessage(
  sessionId: string | number,
  content: string,
  messageType: string = 'text',
  fileInfo?: {
    fileName?: string
    fileSize?: number
    fileType?: string
    fileExt?: string
  },
): Promise<{
  code: number
  message: string
  data: {
    message?: string
    id?: number
    data?: any
  }
}> {
  const endpoint =
    messageType === 'text'
      ? `${BASE_URL}/sessions/${sessionId}/messages/text`
      : `${BASE_URL}/sessions/${sessionId}/messages/media-url`

  const requestData: any = {
    content,
    message_type: messageType,
  }

  // 如果是媒体消息且提供了文件信息，则添加文件相关字段
  if (messageType !== 'text' && fileInfo) {
    if (fileInfo.fileName) requestData.file_name = fileInfo.fileName
    if (fileInfo.fileSize) requestData.file_size = fileInfo.fileSize
    if (fileInfo.fileType) requestData.file_type = fileInfo.fileType
    if (fileInfo.fileExt) requestData.file_ext = fileInfo.fileExt
  }

  return request.post(endpoint, requestData, { showLoading: false })
}

/**
 * 将后端消息格式转换为前端UI格式
 * @param message 后端消息对象
 * @param defaultAvatar 默认头像
 */
export function convertToUIMessage(message: any, defaultAvatar: string): UIMessage {
  // 处理消息类型转换
  let messageType = message.type || message.message_type || 'text'
  let messageContent = message.content

  // 如果是通知类型消息，转换为订单通知格式
  if (messageType === 'notification' && message.notification_type) {
    messageType = 'order_notification'
    messageContent = {
      orderNotification: {
        type: message.notification_type,
        title: '订单通知',
        content: message.content,
        orderId: message.notification_data?.order_id || 0,
        orderNo: message.notification_data?.order_no || '',
        amount: message.notification_data?.pay_amount,
        refundAmount: message.notification_data?.refund_amount,
        actionType: message.notification_data?.action_type,
        actionUrl: message.notification_data?.action_url,
        data: message.notification_data,
      },
    }
  }

  return {
    id: message.message_id || message.id,
    type: messageType,
    content: messageContent,
    senderId: message.sender_id,
    senderName: message.sender_name || '用户',
    avatar: message.sender_avatar || defaultAvatar,
    targetId: message.receiver_id?.toString() || '',
    targetType: message.receiver_type || 'user',
    sendTime: message.created_at ? new Date(message.created_at).getTime() : Date.now(),
    status: 'received',
    fileName: message.file_name,
    fileSize: message.file_size,
    fileType: message.file_type,
    fileExt: message.file_ext,
  }
}

/**
 * 删除消息
 * @param messageId 消息ID
 * @param sessionId 会话ID（必须提供以匹配后端路由）
 */
export function deleteMessage(
  sessionId: string | number,
  messageId: string | number,
): Promise<any> {
  return request.del(
    `${BASE_URL}/sessions/${sessionId}/messages/${messageId}`,
    {},
    { showLoading: true },
  )
}

/**
 * 标记消息已读
 * @param sessionId 会话ID
 * @param messageId 消息ID
 */
export function markMessageRead(
  sessionId: string | number,
  messageId: string | number,
): Promise<any> {
  return request.put(
    `${BASE_URL}/sessions/${sessionId}/messages/${messageId}/read`,
    {},
    { showLoading: false },
  )
}
