/**
 * 聊天会话API服务
 *
 * 此文件提供与聊天会话相关的API接口
 * 包括获取会话列表、获取会话消息等功能
 */
import request from '@/utils/request'

// 基础API路径
const BASE_URL = '/api/v1/chat'

/**
 * 会话类型
 */
export enum SessionType {
  // 单聊
  USER = 'user',
  // 群聊
  GROUP = 'group',
}

/**
 * 会话信息接口
 */
export interface Session {
  id: string // 会话ID
  type: SessionType // 会话类型: user或group
  name: string // 会话名称
  avatar: string // 会话头像
  unread: number // 未读消息数
  lastMessage?: {
    // 最后一条消息
    content: string // 消息内容
    type: string // 消息类型
    sendTime: number // 发送时间
    senderName?: string // 发送者名称(群聊显示)
  }
  updatedAt: number // 会话更新时间
  groupId?: string // 群组ID(群聊)
  userId?: string // 用户ID(单聊)
  isMuted?: boolean // 是否静音
  isSticky?: boolean // 是否置顶
}

/**
 * 消息类型
 */
export enum MessageType {
  TEXT = 'text',
  IMAGE = 'image',
  VOICE = 'voice',
  VIDEO = 'video',
  FILE = 'file',
  SYSTEM = 'system',
  READ_RECEIPT = 'read_receipt', // 已读回执消息
}

/**
 * 消息状态
 */
export enum MessageStatus {
  SENDING = 'sending',
  SENT = 'sent',
  DELIVERED = 'delivered',
  READ = 'read',
  FAILED = 'failed',
}

/**
 * 后端返回的会话数据结构
 */
export interface BackendSession {
  id: string | number // 会话 ID
  session_id?: string | number // 实际会话 ID
  session_type?: string // 会话类型
  type?: string // 会话类型的替代字段
  title?: string // 会话标题
  name?: string // 会话名称
  target_name?: string // 目标名称
  avatar?: string // 会话头像
  unread_count?: number // 未读消息数
  last_message?: {
    content: string
    type: string
    created_at?: string | number
    sender_name?: string
  }
  updated_at?: string | number // 更新时间
  created_at?: string | number // 创建时间
  group_id?: string | number // 群组ID
  receiver_id?: string | number // 接收者ID
  sender_id?: string | number // 发送者ID
  target?: {
    id?: string | number // 目标ID
    name?: string // 目标名称
    avatar?: string // 目标头像
  }
  is_muted?: boolean // 是否静音
  is_sticky?: boolean // 是否置顶
}

/**
 * 消息信息接口
 */
export interface Message {
  id: string // 消息ID
  sessionId: string // 会话ID
  senderId: string // 发送者ID
  senderName?: string // 发送者名称
  senderAvatar?: string // 发送者头像
  type: MessageType // 消息类型
  content: string // 消息内容
  sendTime: number // 发送时间
  status?: MessageStatus // 消息状态
  isRevoked?: boolean // 是否已撤回
  replyTo?: {
    // 回复的消息
    id: string // 被回复消息ID
    content: string // 被回复消息内容
  }
  extend?: any // 扩展信息，如文件大小、时长等
}

/**
 * 获取会话列表
 * @param params 查询参数
 * @returns 会话列表
 */
export function getSessions(params?: {
  pageNo?: number
  pageSize?: number
  keyword?: string
}): Promise<{
  list: Session[]
  total: number
  pageNo: number
  pageSize: number
}> {
  return request.get(`${BASE_URL}/sessions`, params)
}

/**
 * 获取单个会话信息
 * @param sessionId 会话ID
 */
export function getSession(sessionId: string): Promise<Session> {
  return request.get(`${BASE_URL}/sessions/${sessionId}`, {})
}

/**
 * 创建或获取与用户的会话
 * @param userId 用户ID
 */
export function createUserSession(userId: string): Promise<Session> {
  return request.post(`${BASE_URL}/sessions/user/${userId}`, {})
}

/**
 * 创建或获取群组会话
 * @param groupId 群组ID
 */
export function createGroupSession(groupId: string): Promise<Session> {
  return request.post(`${BASE_URL}/sessions/group/${groupId}`, {})
}

/**
 * 设置会话置顶状态
 * @param sessionId 会话ID
 * @param isSticky 是否置顶
 */
export function setSessionSticky(sessionId: string, isSticky: boolean): Promise<any> {
  return request.put(`${BASE_URL}/sessions/${sessionId}/sticky`, { isSticky })
}

/**
 * 设置会话静音状态
 * @param sessionId 会话ID
 * @param isMuted 是否静音
 */
export function setSessionMuted(sessionId: string, isMuted: boolean): Promise<any> {
  return request.put(`${BASE_URL}/sessions/${sessionId}/mute`, { isMuted })
}

/**
 * 删除会话
 * @param sessionId 会话ID
 */
export function deleteSession(sessionId: string): Promise<any> {
  return request.del(`${BASE_URL}/sessions/${sessionId}`, {})
}

/**
 * 获取会话消息列表
 * @param sessionId 会话ID
 * @param params 查询参数
 * @returns 消息列表
 */
export function getMessages(
  sessionId: string,
  params?: {
    beforeId?: string // 在此消息ID之前的消息(不包括此ID)
    afterId?: string // 在此消息ID之后的消息(不包括此ID)
    pageSize?: number // 每页数量
  },
): Promise<{
  list: Message[]
  hasMore: boolean
}> {
  return request.get(`${BASE_URL}/sessions/${sessionId}/messages`, params)
}

/**
 * 发送文本消息
 * @param sessionId 会话ID
 * @param content 消息内容
 * @param replyToId 回复的消息ID（可选）
 */
export function sendTextMessage(
  sessionId: string,
  content: string,
  replyToId?: string,
): Promise<Message> {
  return request.post(`${BASE_URL}/sessions/${sessionId}/messages/text`, {
    content,
    replyToId,
  })
}

/**
 * 撤回消息
 * @param sessionId 会话ID
 * @param messageId 消息ID
 */
export function revokeMessage(sessionId: string, messageId: string): Promise<any> {
  return request.post(`${BASE_URL}/sessions/${sessionId}/messages/${messageId}/revoke`, {})
}

/**
 * 设置消息已读
 * @param sessionId 会话ID
 */
export function readSession(sessionId: string): Promise<any> {
  return request.post(`${BASE_URL}/sessions/${sessionId}/read`, {})
}
