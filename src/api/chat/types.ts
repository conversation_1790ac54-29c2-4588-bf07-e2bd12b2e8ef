// 聊天相关类型定义

/**
 * 会话项类型
 */
export interface SessionItem {
  /** 会话ID */
  id: string
  /** 会话名称 */
  name: string
  /** 头像URL */
  avatar?: string
  /** 最后一条消息内容 */
  lastMessage: string
  /** 最后一条消息类型 */
  lastMessageType: 'text' | 'image' | 'voice' | 'video' | 'file'
  /** 最后一条消息时间戳 */
  lastMessageTime: number
  /** 未读消息数量 */
  unreadCount: number
  /** 会话类型 */
  type: 'user' | 'group'
}

/**
 * 消息类型
 */
export interface MessageItem {
  /** 消息ID */
  id: string
  /** 发送者ID */
  senderId: string
  /** 发送者名称 */
  senderName: string
  /** 发送者头像 */
  senderAvatar?: string
  /** 消息内容 */
  content: string
  /** 消息类型 */
  type: 'text' | 'image' | 'voice' | 'video' | 'file'
  /** 发送时间戳 */
  timestamp: number
  /** 是否为自己发送的消息 */
  isSelf: boolean
  /** 消息状态 */
  status: 'sending' | 'sent' | 'delivered' | 'read' | 'failed'
}

/**
 * 聊天室信息
 */
export interface ChatRoomInfo {
  /** 聊天室ID */
  id: string
  /** 聊天室名称 */
  name: string
  /** 聊天室头像 */
  avatar?: string
  /** 聊天室类型 */
  type: 'user' | 'group'
  /** 成员列表 */
  members?: ChatMember[]
  /** 创建时间 */
  createTime: number
}

/**
 * 聊天成员
 */
export interface ChatMember {
  /** 用户ID */
  id: string
  /** 用户名称 */
  name: string
  /** 用户头像 */
  avatar?: string
  /** 角色 */
  role: 'owner' | 'admin' | 'member'
  /** 加入时间 */
  joinTime: number
}
