/**
 * 优惠券相关API接口
 */

import { http } from '@/utils/http'
import type {
  ICouponListResponse,
  IAvailableCouponsResponse,
  ICouponClaimResponse,
  ICouponValidateResponse,
  ICouponCenterResponse,
} from './coupon.typings'

/**
 * 获取我的优惠券列表
 */
export const getMyCoupons = (params: {
  status?: number // 1:未使用 2:已使用 3:已过期
  merchant_id?: number
  page?: number
  page_size?: number
}) => {
  return http.get<ICouponListResponse>('/api/v1/user/takeout/coupons/my-list', params)
}

/**
 * 获取订单可用优惠券
 */
export const getAvailableCouponsForOrder = (params: {
  merchant_id: number
  total_amount: number
  food_ids?: string
}) => {
  return http.get<IAvailableCouponsResponse>(
    '/api/v1/user/takeout/coupons/available-for-order',
    params,
  )
}

/**
 * 领取优惠券
 */
export const claimCoupon = (data: { coupon_id: number }) => {
  return http.post<ICouponClaimResponse>('/api/v1/user/takeout/coupons/claim', data)
}

/**
 * 验证优惠券是否可用
 */
export const validateCoupon = (data: {
  coupon_id: number
  merchant_id: number
  total_amount: number
  food_ids?: string
}) => {
  return http.post<ICouponValidateResponse>('/api/v1/user/takeout/coupons/validate', data)
}

/**
 * 获取优惠券中心列表
 */
export const getCouponCenter = (params: {
  category?: string
  page?: number
  page_size?: number
}) => {
  return http.get<ICouponCenterResponse>('/api/v1/user/takeout/coupons/center', params)
}

/**
 * 批量领取优惠券
 */
export const batchClaimCoupons = (data: { coupon_ids: number[] }) => {
  return http.post<ICouponClaimResponse>('/api/v1/user/takeout/coupons/batch-claim', data)
}

/**
 * 获取优惠券详情
 */
export const getCouponDetail = (couponId: number) => {
  return http.get<any>(`/api/v1/user/takeout/coupons/${couponId}/detail`)
}

/**
 * 获取商家优惠券列表
 */
export const getMerchantCoupons = (
  merchantId: number,
  params?: {
    page?: number
    page_size?: number
  },
) => {
  return http.get<ICouponCenterResponse>(
    `/api/v1/user/takeout/merchants/${merchantId}/coupons`,
    params,
  )
}

/**
 * 计算优惠券折扣金额
 */
export const calculateCouponDiscount = (data: {
  coupon_id: number
  merchant_id: number
  total_amount: number
  food_ids?: string
}) => {
  return http.post<{ discount_amount: number }>(
    '/api/v1/user/takeout/coupons/calculate-discount',
    data,
  )
}

/**
 * 获取即将过期的优惠券
 */
export const getExpiringSoonCoupons = (params?: {
  days?: number // 多少天内过期，默认7天
  page?: number
  page_size?: number
}) => {
  return http.get<ICouponListResponse>('/api/v1/user/takeout/coupons/expiring-soon', params)
}

/**
 * 标记优惠券为已读
 */
export const markCouponAsRead = (couponId: number) => {
  return http.post<void>(`/api/v1/user/takeout/coupons/${couponId}/mark-read`)
}

/**
 * 获取优惠券使用记录
 */
export const getCouponUsageHistory = (params?: {
  page?: number
  page_size?: number
  start_date?: string
  end_date?: string
}) => {
  return http.get<ICouponListResponse>('/api/v1/user/takeout/coupons/usage-history', params)
}
