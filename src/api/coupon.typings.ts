/**
 * 优惠券相关类型定义
 */

// 优惠券类型枚举
export enum CouponType {
  DISCOUNT = 1, // 满减券
  PERCENTAGE = 2, // 折扣券
  FREE_DELIVERY = 3, // 免配送费券
  GIFT = 4, // 赠品券
  CASHBACK = 5, // 返现券
}

// 优惠券状态枚举
export enum CouponStatus {
  UNUSED = 1, // 未使用
  USED = 2, // 已使用
  EXPIRED = 3, // 已过期
  INVALID = 4, // 已失效
}

// 优惠券适用范围枚举
export enum CouponScope {
  ALL = 1, // 全平台
  MERCHANT = 2, // 指定商家
  CATEGORY = 3, // 指定分类
  PRODUCT = 4, // 指定商品
}

// 基础优惠券信息
export interface ICoupon {
  id: number
  name: string
  description: string
  type: CouponType
  amount: number // 优惠金额或折扣率
  min_order_amount: number // 最低订单金额
  max_discount_amount?: number // 最大优惠金额（折扣券用）
  scope: CouponScope // 适用范围
  merchant_id?: number // 商家ID（商家券）
  merchant_name?: string // 商家名称
  merchant_logo?: string // 商家Logo
  category_ids?: number[] // 分类ID列表
  product_ids?: number[] // 商品ID列表
  start_time: string // 开始时间
  end_time: string // 结束时间
  total_quantity: number // 总发放数量
  claimed_quantity: number // 已领取数量
  per_user_limit: number // 每人限领数量
  usage_limit: number // 使用次数限制
  is_stackable: boolean // 是否可叠加使用
  is_active: boolean // 是否激活
  created_at: string
  updated_at: string
}

// 用户优惠券
export interface IUserCoupon {
  id: number
  user_id: number
  coupon_id: number
  coupon: ICoupon
  status: CouponStatus
  status_text?: string // 状态文本
  claimed_at: string // 领取时间
  used_at?: string // 使用时间
  expire_time: string // 过期时间
  order_id?: number // 使用的订单ID
  can_use: boolean // 当前是否可用
  reason?: string // 不可用原因
  discount_amount?: number // 实际优惠金额
  days_to_expire?: number // 距离过期天数
  is_new?: boolean // 是否为新领取的券
  is_read?: boolean // 是否已读
}

// 优惠券列表响应
export interface ICouponListResponse {
  list: IUserCoupon[]
  total: number
  page?: number
  page_size?: number
  has_more?: boolean
}

// 可用优惠券响应
export interface IAvailableCouponsResponse {
  available_coupons: IUserCoupon[]
  unavailable_coupons: IUserCoupon[]
  best_coupon?: IUserCoupon // 最优优惠券推荐
  total_discount: number // 总优惠金额
}

// 优惠券领取响应 - 匹配后端实际返回的数据结构
export interface ICouponClaimResponse {
  id: number // 用户优惠券ID
  user_id: number // 用户ID
  coupon_id: number // 优惠券模板ID
  coupon: ICoupon // 优惠券详情
  status: number // 状态
  status_text: string // 状态文本
  used_time: string // 使用时间
  order_id: number // 订单ID
  created_at: string // 创建时间
}

// 优惠券验证响应
export interface ICouponValidateResponse {
  valid: boolean
  reason?: string
  discount_amount: number
  final_amount: number // 使用优惠券后的最终金额
}

// 优惠券中心项目
export interface ICouponCenterItem extends ICoupon {
  can_claim: boolean // 当前用户是否可以领取
  claim_status_text: string // 领取状态描述
}

// 优惠券中心响应
export interface ICouponCenterResponse {
  total: number
  list: ICouponCenterItem[]
}

// 优惠券分类
export interface ICouponCategory {
  id: number
  name: string
  icon?: string
  sort_order: number
  coupon_count: number
}

// 优惠券横幅
export interface ICouponBanner {
  id: number
  title: string
  image: string
  link_type: string // 链接类型：coupon, merchant, category
  link_value: string // 链接值
  sort_order: number
}

// 优惠券筛选参数
export interface ICouponFilter {
  status?: CouponStatus
  type?: CouponType
  merchant_id?: number
  category_id?: number
  min_amount?: number
  max_amount?: number
  expiring_days?: number // 多少天内过期
  sort_by?: 'amount' | 'expire_time' | 'created_at'
  sort_order?: 'asc' | 'desc'
}

// 优惠券使用统计
export interface ICouponStats {
  total_coupons: number // 总优惠券数
  unused_coupons: number // 未使用数量
  used_coupons: number // 已使用数量
  expired_coupons: number // 已过期数量
  total_saved: number // 总节省金额
  expiring_soon: number // 即将过期数量
}

// 优惠券组合推荐
export interface ICouponCombination {
  coupons: IUserCoupon[]
  total_discount: number
  final_amount: number
  description: string
}

// 优惠券活动
export interface ICouponActivity {
  id: number
  title: string
  description: string
  image?: string
  start_time: string
  end_time: string
  coupons: ICoupon[]
  rules: string[]
  is_active: boolean
}
