import {
  ILoginRequest,
  LoginRes,
  IRefreshTokenRequest,
  IRefreshResponse,
  IUpdateInfo,
  IUpdatePassword,
  InfoResponse,
  IWxLoginRequest,
  IDeviceListResponse,
} from './login.typings'
import request from '@/utils/request'

/**
 * 登录表单
 */
export interface ILoginForm {
  username: string
  password: string
}

/**
 * 用户登录
 * @param loginData 登录数据
 */
export const login = (loginData: ILoginRequest) => {
  return request<LoginRes>('/api/v1/user/login', {
    method: 'POST',
    data: loginData,
  })
}

/**
 * 获取用户信息
 */
export const getUserInfo = () => {
  return request<InfoResponse>('/api/v1/user/secured/info', {
    method: 'GET',
  })
}

/**
 * 退出登录
 */
export const logout = () => {
  return request<void>('/api/v1/user/logout', {
    method: 'POST',
  })
}

/**
 * 修改用户信息
 */
export const updateInfo = (data: IUpdateInfo) => {
  return request('/api/v1/user/info', {
    method: 'PUT',
    data,
  })
}

/**
 * 修改用户密码
 */
export const updateUserPassword = (data: IUpdatePassword) => {
  return request('/api/v1/user/password', {
    method: 'PUT',
    data,
  })
}

/**
 * 获取微信登录凭证
 * @returns Promise 包含微信登录凭证(code)
 */
export const getWxCode = () => {
  return new Promise<UniApp.LoginRes>((resolve, reject) => {
    uni.login({
      provider: 'weixin',
      success: (res) => resolve(res),
      fail: (err) => reject(new Error(err)),
    })
  })
}

/**
 * 微信登录参数
 */

/**
 * 微信登录
 * @param data 微信登录参数，包含code和设备信息
 * @returns Promise 包含登录结果
 */
export const wxLogin = (data: IWxLoginRequest) => {
  return request<LoginRes>('/api/v1/user/wx-login', {
    method: 'POST',
    data,
  })
}

/**
 * 获取设备列表
 * @returns Promise 包含设备列表
 */
export const getDeviceList = () => {
  return request<IDeviceListResponse>('/api/v1/user/devices', {
    method: 'GET',
  })
}

/**
 * 登出指定设备
 * @param deviceId 设备ID
 * @returns Promise
 */
export const logoutDevice = (deviceId: string) => {
  return request(`/api/v1/user/devices/${deviceId}/logout`, {
    method: 'POST',
  })
}

/**
 * 登出所有其他设备
 * @returns Promise
 */
export const logoutAllOtherDevices = () => {
  return request('/api/v1/user/devices/logout-others', {
    method: 'POST',
  })
}

/**
 * 设置受信任设备
 * @param deviceId 设备ID
 * @param trusted 是否受信任
 * @returns Promise
 */
export const setTrustedDevice = (deviceId: string, trusted: boolean) => {
  return request(`/api/v1/user/devices/${deviceId}/trust`, {
    method: 'PUT',
    data: { trusted },
  })
}

/**
 * 刷新令牌
 * @param refreshToken 刷新令牌
 * @returns
 * 刷新令牌
 * 返回的data结构是直接的token信息，不再嵌token_info
 */
export const refreshToken = (params: IRefreshTokenRequest) => {
  return request<IRefreshResponse>('/api/v1/user/refresh-token', {
    method: 'POST',
    data: params,
  })
}
