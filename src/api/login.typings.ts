import { UniNumberBoxOnFocus } from '@uni-helper/uni-types/index'
import { IDeviceInfo } from '@/utils/device'

/**
 * 用户信息
 */
export type IUserInfoVo = {
  id: number
  username: string
  nickname?: string
  email?: string
  mobile?: string // 改为 mobile以匹配后端字段
  status?: number // 改为 number以匹配后端字段
  avatar?: string
  gender?: number // 改为 number以匹配后端字段
  balance?: number
  points?: number
  level?: number // 新增字段
  birthday?: string // 新增字段
  last_login_at?: string // 新增字段
  created_at?: string // 改名以匹配后端字段
  referral_code?: string // 新增字段
  token: string // 前端本地使用
  refreshToken?: string // 前端本地使用
  tokenExpiration?: number // token过期时间戳，前端本地使用
}

/**
 *  用户信息返回
 */
export type InfoResponse = {
  code: number
  data: IUserInfoVo
  message: string
}

/**
 * 令牌信息
 */
export type ITokenInfo = {
  access_token: string // 访问令牌
  refresh_token: string // 刷新令牌
  token_type: string // 令牌类型，通常是Bearer
  expires_in: number // 过期时间(秒)
}

/**
 * 登录响应
 */
export type ILoginResponse = {
  token_info: ITokenInfo // 令牌相关信息
  user: IUserInfoVo // 用户相关信息
  device_id?: string // 设备ID
  is_new_device?: boolean // 是否为新设备
  risk_level?: number // 风险等级 0-正常 1-低风险 2-中风险 3-高风险
}

/**
 *  登陆返回封装
 */

export type LoginRes = {
  code: number
  data: ILoginResponse
  message: string
}
/**
 * 登录请求参数
 */
export interface ILoginRequest {
  username: string
  password: string
  rememberLogin?: boolean // 是否记住登录状态，保持登录
  device_info?: IDeviceInfo // 设备信息，用于多端登录
}

/**
 * 刷新Token请求参数
 */
export interface IRefreshTokenRequest {
  refresh_token: string
  device_info?: IDeviceInfo // 设备信息，用于多端登录
}

/**
 * 微信登录请求参数
 */
export interface IWxLoginRequest {
  code: string
  device_info?: IDeviceInfo // 设备信息，用于多端登录
}

/**
 * 设备信息响应
 */
export interface IDeviceResponse {
  device_id: string
  device_name: string
  device_type: string
  platform: string
  browser: string
  last_login_at: string
  is_current: boolean
  is_trusted: boolean
  location?: string
  ip_address?: string
}

/**
 * 设备列表响应
 */
export interface IDeviceListResponse {
  code: number
  data: IDeviceResponse[]
  message: string
}

/**
 * 刷新Token直接响应结构
 */
export interface IDirectRefreshTokenResponse {
  access_token: string
  refresh_token: string
  expires_in: number
  token_type: string
}

/**
 *  刷新Token响应封装
 */
export interface IRefreshResponse {
  code: number
  data: IDirectRefreshTokenResponse
  message: string
}
/**
 * 上传成功的信息
 */
export type IUploadSuccessInfo = {
  fileId: number
  originalName: string
  fileName: string
  storagePath: string
  fileHash: string
  fileType: string
  fileBusinessType: string
  fileSize: number
}
/**
 * 更新用户信息
 */
export type IUpdateInfo = {
  id: number
  name: string
  sex: string
}
/**
 * 更新用户信息
 */
export type IUpdatePassword = {
  id: number
  oldPassword: string
  newPassword: string
  confirmPassword: string
}
