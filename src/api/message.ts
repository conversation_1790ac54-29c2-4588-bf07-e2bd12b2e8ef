/**
 * 消息系统相关API接口
 */

import { get, post } from '@/utils/request'
import type {
  IMessageCategoryResponse,
  IMessageListParams,
  IMessageListResponse,
  ISystemNotificationParams,
  ISystemNotificationResponse,
  IOrderNotificationParams,
  IOrderNotificationResponse,
  IServiceMessageParams,
  IServiceMessageResponse,
  ISessionListParams,
  ISessionListResponse,
  IUnreadCountResponse,
  IMarkReadParams,
  IMarkReadResponse,
  IDeleteMessageParams,
  IDeleteMessageResponse,
  IToggleTopParams,
  IToggleTopResponse,
  ISearchMessageParams,
  ISearchMessageResponse,
} from './message.typings'

// API基础路径
const BASE_URL = '/api/v1/chat'

/**
 * 获取消息分类列表
 * @returns 消息分类列表
 */
export const getMessageCategories = (): Promise<IMessageCategoryResponse> => {
  return get(`${BASE_URL}/message-categories`)
}

/**
 * 获取消息列表
 * @param params 查询参数
 * @returns 消息列表
 */
export const getMessageList = (params: IMessageListParams): Promise<IMessageListResponse> => {
  return get(`${BASE_URL}/messages`, params)
}

/**
 * 获取系统通知列表
 * @param params 查询参数
 * @returns 系统通知列表
 */
export const getSystemNotifications = (
  params: ISystemNotificationParams,
): Promise<ISystemNotificationResponse> => {
  return get(`${BASE_URL}/messages`, { ...params, type: 'system' })
}

/**
 * 获取订单通知列表
 * @param params 查询参数
 * @returns 订单通知列表
 */
export const getOrderNotifications = (
  params: IOrderNotificationParams,
): Promise<IOrderNotificationResponse> => {
  return get(`${BASE_URL}/messages`, { ...params, type: 'order' })
}

/**
 * 获取聊天会话列表
 * @param params 查询参数
 * @returns 聊天会话列表
 */
export const getChatSessions = (params: ISessionListParams): Promise<ISessionListResponse> => {
  const finalParams = { ...params, category: 'chat' }
  console.log('getChatSessions API 调用，URL:', `${BASE_URL}/sessions`, '参数:', finalParams)
  return get(`${BASE_URL}/sessions`, finalParams)
}

/**
 * 获取订单会话列表
 * @param params 查询参数
 * @returns 订单会话列表
 */
export const getOrderSessions = (params: ISessionListParams): Promise<ISessionListResponse> => {
  return get(`${BASE_URL}/sessions`, { ...params, category: 'order' })
}

/**
 * 获取客服会话列表
 * @param params 查询参数
 * @returns 客服会话列表
 */
export const getServiceSessions = (
  params: IServiceMessageParams,
): Promise<IServiceMessageResponse> => {
  return get(`${BASE_URL}/sessions`, { ...params, category: 'service' })
}

/**
 * 获取客服消息列表（保持向后兼容）
 * @param params 查询参数
 * @returns 客服消息列表
 */
export const getServiceMessages = (
  params: IServiceMessageParams,
): Promise<IServiceMessageResponse> => {
  return getServiceSessions(params)
}

/**
 * 获取未读消息统计
 * @returns 未读消息统计
 */
export const getUnreadCount = (): Promise<IUnreadCountResponse> => {
  return get(`${BASE_URL}/unread-count`)
}

/**
 * 标记消息为已读
 * @param params 标记参数
 * @returns 标记结果
 */
export const markMessagesAsRead = (params: IMarkReadParams): Promise<IMarkReadResponse> => {
  return post(`${BASE_URL}/mark-read`, params)
}

/**
 * 删除消息
 * @param params 删除参数
 * @returns 删除结果
 */
export const deleteMessages = (params: IDeleteMessageParams): Promise<IDeleteMessageResponse> => {
  return post(`${BASE_URL}/delete`, params)
}

/**
 * 置顶/取消置顶消息
 * @param params 置顶参数
 * @returns 置顶结果
 */
export const toggleMessageTop = (params: IToggleTopParams): Promise<IToggleTopResponse> => {
  return post(`${BASE_URL}/toggle-top`, params)
}

/**
 * 搜索消息
 * @param params 搜索参数
 * @returns 搜索结果
 */
export const searchMessages = (params: ISearchMessageParams): Promise<ISearchMessageResponse> => {
  return get(`${BASE_URL}/search`, params)
}

/**
 * 获取系统通知详情
 * @param id 通知ID
 * @returns 通知详情
 */
export const getSystemNotificationDetail = (id: string) => {
  return get(`${BASE_URL}/system/${id}`)
}

/**
 * 获取订单通知详情
 * @param id 通知ID
 * @returns 通知详情
 */
export const getOrderNotificationDetail = (id: string) => {
  return get(`${BASE_URL}/order/${id}`)
}

/**
 * 标记系统通知为已读
 * @param id 通知ID
 * @returns 标记结果
 */
export const markSystemNotificationAsRead = (id: string) => {
  return post(`${BASE_URL}/system/${id}/read`)
}

/**
 * 标记订单通知为已读
 * @param id 通知ID
 * @returns 标记结果
 */
export const markOrderNotificationAsRead = (id: string) => {
  return post(`${BASE_URL}/order/${id}/read`)
}

/**
 * 批量标记系统通知为已读
 * @param ids 通知ID列表
 * @returns 标记结果
 */
export const batchMarkSystemNotificationsAsRead = (ids: string[]) => {
  return post(`${BASE_URL}/system/batch-read`, { ids })
}

/**
 * 批量标记订单通知为已读
 * @param ids 通知ID列表
 * @returns 标记结果
 */
export const batchMarkOrderNotificationsAsRead = (ids: string[]) => {
  return post(`${BASE_URL}/order/batch-read`, { ids })
}

/**
 * 删除系统通知
 * @param ids 通知ID列表
 * @returns 删除结果
 */
export const deleteSystemNotifications = (ids: string[]) => {
  return post(`${BASE_URL}/system/delete`, { ids })
}

/**
 * 删除订单通知
 * @param ids 通知ID列表
 * @returns 删除结果
 */
export const deleteOrderNotifications = (ids: string[]) => {
  return post(`${BASE_URL}/order/delete`, { ids })
}

/**
 * 清空所有已读系统通知
 * @returns 清空结果
 */
export const clearReadSystemNotifications = () => {
  return post(`${BASE_URL}/system/clear-read`)
}

/**
 * 清空所有已读订单通知
 * @returns 清空结果
 */
export const clearReadOrderNotifications = () => {
  return post(`${BASE_URL}/order/clear-read`)
}

// 导出默认对象
export default {
  getMessageCategories,
  getMessageList,
  getSystemNotifications,
  getOrderNotifications,
  getServiceMessages,
  getUnreadCount,
  markMessagesAsRead,
  deleteMessages,
  toggleMessageTop,
  searchMessages,
  getSystemNotificationDetail,
  getOrderNotificationDetail,
  markSystemNotificationAsRead,
  markOrderNotificationAsRead,
  batchMarkSystemNotificationsAsRead,
  batchMarkOrderNotificationsAsRead,
  deleteSystemNotifications,
  deleteOrderNotifications,
  clearReadSystemNotifications,
  clearReadOrderNotifications,
}
