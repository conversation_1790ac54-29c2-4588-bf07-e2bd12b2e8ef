/**
 * 消息系统相关API类型定义
 */

// 消息分类类型
export type MessageCategoryType = 'chat' | 'system' | 'order' | 'service'

// 消息分类信息
export interface IMessageCategory {
  type: MessageCategoryType
  title: string
  icon: string
  color: string
  unreadCount: number
  path: string
}

// 消息分类列表响应
export interface IMessageCategoryResponse {
  code: number
  message: string
  data: IMessageCategory[]
}

// 消息项基础信息
export interface IMessageItem {
  id: string
  type: MessageCategoryType
  title: string
  content: string
  avatar?: string
  time: string
  isRead: boolean
  isTop: boolean
  unreadCount?: number
  extra?: {
    orderId?: string
    orderNo?: string
    amount?: number
    status?: string
    actionType?: string
    actionUrl?: string
    [key: string]: any
  }
}

// 消息列表查询参数
export interface IMessageListParams {
  page?: number
  pageSize?: number
  type?: MessageCategoryType
  keyword?: string
  isRead?: boolean
}

// 消息列表响应
export interface IMessageListResponse {
  code: number
  message: string
  data: {
    list: IMessageItem[]
    total: number
    page: number
    pageSize: number
    hasMore: boolean
  }
}

// 系统通知类型
export enum SystemNotificationType {
  SYSTEM_MAINTENANCE = 'system_maintenance', // 系统维护
  VERSION_UPDATE = 'version_update', // 版本更新
  SECURITY_NOTICE = 'security_notice', // 安全公告
  ACTIVITY_NOTICE = 'activity_notice', // 活动通知
  POLICY_UPDATE = 'policy_update', // 政策更新
  FEATURE_ANNOUNCEMENT = 'feature_announcement', // 功能公告
}

// 系统通知信息
export interface ISystemNotification {
  id: string
  type: SystemNotificationType
  title: string
  content: string
  summary?: string
  imageUrl?: string
  actionType?: string
  actionUrl?: string
  priority: number // 优先级：1-低，2-中，3-高
  isRead: boolean
  isTop: boolean
  publishTime: string
  expireTime?: string
  createdAt: string
  updatedAt: string
}

// 系统通知列表查询参数
export interface ISystemNotificationParams {
  page?: number
  pageSize?: number
  type?: SystemNotificationType
  isRead?: boolean
  priority?: number
}

// 系统通知列表响应
export interface ISystemNotificationResponse {
  code: number
  message: string
  data: {
    list: ISystemNotification[]
    total: number
    page: number
    pageSize: number
    hasMore: boolean
  }
}

// 订单通知类型
export enum OrderNotificationType {
  PAYMENT_SUCCESS = 'payment_success', // 支付成功
  PAYMENT_FAILED = 'payment_failed', // 支付失败
  ORDER_CONFIRMED = 'order_confirmed', // 订单确认
  ORDER_SHIPPED = 'order_shipped', // 订单发货
  ORDER_DELIVERED = 'order_delivered', // 订单送达
  ORDER_COMPLETED = 'order_completed', // 订单完成
  ORDER_CANCELLED = 'order_cancelled', // 订单取消
  REFUND_APPLIED = 'refund_applied', // 申请退款
  REFUND_APPROVED = 'refund_approved', // 退款通过
  REFUND_REJECTED = 'refund_rejected', // 退款拒绝
  REFUND_SUCCESS = 'refund_success', // 退款成功
  REFUND_FAILED = 'refund_failed', // 退款失败
}

// 订单通知信息
export interface IOrderNotification {
  id: string
  type: OrderNotificationType
  title: string
  content: string
  orderId: number
  orderNo: string
  amount?: number
  refundAmount?: number
  goodsInfo?: {
    goodsId: string
    goodsName: string
    goodsImage: string
    quantity: number
  }[]
  actionType?: string
  actionUrl?: string
  isRead: boolean
  isTop: boolean
  createdAt: string
  updatedAt: string
  extra?: {
    merchantName?: string
    deliveryInfo?: {
      company: string
      trackingNo: string
    }
    [key: string]: any
  }
}

// 订单通知列表查询参数
export interface IOrderNotificationParams {
  page?: number
  pageSize?: number
  type?: OrderNotificationType
  orderId?: number
  orderNo?: string
  isRead?: boolean
  startDate?: string
  endDate?: string
}

// 订单通知列表响应
export interface IOrderNotificationResponse {
  code: number
  message: string
  data: {
    list: IOrderNotification[]
    total: number
    page: number
    pageSize: number
    hasMore: boolean
  }
}

// 客服消息信息
export interface IServiceMessage {
  id: string
  sessionId: string
  title: string
  content: string
  serviceInfo: {
    serviceId: string
    serviceName: string
    serviceAvatar: string
    isOnline: boolean
  }
  lastMessage?: {
    content: string
    time: string
    type: string
  }
  unreadCount: number
  isRead: boolean
  isTop: boolean
  createdAt: string
  updatedAt: string
}

// 客服消息列表查询参数
export interface IServiceMessageParams {
  page?: number
  pageSize?: number
  serviceId?: string
  isRead?: boolean
  keyword?: string
}

// 客服消息列表响应
export interface IServiceMessageResponse {
  code: number
  message: string
  data: {
    category: string
    list: IServiceMessage[]
    total: number
    page: number
    page_count: number
    page_size: number
  }
}

// 会话信息（根据后端返回格式）
export interface ISessionItem {
  id: number
  type: string
  creator_id: number
  creator_type: string
  receiver_id: number
  receiver_type: string
  last_message_id: number
  unread_count: number
  status: number
  created_at: string
  updated_at: string
  last_message: {
    id: number
    session_id: number
    sender_id: number
    sender_type: string
    content: string
    type: string
    resource_id: string
    status: number
    created_at: string
    sender_name: string
    sender_avatar: string
  }
  target_name: string
  target_avatar: string
}

// 会话列表查询参数
export interface ISessionListParams {
  page?: number
  pageSize?: number
  category?: string
  keyword?: string
}

// 会话列表响应
export interface ISessionListResponse {
  code: number
  message: string
  data: {
    category: string
    list: ISessionItem[]
    page: number
    page_count: number
    page_size: number
    total: number
  }
}

// 未读消息统计
export interface IUnreadCount {
  chat: number
  system: number
  order: number
  service: number
  total: number
}

// 未读消息统计响应
export interface IUnreadCountResponse {
  code: number
  message: string
  data: IUnreadCount
}

// 标记已读参数
export interface IMarkReadParams {
  type: MessageCategoryType
  messageIds?: string[]
  markAll?: boolean
}

// 标记已读响应
export interface IMarkReadResponse {
  code: number
  message: string
  data: {
    readCount: number
  }
}

// 删除消息参数
export interface IDeleteMessageParams {
  messageIds: string[]
  type: MessageCategoryType
}

// 删除消息响应
export interface IDeleteMessageResponse {
  code: number
  message: string
  data: {
    deletedCount: number
  }
}

// 置顶/取消置顶参数
export interface IToggleTopParams {
  messageId: string
  type: MessageCategoryType
  isTop: boolean
}

// 置顶/取消置顶响应
export interface IToggleTopResponse {
  code: number
  message: string
  data: {
    messageId: string
    isTop: boolean
  }
}

// 搜索消息参数
export interface ISearchMessageParams {
  keyword: string
  type?: MessageCategoryType
  page?: number
  pageSize?: number
  startDate?: string
  endDate?: string
}

// 搜索消息响应
export interface ISearchMessageResponse {
  code: number
  message: string
  data: {
    list: IMessageItem[]
    total: number
    page: number
    pageSize: number
    hasMore: boolean
    keyword: string
  }
}
