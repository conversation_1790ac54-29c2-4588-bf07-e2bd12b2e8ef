/**
 * 订单相关API接口
 * 提供订单创建、查询、管理等功能的API调用
 */

import { http } from '@/utils/http'
import type { IResData } from '@/utils/http'
import type {
  IOrder,
  IOrderList,
  IOrderDetail,
  ICreateOrderParams,
  IOrderSearchParams,
} from './order.typings'

/**
 * 创建订单
 * @param params 创建订单参数
 * @returns 订单信息
 */
export const createOrder = (params: ICreateOrderParams) => {
  return http.post<IResData<IOrder>>('/api/v1/orders/create', params)
}

/**
 * 获取订单列表（高性能查询）
 * @param params 查询参数
 * @returns 订单列表
 */
export const getOrderList = (params?: IOrderSearchParams) => {
  return http.get<IResData<IOrderList>>('/api/v1/orders/simple/high-performance', params)
}

/**
 * 获取订单详情
 * @param orderId 订单ID
 * @returns 订单详情
 */
export const getOrderDetail = (orderId: number) => {
  return http.get<IResData<IOrderDetail>>(`/api/v1/order/detail/${orderId}`)
}

/**
 * 取消订单
 * @param orderId 订单ID
 * @param reason 取消原因
 * @returns 操作结果
 */
export const cancelOrder = (orderId: number, reason?: string) => {
  return http.post<IResData<void>>(`/api/v1/orders/${orderId}/cancel`, { reason })
}

/**
 * 确认收货
 * @param orderId 订单ID
 * @returns 操作结果
 */
export const confirmOrder = (orderId: number) => {
  return http.put<IResData<void>>(`/api/v1/order/confirm/${orderId}`)
}

/**
 * 申请退款
 * @param orderId 订单ID
 * @param reason 退款原因
 * @param amount 退款金额
 * @returns 操作结果
 */
export const applyRefund = (orderId: number, reason: string, amount?: number) => {
  return http.post<IResData<void>>(`/api/v1/order/refund/${orderId}`, { reason, amount })
}

/**
 * 删除订单
 * @param orderId 订单ID
 * @returns 操作结果
 */
export const deleteOrder = (orderId: number) => {
  return http.delete<IResData<void>>(`/api/v1/order/delete/${orderId}`)
}

/**
 * 再次购买
 * @param orderId 订单ID
 * @returns 操作结果
 */
export const reorder = (orderId: number) => {
  return http.post<IResData<void>>(`/api/v1/order/reorder/${orderId}`)
}

/**
 * 获取订单状态统计
 * @returns 状态统计
 */
export const getOrderStatusCount = () => {
  return http.get<IResData<Record<string, number>>>('/api/v1/orders/simple/status-count')
}
