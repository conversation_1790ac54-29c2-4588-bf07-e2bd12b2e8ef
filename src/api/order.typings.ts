/**
 * 订单相关类型定义
 * 定义订单、订单项等相关的数据结构
 */

/**
 * 订单状态枚举
 */
export enum OrderStatus {
  PENDING_PAYMENT = 1, // 待付款
  PENDING_DELIVERY = 2, // 待发货
  PENDING_RECEIPT = 3, // 待收货
  COMPLETED = 4, // 已完成
  CANCELLED = 5, // 已取消
  REFUNDING = 6, // 退款中
  REFUNDED = 7, // 已退款
}

/**
 * 订单基本信息
 */
export interface IOrder {
  id: number
  orderNo: string
  userId: number
  merchantId: number
  merchantName: string
  status: OrderStatus
  statusText: string
  totalAmount: number
  payAmount: number
  discountAmount: number
  deliveryFee: number
  paymentMethod?: string
  paymentTime?: string
  deliveryTime?: string
  receiptTime?: string
  cancelTime?: string
  cancelReason?: string
  remark?: string
  createTime: string
  updateTime: string
}

/**
 * 订单详情信息
 */
export interface IOrderDetail extends IOrder {
  items: IOrderItem[]
  address: IOrderAddress
  logistics?: IOrderLogistics[]
}

/**
 * 订单商品项
 */
export interface IOrderItem {
  id: number
  orderId: number
  productId: number
  productName: string
  productImage: string
  productPrice: number
  specificationId?: number
  specificationName?: string
  quantity: number
  totalPrice: number
}

/**
 * 订单地址信息
 */
export interface IOrderAddress {
  id: number
  receiverName: string
  receiverPhone: string
  province: string
  city: string
  district: string
  address: string
  fullAddress: string
  isDefault: boolean
}

/**
 * 订单物流信息
 */
export interface IOrderLogistics {
  id: number
  orderId: number
  status: string
  description: string
  createTime: string
}

/**
 * 订单列表响应
 */
export interface IOrderList {
  list: IOrder[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

/**
 * 创建订单参数
 */
export interface ICreateOrderParams {
  cartItemIds?: number[] // 从购物车创建订单
  items?: ICreateOrderItem[] // 直接购买创建订单
  addressId: number
  remark?: string
  couponId?: number
}

/**
 * 创建订单商品项
 */
export interface ICreateOrderItem {
  productId: number
  specificationId?: number
  quantity: number
}

/**
 * 订单搜索参数
 * 对应后端 OrderQueryRequest 结构
 */
export interface IOrderSearchParams {
  /** 用户ID */
  user_id?: number
  /** 订单号 */
  order_no?: string
  /** 订单状态: 10:待付款, 20:已付款, 30:已发货, 40:已收货, 50:已完成, 60:已取消, 70:退款中, 80:已退款 */
  status?: number
  /** 支付状态: 0:未支付, 1:已支付, 2:支付失败 */
  pay_status?: number
  /** 订单类型 */
  order_type?: number
  /** 是否外卖订单 */
  takeout?: boolean
  /** 开始时间 */
  start_time?: string
  /** 结束时间 */
  end_time?: string
  /** 页码，默认为1 */
  page?: number
  /** 每页数量，默认为10 */
  page_size?: number

  // 保留原有参数以兼容现有代码
  /** @deprecated 使用 page_size 替代 */
  pageSize?: number
  /** @deprecated 使用 start_time 和 end_time 替代 */
  keyword?: string
  /** @deprecated 使用 start_time 替代 */
  startTime?: string
  /** @deprecated 使用 end_time 替代 */
  endTime?: string
}
