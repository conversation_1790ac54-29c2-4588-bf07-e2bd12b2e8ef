/**
 * 支付相关API接口
 */

import { request } from '@/utils/request'
import type {
  ICreatePaymentParams,
  ICreatePaymentResponse,
  IPaymentQueryParams,
  IPaymentQueryResponse,
  IRefundParams,
  IRefundResponse,
  IPaymentMethodsResponse,
  IUserBalance,
  IBalanceRecordParams,
  IBalanceRecordResponse,
  IRechargeParams,
  IRechargeResponse,
  IWithdrawParams,
  IWithdrawResponse,
  IBankCard,
  IAddBankCardParams,
  IPaymentPasswordParams,
  IVerifyPaymentPasswordParams,
  IPaymentSecuritySettings,
} from './payment.typings'

/**
 * 创建支付订单
 */
export const createPayment = (params: ICreatePaymentParams) => {
  return request<ICreatePaymentResponse>({
    url: '/api/v1/user/payments/create',
    method: 'POST',
    data: params,
  })
}

/**
 * 查询支付结果
 */
export const queryPayment = (transactionNo: string) => {
  return request<IPaymentQueryResponse>({
    url: `/api/v1/user/payments/query/${transactionNo}`,
    method: 'GET',
  })
}

/**
 * 关闭支付订单
 */
export const closePayment = (transactionNo: string) => {
  return request({
    url: `/api/v1/user/payments/close/${transactionNo}`,
    method: 'POST',
  })
}

/**
 * 创建退款申请
 */
export const createRefund = (params: IRefundParams) => {
  return request<IRefundResponse>({
    url: '/api/v1/user/refunds/create',
    method: 'POST',
    data: params,
  })
}

/**
 * 查询退款状态
 */
export const queryRefund = (refundNo: string) => {
  return request({
    url: `/api/v1/user/refunds/query/${refundNo}`,
    method: 'GET',
  })
}

/**
 * 获取支付方式列表
 */
export const getPaymentMethods = () => {
  return request<IPaymentMethodsResponse>({
    url: '/api/v1/payment/methods',
    method: 'GET',
  })
}

/**
 * 获取用户余额
 */
export const getUserBalance = () => {
  return request<IUserBalance>({
    url: '/api/v1/user/payment/balance',
    method: 'GET',
  })
}

/**
 * 获取余额变动记录
 */
export const getBalanceRecords = (params: IBalanceRecordParams) => {
  return request<IBalanceRecordResponse>({
    url: '/payment/balance/records',
    method: 'GET',
    data: params,
  })
}

/**
 * 余额充值
 */
export const rechargeBalance = (params: IRechargeParams) => {
  return request<IRechargeResponse>({
    url: '/payment/recharge',
    method: 'POST',
    data: params,
  })
}

/**
 * 余额提现
 */
export const withdrawBalance = (params: IWithdrawParams) => {
  return request<IWithdrawResponse>({
    url: '/payment/withdraw',
    method: 'POST',
    data: params,
  })
}

/**
 * 获取银行卡列表
 */
export const getBankCards = () => {
  return request<IBankCard[]>({
    url: '/api/v1/user/payment/bank-cards',
    method: 'GET',
  })
}

/**
 * 添加银行卡
 */
export const addBankCard = (params: IAddBankCardParams) => {
  return request<IBankCard>({
    url: '/payment/bank-cards',
    method: 'POST',
    data: params,
  })
}

/**
 * 删除银行卡
 */
export const deleteBankCard = (cardId: string) => {
  return request({
    url: `/payment/bank-cards/${cardId}`,
    method: 'DELETE',
  })
}

/**
 * 设置默认银行卡
 */
export const setDefaultBankCard = (cardId: string) => {
  return request({
    url: `/payment/bank-cards/${cardId}/default`,
    method: 'POST',
  })
}

/**
 * 设置支付密码
 */
export const setPaymentPassword = (params: IPaymentPasswordParams) => {
  return request({
    url: '/payment/password/set',
    method: 'POST',
    data: params,
  })
}

/**
 * 修改支付密码
 */
export const updatePaymentPassword = (params: IPaymentPasswordParams & { oldPassword: string }) => {
  return request({
    url: '/payment/password/update',
    method: 'POST',
    data: params,
  })
}

/**
 * 重置支付密码
 */
export const resetPaymentPassword = (params: IPaymentPasswordParams) => {
  return request({
    url: '/payment/password/reset',
    method: 'POST',
    data: params,
  })
}

/**
 * 验证支付密码
 */
export const verifyPaymentPassword = (params: IVerifyPaymentPasswordParams) => {
  return request<{ valid: boolean }>({
    url: '/payment/password/verify',
    method: 'POST',
    data: params,
  })
}

/**
 * 获取支付安全设置
 */
export const getPaymentSecuritySettings = () => {
  return request<IPaymentSecuritySettings>({
    url: '/payment/security/settings',
    method: 'GET',
  })
}

/**
 * 更新支付安全设置
 */
export const updatePaymentSecuritySettings = (settings: Partial<IPaymentSecuritySettings>) => {
  return request({
    url: '/payment/security/settings',
    method: 'POST',
    data: settings,
  })
}

/**
 * 发送支付验证码
 */
export const sendPaymentSmsCode = (phone: string, scene: string) => {
  return request({
    url: '/payment/sms/send',
    method: 'POST',
    data: { phone, scene },
  })
}

/**
 * 验证支付验证码
 */
export const verifyPaymentSmsCode = (phone: string, code: string, scene: string) => {
  return request<{ valid: boolean }>({
    url: '/payment/sms/verify',
    method: 'POST',
    data: { phone, code, scene },
  })
}
