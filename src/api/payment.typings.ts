/**
 * 支付相关API类型定义
 */

// 支付方式枚举
export enum PaymentMethod {
  WECHAT = 'wechat',
  ALIPAY = 'alipay',
  BALANCE = 'balance',
  CREDIT_CARD = 'credit_card',
}

// 支付状态枚举
export enum PaymentStatus {
  PENDING = 'pending',
  SUCCESS = 'success',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  REFUNDING = 'refunding',
  REFUNDED = 'refunded',
}

// 支付订单信息
export interface IPaymentOrder {
  id: string
  orderNo: string
  amount: number
  currency: string
  title: string
  description?: string
  paymentMethod: PaymentMethod
  status: PaymentStatus
  createdAt: string
  updatedAt: string
  expiredAt?: string
  paidAt?: string
  refundedAt?: string
}

// 创建支付订单参数
export interface ICreatePaymentParams {
  orderNo: string
  amount: number
  paymentMethod: PaymentMethod
  title?: string
  description?: string
  returnUrl?: string
  notifyUrl?: string
  clientIp?: string
  deviceInfo?: string
  couponId?: string | number
  bankCardId?: string | number
  password?: string
}

// 支付订单响应
export interface ICreatePaymentResponse {
  paymentId: string
  transactionNo: string
  paymentUrl?: string
  qrCode?: string
  prepayId?: string
  paymentParams?: Record<string, any>
  expiredAt: string
  status?: PaymentStatus
}

// 支付结果查询参数
export interface IPaymentQueryParams {
  paymentId?: string
  orderNo?: string
}

// 支付结果响应
export interface IPaymentQueryResponse {
  paymentId: string
  orderNo: string
  status: PaymentStatus
  amount: number
  paidAmount?: number
  paidAt?: string
  failureReason?: string
}

// 退款申请参数
export interface IRefundParams {
  transactionNo: string
  refundAmount: number
  reason: string
  refundNo?: string
  password?: string
}

// 退款响应
export interface IRefundResponse {
  refundId: string
  refundNo: string
  status: string
  refundAmount: number
  estimatedArrival?: string
}

// 支付方式配置
export interface IPaymentMethodConfig {
  method: PaymentMethod
  name: string
  icon: string
  enabled: boolean
  description?: string
  minAmount?: number
  maxAmount?: number
  feeRate?: number
}

// 支付方式列表响应
export interface IPaymentMethodsResponse {
  methods: IPaymentMethodConfig[]
}

// 用户余额信息
export interface IUserBalance {
  balance: number
  frozenBalance: number
  availableBalance: number
  currency: string
}

// 余额变动记录
export interface IBalanceRecord {
  id: string
  type: 'income' | 'expense' | 'freeze' | 'unfreeze'
  amount: number
  balance: number
  description: string
  relatedOrderNo?: string
  createdAt: string
}

// 余额记录查询参数
export interface IBalanceRecordParams {
  page?: number
  pageSize?: number
  type?: string
  startDate?: string
  endDate?: string
}

// 余额记录列表响应
export interface IBalanceRecordResponse {
  records: IBalanceRecord[]
  total: number
  page: number
  pageSize: number
}

// 充值参数
export interface IRechargeParams {
  amount: number
  paymentMethod: PaymentMethod
  returnUrl?: string
}

// 充值响应
export interface IRechargeResponse {
  rechargeId: string
  paymentId: string
  paymentUrl?: string
  qrCode?: string
  paymentParams?: Record<string, any>
}

// 提现参数
export interface IWithdrawParams {
  amount: number
  bankCard?: string
  alipayAccount?: string
  password: string
}

// 提现响应
export interface IWithdrawResponse {
  withdrawId: string
  status: string
  estimatedArrival: string
}

// 银行卡信息
export interface IBankCard {
  id: string
  cardNo: string
  bankName: string
  cardType: string
  holderName: string
  isDefault: boolean
  createdAt: string
}

// 添加银行卡参数
export interface IAddBankCardParams {
  cardNo: string
  bankName: string
  cardType: string
  holderName: string
  idCard: string
  phone: string
  isDefault?: boolean
}

// 支付密码设置参数
export interface IPaymentPasswordParams {
  password: string
  confirmPassword: string
  smsCode?: string
}

// 支付密码验证参数
export interface IVerifyPaymentPasswordParams {
  password: string
  scene: string
}

// 支付安全设置
export interface IPaymentSecuritySettings {
  hasPaymentPassword: boolean
  fingerprintEnabled: boolean
  faceIdEnabled: boolean
  smsVerificationEnabled: boolean
  dailyLimit: number
  singleLimit: number
}
