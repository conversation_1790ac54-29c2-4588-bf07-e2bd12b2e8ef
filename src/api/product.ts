/**
 * 商品相关API接口
 * 提供商品浏览、搜索、详情等功能的API调用
 */

import { http } from '@/utils/http'
import type {
  IProduct,
  IProductDetail,
  IProductList,
  IProductSearchParams,
  ICategory,
  ICategoryList,
} from './product.typings'

/**
 * 获取商品列表
 * @param params 搜索参数
 * @returns 商品列表
 */
export const getProductList = (params: IProductSearchParams) => {
  return http.get<IProductList>('/user/product/list', params)
}

/**
 * 获取商品详情
 * @param productId 商品ID
 * @returns 商品详情
 */
export const getProductDetail = (productId: number) => {
  return http.get<IProductDetail>(`/user/product/detail/${productId}`)
}

/**
 * 获取商品分类列表
 * @returns 分类列表
 */
export const getCategoryList = () => {
  return http.get<ICategoryList>('/user/category/list')
}

/**
 * 搜索商品
 * @param keyword 搜索关键词
 * @param params 其他搜索参数
 * @returns 搜索结果
 */
export const searchProducts = (keyword: string, params?: Partial<IProductSearchParams>) => {
  return http.get<IProductList>('/user/product/search', {
    keyword,
    ...params,
  })
}

/**
 * 获取热门商品
 * @param limit 数量限制
 * @returns 热门商品列表
 */
export const getHotProducts = (limit: number = 10) => {
  return http.get<IProductList>('/user/product/hot', { limit })
}

/**
 * 获取推荐商品
 * @param limit 数量限制
 * @returns 推荐商品列表
 */
export const getRecommendProducts = (limit: number = 10) => {
  return http.get<IProductList>('/user/product/recommend', { limit })
}

/**
 * 根据分类获取商品
 * @param categoryId 分类ID
 * @param params 其他参数
 * @returns 商品列表
 */
export const getProductsByCategory = (
  categoryId: number,
  params?: Partial<IProductSearchParams>,
) => {
  return http.get<IProductList>(`/user/product/category/${categoryId}`, params)
}
