/**
 * 商品相关类型定义
 * 定义商品、分类等相关的数据结构
 */

/**
 * 商品基本信息
 */
export interface IProduct {
  id: number
  name: string
  description: string
  price: number
  originalPrice?: number
  stock: number
  sales: number
  images: string[]
  thumbnail: string
  categoryId: number
  categoryName: string
  merchantId: number
  merchantName: string
  status: number
  isHot: boolean
  isRecommend: boolean
  createTime: string
  updateTime: string
}

/**
 * 商品详情信息
 */
export interface IProductDetail extends IProduct {
  detail: string
  specifications: IProductSpecification[]
  attributes: IProductAttribute[]
  reviews: IProductReview[]
  reviewCount: number
  averageRating: number
}

/**
 * 商品规格
 */
export interface IProductSpecification {
  id: number
  name: string
  price: number
  stock: number
  image?: string
  attributes: Record<string, string>
}

/**
 * 商品属性
 */
export interface IProductAttribute {
  name: string
  value: string
}

/**
 * 商品评价
 */
export interface IProductReview {
  id: number
  userId: number
  userName: string
  userAvatar: string
  rating: number
  content: string
  images: string[]
  createTime: string
}

/**
 * 商品列表响应
 */
export interface IProductList {
  list: IProduct[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

/**
 * 商品搜索参数
 */
export interface IProductSearchParams {
  page?: number
  pageSize?: number
  keyword?: string
  categoryId?: number
  merchantId?: number
  minPrice?: number
  maxPrice?: number
  sortBy?: 'price' | 'sales' | 'createTime' | 'rating'
  sortOrder?: 'asc' | 'desc'
  isHot?: boolean
  isRecommend?: boolean
}

/**
 * 商品分类
 */
export interface ICategory {
  id: number
  name: string
  icon?: string
  image?: string
  parentId: number
  level: number
  sort: number
  status: number
  children?: ICategory[]
}

/**
 * 分类列表响应
 */
export interface ICategoryList {
  list: ICategory[]
  total: number
}
