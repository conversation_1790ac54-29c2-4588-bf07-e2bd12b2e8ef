/**
 * 促销活动相关API
 */
import { request } from '@/utils/request'
import type {
  IMerchantPromotionsResponse,
  IPromotionValidationParams,
  IPromotionValidationResponse,
  IMerchantsPromotionsAndCouponsResponse,
} from './promotion.typings'

/**
 * 获取商家促销活动列表（旧版API，已废弃）
 * @param merchantId 商家ID
 * @param params 查询参数（包含food_ids和total_amount）
 * @returns 促销活动列表
 * @deprecated 请使用 getMerchantsPromotionsAndCoupons 替代
 */
export const getMerchantPromotions = (
  merchantId: number,
  params: {
    food_ids: string
    total_amount: number
  },
) => {
  return request<IMerchantPromotionsResponse>(
    `/api/v1/user/takeout/merchant/${merchantId}/promotions`,
    {
      method: 'POST',
      data: params,
    },
  )
}

/**
 * 获取多个商家的促销活动和优惠券信息（新版API）
 * @param merchantIds 商家ID数组
 * @returns 商家促销活动和优惠券信息
 */
export const getMerchantsPromotionsAndCoupons = (merchantIds: number[]) => {
  return request<IMerchantsPromotionsAndCouponsResponse>(
    `/api/v1/user/takeout/merchants/promotions-coupons`,
    {
      method: 'POST',
      data: {
        merchant_ids: merchantIds,
      },
    },
  )
}

/**
 * 验证促销活动是否适用
 * @param params 验证参数
 * @returns 验证结果
 */
export const validatePromotions = (params: IPromotionValidationParams) => {
  return request<IPromotionValidationResponse>('/api/v1/user/takeout/promotions/validate', {
    method: 'POST',
    data: params,
  })
}

/**
 * 获取单个促销活动详情
 * @param promotionId 促销活动ID
 * @returns 促销活动详情
 */
export const getPromotionDetail = (promotionId: number) => {
  return request(`/api/v1/user/takeout/promotions/${promotionId}`, {
    method: 'GET',
  })
}
