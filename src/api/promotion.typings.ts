/**
 * 促销活动相关类型定义
 */

// 促销活动类型枚举
export enum PromotionType {
  DISCOUNT = 1, // 满减
  PERCENTAGE = 2, // 折扣
  FREE_DELIVERY = 3, // 免配送费
  FULL_REDUCTION = 4, // 满减活动
}

// 促销活动状态枚举
export enum PromotionStatus {
  DRAFT = 1, // 草稿
  ACTIVE = 2, // 进行中
  PAUSED = 3, // 已暂停
  ENDED = 4, // 已结束
  CANCELLED = 5, // 已取消
}

// 促销规则中的优惠券信息
export interface IPromotionCouponRule {
  name: string // 优惠券名称
  type: number // 优惠券类型
  amount: number // 优惠金额
  min_order_amount: number // 最小订单金额
  per_user_limit: number // 每用户限制次数
  valid_days: number // 有效天数
}

// 促销规则
export interface IPromotionRules {
  coupon?: IPromotionCouponRule
  // 可以扩展其他规则类型
  [key: string]: any
}

// 促销活动信息
export interface IPromotion {
  id: number
  merchant_id: number
  name: string
  description: string
  type: PromotionType
  type_text: string
  start_time: string
  end_time: string
  status: PromotionStatus
  status_text: string
  rules: string | IPromotionRules // JSON字符串或解析后的对象
  max_usage_count: number
  usage_count: number
  created_at: string
}

// 商家促销活动响应
export interface IMerchantPromotionsResponse {
  promotion_info: string
  promotions: IPromotion[]
}

// 促销活动查询参数
export interface IPromotionQueryParams {
  merchant_id?: number
  status?: PromotionStatus
  type?: PromotionType
}

// 促销活动应用结果
export interface IPromotionApplicationResult {
  promotion: IPromotion
  applicable: boolean // 是否适用
  reason?: string // 不适用的原因
  discount_amount: number // 折扣金额
  final_amount: number // 最终金额
}

// 促销活动验证参数
export interface IPromotionValidationParams {
  merchant_id: number
  total_amount: number
  user_id?: number
  food_ids?: string
}

// 促销活动验证响应
export interface IPromotionValidationResponse {
  applicable_promotions: IPromotionApplicationResult[]
  total_discount: number
  final_amount: number
}

// 促销活动信息DTO（与后端保持一致）
export interface IPromotionInfoDTO {
  id: number
  name: string
  description: string
  type: number
  type_name: string
  rules: string
  start_time: string
  end_time: string
}

// 优惠券响应（与后端保持一致）
export interface ICouponResponse {
  id: number
  merchant_id: number
  merchant_name: string
  merchant_logo: string
  name: string
  description: string
  type: number
  type_text: string
  amount: number
  min_order_amount: number
  start_time: string
  end_time: string
  status: number
  status_text: string
}

// 用户优惠券响应（与后端保持一致）
export interface IUserCouponResponse {
  id: number
  user_id: number
  coupon_id: number
  coupon: ICouponResponse
  status: number
  status_text: string
  used_time: string
  order_id: number
  created_at: string
}

// 商家促销和优惠券信息（与后端保持一致）
export interface IMerchantPromotionCouponInfo {
  merchant_id: number
  merchant_name: string
  promotions: IPromotionInfoDTO[]
  coupons: IUserCouponResponse[]
  has_promotion: boolean
  has_coupon: boolean
}

// 多商家促销活动和优惠券响应
export interface IMerchantsPromotionsAndCouponsResponse {
  data: IMerchantPromotionCouponInfo[]
}
