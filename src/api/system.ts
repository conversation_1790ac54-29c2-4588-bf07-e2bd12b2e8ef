/**
 * 系统配置相关API
 * 包含系统基本信息、联系方式、维护模式、配置详情、外卖全局分类等API接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
import { http } from '@/utils/http'
import type {
  IApiResponse,
  ISystemInfo,
  IContactInfo,
  IMaintenanceMode,
  IConfigDetailsParams,
  IConfigResponse,
  IGlobalCategory,
} from './system.typings'

/**
 * 系统配置相关API
 * 注意：这里只使用不需要认证的接口
 */

/**
 * 获取系统基本信息
 * 包括网站名称、版本、Logo等系统基本信息
 */
export const getSystemInfo = () => {
  return http<IApiResponse<ISystemInfo>>({
    url: '/api/v1/system/info/basic',
    method: 'GET',
  })
}

/**
 * 获取联系方式信息
 * 包括邮箱、电话、地址等联系信息
 */
export const getContactInfo = () => {
  return http<IApiResponse<IContactInfo>>({
    url: '/api/v1/system/info/contact',
    method: 'GET',
  })
}

/**
 * 获取维护模式状态
 * 检查系统是否处于维护模式
 */
export const getMaintenanceMode = () => {
  return http<IApiResponse<IMaintenanceMode>>({
    url: '/api/v1/system/maintenance',
    method: 'GET',
  })
}

/**
 * 获取公开配置信息
 * @param key 配置键名
 */
export const getPublicConfig = (key: string) => {
  return http<IApiResponse<any>>({
    url: `/api/v1/system/info/${key}`,
    method: 'GET',
  })
}

/**
 * 获取配置详细信息
 * @param params 查询参数
 */
export const getConfigDetails = (params: IConfigDetailsParams) => {
  return http<IApiResponse<IConfigResponse>>({
    url: '/api/v1/system/configs/details',
    method: 'GET',
    data: params,
  })
}

/**
 * 获取系统公告列表
 * 获取当前有效的系统公告
 */
export const getActiveNotices = () => {
  return http<IApiResponse<any[]>>({
    url: '/api/v1/system/notices',
    method: 'GET',
  })
}

/**
 * 获取指定公告详情
 * @param id 公告ID
 */
export const getNotice = (id: number) => {
  return http<IApiResponse<any>>({
    url: `/api/v1/system/notices/${id}`,
    method: 'GET',
  })
}

/**
 * 获取地址选择器选项
 * 用于地址选择组件
 */
export const getAddressOptions = () => {
  return http<IApiResponse<any>>({
    url: '/api/v1/system/addresses/options',
    method: 'GET',
  })
}

/**
 * AI聊天接口
 * @param data 聊天数据
 */
export const chatCompletion = (data: any) => {
  return http<IApiResponse<any>>({
    url: '/api/v1/system/chat/completion',
    method: 'POST',
    data,
  })
}

/**
 * UI生成器接口
 * @param data 生成参数
 */
export const generateUIConfig = (data: any) => {
  return http<IApiResponse<any>>({
    url: '/api/v1/system/ui/generate',
    method: 'POST',
    data,
  })
}

/**
 * 自定义UI生成器接口
 * @param data 生成参数
 */
export const generateCustomUIConfig = (data: any) => {
  return http<IApiResponse<any>>({
    url: '/api/v1/system/ui/generate/custom',
    method: 'POST',
    data,
  })
}

/**
 * 获取外卖全局分类列表
 * 获取外卖系统的全局分类信息，包含层级结构
 */
export const getGlobalCategories = () => {
  return http<IApiResponse<IGlobalCategory[]>>({
    url: '/api/v1/merchant/merchant-categories',
    method: 'GET',
  })
}
