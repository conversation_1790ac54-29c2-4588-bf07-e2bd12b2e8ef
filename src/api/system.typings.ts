/**
 * 系统配置相关类型定义
 *
 * <AUTHOR>
 * @email <EMAIL>
 */

// ==================== 基础响应类型 ====================

/**
 * API响应基础结构
 */
export interface IApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp?: number
}

/**
 * 分页响应结构
 */
export interface IPaginationResponse<T = any> {
  list: T[]
  total: number
  page: number
  pageSize: number
}

// ==================== 系统信息相关 ====================

/**
 * 系统基本信息
 */
export interface ISystemInfo {
  /** 网站名称 */
  siteName: string
  /** 网站版本 */
  siteVersion: string
  /** API版本 */
  apiVersion: string
  /** 网站Logo */
  siteLogo: string
  /** 网站图标 */
  siteFavicon: string
  /** 版权信息 */
  copyright: string
  /** 配置版本号 */
  configVersion: string
  /** 联系邮箱 */
  email: string
  /** 联系电话 */
  phone: string
  /** 联系地址 */
  address: string
  /** 地区 */
  region: string
  /** 最后更新时间 */
  updatedAt: string
}

/**
 * 联系方式信息
 */
export interface IContactInfo {
  /** 联系邮箱 */
  email: string
  /** 联系电话 */
  phone: string
  /** 联系地址 */
  address: string
  /** 工作时间 */
  workTime: string
  /** QQ客服 */
  qq: string
  /** 微信客服 */
  wechat: string
  /** 公司名称 */
  companyName: string
  /** 经度 */
  longitude: string
  /** 纬度 */
  latitude: string
}

/**
 * 维护模式信息
 */
export interface IMaintenanceMode {
  /** 是否开启维护模式 */
  enabled: boolean
  /** 维护消息 */
  message: string
  /** 预计恢复时间 */
  estimatedRecoveryTime: string
}

// ==================== 配置相关 ====================

/**
 * 系统配置项
 */
export interface ISystemConfig {
  /** 配置ID */
  id: number
  /** 配置键名 */
  configKey: string
  /** 配置值 */
  configValue: string
  /** 配置类型 */
  configType: string
  /** 配置分类 */
  category: string
  /** 配置描述 */
  description: string
  /** 版本号 */
  version: number
  /** 状态：1-启用，0-禁用 */
  status: number
  /** 是否系统级配置：1-是，0-否 */
  isSystem: number
  /** 创建时间 */
  createdAt: string
  /** 更新时间 */
  updatedAt: string
}

/**
 * 配置详情响应
 */
export interface IConfigResponse {
  /** 配置列表 */
  list: ISystemConfig[]
  /** 总记录数 */
  total: number
  /** 当前页码 */
  page: number
  /** 每页数量 */
  pageSize: number
}

/**
 * 配置详情查询参数
 */
export interface IConfigDetailsParams {
  /** 搜索关键词 */
  keyword?: string
  /** 状态筛选：1-启用，0-禁用 */
  status?: number
  /** 系统配置筛选：1-是，0-否 */
  isSystem?: number
  /** 配置分类筛选 */
  category?: string
  /** 当前页码 */
  page?: number
  /** 每页数量 */
  page_size?: number
}

// ==================== 轮播图相关 ====================

/**
 * 轮播图原始数据结构（后端返回）
 */
export interface IBannerRawInfo {
  /** 轮播图ID */
  id: number
  /** 图片URL */
  image: string
  /** 链接URL */
  link: string
  /** 标题 */
  title: string
  /** 排序 */
  sort_order: number
  /** 状态 */
  status: number
}

/**
 * 轮播图信息（前端使用）
 */
export interface IBannerInfo {
  /** 轮播图ID */
  id: number
  /** 图片URL */
  imageUrl: string
  /** 链接URL */
  linkUrl: string
  /** 标题 */
  title: string
  /** 排序 */
  sort: number
  /** 状态 */
  status: number
}

// ==================== 功能导航相关 ====================

/**
 * 功能导航原始数据结构（后端返回）
 */
export interface IAppMenuRawInfo {
  /** 导航ID */
  id: number
  /** 标题 */
  title: string
  /** 类型 */
  type: string
  /** 图标类型 */
  iconType: string
  /** 图标名称 */
  icon: string
  /** 图标URL */
  iconUrl: string
  /** 动作/链接 */
  action: string
  /** 描述 */
  description: string
  /** 排序 */
  sort_order: number
  /** 状态 */
  status: number
}

/**
 * 功能导航信息（前端使用）
 */
export interface IAppMenuInfo {
  /** 导航ID */
  id: number
  /** 标题 */
  title: string
  /** 类型 */
  type: string
  /** 图标类型 */
  iconType: string
  /** 图标名称 */
  icon: string
  /** 图标URL */
  iconUrl: string
  /** 动作/链接 */
  action: string
  /** 描述 */
  description: string
  /** 排序 */
  sort: number
  /** 状态 */
  status: number
}

// ==================== 公告相关 ====================

/**
 * 系统公告
 */
export interface ISystemNotice {
  /** 公告ID */
  id: number
  /** 公告标题 */
  title: string
  /** 公告内容 */
  content: string
  /** 公告类型 */
  type: string
  /** 优先级 */
  priority: number
  /** 状态：1-启用，0-禁用 */
  status: number
  /** 开始时间 */
  startTime: string
  /** 结束时间 */
  endTime: string
  /** 创建时间 */
  createdAt: string
  /** 更新时间 */
  updatedAt: string
}

// ==================== 地址相关 ====================

/**
 * 地址选项
 */
export interface IAddressOption {
  /** 地址代码 */
  code: string
  /** 地址名称 */
  name: string
  /** 父级代码 */
  parentCode?: string
  /** 级别 */
  level: number
  /** 子级选项 */
  children?: IAddressOption[]
}

// ==================== AI聊天相关 ====================

/**
 * AI聊天请求
 */
export interface IChatRequest {
  /** 消息内容 */
  message: string
  /** 会话ID */
  sessionId?: string
  /** 模型名称 */
  model?: string
  /** 最大令牌数 */
  maxTokens?: number
  /** 温度参数 */
  temperature?: number
}

/**
 * AI聊天响应
 */
export interface IChatResponse {
  /** 回复内容 */
  content: string
  /** 会话ID */
  sessionId: string
  /** 使用的令牌数 */
  tokensUsed: number
  /** 模型名称 */
  model: string
}

// ==================== 外卖全局分类相关 ====================

/**
 * 外卖全局分类信息
 */
export interface IGlobalCategory {
  /** 分类ID */
  id: number
  /** 分类名称 */
  name: string
  /** 分类图标 */
  icon: string
  /** 分类级别 */
  level: number
  /** 父级分类ID */
  parentId?: number
  /** 排序值 */
  sortOrder: number
  /** 是否激活 */
  isActive: boolean
  /** 子分类列表 */
  children?: IGlobalCategory[]
}

// ==================== UI生成器相关 ====================

/**
 * UI生成请求
 */
export interface IUIGenerateRequest {
  /** 组件类型 */
  componentType: string
  /** 配置参数 */
  config: Record<string, any>
  /** 样式配置 */
  style?: Record<string, any>
}

/**
 * UI生成响应
 */
export interface IUIGenerateResponse {
  /** 生成的配置 */
  config: Record<string, any>
  /** 预览URL */
  previewUrl?: string
  /** 生成时间 */
  generatedAt: string
}
