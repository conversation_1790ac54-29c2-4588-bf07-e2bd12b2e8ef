import { http } from '@/utils/http'
import type {
  IUser,
  ILoginParams,
  IPhoneLoginParams,
  IWxLoginParams,
  IRegisterParams,
  IUserProfile,
  IChangePasswordParams,
  IResetPasswordParams,
  ISendCodeParams,
  IBindPhoneParams,
  ILoginResponse,
  IUserStats,
  IUserSettings,
  IFavoriteList,
  IFavoriteItem,
  IFavoriteFolder,
  IFavoriteFolderList,
  IAddFavoriteRequest,
  IUpdateFavoriteRequest,
  IFavoriteQueryParams,
  IFavoriteStatus,
  IFavoriteStatistics,
  ICreateFolderRequest,
  IUpdateFolderRequest,
  IBatchDeleteRequest,
  IBatchMoveRequest,
  // 历史记录相关类型
  IHistoryItem,
  IHistoryDetail,
  IAddHistoryRequest,
  IHistoryQueryParams,
  IHistoryListResponse,
  IHistoryStatistics,
  IHistoryTypeStatistics,
  IPopularHistoryItem,
  IHistoryTypeInfo,
  IHistoryAnalytics,
  IExportHistoryRequest,
  IBatchDeleteHistoryRequest,
  IClearHistoryRequest,
  IBrowseHistoryList,
  IUserWallet,
  IWalletRecordList,
  IUserPoints,
  IPointsRecordList,
  ICouponList,
  IFeedback,
  IFeedbackList,
} from './user.typings'
import { FavoriteType, HistoryType } from './user.typings'

/**
 * 用户登录
 */
export const login = (params: ILoginParams) => {
  return http<ILoginResponse>({
    url: '/auth/login',
    method: 'POST',
    data: params,
  })
}

/**
 * 手机号登录
 */
export const phoneLogin = (params: IPhoneLoginParams) => {
  return http<ILoginResponse>({
    url: '/auth/phone-login',
    method: 'POST',
    data: params,
  })
}

/**
 * 微信登录
 */
export const wxLogin = (params: IWxLoginParams) => {
  return http<ILoginResponse>({
    url: '/auth/wx-login',
    method: 'POST',
    data: params,
  })
}

/**
 * 用户注册
 */
export const register = (params: IRegisterParams) => {
  return http<ILoginResponse>({
    url: '/auth/register',
    method: 'POST',
    data: params,
  })
}

/**
 * 退出登录
 */
export const logout = () => {
  return http({
    url: '/auth/logout',
    method: 'POST',
  })
}

/**
 * 获取用户信息
 */
export const getUserInfo = () => {
  return http<IUser>({
    url: '/api/v1/user/secured/info',
    method: 'GET',
  })
}

/**
 * 更新用户信息
 */
export const updateUserInfo = (params: Partial<IUserProfile>) => {
  return http<IUser>({
    url: '/api/v1/user/secured/info',
    method: 'PUT',
    data: params,
  })
}

/**
 * 修改密码
 */
export const changePassword = (params: IChangePasswordParams) => {
  return http({
    url: '/user/change-password',
    method: 'POST',
    data: params,
  })
}

/**
 * 重置密码
 */
export const resetPassword = (params: IResetPasswordParams) => {
  return http({
    url: '/auth/reset-password',
    method: 'POST',
    data: params,
  })
}

/**
 * 发送验证码
 */
export const sendVerificationCode = (params: ISendCodeParams) => {
  return http({
    url: '/auth/send-code',
    method: 'POST',
    data: params,
  })
}

/**
 * 绑定手机号
 */
export const bindPhone = (params: IBindPhoneParams) => {
  return http({
    url: '/user/bind-phone',
    method: 'POST',
    data: params,
  })
}

/**
 * 上传头像
 */
export const uploadAvatar = (filePath: string) => {
  return new Promise((resolve, reject) => {
    uni.uploadFile({
      url: `${import.meta.env.VITE_SERVER_BASEURL}/user/upload-avatar`,
      filePath,
      name: 'avatar',
      header: {
        Authorization: `Bearer ${uni.getStorageSync('token')}`,
      },
      success: (res) => {
        try {
          const data = JSON.parse(res.data)
          resolve(data)
        } catch (error) {
          reject(error)
        }
      },
      fail: reject,
    })
  })
}

/**
 * 获取用户统计信息
 */
export const getUserStats = () => {
  return http<IUserStats>({
    url: '/user/stats',
    method: 'GET',
  })
}

/**
 * 获取用户设置
 */
export const getUserSettings = () => {
  return http<IUserSettings>({
    url: '/user/settings',
    method: 'GET',
  })
}

/**
 * 更新用户设置
 */
export const updateUserSettings = (params: Partial<IUserSettings>) => {
  return http<IUserSettings>({
    url: '/user/settings',
    method: 'PUT',
    data: params,
  })
}

// ==================== 收藏相关接口 ====================

/**
 * 添加收藏
 */
export const addFavorite = (params: IAddFavoriteRequest) => {
  return http<IFavoriteItem>({
    url: '/api/v1/user/secured/favorites/add',
    method: 'POST',
    data: params,
  })
}

/**
 * 更新收藏
 */
export const updateFavorite = (id: number, params: IUpdateFavoriteRequest) => {
  return http<IFavoriteItem>({
    url: `/api/v1/user/secured/favorites/update/${id}`,
    method: 'POST',
    data: params,
  })
}

/**
 * 删除收藏
 */
export const deleteFavorite = (id: number) => {
  return http({
    url: `/api/v1/user/secured/favorites/delete/${id}`,
    method: 'POST',
  })
}

/**
 * 批量删除收藏
 */
export const batchDeleteFavorites = (params: IBatchDeleteRequest) => {
  return http({
    url: '/api/v1/user/secured/favorites/batch-delete',
    method: 'POST',
    data: params,
  })
}

/**
 * 获取收藏详情
 */
export const getFavoriteDetail = (id: number) => {
  return http<IFavoriteItem>({
    url: `/api/v1/user/secured/favorites/detail/${id}`,
    method: 'GET',
  })
}

/**
 * 获取收藏列表
 */
export const getFavoriteList = (params: IFavoriteQueryParams = {}) => {
  return http<IFavoriteList>({
    url: '/api/v1/user/secured/favorites/list',
    method: 'GET',
    data: params,
  })
}

/**
 * 搜索收藏
 */
export const searchFavorites = (params: IFavoriteQueryParams) => {
  return http<IFavoriteList>({
    url: '/api/v1/user/secured/favorites/search',
    method: 'GET',
    data: params,
  })
}

/**
 * 按类型获取收藏
 */
export const getFavoritesByType = (type: FavoriteType, params: IFavoriteQueryParams = {}) => {
  return http<IFavoriteList>({
    url: `/api/v1/user/secured/favorites/type/${type}`,
    method: 'GET',
    data: params,
  })
}

/**
 * 检查收藏状态
 */
export const checkFavoriteStatus = (type: FavoriteType, targetId: number) => {
  return http<IFavoriteStatus>({
    url: '/api/v1/user/secured/favorites/status',
    method: 'GET',
    query: { type, target_id: targetId },
  })
}

/**
 * 获取收藏统计
 */
export const getFavoriteStatistics = () => {
  return http<IFavoriteStatistics>({
    url: '/api/v1/user/secured/favorites/statistics',
    method: 'GET',
  })
}

/**
 * 获取收藏类型列表
 */
export const getFavoriteTypes = () => {
  return http<{ type: FavoriteType; name: string; description: string }[]>({
    url: '/api/v1/user/secured/favorites/types',
    method: 'GET',
  })
}

/**
 * 批量移动收藏
 */
export const batchMoveFavorites = (params: IBatchMoveRequest) => {
  return http({
    url: '/api/v1/user/secured/favorites/batch-move',
    method: 'POST',
    data: params,
  })
}

/**
 * 清空收藏
 */
export const clearFavorites = (type?: FavoriteType) => {
  return http({
    url: '/api/v1/user/secured/favorites/clear',
    method: 'POST',
    data: type ? { type } : {},
  })
}

// ==================== 收藏夹相关接口 ====================

/**
 * 创建收藏夹
 */
export const createFavoriteFolder = (params: ICreateFolderRequest) => {
  return http<IFavoriteFolder>({
    url: '/api/v1/user/secured/favorites/folders/create',
    method: 'POST',
    data: params,
  })
}

/**
 * 更新收藏夹
 */
export const updateFavoriteFolder = (id: number, params: IUpdateFolderRequest) => {
  return http<IFavoriteFolder>({
    url: `/api/v1/user/secured/favorites/folders/update/${id}`,
    method: 'POST',
    data: params,
  })
}

/**
 * 删除收藏夹
 */
export const deleteFavoriteFolder = (id: number) => {
  return http({
    url: `/api/v1/user/secured/favorites/folders/delete/${id}`,
    method: 'POST',
  })
}

/**
 * 获取收藏夹详情
 */
export const getFavoriteFolderDetail = (id: number) => {
  return http<IFavoriteFolder>({
    url: `/api/v1/user/secured/favorites/folders/detail/${id}`,
    method: 'GET',
  })
}

/**
 * 获取收藏夹列表
 */
export const getFavoriteFolderList = () => {
  return http<IFavoriteFolderList>({
    url: '/api/v1/user/secured/favorites/folders/list',
    method: 'GET',
  })
}

// ==================== 兼容旧版本接口 ====================

/**
 * 添加收藏（兼容旧版本）
 */
export const addToFavorites = (productId: number) => {
  return addFavorite({
    type: FavoriteType.MALL_PRODUCT,
    target_id: productId,
    target_name: '',
  })
}

/**
 * 取消收藏（兼容旧版本）
 */
export const removeFromFavorites = (productId: number) => {
  // 需要先查询收藏ID，然后删除
  return checkFavoriteStatus(FavoriteType.MALL_PRODUCT, productId).then((status) => {
    if (status.is_favorited && status.favorite_id) {
      return deleteFavorite(status.favorite_id)
    }
    throw new Error('未找到收藏记录')
  })
}

/**
 * 批量取消收藏（兼容旧版本）
 */
export const batchRemoveFromFavorites = (productIds: number[]) => {
  // 这里需要根据productIds查询对应的收藏ID，然后批量删除
  // 简化实现，实际应该先查询再删除
  return batchDeleteFavorites({ ids: productIds })
}

// ==================== 历史记录相关接口 ====================

/**
 * 添加历史记录
 */
export const addHistory = (params: IAddHistoryRequest) => {
  return http<IHistoryItem>({
    url: '/api/v1/user/secured/history/add',
    method: 'POST',
    data: params,
  })
}

/**
 * 获取历史记录列表
 */
export const getHistoryList = (params: IHistoryQueryParams = {}) => {
  return http<IHistoryListResponse>({
    url: '/api/v1/user/secured/history/list',
    method: 'GET',
    data: params,
  })
}

/**
 * 获取历史记录详情
 */
export const getHistoryDetail = (id: number) => {
  return http<IHistoryItem>({
    url: `/api/v1/user/secured/history/detail/${id}`,
    method: 'GET',
  })
}

/**
 * 删除历史记录
 */
export const deleteHistory = (id: number) => {
  return http({
    url: `/api/v1/user/secured/history/delete/${id}`,
    method: 'POST',
  })
}

/**
 * 批量删除历史记录
 */
export const batchDeleteHistory = (params: IBatchDeleteHistoryRequest) => {
  return http({
    url: '/api/v1/user/secured/history/batch-delete',
    method: 'POST',
    data: params,
  })
}

/**
 * 按类型获取历史记录
 */
export const getHistoryByType = (type: HistoryType, params: IHistoryQueryParams = {}) => {
  return http<IHistoryListResponse>({
    url: `/api/v1/user/secured/history/type/${type}`,
    method: 'GET',
    data: params,
  })
}

/**
 * 搜索历史记录
 */
export const searchHistory = (params: IHistoryQueryParams) => {
  return http<IHistoryListResponse>({
    url: '/api/v1/user/secured/history/search',
    method: 'GET',
    data: params,
  })
}

/**
 * 获取历史记录统计
 */
export const getHistoryStatistics = () => {
  return http<IHistoryStatistics>({
    url: '/api/v1/user/secured/history/statistics',
    method: 'GET',
  })
}

/**
 * 获取历史记录类型列表
 */
export const getHistoryTypes = () => {
  return http<IHistoryTypeInfo[]>({
    url: '/api/v1/user/secured/history/types',
    method: 'GET',
  })
}

/**
 * 获取历史记录分析数据
 */
export const getHistoryAnalytics = (period: string = 'week') => {
  return http<IHistoryAnalytics>({
    url: '/api/v1/user/secured/history/analytics',
    method: 'GET',
    data: { period },
  })
}

/**
 * 清空历史记录
 */
export const clearHistory = (params: IClearHistoryRequest = {}) => {
  return http({
    url: '/api/v1/user/secured/history/clear',
    method: 'POST',
    data: params,
  })
}

/**
 * 导出历史记录
 */
export const exportHistory = (params: IExportHistoryRequest) => {
  return http({
    url: '/api/v1/user/secured/history/export',
    method: 'POST',
    data: params,
    responseType: 'blob',
  })
}

/**
 * 获取热门历史项目
 */
export const getPopularHistory = (type?: HistoryType, limit: number = 10) => {
  return http<IPopularHistoryItem[]>({
    url: '/api/v1/user/secured/history/popular',
    method: 'GET',
    data: { type, limit },
  })
}

/**
 * 获取最近历史记录
 */
export const getRecentHistory = (limit: number = 10) => {
  return http<IHistoryItem[]>({
    url: '/api/v1/user/secured/history/recent',
    method: 'GET',
    data: { limit },
  })
}

/**
 * 获取浏览历史
 */
export const getBrowseHistory = (params: { page?: number; pageSize?: number }) => {
  return http<IBrowseHistoryList>({
    url: '/user/browse-history',
    method: 'GET',
    data: params,
  })
}

/**
 * 添加浏览历史
 */
export const addBrowseHistory = (productId: number) => {
  return http({
    url: '/user/browse-history',
    method: 'POST',
    data: { productId },
  })
}

/**
 * 清除浏览历史
 */
export const clearBrowseHistory = () => {
  return http({
    url: '/user/browse-history',
    method: 'DELETE',
  })
}

/**
 * 删除指定浏览历史
 */
export const removeBrowseHistory = (ids: number[]) => {
  return http({
    url: '/user/browse-history/batch',
    method: 'DELETE',
    data: { ids },
  })
}

/**
 * 获取用户钱包信息
 */
export const getUserWallet = () => {
  return http<IUserWallet>({
    url: '/user/wallet',
    method: 'GET',
  })
}

/**
 * 获取钱包记录
 */
export const getWalletRecords = (params: {
  page?: number
  pageSize?: number
  type?: 'income' | 'expense'
}) => {
  return http<IWalletRecordList>({
    url: '/user/wallet/records',
    method: 'GET',
    data: params,
  })
}

/**
 * 获取用户积分信息
 */
export const getUserPoints = () => {
  return http<IUserPoints>({
    url: '/user/points',
    method: 'GET',
  })
}

/**
 * 获取积分记录
 */
export const getPointsRecords = (params: {
  page?: number
  pageSize?: number
  type?: 'earn' | 'use' | 'expire'
}) => {
  return http<IPointsRecordList>({
    url: '/user/points/records',
    method: 'GET',
    data: params,
  })
}

/**
 * 获取用户优惠券
 */
export const getUserCoupons = (params: {
  page?: number
  pageSize?: number
  status?: 'unused' | 'used' | 'expired'
}) => {
  return http<ICouponList>({
    url: '/user/coupons',
    method: 'GET',
    data: params,
  })
}

/**
 * 领取优惠券
 */
export const receiveCoupon = (couponId: number) => {
  return http({
    url: `/user/coupons/receive/${couponId}`,
    method: 'POST',
  })
}

/**
 * 提交反馈
 */
export const submitFeedback = (
  params: Omit<IFeedback, 'id' | 'status' | 'reply' | 'createdAt' | 'updatedAt'>,
) => {
  return http({
    url: '/user/feedback',
    method: 'POST',
    data: params,
  })
}

/**
 * 获取反馈列表
 */
export const getFeedbackList = (params: { page?: number; pageSize?: number }) => {
  return http<IFeedbackList>({
    url: '/user/feedback',
    method: 'GET',
    data: params,
  })
}

/**
 * 获取反馈详情
 */
export const getFeedbackDetail = (id: number) => {
  return http<IFeedback>({
    url: `/user/feedback/${id}`,
    method: 'GET',
  })
}

/**
 * 删除账户
 */
export const deleteAccount = (password: string) => {
  return http({
    url: '/user/delete-account',
    method: 'POST',
    data: { password },
  })
}
