// 用户基本信息
export interface IUser {
  id: number
  username: string
  nickname: string
  avatar: string
  phone: string
  email: string
  gender: number // 0: 未知, 1: 男, 2: 女
  birthday: string
  level: string
  status: number // 0: 禁用, 1: 正常
  createdAt: string
  updatedAt: string
}

// 登录参数
export interface ILoginParams {
  username: string
  password: string
  code?: string // 验证码
  uuid?: string // 验证码UUID
}

// 手机号登录参数
export interface IPhoneLoginParams {
  phone: string
  code: string // 短信验证码
}

// 微信登录参数
export interface IWxLoginParams {
  code: string
  encryptedData?: string
  iv?: string
}

// 注册参数
export interface IRegisterParams {
  username: string
  password: string
  confirmPassword: string
  phone: string
  code: string // 短信验证码
  nickname?: string
  avatar?: string
}

// 用户资料
export interface IUserProfile {
  nickname: string
  avatar: string
  gender: number
  birthday: string
  email: string
}

// 修改密码参数
export interface IChangePasswordParams {
  oldPassword: string
  newPassword: string
}

// 重置密码参数
export interface IResetPasswordParams {
  phone: string
  code: string
  newPassword: string
}

// 发送验证码参数
export interface ISendCodeParams {
  phone: string
  type: 'login' | 'register' | 'reset' | 'bind'
}

// 绑定手机号参数
export interface IBindPhoneParams {
  phone: string
  code: string
}

// 登录响应
export interface ILoginResponse {
  token: string
  userInfo: IUser
  expiresIn: number
}

// 用户统计信息
export interface IUserStats {
  balance: string
  points: number
  couponCount: number
  favoriteCount: number
  orderCount: number
}

// 用户设置
export interface IUserSettings {
  pushNotification: boolean
  orderNotification: boolean
  promotionNotification: boolean
  soundEnabled: boolean
  vibrationEnabled: boolean
  language: string
  theme: string
}

// 收藏类型枚举
export enum FavoriteType {
  TAKEOUT_FOOD = 'takeout_food',
  MALL_PRODUCT = 'mall_product',
  MERCHANT = 'merchant',
  CATEGORY = 'category',
  COMBO = 'combo',
  COUPON = 'coupon',
}

// 收藏夹
export interface IFavoriteFolder {
  id: number
  user_id: number
  name: string
  description: string
  icon: string
  color: string
  sort_order: number
  is_default: boolean
  is_public: boolean
  item_count: number
  created_at: string
  updated_at: string
}

// 收藏项
export interface IFavoriteItem {
  id: number
  user_id: number
  type: FavoriteType
  type_name: string
  target_id: number
  target_name: string
  target_image: string
  folder_id: number
  folder_name: string
  extra_data: Record<string, any>
  tags: string[]
  notes: string
  sort_order: number
  is_public: boolean
  created_at: string
  updated_at: string
}

// 兼容旧版本的收藏商品接口
export interface IFavoriteProduct {
  id: number
  productId: number
  productName: string
  productImage: string
  productPrice: number
  productOriginalPrice: number
  isAvailable: boolean
  createdAt: string
  // 扩展字段
  title?: string
  description?: string
  price?: number
  originalPrice?: number
  image?: string
  tags?: string[]
  checked?: boolean
}

// 收藏列表响应
export interface IFavoriteList {
  list: IFavoriteItem[]
  total: number
  page: number
  page_size: number
}

// 收藏夹列表响应
export interface IFavoriteFolderList {
  list: IFavoriteFolder[]
  total: number
}

// 添加收藏请求
export interface IAddFavoriteRequest {
  type: FavoriteType
  target_id: number
  target_name: string
  target_image?: string
  folder_id?: number
  extra_data?: Record<string, any>
  tags?: string[]
  notes?: string
  is_public?: boolean
}

// 更新收藏请求
export interface IUpdateFavoriteRequest {
  folder_id?: number
  tags?: string[]
  notes?: string
  sort_order?: number
  is_public?: boolean
}

// 收藏查询参数
export interface IFavoriteQueryParams {
  type?: FavoriteType
  folder_id?: number
  tags?: string
  keyword?: string
  is_public?: boolean
  start_date?: string
  end_date?: string
  sort_by?: string
  sort_order?: 'asc' | 'desc'
  page?: number
  page_size?: number
}

// 收藏状态
export interface IFavoriteStatus {
  is_favorited: boolean
  favorite_id?: number
  folder_id?: number
  created_at?: string
}

// 收藏统计
export interface IFavoriteStatistics {
  total_count: number
  today_count: number
  week_count: number
  month_count: number
  type_statistics: ITypeStatistics[]
  folder_statistics: IFolderStatistics[]
}

// 类型统计
export interface ITypeStatistics {
  type: FavoriteType
  type_name: string
  count: number
  percentage: number
}

// 收藏夹统计
export interface IFolderStatistics {
  folder_id: number
  folder_name: string
  count: number
  percentage: number
}

// 创建收藏夹请求
export interface ICreateFolderRequest {
  name: string
  description?: string
  icon?: string
  color?: string
  is_public?: boolean
}

// 更新收藏夹请求
export interface IUpdateFolderRequest {
  name?: string
  description?: string
  icon?: string
  color?: string
  sort_order?: number
  is_public?: boolean
}

// 批量删除请求
export interface IBatchDeleteRequest {
  ids: number[]
}

// 批量移动请求
export interface IBatchMoveRequest {
  ids: number[]
  folder_id: number
}

// ==================== 历史记录相关类型定义 ====================

// 历史记录类型枚举
export enum HistoryType {
  TAKEOUT_FOOD = 'takeout_food',
  MALL_PRODUCT = 'mall_product',
  MERCHANT = 'merchant',
  CATEGORY = 'category',
  SEARCH = 'search',
  PAGE = 'page',
}

// 历史记录项
export interface IHistoryItem {
  id: number
  user_id: number
  type: HistoryType
  target_id: number
  target_name: string
  target_image: string
  visit_count: number
  last_visit_at: string
  created_at: string
  updated_at: string
}

// 历史记录详情
export interface IHistoryDetail {
  id: number
  history_id: number
  user_id: number
  type: HistoryType
  target_id: number
  extra_data: Record<string, any>
  user_agent: string
  ip: string
  platform: string
  source: string
  duration: number
  created_at: string
}

// 添加历史记录请求
export interface IAddHistoryRequest {
  type: HistoryType
  target_id: number
  target_name: string
  target_image?: string
  extra_data?: Record<string, any>
  user_agent?: string
  platform?: string
  source?: string
  duration?: number
}

// 历史记录查询参数
export interface IHistoryQueryParams {
  type?: HistoryType
  keyword?: string
  start_date?: string
  end_date?: string
  page?: number
  page_size?: number
}

// 历史记录列表响应
export interface IHistoryListResponse {
  total: number
  page: number
  page_size: number
  list: IHistoryItem[]
}

// 历史记录统计
export interface IHistoryStatistics {
  total_count: number
  today_count: number
  week_count: number
  month_count: number
  type_statistics: IHistoryTypeStatistics[]
  recent_history: IHistoryItem[]
  popular_items: IPopularHistoryItem[]
}

// 历史记录类型统计
export interface IHistoryTypeStatistics {
  type: HistoryType
  type_name: string
  count: number
  percentage: number
}

// 热门历史项目
export interface IPopularHistoryItem {
  type: HistoryType
  type_name: string
  target_id: number
  target_name: string
  target_image: string
  visit_count: number
  last_visit_at: string
}

// 历史记录类型信息
export interface IHistoryTypeInfo {
  type: HistoryType
  name: string
  description: string
  count: number
}

// 历史记录趋势数据
export interface IHistoryTrendData {
  date: string
  count: number
}

// 历史记录分析
export interface IHistoryAnalytics {
  period: string
  trend_data: IHistoryTrendData[]
  type_data: IHistoryTypeStatistics[]
  peak_hours: number[]
  active_days: string[]
}

// 导出历史记录请求
export interface IExportHistoryRequest {
  type?: HistoryType
  start_date?: string
  end_date?: string
  format: 'csv' | 'excel'
}

// 批量删除历史记录请求
export interface IBatchDeleteHistoryRequest {
  ids: number[]
}

// 清空历史记录请求
export interface IClearHistoryRequest {
  type?: HistoryType
  start_date?: string
  end_date?: string
}

// 浏览历史
export interface IBrowseHistory {
  id: number
  productId: number
  productName: string
  productImage: string
  productPrice: number
  productOriginalPrice: number
  isAvailable: boolean
  browseTime: string
}

// 浏览历史列表
export interface IBrowseHistoryList {
  list: IBrowseHistory[]
  total: number
  page: number
  pageSize: number
}

// 用户钱包
export interface IUserWallet {
  balance: string
  frozenBalance: string
  totalIncome: string
  totalExpense: string
}

// 钱包记录
export interface IWalletRecord {
  id: number
  type: 'income' | 'expense'
  amount: string
  description: string
  orderId?: number
  createdAt: string
}

// 钱包记录列表
export interface IWalletRecordList {
  list: IWalletRecord[]
  total: number
  page: number
  pageSize: number
}

// 用户积分
export interface IUserPoints {
  points: number
  totalEarned: number
  totalUsed: number
  expiredPoints: number
  expiredDate: string
}

// 积分记录
export interface IPointsRecord {
  id: number
  type: 'earn' | 'use' | 'expire'
  points: number
  description: string
  orderId?: number
  createdAt: string
}

// 积分记录列表
export interface IPointsRecordList {
  list: IPointsRecord[]
  total: number
  page: number
  pageSize: number
}

// 优惠券
export interface ICoupon {
  id: number
  name: string
  type: 'discount' | 'cash' | 'shipping'
  value: number // 折扣值或减免金额
  minAmount: number // 最低使用金额
  maxDiscount?: number // 最大折扣金额（折扣券）
  startTime: string
  endTime: string
  status: 'unused' | 'used' | 'expired'
  description: string
  scope: 'all' | 'category' | 'product'
  scopeIds: number[]
}

// 优惠券列表
export interface ICouponList {
  list: ICoupon[]
  total: number
  page: number
  pageSize: number
}

// 反馈类型
export interface IFeedback {
  id?: number
  type: 'bug' | 'suggestion' | 'complaint' | 'other'
  title: string
  content: string
  images?: string[]
  contact?: string
  status?: 'pending' | 'processing' | 'resolved' | 'closed'
  reply?: string
  createdAt?: string
  updatedAt?: string
}

// 反馈列表
export interface IFeedbackList {
  list: IFeedback[]
  total: number
  page: number
  pageSize: number
}
