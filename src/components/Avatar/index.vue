<!--
 * 头像组件
 * 用于显示用户头像，支持图片头像和文字头像
 * 当图片加载失败时，自动显示文字头像
-->
<template>
  <view
    class="avatar"
    :class="[`avatar--${shape}`, `avatar--${sizeClass}`]"
    :style="avatarStyle"
    @click="handleClick"
  >
    <!-- 图片头像 -->
    <image
      v-if="src && !imageError"
      :src="src"
      class="avatar__image"
      mode="aspectFill"
      @error="handleImageError"
      @load="handleImageLoad"
    />

    <!-- 文字头像 -->
    <view v-else class="avatar__text" :style="textStyle">
      {{ displayText }}
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

/**
 * 头像组件属性定义
 */
interface Props {
  /** 头像图片地址 */
  src?: string
  /** 头像尺寸，可以是数字(px)或预设尺寸 */
  size?: number | string
  /** 头像形状 */
  shape?: 'circle' | 'square'
  /** 文字内容，当图片加载失败时显示 */
  text?: string
  /** 背景颜色 */
  bgColor?: string
  /** 文字颜色 */
  textColor?: string
}

/**
 * 事件定义
 */
interface Emits {
  (e: 'click'): void
}

const props = withDefaults(defineProps<Props>(), {
  size: 40,
  shape: 'circle',
  text: '',
  bgColor: '#f0f0f0',
  textColor: '#666',
})

const emit = defineEmits<Emits>()

// 图片加载错误状态
const imageError = ref(false)

// 预设尺寸映射
const sizeMap = {
  mini: 24,
  small: 32,
  medium: 40,
  large: 48,
  huge: 64,
}

// 计算实际尺寸
const actualSize = computed(() => {
  if (typeof props.size === 'number') {
    return props.size
  }
  return sizeMap[props.size as keyof typeof sizeMap] || 40
})

// 尺寸类名
const sizeClass = computed(() => {
  if (typeof props.size === 'string' && props.size in sizeMap) {
    return props.size
  }
  return 'custom'
})

// 头像样式
const avatarStyle = computed(() => {
  const size = `${actualSize.value}px`
  return {
    width: size,
    height: size,
    backgroundColor: props.src && !imageError.value ? 'transparent' : props.bgColor,
  }
})

// 文字样式
const textStyle = computed(() => {
  const fontSize = Math.floor(actualSize.value * 0.4)
  return {
    color: props.textColor,
    fontSize: `${fontSize}px`,
    lineHeight: `${actualSize.value}px`,
  }
})

// 显示的文字
const displayText = computed(() => {
  if (!props.text) return ''
  // 如果是中文，取第一个字符
  // 如果是英文，取第一个字母并转大写
  const firstChar = props.text.charAt(0)
  return /[\u4e00-\u9fa5]/.test(firstChar) ? firstChar : firstChar.toUpperCase()
})

/**
 * 处理图片加载错误
 */
const handleImageError = () => {
  imageError.value = true
}

/**
 * 处理图片加载成功
 */
const handleImageLoad = () => {
  imageError.value = false
}

/**
 * 处理点击事件
 */
const handleClick = () => {
  emit('click')
}

// 监听 src 变化，重置错误状态
watch(
  () => props.src,
  () => {
    imageError.value = false
  },
)
</script>

<style lang="scss" scoped>
.avatar {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  flex-shrink: 0;

  &--circle {
    border-radius: 50%;
  }

  &--square {
    border-radius: 8rpx;
  }

  &__image {
    width: 100%;
    height: 100%;
  }

  &__text {
    font-weight: 500;
    text-align: center;
    user-select: none;
  }
}
</style>
