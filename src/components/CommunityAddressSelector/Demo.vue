<!--
  社区地址选择器组件使用示例
  展示了在不同场景中如何调用社区地址选择器组件
-->
<template>
  <div class="address-selector-demo">
    <h3>社区地址选择器示例</h3>

    <!-- 基础用法 -->
    <div class="demo-section">
      <h4>基础用法</h4>
      <CommunityAddressSelector @address-selected="handleAddressSelected" />
      <div v-if="selectedAddress" class="selected-info">
        <p>已选择: {{ selectedAddress.fullPath }}</p>
        <p v-if="selectedAddress.longitude && selectedAddress.latitude">
          坐标: {{ selectedAddress.longitude }}, {{ selectedAddress.latitude }}
        </p>
      </div>
    </div>

    <!-- 在表单中使用 -->
    <div class="demo-section">
      <h4>在表单中使用</h4>
      <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
        <el-form-item label="所在社区" prop="address">
          <CommunityAddressSelector @address-selected="handleFormAddressSelected" />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="submitForm">提交</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 自定义样式 -->
    <div class="demo-section">
      <h4>自定义样式</h4>
      <CommunityAddressSelector
        :theme="{
          activeColor: '#ff6700',
          borderRadius: '8px',
          fontSize: '14px',
        }"
        placeholder="请选择您的社区地址"
        @address-selected="handleCustomAddressSelected"
      />
    </div>

    <!-- 禁用状态 -->
    <div class="demo-section">
      <h4>禁用状态</h4>
      <CommunityAddressSelector disabled placeholder="禁用状态" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import CommunityAddressSelector from './index.vue'
import type { SelectedAddressInfo } from './types'

// 基础用法
const selectedAddress = ref<SelectedAddressInfo | null>(null)

const handleAddressSelected = (addressInfo: SelectedAddressInfo) => {
  selectedAddress.value = addressInfo
  console.log('选中的地址:', addressInfo)
}

// 在表单中使用
const formRef = ref(null)
const form = reactive({
  address: null as SelectedAddressInfo | null,
})

const rules = {
  address: [{ required: true, message: '请选择社区地址', trigger: 'change' }],
}

const handleFormAddressSelected = (addressInfo: SelectedAddressInfo) => {
  form.address = addressInfo
}

const submitForm = async () => {
  if (!formRef.value) return

  try {
    // @ts-ignore
    await formRef.value.validate()
    console.log('表单提交成功，地址信息:', form.address)
    alert('表单验证通过！')
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 自定义样式
const handleCustomAddressSelected = (addressInfo: SelectedAddressInfo) => {
  console.log('自定义样式选择器选中地址:', addressInfo)
}
</script>

<style scoped>
.address-selector-demo {
  padding: 20px;
  max-width: 600px;
  margin: 0 auto;
}

.demo-section {
  margin-bottom: 30px;
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.selected-info {
  margin-top: 10px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

h3 {
  margin-bottom: 20px;
}

h4 {
  margin-bottom: 15px;
}
</style>
