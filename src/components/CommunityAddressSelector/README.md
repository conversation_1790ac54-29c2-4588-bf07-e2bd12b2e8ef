# 社区地址选择器组件

社区地址选择器组件是基于Wot Design Uni的级联选择器实现的自定义地址选择组件，专为小区、楼栋、单元的多级选择设计。该组件支持懒加载和缓存优化，适用于移动端H5和各类小程序平台。

## 特性

- **多级联动选择**：支持小区、楼栋、单元三级选择
- **懒加载支持**：可按需加载子节点，减少初始数据加载量
- **缓存优化**：实现本地缓存策略，提高多次使用性能
- **完整地址信息**：返回包含ID、名称、经纬度等完整信息
- **移动端适配**：针对H5和小程序优化的交互体验
- **自定义占位文本**：可自定义未选择时的显示文本
- **禁用状态**：支持禁用状态设置

## 使用方式

### 基础使用

```vue
<template>
  <CommunityAddressSelector @address-selected="handleAddressSelected" />
</template>

<script setup lang="ts">
import CommunityAddressSelector from '@/components/CommunityAddressSelector/index.vue'
import { SelectedAddressInfo } from '@/components/CommunityAddressSelector/types'

const handleAddressSelected = (addressInfo: SelectedAddressInfo) => {
  console.log('选中的地址信息:', addressInfo)
  // addressInfo包含:
  // - fullPath: 完整地址文本路径 (如"xx小区/xx楼/xx单元")
  // - communityId: 小区ID
  // - buildingId: 楼栋ID
  // - unitId: 单元ID
  // - longitude: 经度
  // - latitude: 纬度
  // - selectedKeys: 选中的所有节点ID数组
}
</script>
```

### 高级用法

#### 设置初始值

```vue
<template>
  <CommunityAddressSelector :initial-value="[1, 11]" @address-selected="handleAddressSelected" />
</template>
```

#### 启用懒加载模式

```vue
<template>
  <CommunityAddressSelector :lazy-load="true" @address-selected="handleAddressSelected" />
</template>
```

#### 自定义占位符和禁用状态

```vue
<template>
  <CommunityAddressSelector
    placeholder="点击选择社区地址"
    :disabled="isFormReadOnly"
    @address-selected="handleAddressSelected"
  />
</template>
```

## 属性说明

| 属性名       | 类型    | 默认值   | 说明                                                    |
| ------------ | ------- | -------- | ------------------------------------------------------- |
| initialValue | Array   | []       | 初始选中项的值数组，如[communityId, buildingId, unitId] |
| placeholder  | String  | '请选择' | 未选择时的占位文本                                      |
| disabled     | Boolean | false    | 是否禁用选择器                                          |
| lazyLoad     | Boolean | false    | 是否启用懒加载模式                                      |

## 事件说明

| 事件名           | 参数                | 说明                                       |
| ---------------- | ------------------- | ------------------------------------------ |
| address-selected | SelectedAddressInfo | 地址选择完成后触发，返回完整的地址信息对象 |

## 类型定义

组件导出了以下类型，可以在TypeScript项目中使用：

- `SelectedAddressInfo`: 选中的地址信息
- `AddressOption`: 地址选项数据结构
- `AddressNodeData`: 地址节点数据

## 缓存机制

组件内部实现了基于uni-app的本地存储缓存，默认缓存时长为24小时。缓存键值为：

- `community_address_options`: 缓存全部社区地址选项
- `community_address_children_{id}`: 缓存指定ID的子节点数据

## 适配说明

该组件已针对多种平台进行了适配：

- H5
- 微信小程序
- App

## 测试覆盖

组件提供了完整的单元测试和集成测试，包括：

- 组件渲染测试
- 选择事件测试
- 懒加载功能测试
- 初始值加载测试
- 与表单页面集成测试
