<!--
  社区地址选择器组件 - 移动端适配版
  基于Wot Design Uni实现，支持动态加载小区、楼栋、单元等多级地址数据
  
  特性:
  - 多级联动选择（小区/楼栋/单元）
  - 懒加载数据，优化性能
  - 支持数据搜索和过滤
  - 返回完整地址信息对象
  - 包含地理位置坐标数据
  - 支持弹窗模式和内联模式
-->
<template>
  <view
    class="community-address-selector"
    :class="{ 'is-loading': loading, 'is-disabled': disabled }"
  >
    <!-- 内联模式：直接显示选择器 -->
    <template v-if="mode === 'inline'">
      <!-- <div class="debug-info" style="background: #f0f0f0; padding: 10px; margin: 10px 0; font-size: 12px;">
        <div>调试信息:</div>
        <div>selectedKeys: {{ selectedKeys }}</div>
        <div>pickerColumns长度: {{ pickerColumns.length }}</div>
        <div>第一列数据: {{ pickerColumns[0] ? pickerColumns[0].map(item => `${item.label}(${item.value})`) : '无' }}</div>
      </div> -->
      <wd-col-picker
        v-model="selectedKeys"
        :columns="pickerColumns"
        :loading="loading"
        :column-change="handleColumnChange"
        @confirm="handleConfirm"
        @cancel="handleCancel"
        inline
      />
    </template>

    <!-- 弹窗模式：点击触发弹窗 -->
    <template v-else>
      <!-- 输入框触发器 -->
      <wd-input
        :value="displayText"
        readonly
        :placeholder="placeholder"
        :disabled="disabled"
        @click="handleInputClick"
      />

      <!-- 地址多列选择器（弹窗形式） -->
      <wd-col-picker
        v-model="selectedKeys"
        :visible="visible"
        :columns="pickerColumns"
        :title="title"
        :loading="loading"
        :column-change="handleColumnChange"
        @confirm="handleConfirm"
        @cancel="handleCancel"
      />
    </template>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed } from 'vue'
import { get } from '@/utils/request'

// API基础URL
const API_BASE_URL = '/api/v1/system'

/**
 * 地址选项数据结构
 */
interface AddressOption {
  value: number | string // 选项的值 (地址ID)
  label: string // 选项的标签 (地址名称)
  level: number // 地址层级 (1:小区, 2:楼栋, 3:单元)
  isLeaf?: boolean // 是否为叶子节点
  children?: AddressOption[] // 子选项
  longitude?: number // 经度 (可选)
  latitude?: number // 纬度 (可选)
}

/**
 * 选中地址信息
 */
interface SelectedAddressInfo {
  fullPath: string // 完整地址路径
  longitude: number | null // 经度
  latitude: number | null // 纬度
  communityId: number | null // 选定的小区ID
  buildingId: number | null // 选定的楼栋ID
  unitId: number | null // 选定的单元ID
  selectedKeys: (string | number)[] // 保存选中的键值数组
}

/**
 * 组件Props
 */
interface CommunityAddressSelectorProps {
  initialValue?: (string | number)[]
  placeholder?: string
  disabled?: boolean
  clearable?: boolean
  title?: string
  visible?: boolean
  mode?: 'popup' | 'inline' // 显示模式：弹窗模式或内联模式
}

// 定义组件props
const props = withDefaults(defineProps<CommunityAddressSelectorProps>(), {
  initialValue: () => [],
  placeholder: '请选择社区地址',
  disabled: false,
  clearable: true,
  title: '选择社区地址',
  visible: false,
  mode: 'popup',
})

// 定义组件emit事件
const emit = defineEmits<{
  (e: 'address-selected', info: SelectedAddressInfo): void
  (e: 'close'): void
  (e: 'open'): void
  (e: 'update:visible', visible: boolean): void
}>()

// 组件内部状态
const loading = ref(false)
const selectedKeys = ref<(string | number)[]>(props.initialValue || [])
const internalVisible = ref(false) // 内部控制的弹窗显示状态
// ColPicker需要的列数据格式
const pickerColumns = ref<AddressOption[][]>([[]])
// 存储完整的地址数据用于查找
const addressData = ref<AddressOption[]>([])

// 计算属性
/**
 * 弹窗显示状态（支持外部控制和内部控制）
 */
const visible = computed(() => {
  return props.mode === 'popup' ? props.visible || internalVisible.value : false
})

/**
 * 显示文本（用于弹窗模式的输入框）
 */
const displayText = computed(() => {
  console.log('displayText computed: selectedKeys.value =', selectedKeys.value)
  console.log('displayText computed: addressData.value =', addressData.value)
  if (selectedKeys.value && selectedKeys.value.length > 0) {
    const selectedNodes = getSelectedNodes(selectedKeys.value)
    console.log('displayText computed: selectedNodes =', selectedNodes)
    const text = selectedNodes.map((node) => node.label).join('/')
    console.log('displayText computed: final text =', text)
    return text
  }
  console.log('displayText computed: returning empty string')
  return ''
})

/**
 * 处理输入框点击（仅在弹窗模式下使用）
 */
const handleInputClick = () => {
  if (props.disabled) return
  if (props.mode === 'popup') {
    internalVisible.value = true
    emit('open') // 触发打开事件
  }
}

/**
 * 将后端API返回的地址数据转换为级联选择器选项格式
 */
const transformAddressData = (addresses: any[], currentLevel: number): AddressOption[] => {
  if (!Array.isArray(addresses)) {
    console.error('Invalid address data received:', addresses)
    return []
  }
  return addresses.map((addr) => {
    const hasNoChildren =
      !addr.children || !Array.isArray(addr.children) || addr.children.length === 0

    // 确保value是数字类型
    const rawValue = addr.value || addr.id
    const value = typeof rawValue === 'number' ? rawValue : Number(rawValue)

    console.log(
      `transformAddressData: 转换地址数据 - 原始值: ${rawValue}, 转换后: ${value}, 标签: ${addr.label || addr.name}`,
    )

    return {
      value: value,
      label: addr.label || addr.name,
      level: addr.level || currentLevel,
      longitude: addr.longitude,
      latitude: addr.latitude,
      isLeaf:
        (addr.level || currentLevel) === 3 || hasNoChildren || (addr.level || currentLevel) === 2,
      children: addr.children
        ? transformAddressData(addr.children, (addr.level || currentLevel) + 1)
        : undefined,
    }
  })
}

/**
 * 根据层级设置是否为叶子节点
 */
const markLeafNodes = (data: AddressOption[]): AddressOption[] => {
  if (!Array.isArray(data)) return []

  return data.map((item) => {
    const node = { ...item }

    if (node.level === 2 || node.level === 3 || !node.children || !node.children.length) {
      node.isLeaf = true
    }

    if (node.children && node.children.length > 0) {
      node.children = markLeafNodes(node.children)
    }

    return node
  })
}

/**
 * 根据选中的value路径获取对应的选项节点
 */
const getSelectedNodes = (valuePath: (string | number)[]): AddressOption[] => {
  console.log('getSelectedNodes: valuePath =', valuePath)
  console.log('getSelectedNodes: addressData.value =', addressData.value)

  if (!valuePath || !valuePath.length || !addressData.value || !addressData.value.length) {
    console.log('getSelectedNodes: 返回空数组，原因：', {
      noValuePath: !valuePath || !valuePath.length,
      noAddressData: !addressData.value || !addressData.value.length,
    })
    return []
  }

  const result: AddressOption[] = []
  let currentOptions = addressData.value
  let currentNode: AddressOption | undefined

  for (let i = 0; i < valuePath.length; i++) {
    const value = valuePath[i]
    console.log(`getSelectedNodes: 查找第${i}级，value=${value}，当前选项:`, currentOptions)
    currentNode = currentOptions.find((node) => String(node.value) === String(value))

    if (currentNode) {
      console.log(`getSelectedNodes: 找到第${i}级节点:`, currentNode)
      result.push(currentNode)
      if (currentNode.children && currentNode.children.length > 0) {
        currentOptions = currentNode.children
      } else {
        console.log(`getSelectedNodes: 第${i}级节点没有子节点，停止查找`)
        break
      }
    } else {
      console.log(`getSelectedNodes: 第${i}级节点未找到，value=${value}`)
      break
    }
  }

  console.log('getSelectedNodes: 最终结果 =', result)
  return result
}

/**
 * 清除选择的地址
 */
const clearSelection = () => {
  selectedKeys.value = []
  updatePickerColumns()
  emit('address-selected', {
    fullPath: '',
    longitude: null,
    latitude: null,
    communityId: null,
    buildingId: null,
    unitId: null,
    selectedKeys: [],
  })
}

/**
 * 取消选择
 */
const handleCancel = () => {
  if (props.mode === 'popup') {
    internalVisible.value = false
    emit('update:visible', false)
  }
  emit('close')
}

/**
 * 更新ColPicker的列数据
 */
const updatePickerColumns = () => {
  console.log('updatePickerColumns: 开始更新，addressData.value =', addressData.value)
  console.log('updatePickerColumns: selectedKeys.value =', selectedKeys.value)

  if (!addressData.value || !addressData.value.length) {
    console.log('updatePickerColumns: addressData为空，设置空列')
    pickerColumns.value = [[]]
    return
  }

  // 第一列始终是顶级地址
  const columns: AddressOption[][] = [addressData.value]
  console.log('updatePickerColumns: 第一列数据 =', addressData.value)

  // 根据当前选中的值，构建后续列
  if (selectedKeys.value && selectedKeys.value.length > 0) {
    let currentOptions = addressData.value

    for (let i = 0; i < selectedKeys.value.length; i++) {
      const value = selectedKeys.value[i]
      console.log(`updatePickerColumns: 处理第${i}级，value=${value}，当前选项:`, currentOptions)
      const selectedNode = currentOptions.find((node) => String(node.value) === String(value))

      if (selectedNode) {
        console.log(`updatePickerColumns: 找到第${i}级节点:`, selectedNode)
        if (selectedNode.children && selectedNode.children.length > 0) {
          columns.push(selectedNode.children)
          currentOptions = selectedNode.children
          console.log(`updatePickerColumns: 添加第${i + 1}级列数据:`, selectedNode.children)
        } else {
          console.log(`updatePickerColumns: 第${i}级节点没有子节点，停止构建列`)
          break
        }
      } else {
        console.log(`updatePickerColumns: 第${i}级节点未找到，value=${value}`)
        break
      }
    }
  }

  console.log('updatePickerColumns: 最终列数据 =', columns)
  pickerColumns.value = columns
}

/**
 * 处理列变化事件 - 支持wd-col-picker的动态加载
 */
const handleColumnChange = async ({ columnIndex, selectedItem, resolve, finish }: any) => {
  try {
    if (!selectedItem || !selectedItem.value) {
      finish()
      return
    }

    // 如果选中项有子级数据，直接返回
    if (selectedItem.children && selectedItem.children.length > 0) {
      resolve(selectedItem.children)
      return
    }

    // 如果是叶子节点或第三级，结束选择
    if (selectedItem.isLeaf || selectedItem.level >= 3) {
      finish()
      return
    }

    // 动态加载子级数据
    const childrenData = await loadChildrenData(selectedItem.value, selectedItem.level + 1)

    if (childrenData && childrenData.length > 0) {
      resolve(childrenData)
    } else {
      finish()
    }
  } catch (error) {
    console.error('加载子级数据错误:', error)
    finish()
  }
}

/**
 * 处理确认选择
 */
const handleConfirm = ({ value }: { value: (string | number)[] }) => {
  selectedKeys.value = value
  processSelectedValue(value)

  if (props.mode === 'popup') {
    internalVisible.value = false
    emit('update:visible', false)
  }
  emit('close')
}

/**
 * 处理选择的值并触发事件
 */
const processSelectedValue = (value: (string | number)[]) => {
  if (value && value.length > 0) {
    // 获取选中的所有ID
    const communityId = value[0] || null
    const buildingId = value.length > 1 ? value[1] : null
    const unitId = value.length > 2 ? value[2] : null

    // 根据选择的值查找对应的节点
    const selectedNodes = getSelectedNodes(value)

    // 获取完整地址路径
    const fullPath = selectedNodes.map((node) => node.label).join('/')

    // 获取最后一级节点的经纬度
    const lastNode = selectedNodes[selectedNodes.length - 1]

    // 构造完整的地址信息对象
    const fullInfo: SelectedAddressInfo = {
      fullPath,
      longitude: lastNode?.longitude || null,
      latitude: lastNode?.latitude || null,
      communityId: communityId as number | null,
      buildingId: buildingId as number | null,
      unitId: unitId as number | null,
      selectedKeys: value,
    }

    emit('address-selected', fullInfo)
  } else {
    // 清空选择
    emit('address-selected', {
      fullPath: '',
      longitude: null,
      latitude: null,
      communityId: null,
      buildingId: null,
      unitId: null,
      selectedKeys: [],
    })
  }
}

/**
 * 通过详细地址字符串计算selectedKeys
 * @param detailedAddress 详细地址字符串，格式如："小区名/楼栋名/单元名"
 * @returns 计算出的selectedKeys数组
 */
const calculateSelectedKeysFromAddress = (detailedAddress: string): (string | number)[] => {
  console.log('calculateSelectedKeysFromAddress: 开始计算，详细地址 =', detailedAddress)

  if (!detailedAddress || !addressData.value || !addressData.value.length) {
    console.log('calculateSelectedKeysFromAddress: 地址为空或数据未加载，返回空数组')
    return []
  }

  // 分割地址字符串
  const addressParts = detailedAddress.split('/').filter((part) => part.trim())
  console.log('calculateSelectedKeysFromAddress: 地址分割结果 =', addressParts)

  if (addressParts.length === 0) {
    console.log('calculateSelectedKeysFromAddress: 地址分割后为空，返回空数组')
    return []
  }

  const result: (string | number)[] = []
  let currentOptions = addressData.value

  // 逐级查找匹配的节点
  for (let i = 0; i < addressParts.length; i++) {
    const addressPart = addressParts[i].trim()
    console.log(
      `calculateSelectedKeysFromAddress: 查找第${i}级，地址部分="${addressPart}"，当前选项:`,
      currentOptions,
    )

    // 在当前级别中查找匹配的节点
    const matchedNode = currentOptions.find(
      (node) =>
        node.label === addressPart ||
        node.label.includes(addressPart) ||
        addressPart.includes(node.label),
    )

    if (matchedNode) {
      console.log(`calculateSelectedKeysFromAddress: 找到第${i}级匹配节点:`, matchedNode)
      result.push(matchedNode.value)

      // 如果有子节点，继续下一级查找
      if (matchedNode.children && matchedNode.children.length > 0) {
        currentOptions = matchedNode.children
      } else {
        console.log(`calculateSelectedKeysFromAddress: 第${i}级节点没有子节点，停止查找`)
        break
      }
    } else {
      console.log(
        `calculateSelectedKeysFromAddress: 第${i}级未找到匹配节点，地址部分="${addressPart}"`,
      )
      // 尝试模糊匹配
      const fuzzyMatch = currentOptions.find((node) => {
        const nodeName = node.label.toLowerCase()
        const searchName = addressPart.toLowerCase()
        return nodeName.includes(searchName) || searchName.includes(nodeName)
      })

      if (fuzzyMatch) {
        console.log(`calculateSelectedKeysFromAddress: 第${i}级模糊匹配到节点:`, fuzzyMatch)
        result.push(fuzzyMatch.value)
        if (fuzzyMatch.children && fuzzyMatch.children.length > 0) {
          currentOptions = fuzzyMatch.children
        } else {
          break
        }
      } else {
        console.log(`calculateSelectedKeysFromAddress: 第${i}级完全未找到匹配，停止查找`)
        break
      }
    }
  }

  console.log('calculateSelectedKeysFromAddress: 最终计算结果 =', result)
  return result
}

/**
 * 加载子级地址数据
 */
const loadChildrenData = async (
  parentId: number | string,
  level: number,
): Promise<AddressOption[]> => {
  try {
    loading.value = true

    const response: any = await get(`${API_BASE_URL}/addresses/parent/${parentId}`, {
      level: level,
    })

    if (Array.isArray(response)) {
      return transformAddressData(response, level)
    } else {
      console.error('Failed to load children or invalid data format:', response)
      return []
    }
  } catch (error) {
    console.error('Error loading children addresses:', error)
    return []
  } finally {
    loading.value = false
  }
}

// 暴露方法给外部使用
defineExpose({
  calculateSelectedKeysFromAddress,
  clearSelection,
  getSelectedNodes,
  updatePickerColumns,
})

// 监听props.initialValue变化
watch(
  () => props.initialValue,
  (newVal) => {
    console.log('CommunityAddressSelector: initialValue changed:', newVal)
    if (newVal && Array.isArray(newVal) && newVal.length > 0) {
      // 确保数据类型正确，将所有值转换为数字类型
      selectedKeys.value = newVal.map((val) => {
        const numVal = Number(val)
        return isNaN(numVal) ? val : numVal
      })
      console.log('CommunityAddressSelector: selectedKeys updated:', selectedKeys.value)
      // 延迟更新，确保addressData已加载
      if (addressData.value && addressData.value.length > 0) {
        updatePickerColumns()
      }
    } else {
      console.log('CommunityAddressSelector: initialValue is empty, clearing selection')
      selectedKeys.value = []
      updatePickerColumns()
    }
  },
  { deep: true, immediate: true },
)

/**
 * 组件初始化时加载地址数据
 */
onMounted(async () => {
  console.log('CommunityAddressSelector: onMounted 开始，initialValue =', props.initialValue)
  try {
    loading.value = true

    // 尝试使用options API获取全量数据
    const response: any = await get(`${API_BASE_URL}/addresses/options`)

    console.log('CommunityAddressSelector: API响应 =', response)

    // 检查响应是否有效
    if (
      response &&
      response.data &&
      response.data.options &&
      Array.isArray(response.data.options)
    ) {
      // 处理data.options数据，并标记叶子节点
      addressData.value = markLeafNodes(transformAddressData(response.data.options, 1))
      console.log('CommunityAddressSelector: 处理data.options，结果 =', addressData.value)
    } else if (response && response.options && Array.isArray(response.options)) {
      // 兼容直接在response中包含options的情况
      addressData.value = markLeafNodes(transformAddressData(response.options, 1))
      console.log('CommunityAddressSelector: 处理response.options，结果 =', addressData.value)
    } else if (response && Array.isArray(response)) {
      // 兼容response直接是数组的情况
      const firstLevelAddresses = response.filter(
        (addr: any) => addr.level === 1 || addr.parentId === 0,
      )
      let transformedData = transformAddressData(firstLevelAddresses, 1)
      // 处理变换后的数据，确保叶子节点标记
      addressData.value = markLeafNodes(transformedData)
      console.log('CommunityAddressSelector: 处理数组响应，结果 =', addressData.value)
    } else {
      console.error('CommunityAddressSelector: 无效的API响应格式:', response)
      addressData.value = []
    }

    // 如果有初始值，设置selectedKeys
    if (props.initialValue && Array.isArray(props.initialValue) && props.initialValue.length > 0) {
      console.log('CommunityAddressSelector: 设置初始值 =', props.initialValue)
      // 确保数据类型正确，将所有值转换为数字类型
      selectedKeys.value = props.initialValue.map((val) => {
        const numVal = Number(val)
        return isNaN(numVal) ? val : numVal
      })
      console.log('CommunityAddressSelector: 转换后的selectedKeys =', selectedKeys.value)
    }

    // 更新ColPicker的列数据
    updatePickerColumns()

    console.log('CommunityAddressSelector: 初始化完成，selectedKeys =', selectedKeys.value)
    console.log('CommunityAddressSelector: 初始化完成，pickerColumns =', pickerColumns.value)
  } catch (error) {
    console.error('CommunityAddressSelector: 加载地址数据错误:', error)
    addressData.value = []
  } finally {
    loading.value = false
  }
})
</script>

<style scoped>
.community-address-selector {
  position: relative;
  width: 100%;
}
</style>
