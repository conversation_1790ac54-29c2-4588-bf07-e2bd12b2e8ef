/**
 * 社区地址选择器类型定义文件
 * 定义了组件所需的各种接口和类型
 */

/**
 * 地址选项数据结构
 */
export interface AddressOption {
  value: number | string // 选项的值 (地址ID)
  label: string // 选项的标签 (地址名称)
  level: number // 地址层级 (1:小区, 2:楼栋, 3:单元)
  isLeaf: boolean // 是否为叶子节点
  children?: AddressOption[] // 子选项
  longitude?: number // 经度 (可选)
  latitude?: number // 纬度 (可选)
}

/**
 * 多列选择器配置属性（适用于wd-col-picker组件）
 */
export interface ColPickerProps {
  visible: boolean // 是否可见
  loading: boolean // 是否加载中
  title: string // 标题
  columns: AddressOption[][] // 多列数据（二维数组）
  columnChange?: (options: {
    selectedItem: AddressOption // 当前选中项
    columnIndex: number // 当前列索引
    resolve: (options: AddressOption[]) => void // 解析函数
    finish: () => void // 结束函数
  }) => void
}

/**
 * 级联选择器配置属性（兼容旧版）
 */
export interface CascaderProps {
  expandTrigger: 'hover' | 'click' // 展开触发方式
  checkStrictly: boolean // 可选择任意层级
  lazy: boolean // 是否启用懒加载
  lazyLoad?: (node: any, resolve: (options: AddressOption[]) => void) => void // 懒加载函数
  value: string // 值字段名
  label: string // 标签字段名
  children: string // 子节点字段名
  multiple?: boolean // 是否多选
  virtualScroll?: boolean // 是否使用虚拟滚动
  height?: number // 虚拟滚动高度
}

/**
 * 选中地址信息接口
 */
export interface SelectedAddressInfo {
  fullPath: string // 完整地址路径（如: "星河小区/1号楼/2单元"）
  longitude: number | null // 经度坐标
  latitude: number | null // 纬度坐标
  communityId: number | null // 小区ID
  buildingId: number | null // 楼栋ID
  unitId: number | null // 单元ID
  selectedKeys: (string | number)[] // 选中的键值数组，用于保存当前选择状态
}

/**
 * 组件Props接口
 */
export interface CommunityAddressSelectorProps {
  initialValue?: Array<string | number> // 初始选中的地址值路径
  placeholder?: string // 选择器的占位文本
  disabled?: boolean // 是否禁用选择器
  size?: 'large' | 'default' | 'small' // 选择器大小
  theme?: {
    // 主题配置
    activeColor?: string // 激活颜色
    borderRadius?: string // 边框圆角
    fontSize?: string // 字体大小
  }
  filterable?: boolean // 是否可搜索
  clearable?: boolean // 是否可清空
}
