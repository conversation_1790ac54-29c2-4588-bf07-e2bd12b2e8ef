/**
 * 社区地址选择器工具函数
 * 提供数据转换、节点标记等功能
 */
import type { AddressOption } from './types'

/**
 * 将后端API返回的地址数据转换为级联选择器选项格式
 * @param addresses 后端返回的地址数据
 * @param currentLevel 当前地址层级
 * @returns 转换后的选项数组
 */
export const transformAddressData = (addresses: any[], currentLevel: number): AddressOption[] => {
  if (!Array.isArray(addresses)) {
    return []
  }

  return addresses.map((addr) => {
    const hasNoChildren =
      !addr.children || !Array.isArray(addr.children) || addr.children.length === 0

    return {
      value: addr.value || addr.id,
      label: addr.label || addr.name,
      level: addr.level || currentLevel,
      longitude: addr.longitude,
      latitude: addr.latitude,
      isLeaf:
        (addr.level || currentLevel) === 3 || hasNoChildren || (addr.level || currentLevel) === 2,
      children: addr.children
        ? transformAddressData(addr.children, (addr.level || currentLevel) + 1)
        : undefined,
    }
  })
}

/**
 * 根据层级设置是否为叶子节点
 * @param data 地址数据
 * @returns 处理后的地址数据
 */
export const markLeafNodes = (data: AddressOption[]): AddressOption[] => {
  if (!Array.isArray(data)) return []

  return data.map((item) => {
    const node = { ...item }

    // 层级为2(楼栋)或3(单元)，或没有子节点的，标记为叶子节点
    if (node.level === 2 || node.level === 3 || !node.children || !node.children.length) {
      node.isLeaf = true
    }

    // 递归处理子节点
    if (node.children && node.children.length > 0) {
      node.children = markLeafNodes(node.children)
    }

    return node
  })
}

/**
 * 通过选中值路径查找对应的节点
 * @param options 选项数据（支持一维或二维数组格式）
 * @param selectedValues 选中的值路径
 * @returns 选中的节点数组
 */
export const findSelectedNodes = (
  options: AddressOption[] | AddressOption[][],
  selectedValues: (string | number)[],
): AddressOption[] => {
  if (!selectedValues || !selectedValues.length || !options || !options.length) {
    return []
  }

  const result: AddressOption[] = []

  // 处理二维数组格式（适用于wd-col-picker）
  if (Array.isArray(options[0])) {
    // 将二维数组转换为我们需要的形式处理
    const optionsArray = options as AddressOption[][]
    for (let i = 0; i < selectedValues.length && i < optionsArray.length; i++) {
      const columnOptions = optionsArray[i]
      const value = selectedValues[i]
      const foundNode = columnOptions.find((option) => option.value === value)
      if (foundNode) {
        result.push(foundNode)
      } else {
        break
      }
    }
    return result
  }

  // 下面是原来处理一维数组的逻辑
  let currentOptions = options as AddressOption[]
  let foundNode: AddressOption | undefined

  // 遍历选中值路径，查找对应节点
  for (const value of selectedValues) {
    foundNode = currentOptions.find((option) => option.value === value)

    if (foundNode) {
      result.push(foundNode)
      if (foundNode.children && foundNode.children.length) {
        currentOptions = foundNode.children
      } else {
        break
      }
    } else {
      break
    }
  }

  return result
}

/**
 * 适配不同格式的地址数据
 * @param response API响应数据
 * @returns 标准化的地址数据
 */
export const adaptAddressData = (response: any): any[] => {
  // 尝试识别不同的数据格式
  if (response && response.options && Array.isArray(response.options)) {
    return response.options
  } else if (response && response.data && Array.isArray(response.data)) {
    return response.data
  } else if (response && Array.isArray(response)) {
    return response
  } else if (response && response.result && Array.isArray(response.result)) {
    return response.result
  }

  console.error('无法识别的数据格式:', response)
  return []
}

/**
 * 缓存管理
 */
export const addressCache = {
  /**
   * 获取缓存的地址数据
   * @returns 缓存的地址数据或null
   */
  get(): AddressOption[] | null {
    try {
      const cachedData = localStorage.getItem('community_address_options')
      const cacheTime = localStorage.getItem('community_address_cache_time')

      if (cachedData && cacheTime) {
        // 缓存过期时间为24小时
        if (Date.now() - parseInt(cacheTime) > 24 * 60 * 60 * 1000) {
          this.clear()
          return null
        }
        return JSON.parse(cachedData)
      }
    } catch (e) {
      console.error('读取缓存失败:', e)
      this.clear()
    }
    return null
  },

  /**
   * 设置地址数据缓存
   * @param data 要缓存的地址数据
   */
  set(data: AddressOption[]): void {
    try {
      localStorage.setItem('community_address_options', JSON.stringify(data))
      localStorage.setItem('community_address_cache_time', Date.now().toString())
    } catch (e) {
      console.error('缓存数据失败:', e)
    }
  },

  /**
   * 清除地址数据缓存
   */
  clear(): void {
    localStorage.removeItem('community_address_options')
    localStorage.removeItem('community_address_cache_time')
  },
}
