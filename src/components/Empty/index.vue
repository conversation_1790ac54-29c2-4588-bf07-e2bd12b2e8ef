<!--
 * 空状态组件
 * 用于显示空数据状态，支持自定义图片、描述文字和操作按钮
-->
<template>
  <view class="empty" :class="[`empty--${mode}`]">
    <!-- 图片区域 -->
    <view class="empty__image">
      <image v-if="image" :src="image" class="empty__img" :style="imageStyle" mode="aspectFit" />
      <view v-else class="empty__default-icon">
        <wd-icon name="inbox" size="120" color="#d9d9d9" />
      </view>
    </view>

    <!-- 描述文字 -->
    <view v-if="description" class="empty__description">
      <text>{{ description }}</text>
    </view>

    <!-- 操作按钮 -->
    <view v-if="$slots.default" class="empty__actions">
      <slot></slot>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue'

/**
 * 空状态组件属性定义
 */
interface Props {
  /** 空状态图片地址 */
  image?: string
  /** 描述文字 */
  description?: string
  /** 图片样式 */
  imageStyle?: Record<string, any>
  /** 显示模式 */
  mode?: 'page' | 'section'
}

const props = withDefaults(defineProps<Props>(), {
  description: '暂无数据',
  mode: 'section',
})

// 计算图片样式
const computedImageStyle = computed(() => {
  const defaultStyle = {
    width: props.mode === 'page' ? '200rpx' : '160rpx',
    height: props.mode === 'page' ? '200rpx' : '160rpx',
  }
  return { ...defaultStyle, ...props.imageStyle }
})
</script>

<style lang="scss" scoped>
.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;

  &--page {
    min-height: 60vh;
    padding: 80rpx 40rpx;
  }

  &--section {
    padding: 60rpx 40rpx;
  }

  &__image {
    margin-bottom: 32rpx;

    .empty__img {
      display: block;
    }

    .empty__default-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 160rpx;
      height: 160rpx;
      margin: 0 auto;
    }
  }

  &__description {
    margin-bottom: 32rpx;

    text {
      font-size: 28rpx;
      color: #999;
      line-height: 1.5;
    }
  }

  &__actions {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 24rpx;
  }
}

// 页面模式下的样式调整
.empty--page {
  .empty__image {
    margin-bottom: 40rpx;

    .empty__default-icon {
      width: 200rpx;
      height: 200rpx;
    }
  }

  .empty__description {
    margin-bottom: 40rpx;

    text {
      font-size: 32rpx;
    }
  }
}
</style>
