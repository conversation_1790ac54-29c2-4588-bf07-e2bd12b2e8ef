<template>
  <view class="favorite-button" @click="toggleFavorite">
    <wd-icon
      :name="isFavorited ? 'heart-fill' : 'heart'"
      :size="size"
      :color="isFavorited ? '#ff4757' : '#999'"
      class="favorite-icon"
      :class="{ favorited: isFavorited, loading: loading }"
    />
    <text v-if="showText" class="favorite-text">
      {{ isFavorited ? '已收藏' : '收藏' }}
    </text>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { checkFavoriteStatus, addFavorite, deleteFavorite } from '@/api/user'
import type { FavoriteType, IAddFavoriteRequest } from '@/api/user.typings'

// Props
interface Props {
  type: FavoriteType
  targetId: number
  targetName: string
  targetImage?: string
  extraData?: Record<string, any>
  size?: number
  showText?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 24,
  showText: false,
})

// Emits
const emit = defineEmits<{
  change: [isFavorited: boolean]
}>()

// 响应式数据
const isFavorited = ref(false)
const favoriteId = ref<number | null>(null)
const loading = ref(false)

// 方法
const checkStatus = async () => {
  try {
    // 验证参数
    if (!props.targetId || props.targetId <= 0) {
      console.warn('FavoriteButton: targetId无效', props.targetId)
      return
    }

    const result :any = await checkFavoriteStatus(props.type, props.targetId)

    // 检查返回数据是否有效
    if (result && result.data) {
      isFavorited.value = result.data.is_favorited || false
      favoriteId.value = result.data.favorite_id || null
    } else {
      console.warn('FavoriteButton: API返回数据无效', result)
      // 设置默认值
      isFavorited.value = false
      favoriteId.value = null
    }
  } catch (error) {
    console.error('检查收藏状态失败:', error)
    // 设置默认值，避免组件崩溃
    isFavorited.value = false
    favoriteId.value = null
  }
}

const toggleFavorite = async () => {
  if (loading.value) return

  try {
    loading.value = true

    if (isFavorited.value) {
      // 取消收藏
      if (favoriteId.value) {
        await deleteFavorite(favoriteId.value)
        isFavorited.value = false
        favoriteId.value = null

        uni.showToast({
          title: '取消收藏成功',
          icon: 'success',
        })
      }
    } else {
      // 添加收藏
      const request: IAddFavoriteRequest = {
        type: props.type,
        target_id: props.targetId,
        target_name: props.targetName,
        target_image: props.targetImage,
        extra_data: props.extraData,
      }

      const result :any = await addFavorite(request)
      isFavorited.value = true
      favoriteId.value = result.data.id

      uni.showToast({
        title: '收藏成功',
        icon: 'success',
      })
    }

    emit('change', isFavorited.value)
  } catch (error) {
    console.error('收藏操作失败:', error)
    uni.showToast({
      title: '操作失败',
      icon: 'error',
    })
  } finally {
    loading.value = false
  }
}

// 监听 props 变化
watch(
  () => [props.type, props.targetId],
  () => {
    checkStatus()
  },
  { immediate: false },
)

// 生命周期
onMounted(() => {
  checkStatus()
})
</script>

<style lang="scss" scoped>
.favorite-button {
  display: flex;
  align-items: center;
  gap: 8rpx;
  cursor: pointer;
  transition: all 0.3s;

  &:active {
    transform: scale(0.95);
  }

  .favorite-icon {
    transition: all 0.3s;

    &.favorited {
      animation: heartBeat 0.6s ease-in-out;
    }

    &.loading {
      opacity: 0.6;
    }
  }

  .favorite-text {
    font-size: 24rpx;
    color: #666;
    transition: color 0.3s;
  }

  &:hover .favorite-text {
    color: #333;
  }
}

@keyframes heartBeat {
  0% {
    transform: scale(1);
  }
  14% {
    transform: scale(1.3);
  }
  28% {
    transform: scale(1);
  }
  42% {
    transform: scale(1.3);
  }
  70% {
    transform: scale(1);
  }
}
</style>
