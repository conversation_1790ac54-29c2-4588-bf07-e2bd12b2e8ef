# FileUpload 文件上传组件

通用的文件上传组件，支持图片、头像、文档等多种文件类型的上传，提供丰富的配置选项和回调函数。

## 功能特性

- 🖼️ **多类型支持**：支持图片、头像、文档等多种文件类型
- 👤 **头像优化**：专门的头像上传模式，自动压缩优化
- 📱 **跨平台兼容**：兼容微信小程序、H5等多个平台
- 🎯 **灵活配置**：可配置文件类型、大小限制、数量限制等
- 📊 **进度显示**：实时显示上传进度
- 🔄 **重试机制**：上传失败后支持重试
- 👀 **预览功能**：支持图片预览和文件信息查看
- 🎨 **自定义样式**：支持插槽自定义上传按钮和提示信息

## 基础用法

### 图片上传

```vue
<template>
  <FileUpload
    v-model="imageList"
    file-type="image"
    :max-count="3"
    :max-size="5"
    upload-url="/api/upload/image"
    @success="handleSuccess"
    @error="handleError"
  />
</template>

<script setup>
import { ref } from 'vue'
import FileUpload from '@/components/FileUpload.vue'

const imageList = ref([])

const handleSuccess = (file, response) => {
  console.log('上传成功:', file, response)
}

const handleError = (file, error) => {
  console.log('上传失败:', file, error)
}
</script>
```

### 头像上传

```vue
<template>
  <!-- 自定义头像显示区域 -->
  <view class="avatar-section" @click="handleAvatarUpload">
    <view class="avatar-wrapper">
      <image :src="currentAvatar" class="avatar" mode="aspectFill" />
      <view class="avatar-mask">
        <text>更换头像</text>
      </view>
      <!-- 删除按钮 -->
      <view v-if="hasAvatar" class="delete-btn" @click.stop="deleteAvatar">
        <text>×</text>
      </view>
    </view>
  </view>

  <!-- 隐藏的文件上传组件 -->
  <FileUpload
    ref="avatarUploadRef"
    v-model="avatarList"
    file-type="avatar"
    :max-count="1"
    :max-size="5"
    upload-url="/api/v1/user/secured/upload"
    :form-data="{ file_usage: 'avatar' }"
    :show-upload-button="false"
    :show-file-list="false"
    :show-tips="false"
    @success="handleAvatarSuccess"
    @error="handleAvatarError"
  />
</template>

<script setup>
import { ref, computed } from 'vue'
import FileUpload from '@/components/FileUpload.vue'

const avatarList = ref([])
const avatarUploadRef = ref()
const defaultAvatar = '/static/default-avatar.png'

const currentAvatar = computed(() => {
  return avatarList.value[0]?.url || defaultAvatar
})

const hasAvatar = computed(() => {
  return avatarList.value.length > 0
})

const handleAvatarUpload = () => {
  avatarUploadRef.value?.upload()
}

const handleAvatarSuccess = (file, response) => {
  console.log('头像上传成功:', response)
  // 处理头像上传成功逻辑
}

const handleAvatarError = (file, error) => {
  console.log('头像上传失败:', error)
}

const deleteAvatar = () => {
  avatarList.value = []
}
</script>
```

### 文件上传

```vue
<template>
  <FileUpload
    v-model="fileList"
    file-type="file"
    :max-count="5"
    :max-size="10"
    upload-url="/api/upload/file"
    trigger-text="选择文档"
    tips-text="支持 PDF、Word、Excel 等格式，单个文件不超过 10MB"
  />
</template>

<script setup>
import { ref } from 'vue'
import FileUpload from '@/components/FileUpload.vue'

const fileList = ref([])
</script>
```

### 混合类型上传

```vue
<template>
  <FileUpload v-model="mixedList" file-type="all" :max-count="10" upload-url="/api/upload" />
</template>

<script setup>
import { ref } from 'vue'
import FileUpload from '@/components/FileUpload.vue'

const mixedList = ref([])
</script>
```

## 高级用法

### 自定义上传按钮

```vue
<template>
  <FileUpload v-model="fileList">
    <template #trigger>
      <view class="custom-trigger">
        <text class="icon">📎</text>
        <text>点击上传附件</text>
      </view>
    </template>
  </FileUpload>
</template>

<style>
.custom-trigger {
  display: flex;
  align-items: center;
  padding: 20rpx 40rpx;
  background: linear-gradient(45deg, #409eff, #67c23a);
  color: white;
  border-radius: 50rpx;
  gap: 16rpx;
}
</style>
```

### 自定义提示信息

```vue
<template>
  <FileUpload v-model="fileList">
    <template #tips>
      <view class="custom-tips">
        <text>• 支持 JPG、PNG、PDF 格式</text>
        <text>• 单个文件不超过 5MB</text>
        <text>• 最多上传 3 个文件</text>
      </view>
    </template>
  </FileUpload>
</template>

<style>
.custom-tips {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  font-size: 24rpx;
  color: #666;
}
</style>
```

### 手动控制上传

```vue
<template>
  <FileUpload ref="uploadRef" v-model="fileList" :auto-upload="false" @select="handleSelect" />

  <view class="upload-actions">
    <button @click="startUpload">开始上传</button>
    <button @click="clearFiles">清空文件</button>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import FileUpload from '@/components/FileUpload.vue'

const uploadRef = ref()
const fileList = ref([])

const handleSelect = (files) => {
  console.log('选择了文件:', files)
}

const startUpload = () => {
  // 手动上传所有文件
  fileList.value.forEach((file) => {
    if (file.status !== 'success') {
      uploadRef.value.uploadFile(file)
    }
  })
}

const clearFiles = () => {
  uploadRef.value.clear()
}
</script>
```

## API 参考

### Props

| 参数               | 类型                                     | 默认值               | 说明                       |
| ------------------ | ---------------------------------------- | -------------------- | -------------------------- |
| `v-model`          | `FileItem[]`                             | `[]`                 | 文件列表，支持双向绑定     |
| `uploadUrl`        | `string`                                 | `'/api/upload'`      | 上传接口地址               |
| `fileType`         | `'image' \| 'file' \| 'avatar' \| 'all'` | `'image'`            | 文件类型限制               |
| `maxCount`         | `number`                                 | `0`                  | 最大上传数量，0 表示不限制 |
| `maxSize`          | `number`                                 | `10`                 | 文件大小限制（MB）         |
| `accept`           | `string[]`                               | `['*']`              | 支持的文件格式             |
| `showDelete`       | `boolean`                                | `true`               | 是否显示删除按钮           |
| `showUploadButton` | `boolean`                                | `true`               | 是否显示上传按钮           |
| `showFileList`     | `boolean`                                | `true`               | 是否显示文件列表           |
| `showTips`         | `boolean`                                | `true`               | 是否显示提示信息           |
| `triggerText`      | `string`                                 | `'选择文件'`         | 上传按钮文字               |
| `tipsText`         | `string`                                 | `'点击选择文件上传'` | 提示文字                   |
| `formData`         | `Record<string, any>`                    | `{}`                 | 额外的表单数据             |
| `autoUpload`       | `boolean`                                | `true`               | 是否自动上传               |

### Events

| 事件名              | 参数                                 | 说明               |
| ------------------- | ------------------------------------ | ------------------ |
| `update:modelValue` | `(files: FileItem[])`                | 文件列表变化时触发 |
| `success`           | `(file: FileItem, response: any)`    | 文件上传成功时触发 |
| `error`             | `(file: FileItem, error: any)`       | 文件上传失败时触发 |
| `progress`          | `(file: FileItem, progress: number)` | 上传进度变化时触发 |
| `select`            | `(files: FileItem[])`                | 文件选择时触发     |
| `remove`            | `(file: FileItem, index: number)`    | 文件删除时触发     |
| `preview`           | `(file: FileItem, index: number)`    | 文件预览时触发     |

### Slots

| 插槽名    | 说明           |
| --------- | -------------- |
| `trigger` | 自定义上传按钮 |
| `tips`    | 自定义提示信息 |

### Methods

通过 `ref` 可以调用以下方法：

| 方法名            | 参数               | 返回值       | 说明                   |
| ----------------- | ------------------ | ------------ | ---------------------- |
| `upload`          | -                  | -            | 手动触发文件选择       |
| `uploadFile`      | `(file: FileItem)` | -            | 手动上传指定文件       |
| `clear`           | -                  | -            | 清空文件列表           |
| `getFileList`     | -                  | `FileItem[]` | 获取文件列表           |
| `getSuccessFiles` | -                  | `FileItem[]` | 获取成功上传的文件列表 |

### FileItem 接口

```typescript
interface FileItem {
  id?: string // 文件唯一标识
  name: string // 文件名
  size: number // 文件大小（字节）
  type: 'image' | 'file' // 文件类型
  url?: string // 上传成功后的文件地址
  tempPath?: string // 本地临时路径
  status: 'uploading' | 'success' | 'failed' // 上传状态
  progress?: number // 上传进度（0-100）
  [key: string]: any // 其他自定义字段
}
```

## 样式定制

组件使用 SCSS 编写样式，支持通过 CSS 变量或覆盖样式类进行定制：

```scss
// 自定义主题色
.file-upload {
  --primary-color: #409eff;
  --success-color: #67c23a;
  --error-color: #f56c6c;
  --border-color: #dcdfe6;
}

// 覆盖默认样式
.file-upload .default-trigger {
  border-color: var(--primary-color);
  background: linear-gradient(45deg, #409eff, #67c23a);
  color: white;
}
```

## 文件类型说明

### fileType 参数详解

- **`image`**: 普通图片上传

  - 支持选择多张图片
  - 使用原图或压缩图
  - 适用于相册、商品图片等场景

- **`avatar`**: 头像上传（新增）

  - 专门为头像上传优化
  - 自动使用压缩图片以减少文件大小
  - 限制只能选择1张图片
  - 文件命名为 `avatar_时间戳.jpg`
  - 适用于用户头像、个人资料图片等场景

- **`file`**: 文档文件上传

  - 支持各种文档格式
  - 仅在非微信小程序平台可用
  - 适用于PDF、Word、Excel等文档上传

- **`all`**: 混合类型上传
  - 用户可选择图片或文件
  - 显示选择菜单供用户选择类型

## 注意事项

1. **平台兼容性**：

   - 微信小程序：支持图片和视频选择
   - H5：支持所有文件类型选择
   - App：根据平台能力支持不同文件类型

2. **文件大小限制**：

   - 组件会在前端检查文件大小
   - 建议后端也进行大小验证
   - 头像上传建议限制在5MB以内

3. **上传接口**：

   - 接口应返回标准的 JSON 格式
   - 成功响应应包含文件 URL
   - 头像上传建议使用 `file_usage: 'avatar'` 标识

4. **权限要求**：
   - 微信小程序需要配置相册访问权限
   - H5 需要用户主动触发文件选择

## 更新日志

### v1.2.0

- 新增 `showFileList` 属性，支持隐藏文件列表显示
- 优化头像上传场景，支持自定义UI显示
- 更新头像上传示例，展示自定义头像区域的实现
- 完善文档说明

### v1.1.0

- 新增 `avatar` 文件类型支持
- 添加专门的头像上传优化逻辑
- 头像上传自动使用压缩图片
- 优化头像文件命名规则
- 更新文档和示例代码

### v1.0.0

- 初始版本发布
- 支持图片和文件上传
- 提供丰富的配置选项和事件回调
- 兼容多个平台
