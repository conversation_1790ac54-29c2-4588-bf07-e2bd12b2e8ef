<!--
  通用文件上传组件 - 基于wd-upload设计

  此组件提供统一的文件上传功能，支持图片、文档、头像等多种文件类型
  包括文件选择、上传进度显示、预览、删除等功能
  可配置上传类型、大小限制、数量限制等参数

  支持的文件类型:
  - image: 普通图片上传
  - avatar: 头像上传（优化压缩，单张限制）
  - file: 文档文件上传
  - all: 所有类型

  匹配后端API:
  - POST /api/v1/user/secured/upload - 文件上传
  - GET /api/v1/user/secured/upload/config - 获取上传配置
  - GET /api/v1/user/secured/upload/list - 获取文件列表
  - GET /api/v1/user/secured/upload/:id - 获取文件信息
  - DELETE /api/v1/user/secured/upload/:id - 删除文件
-->
<template>
  <view class="file-upload">
    <!-- 上传按钮区域 -->
    <view class="upload-trigger" @click="handleUpload" v-if="showUploadButton">
      <slot name="trigger">
        <view
          class="default-trigger"
          :class="{ disabled: loading || (maxCount > 0 && fileList.length >= maxCount) }"
        >
          <text class="iconfont icon-add"></text>
          <text class="trigger-text">{{ triggerText }}</text>
        </view>
      </slot>
    </view>

    <!-- 文件列表 -->
    <view class="file-list" v-if="showFileList && fileList.length > 0">
      <view
        class="file-item"
        v-for="(file, index) in fileList"
        :key="file.id || index"
        :class="[`file-${file.type}`, { uploading: file.status === 'uploading' }]"
      >
        <!-- 图片预览 -->
        <view v-if="file.type === 'image'" class="image-preview" @click="previewImage(file, index)">
          <image class="preview-image" :src="file.url || file.tempPath" mode="aspectFill" />

          <!-- 上传进度遮罩 -->
          <view v-if="file.status === 'uploading'" class="upload-mask">
            <view class="progress-circle">
              <text class="progress-text">{{ file.progress || 0 }}%</text>
              <!-- 调试信息 -->
              <text class="debug-info" style="font-size: 10px; color: #666; margin-top: 5px">
                {{ file.name }} - {{ file.status }}
              </text>
            </view>
          </view>

          <!-- 上传失败遮罩 -->
          <view
            v-if="file.status === 'failed'"
            class="failed-mask"
            @click.stop="retryUpload(file, index)"
          >
            <text class="iconfont icon-refresh"></text>
            <text class="failed-text">重试</text>
          </view>
        </view>

        <!-- 文件信息 -->
        <view v-else class="file-info" @click="previewFile(file, index)">
          <view class="file-icon">
            <text class="iconfont" :class="getFileIcon(file.name)"></text>
          </view>
          <view class="file-details">
            <text class="file-name">{{ file.name }}</text>
            <text class="file-size">{{ formatFileSize(file.size) }}</text>

            <!-- 上传进度 -->
            <view v-if="file.status === 'uploading'" class="upload-progress">
              <view class="progress-bar">
                <view class="progress-fill" :style="{ width: `${file.progress || 0}%` }"></view>
              </view>
              <text class="progress-text">{{ file.progress || 0 }}%</text>
              <!-- 调试信息 -->
              <text class="debug-info" style="font-size: 10px; color: #666">
                {{ file.name }} - {{ file.status }} - ID: {{ file.id }}
              </text>
            </view>

            <!-- 上传失败 -->
            <view v-if="file.status === 'failed'" class="upload-failed">
              <text class="failed-text">上传失败</text>
              <text class="retry-btn" @click.stop="retryUpload(file, index)">重试</text>
            </view>
          </view>
        </view>

        <!-- 删除按钮 -->
        <view
          v-if="showDelete && file.status !== 'uploading'"
          class="delete-btn"
          @click.stop="removeFile(index)"
        >
          <text class="iconfont icon-close"></text>
        </view>
      </view>
    </view>

    <!-- 上传提示信息 -->
    <view v-if="showTips" class="upload-tips">
      <slot name="tips">
        <text class="tips-text">{{ tipsText }}</text>
      </slot>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, watch, computed, onMounted, nextTick } from 'vue'
import { getEnvBaseUrl } from '@/utils' // Added for URL construction
import request from '@/utils/request'

/**
 * 文件对象接口 - 匹配后端API响应
 */
export interface FileItem {
  id?: string | number
  name: string
  size: number
  type: 'image' | 'file'
  url?: string
  tempPath?: string
  status: 'uploading' | 'success' | 'failed'
  progress?: number
  // 后端返回的额外字段
  file_id?: string | number
  file_path?: string
  file_type?: string
  mime_type?: string
  created_at?: string
  updated_at?: string
  [key: string]: any
}

/**
 * 上传配置接口
 */
export interface UploadConfig {
  max_file_size: number // 最大文件大小(字节)
  allowed_types: string[] // 允许的文件类型
  max_files: number // 最大文件数量
  upload_path: string // 上传路径
}

/**
 * 上传响应接口
 */
export interface UploadResponse {
  code: number
  message: string
  data: {
    id: string | number
    file_name: string
    file_path: string
    file_url?: string
    file_size: number
    file_type: string
    file_ext: string
    file_usage: string
    is_anonymous: boolean
    storage: string
    module: string
    created_at: string
    // 兼容旧版本字段
    name?: string
    size?: number
    url?: string
    mime_type?: string
  }
}

/**
 * 组件属性接口
 */
interface Props {
  /** 上传地址 */
  uploadUrl?: string
  /** 文件类型：image-图片，file-文件，avatar-头像，all-所有类型 */
  fileType?: 'image' | 'file' | 'avatar' | 'all'
  /** 最大上传数量，0表示不限制 */
  maxCount?: number
  /** 文件大小限制（MB） */
  maxSize?: number
  /** 支持的文件格式 */
  accept?: string[]
  /** 是否显示删除按钮 */
  showDelete?: boolean
  /** 是否显示上传按钮 */
  showUploadButton?: boolean
  /** 是否显示文件列表 */
  showFileList?: boolean
  /** 是否显示提示信息 */
  showTips?: boolean
  /** 上传按钮文字 */
  triggerText?: string
  /** 提示文字 */
  tipsText?: string
  /** 额外的表单数据 */
  formData?: Record<string, any>
  /** 初始文件列表 */
  modelValue?: FileItem[]
  /** 是否自动上传 */
  autoUpload?: boolean
}

/**
 * 组件事件接口
 */
interface Emits {
  /** 文件列表变化 */
  (e: 'update:modelValue', files: FileItem[]): void
  /** 上传成功 */
  (e: 'success', file: FileItem, response: any): void
  /** 上传失败 */
  (e: 'error', file: FileItem, error: any): void
  /** 上传进度 */
  (e: 'progress', file: FileItem, progress: number): void
  /** 文件选择 */
  (e: 'select', files: FileItem[]): void
  /** 文件删除 */
  (e: 'remove', file: FileItem, index: number): void
  /** 文件预览 */
  (e: 'preview', file: FileItem, index: number): void
}

// 定义属性和事件
const props = withDefaults(defineProps<Props>(), {
  uploadUrl: '/api/v1/user/secured/upload',
  fileType: 'image',
  maxCount: 0,
  maxSize: 10,
  accept: () => ['*'],
  showDelete: true,
  showUploadButton: true,
  showFileList: true,
  showTips: true,
  triggerText: '选择文件',
  tipsText: '点击选择文件上传',
  formData: () => ({}),
  modelValue: () => [],
  autoUpload: true,
})

const emit = defineEmits<Emits>()

// 响应式数据
const fileList = ref<FileItem[]>([...props.modelValue])
const loading = ref(false)
const uploadConfig = ref<UploadConfig | null>(null)
const uploadTask = ref<UniApp.UploadTask | null>(null)

// 计算属性
const showUploadButton = computed(() => {
  if (!props.showUploadButton) return false
  if (props.maxCount > 0 && fileList.value.length >= props.maxCount) return false
  return true
})

// 监听外部文件列表变化
watch(
  () => props.modelValue,
  (newFiles) => {
    // 避免不必要的更新，防止递归
    if (JSON.stringify(newFiles) !== JSON.stringify(fileList.value)) {
      fileList.value = [...newFiles]
    }
  },
  { deep: true },
)

// 监听内部文件列表变化，同步到外部
watch(
  fileList,
  (newFiles) => {
    // 避免不必要的更新，防止递归
    if (JSON.stringify(newFiles) !== JSON.stringify(props.modelValue)) {
      emit('update:modelValue', newFiles)
    }
  },
  { deep: true },
)

/**
 * 处理文件上传
 */
const handleUpload = () => {
  console.log('FileUpload handleUpload 被调用, fileType:', props.fileType)

  if (loading.value) {
    console.log('当前正在上传中，跳过')
    return
  }

  if (props.maxCount > 0 && fileList.value.length >= props.maxCount) {
    console.log('已达到最大上传数量限制:', props.maxCount)
    uni.showToast({
      title: `最多只能上传${props.maxCount}个文件`,
      icon: 'none',
    })
    return
  }

  // 根据文件类型选择不同的选择器
  if (props.fileType === 'image') {
    console.log('调用 chooseImage')
    chooseImage()
  } else if (props.fileType === 'avatar') {
    console.log('调用 chooseAvatar')
    chooseAvatar()
  } else if (props.fileType === 'file') {
    console.log('调用 chooseFile')
    chooseFile()
  } else {
    console.log('显示文件类型选择菜单')
    // 显示选择菜单
    uni.showActionSheet({
      itemList: ['选择图片', '选择文件'],
      success: (res) => {
        if (res.tapIndex === 0) {
          chooseImage()
        } else {
          chooseFile()
        }
      },
    })
  }
}

/**
 * 获取上传配置
 */
const getUploadConfig = async () => {
  try {
    const response = await request<{ code: number; data: UploadConfig }>(
      '/api/v1/user/secured/upload/config',
      { method: 'GET' },
    )
    if (response.code === 200) {
      uploadConfig.value = response.data
      return response.data
    }
  } catch (error) {
    console.error('获取上传配置失败:', error)
  }
  return null
}

/**
 * 选择图片
 */
const chooseImage = () => {
  const maxCount = props.maxCount > 0 ? Math.min(9, props.maxCount - fileList.value.length) : 9

  // #ifdef MP-WEIXIN
  uni.chooseMedia({
    count: maxCount,
    mediaType: ['image'],
    sourceType: ['album', 'camera'],
    success: (res) => {
      handleFileSelect(
        res.tempFiles.map((file, index) => {
          // 从路径提取文件名，如果没有扩展名则添加.jpg
          let fileName = file.tempFilePath.split('/').pop() || 'image.jpg'
          // 检查文件名是否有扩展名，如果没有则添加.jpg
          if (!fileName.includes('.') || fileName.endsWith('.')) {
            fileName = `image_${index}_${Date.now()}.jpg`
          }
          return {
            name: fileName,
            size: file.size,
            type: 'image' as const,
            tempPath: file.tempFilePath,
            status: 'uploading' as const,
            progress: 0,
          }
        }),
      )
    },
    fail: (error) => {
      console.error('选择图片失败:', error)
      uni.showToast({
        title: '选择图片失败',
        icon: 'none',
      })
    },
  })
  // #endif

  // #ifndef MP-WEIXIN
  uni.chooseImage({
    count: maxCount,
    sizeType: ['original', 'compressed'],
    sourceType: ['album', 'camera'],
    success: (res) => {
      const tempFilePaths = Array.isArray(res.tempFilePaths)
        ? res.tempFilePaths
        : [res.tempFilePaths]
      handleFileSelect(
        tempFilePaths.map((path, index) => {
          // 从路径提取文件名，如果没有扩展名则添加.jpg
          let fileName = path.split('/').pop() || `image_${index}.jpg`
          // 检查文件名是否有扩展名，如果没有则添加.jpg
          if (!fileName.includes('.') || fileName.endsWith('.')) {
            fileName = `image_${index}_${Date.now()}.jpg`
          }
          return {
            name: fileName,
            size: res.tempFiles?.[index]?.size || 0,
            type: 'image' as const,
            tempPath: path,
            status: 'uploading' as const,
            progress: 0,
          }
        }),
      )
    },
    fail: (error) => {
      console.error('选择图片失败:', error)
      uni.showToast({
        title: '选择图片失败',
        icon: 'none',
      })
    },
  })
  // #endif
}

/**
 * 选择头像 - 专门为头像上传优化的选择器
 */
const chooseAvatar = () => {
  console.log('chooseAvatar 函数被调用')
  // 头像只能选择1张
  const maxCount = 1

  // #ifdef MP-WEIXIN
  uni.chooseMedia({
    count: maxCount,
    mediaType: ['image'],
    sourceType: ['album', 'camera'],
    // 头像建议使用压缩图片以减少文件大小
    sizeType: ['compressed'],
    success: (res) => {
      handleFileSelect(
        res.tempFiles.map((file, index) => {
          // 头像文件命名
          let fileName = `avatar_${Date.now()}.jpg`
          return {
            name: fileName,
            size: file.size,
            type: 'image' as const,
            tempPath: file.tempFilePath,
            status: 'uploading' as const,
            progress: 0,
          }
        }),
      )
    },
    fail: (error) => {
      console.error('选择头像失败:', error)
      uni.showToast({
        title: '选择头像失败',
        icon: 'none',
      })
    },
  })
  // #endif

  // #ifndef MP-WEIXIN
  uni.chooseImage({
    count: maxCount,
    // 头像建议使用压缩图片
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: (res) => {
      const tempFilePaths = Array.isArray(res.tempFilePaths)
        ? res.tempFilePaths
        : [res.tempFilePaths]
      handleFileSelect(
        tempFilePaths.map((path, index) => {
          // 头像文件命名
          let fileName = `avatar_${Date.now()}.jpg`
          return {
            name: fileName,
            size: res.tempFiles?.[index]?.size || 0,
            type: 'image' as const,
            tempPath: path,
            status: 'uploading' as const,
            progress: 0,
          }
        }),
      )
    },
    fail: (error) => {
      console.error('选择头像失败:', error)
      uni.showToast({
        title: '选择头像失败',
        icon: 'none',
      })
    },
  })
  // #endif
}

/**
 * 选择文件
 */
const chooseFile = () => {
  // #ifndef MP-WEIXIN
  uni.chooseFile({
    count: props.maxCount > 0 ? Math.min(9, props.maxCount - fileList.value.length) : 9,
    type: 'all',
    success: (res) => {
      const tempFiles = Array.isArray(res.tempFiles) ? res.tempFiles : [res.tempFiles]
      handleFileSelect(
        tempFiles.map((file, index) => {
          // 获取文件名，优先使用file.name，其次从路径提取
          let fileName =
            (file as any).name || (file as any).tempFilePath?.split('/').pop() || 'unknown_file'
          // 检查文件名是否有扩展名，如果没有则添加.txt
          if (!fileName.includes('.') || fileName.endsWith('.')) {
            fileName = `file_${index}_${Date.now()}.txt`
          }
          return {
            name: fileName,
            size: file.size || 0,
            type: 'file' as const,
            tempPath: (file as any).path || (file as any).tempFilePath,
            status: 'uploading' as const,
            progress: 0,
          }
        }),
      )
    },
    fail: (error) => {
      console.error('选择文件失败:', error)
      uni.showToast({
        title: '选择文件失败',
        icon: 'none',
      })
    },
  })
  // #endif

  // #ifndef H5
  uni.showToast({
    title: '当前平台不支持文件选择',
    icon: 'none',
  })
  // #endif
}

/**
 * 处理文件选择结果
 */
const handleFileSelect = (selectedFiles: FileItem[]) => {
  // 检查文件大小
  const validFiles = selectedFiles.filter((file) => {
    const sizeInMB = file.size / 1024 / 1024
    if (sizeInMB > props.maxSize) {
      uni.showToast({
        title: `文件 ${file.name} 大小超过 ${props.maxSize}MB`,
        icon: 'none',
      })
      return false
    }
    return true
  })

  if (validFiles.length === 0) return

  // 检查数量限制
  if (props.maxCount > 0) {
    const remainingCount = props.maxCount - fileList.value.length
    if (validFiles.length > remainingCount) {
      validFiles.splice(remainingCount)
      uni.showToast({
        title: `最多只能选择 ${remainingCount} 个文件`,
        icon: 'none',
      })
    }
  }

  // 添加文件到列表
  validFiles.forEach((file) => {
    file.id = Date.now() + Math.random().toString(36).substr(2, 9)
  })

  // 创建新数组避免递归更新
  fileList.value = [...fileList.value, ...validFiles]
  emit('select', validFiles)

  // 自动上传
  if (props.autoUpload) {
    nextTick(() => {
      validFiles.forEach((file) => {
        uploadFile(file)
      })
    })
  }
}

/**
 * 上传单个文件 - 使用新的API接口
 */
const uploadFile = async (file: FileItem) => {
  // 改为 async
  if (!file.tempPath) return

  loading.value = true
  // 使用nextTick确保初始状态正确设置
  await nextTick(() => {
    file.status = 'uploading'
    file.progress = 0
    console.log(
      'Upload Start - 初始化状态:',
      file.name,
      'status:',
      file.status,
      'progress:',
      file.progress,
    )
  })

  // 构建表单数据
  const formDataPayload = {
    // 使用不同的变量名以区分 FormData API
    ...props.formData,
    file_usage: props.formData.file_usage || 'avatar',
    file_type: file.type,
  }

  // 使用uni.uploadFile进行文件上传
  // H5端需要特殊配置以确保multipart/form-data正确发送

  // H5端不设置content-type，让浏览器自动设置multipart/form-data
  // 这样可以确保boundary正确设置
  const token = uni.getStorageSync('token')
  const requestHeaders = new Headers() // 使用 Headers API
  if (token) {
    requestHeaders.append('Authorization', 'Bearer ' + token)
  }
  // 对于 FormData + fetch，不需要手动设置 Content-Type，浏览器会自动处理

  // #ifdef H5
  if (file.tempPath.startsWith('blob:')) {
    let absoluteUploadUrl = props.uploadUrl
    if (!absoluteUploadUrl.startsWith('http')) {
      const baseUrl = getEnvBaseUrl()
      // #ifdef H5
      // Access Vite env variables carefully
      const viteEnv = import.meta.env
      const appProxyEnabled =
        viteEnv && viteEnv.VITE_APP_PROXY
          ? JSON.parse((viteEnv.VITE_APP_PROXY as string) || 'false')
          : false
      const proxyPrefix =
        viteEnv && viteEnv.VITE_APP_PROXY_PREFIX ? (viteEnv.VITE_APP_PROXY_PREFIX as string) : ''

      if (appProxyEnabled) {
        absoluteUploadUrl = proxyPrefix + absoluteUploadUrl
      } else {
        absoluteUploadUrl = baseUrl + absoluteUploadUrl
      }
      // #else
      // For non-H5, or if H5 specific logic isn't hit, assume baseUrl prefixing
      // This part might need adjustment if non-H5 also has proxy logic outside request.ts
      absoluteUploadUrl = baseUrl + absoluteUploadUrl
      // #endif
    }

    console.log('H5 platform: Using fetch API for blob upload.')
    try {
      // 模拟上传开始进度 - 使用nextTick确保响应式更新
      await nextTick(() => {
        file.progress = 10
        console.log('H5 Upload Progress: 10% - 开始上传', file.name, file.progress)
        // 强制触发响应式更新
        const index = fileList.value.findIndex((f) => f.id === file.id)
        if (index !== -1) {
          fileList.value[index] = { ...file }
        }
      })

      const response = await fetch(file.tempPath)
      const blob = await response.blob()

      // 模拟文件准备完成进度
      await nextTick(() => {
        file.progress = 30
        console.log('H5 Upload Progress: 30% - 文件准备完成', file.name, file.progress)
        // 强制触发响应式更新
        const index = fileList.value.findIndex((f) => f.id === file.id)
        if (index !== -1) {
          fileList.value[index] = { ...file }
        }
      })

      const fd = new FormData()
      fd.append('file', blob, file.name) // 'file' 是后端期望的字段名

      // 添加其他表单数据
      for (const key in formDataPayload) {
        if (Object.prototype.hasOwnProperty.call(formDataPayload, key)) {
          fd.append(key, formDataPayload[key])
        }
      }

      // 模拟开始上传进度
      await nextTick(() => {
        file.progress = 50
        console.log('H5 Upload Progress: 50% - 开始上传', file.name, file.progress)
        // 强制触发响应式更新
        const index = fileList.value.findIndex((f) => f.id === file.id)
        if (index !== -1) {
          fileList.value[index] = { ...file }
        }
      })

      console.log('Final Upload URL for fetch:', absoluteUploadUrl)
      const uploadResponse = await fetch(absoluteUploadUrl, {
        method: 'POST',
        headers: requestHeaders, // Authorization header
        body: fd,
        // 注意：fetch API 上传进度监控需要更复杂的处理，这里暂时简化
      })

      // 模拟上传完成等待响应进度
      await nextTick(() => {
        file.progress = 90
        console.log('H5 Upload Progress: 90% - 等待响应', file.name, file.progress)
        // 强制触发响应式更新
        const index = fileList.value.findIndex((f) => f.id === file.id)
        if (index !== -1) {
          fileList.value[index] = { ...file }
        }
      })

      const responseDataText = await uploadResponse.text()
      const responseData: UploadResponse = JSON.parse(responseDataText)

      if (uploadResponse.ok && responseData.code === 200) {
        // 优先使用后端返回的file_url，其次使用url，最后构建URL
        let fileUrl = responseData.data.file_url || responseData.data.url
        if (!fileUrl && responseData.data.file_path) {
          // 如果没有直接的URL，使用file_path构建
          fileUrl = `/uploads/${responseData.data.file_path}`
        }

        // 使用nextTick确保最终进度更新
        await nextTick(() => {
          Object.assign(file, {
            status: 'success',
            url: fileUrl,
            file_id: responseData.data.id,
            file_path: responseData.data.file_path,
            file_type: responseData.data.file_type,
            mime_type: responseData.data.mime_type,
            created_at: responseData.data.created_at,
            progress: 100,
          })
          console.log('H5 Upload Progress: 100% - 上传完成', file.name, file.progress)
          // 强制触发响应式更新
          const index = fileList.value.findIndex((f) => f.id === file.id)
          if (index !== -1) {
            fileList.value[index] = { ...file }
          }
        })
        emit('success', file, responseData)
        uni.showToast({ title: '上传成功', icon: 'success' })
      } else {
        Object.assign(file, { status: 'failed', progress: 0 })
        emit('error', file, responseData)
        uni.showToast({ title: responseData.message || '上传失败', icon: 'none' })
      }
    } catch (error) {
      console.error('H5 fetch upload failed:', error)
      Object.assign(file, { status: 'failed', progress: 0 })
      emit('error', file, error)
      uni.showToast({ title: '上传失败', icon: 'none' })
    } finally {
      loading.value = false
    }
    return // H5 fetch 路径结束
  }
  // #endif

  // 非 H5 或非 blob URL，继续使用 uni.uploadFile
  const uniUploadHeaders: Record<string, string> = {}
  if (token) {
    uniUploadHeaders['Authorization'] = 'Bearer ' + token
  }

  console.log('Using uni.uploadFile:', {
    url: props.uploadUrl,
    filePath: file.tempPath,
    headers: uniUploadHeaders,
    formData: formDataPayload,
  })
  uploadTask.value = uni.uploadFile({
    url: props.uploadUrl,
    filePath: file.tempPath,
    name: 'file',
    formData: formDataPayload, // 确保这里传递的是普通对象
    header: uniUploadHeaders,
    success: (res) => {
      try {
        const response: UploadResponse = JSON.parse(res.data as string)
        if (response.code === 200) {
          // 上传成功 - 使用nextTick避免递归更新
          // 批量更新文件状态
          // 优先使用后端返回的file_url，其次使用url，最后构建URL
          let fileUrl = response.data.file_url || response.data.url
          if (!fileUrl && response.data.file_path) {
            // 如果没有直接的URL，使用file_path构建
            fileUrl = `/uploads/${response.data.file_path}`
          }

          Object.assign(file, {
            status: 'success',
            url: fileUrl,
            file_id: response.data.id,
            file_path: response.data.file_path,
            file_type: response.data.file_type,
            mime_type: response.data.mime_type,
            created_at: response.data.created_at,
            progress: 100,
          })

          emit('success', file, response)
          uni.showToast({
            title: '上传成功',
            icon: 'success',
          })
        } else {
          // 上传失败
          // 批量更新失败状态
          Object.assign(file, {
            status: 'failed',
            progress: 0,
          })

          emit('error', file, response)
          uni.showToast({
            title: response.message || '上传失败',
            icon: 'none',
          })
        }
      } catch (error) {
        console.error('解析上传响应失败:', error)
        // 批量更新错误状态
        Object.assign(file, {
          status: 'failed',
          progress: 0,
        })

        emit('error', file, error)
        uni.showToast({
          title: '上传失败',
          icon: 'none',
        })
      }
      loading.value = false
    },
    fail: (error) => {
      console.error('uni.uploadFile failed:', error)
      // 批量更新失败状态
      Object.assign(file, {
        status: 'failed',
        progress: 0,
      })

      emit('error', file, error)
      uni.showToast({
        title: '上传失败',
        icon: 'none',
      })
      loading.value = false
    },
  })

  // 监听上传进度
  if (uploadTask.value) {
    uploadTask.value.onProgressUpdate((res) => {
      // 直接更新进度
      file.progress = res.progress
      emit('progress', file, res.progress)
    })
  }
}

/**
 * 重试上传
 */
const retryUpload = (file: FileItem, index: number) => {
  if (file.tempPath) {
    uploadFile(file)
  }
}

/**
 * 删除文件 - 调用后端删除API
 */
const removeFile = async (index: number) => {
  const file = fileList.value[index]

  // 如果文件已上传成功且有file_id，调用后端删除API
  if (file.status === 'success' && file.file_id) {
    try {
      const response = await request<{ code: number; message: string }>(
        `/api/v1/user/secured/upload/${file.file_id}`,
        { method: 'DELETE' },
      )

      if (response.code === 200) {
        uni.showToast({
          title: '删除成功',
          icon: 'success',
        })
      } else {
        uni.showToast({
          title: response.message || '删除失败',
          icon: 'none',
        })
        return // 删除失败，不移除本地文件
      }
    } catch (error) {
      console.error('删除文件失败:', error)
      uni.showToast({
        title: '删除失败',
        icon: 'none',
      })
      return // 删除失败，不移除本地文件
    }
  }

  // 从本地列表中移除
  fileList.value.splice(index, 1)
  emit('remove', file, index)
}

/**
 * 预览图片
 */
const previewImage = (file: FileItem, index: number) => {
  if (file.status !== 'success' || !file.url) return

  const urls = fileList.value
    .filter((f) => f.type === 'image' && f.status === 'success' && f.url)
    .map((f) => f.url!)

  const current = file.url

  uni.previewImage({
    urls,
    current,
  })

  emit('preview', file, index)
}

/**
 * 预览文件
 */
const previewFile = (file: FileItem, index: number) => {
  if (file.status !== 'success' || !file.url) return

  // 可以根据文件类型进行不同的预览处理
  uni.showModal({
    title: '文件预览',
    content: `文件名：${file.name}\n大小：${formatFileSize(file.size)}\n地址：${file.url}`,
    showCancel: false,
  })

  emit('preview', file, index)
}

/**
 * 获取文件图标
 */
const getFileIcon = (fileName: string): string => {
  const ext = fileName.split('.').pop()?.toLowerCase()
  const iconMap: Record<string, string> = {
    pdf: 'icon-pdf',
    doc: 'icon-word',
    docx: 'icon-word',
    xls: 'icon-excel',
    xlsx: 'icon-excel',
    ppt: 'icon-ppt',
    pptx: 'icon-ppt',
    txt: 'icon-txt',
    zip: 'icon-zip',
    rar: 'icon-zip',
  }
  return iconMap[ext || ''] || 'icon-file'
}

/**
 * 格式化文件大小
 */
const formatFileSize = (size: number): string => {
  if (size < 1024) {
    return `${size}B`
  } else if (size < 1024 * 1024) {
    return `${(size / 1024).toFixed(1)}KB`
  } else {
    return `${(size / 1024 / 1024).toFixed(1)}MB`
  }
}

/**
 * 获取文件列表 - 调用后端API
 */
const getFileList = async (page = 1, limit = 20) => {
  try {
    const response = await request<{
      code: number
      data: {
        list: FileItem[]
        total: number
        page: number
        limit: number
      }
    }>('/api/v1/user/secured/upload/list', {
      method: 'GET',
      data: { page, limit },
    })

    if (response.code === 200) {
      return response.data
    }
  } catch (error) {
    console.error('获取文件列表失败:', error)
  }
  return null
}

/**
 * 获取文件信息 - 调用后端API
 */
const getFileInfo = async (fileId: string | number) => {
  try {
    const response = await request<{
      code: number
      data: FileItem
    }>(`/api/v1/user/secured/upload/${fileId}`, {
      method: 'GET',
    })

    if (response.code === 200) {
      return response.data
    }
  } catch (error) {
    console.error('获取文件信息失败:', error)
  }
  return null
}

// 组件挂载时获取上传配置
onMounted(() => {
  getUploadConfig()
})

// 暴露方法给外部使用
defineExpose({
  /** 手动触发上传 */
  upload: handleUpload,
  /** 手动上传指定文件 */
  uploadFile,
  /** 清空文件列表 */
  clear: () => {
    fileList.value = []
  },
  /** 获取文件列表 */
  getFileList: () => fileList.value,
  /** 获取成功上传的文件列表 */
  getSuccessFiles: () => fileList.value.filter((f) => f.status === 'success'),
  /** 获取上传配置 */
  getUploadConfig,
  /** 获取远程文件列表 */
  getRemoteFileList: getFileList,
  /** 获取文件信息 */
  getFileInfo,
  /** 取消上传 */
  cancelUpload: () => {
    if (uploadTask.value) {
      uploadTask.value.abort()
      uploadTask.value = null
    }
  },
})
</script>

<style lang="scss" scoped>
.file-upload {
  width: 100%;

  .upload-trigger {
    margin-bottom: 20rpx;

    .default-trigger {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 200rpx;
      height: 200rpx;
      border: 2rpx dashed #dcdfe6;
      border-radius: 12rpx;
      background-color: #fafafa;
      transition: all 0.3s;

      &:not(.disabled):active {
        background-color: #f0f0f0;
        border-color: #409eff;
      }

      &.disabled {
        opacity: 0.5;
        pointer-events: none;
      }

      .iconfont {
        font-size: 48rpx;
        color: #8c939d;
        margin-bottom: 16rpx;
      }

      .trigger-text {
        font-size: 28rpx;
        color: #8c939d;
      }
    }
  }

  .file-list {
    display: flex;
    flex-wrap: wrap;
    gap: 20rpx;

    .file-item {
      position: relative;
      border-radius: 12rpx;
      overflow: hidden;
      background-color: #fff;
      box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);

      &.file-image {
        width: 200rpx;
        height: 200rpx;
      }

      &.file-file {
        width: 100%;
        padding: 24rpx;
      }

      .image-preview {
        position: relative;
        width: 100%;
        height: 100%;

        .preview-image {
          width: 100%;
          height: 100%;
        }

        .upload-mask {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: rgba(0, 0, 0, 0.6);
          display: flex;
          align-items: center;
          justify-content: center;

          .progress-circle {
            width: 80rpx;
            height: 80rpx;
            border-radius: 50%;
            border: 4rpx solid rgba(255, 255, 255, 0.3);
            border-top-color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: rotate 1s linear infinite;

            .progress-text {
              font-size: 24rpx;
              color: #fff;
              font-weight: bold;
            }
          }
        }

        .failed-mask {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: rgba(0, 0, 0, 0.6);
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;

          .iconfont {
            font-size: 48rpx;
            color: #fff;
            margin-bottom: 8rpx;
          }

          .failed-text {
            font-size: 24rpx;
            color: #fff;
          }
        }
      }

      .file-info {
        display: flex;
        align-items: center;

        .file-icon {
          width: 80rpx;
          height: 80rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: #f5f7fa;
          border-radius: 8rpx;
          margin-right: 24rpx;

          .iconfont {
            font-size: 48rpx;
            color: #606266;
          }
        }

        .file-details {
          flex: 1;
          min-width: 0;

          .file-name {
            display: block;
            font-size: 32rpx;
            color: #303133;
            font-weight: 500;
            margin-bottom: 8rpx;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .file-size {
            display: block;
            font-size: 24rpx;
            color: #909399;
            margin-bottom: 8rpx;
          }

          .upload-progress {
            display: flex;
            align-items: center;
            gap: 16rpx;

            .progress-bar {
              flex: 1;
              height: 8rpx;
              background-color: #f0f0f0;
              border-radius: 4rpx;
              overflow: hidden;

              .progress-fill {
                height: 100%;
                background-color: #409eff;
                transition: width 0.3s;
              }
            }

            .progress-text {
              font-size: 24rpx;
              color: #409eff;
              min-width: 80rpx;
            }
          }

          .upload-failed {
            display: flex;
            align-items: center;
            gap: 16rpx;

            .failed-text {
              font-size: 24rpx;
              color: #f56c6c;
            }

            .retry-btn {
              font-size: 24rpx;
              color: #409eff;
              padding: 8rpx 16rpx;
              border: 1rpx solid #409eff;
              border-radius: 4rpx;
            }
          }
        }
      }

      .delete-btn {
        position: absolute;
        top: -12rpx;
        right: -12rpx;
        width: 48rpx;
        height: 48rpx;
        background-color: #f56c6c;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);

        .iconfont {
          font-size: 24rpx;
          color: #fff;
        }
      }
    }
  }

  .upload-tips {
    margin-top: 20rpx;

    .tips-text {
      font-size: 24rpx;
      color: #909399;
      line-height: 1.5;
    }
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
