<template>
  <!-- 历史记录追踪组件，无UI，仅用于记录用户行为 -->
</template>

<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue'
import { addHistory } from '@/api/user'
import { useUserStore } from '@/store/user'
import type { HistoryType, IAddHistoryRequest } from '@/api/user.typings'

// Props
interface Props {
  type: HistoryType
  targetId: number
  targetName: string
  targetImage?: string
  extraData?: Record<string, any>
  source?: string
  autoTrack?: boolean // 是否自动追踪（页面进入时立即记录）
  trackOnLeave?: boolean // 是否在页面离开时记录停留时长
}

const props = withDefaults(defineProps<Props>(), {
  autoTrack: true,
  trackOnLeave: true,
})

// Store
const userStore = useUserStore()

// 变量
let startTime: number = 0
let tracked = false

// 方法
const trackHistory = async (duration?: number) => {
  // 检查用户是否已登录
  if (!userStore.isLoggedIn) {
    return
  }

  // 避免重复记录
  if (tracked && !duration) {
    return
  }

  try {
    const request: IAddHistoryRequest = {
      type: props.type,
      target_id: props.targetId,
      target_name: props.targetName,
      target_image: props.targetImage,
      extra_data: props.extraData,
      user_agent: navigator.userAgent,
      platform: getPlatform(),
      source: props.source || 'direct',
      duration: duration || 0,
    }

    await addHistory(request)
    tracked = true
  } catch (error) {
    console.warn('记录历史失败:', error)
  }
}

const trackWithDuration = () => {
  if (startTime > 0) {
    const duration = Math.floor((Date.now() - startTime) / 1000)
    trackHistory(duration)
  }
}

const getPlatform = (): string => {
  // #ifdef H5
  return 'h5'
  // #endif

  // #ifdef MP-WEIXIN
  return 'weixin'
  // #endif

  // #ifdef MP-ALIPAY
  return 'alipay'
  // #endif

  // #ifdef APP-PLUS
  return 'app'
  // #endif

  return 'unknown'
}

// 页面可见性变化处理
const handleVisibilityChange = () => {
  if (document.hidden) {
    // 页面隐藏时记录停留时长
    if (props.trackOnLeave) {
      trackWithDuration()
    }
  } else {
    // 页面显示时重新开始计时
    startTime = Date.now()
  }
}

// 页面卸载处理
const handleBeforeUnload = () => {
  if (props.trackOnLeave) {
    trackWithDuration()
  }
}

// 生命周期
onMounted(() => {
  startTime = Date.now()

  // 自动追踪
  if (props.autoTrack) {
    trackHistory()
  }

  // 监听页面可见性变化
  if (typeof document !== 'undefined') {
    document.addEventListener('visibilitychange', handleVisibilityChange)
    window.addEventListener('beforeunload', handleBeforeUnload)
  }
})

onUnmounted(() => {
  // 页面卸载时记录停留时长
  if (props.trackOnLeave) {
    trackWithDuration()
  }

  // 移除事件监听
  if (typeof document !== 'undefined') {
    document.removeEventListener('visibilitychange', handleVisibilityChange)
    window.removeEventListener('beforeunload', handleBeforeUnload)
  }
})

// 暴露方法给父组件
defineExpose({
  track: trackHistory,
  trackWithDuration,
})
</script>

<style scoped>
/* 无样式，这是一个功能组件 */
</style>
