<!--
 * 商家卡片组件
 * 
 * 功能特性：
 * - 商家基本信息展示：名称、Logo、营业状态
 * - 评分和销量展示：星级评分、月销量
 * - 配送信息展示：配送时间、距离、配送费
 * - 起送金额和分类信息
 * - 促销活动信息展示
 * - 支持点击事件和自定义样式
 * - 响应式设计，适配移动端
-->
<template>
  <view 
    class="merchant-card" 
    :class="customClass"
    @click="handleClick"
  >
    <!-- 商家Logo -->
    <view class="merchant-logo">
      <image :src="merchant.logo" mode="aspectFill" />
      <view v-if="merchant.operation_status !== 1" class="closed-overlay">
        <text class="closed-text">休息中</text>
      </view>
    </view>

    <!-- 商家信息 -->
    <view class="merchant-info">
      <!-- 商家名称和状态 -->
      <view class="merchant-header">
        <view class="merchant-name">{{ merchant.name }}</view>
        <view
          class="merchant-status"
          :class="{
            open: merchant.operation_status === 1,
            closed: merchant.operation_status !== 1,
          }"
        >
          {{ merchant.operation_status === 1 ? '营业中' : '休息中' }}
        </view>
      </view>

      <!-- 评分和销量 -->
      <view class="merchant-rating">
        <wd-rate :value="merchant.rating" size="14" readonly />
        <text class="rating-text">{{ merchant.rating.toFixed(1) }}</text>
        <text class="sales-text">月售{{ merchant.month_sales }}</text>
      </view>

      <!-- 配送信息 -->
      <view class="merchant-delivery">
        <view class="delivery-item">
          <wd-icon name="calendar" size="12" color="#666" />
          <text>{{ merchant.delivery_time }}分钟</text>
        </view>
        <view class="delivery-item" v-if="distance">
          <wd-icon name="location" size="12" color="#666" />
          <text>{{ distance }}</text>
        </view>
        <view class="delivery-item">
          <wd-icon name="wallet" size="12" color="#666" />
          <text>配送费¥{{ merchant.delivery_fee.toFixed(2) }}</text>
        </view>
      </view>

      <!-- 起送金额和分类 -->
      <view class="merchant-meta">
        <text class="min-amount">起送¥{{ merchant.min_order_amount }}</text>
        <text class="category">{{ merchant.category_name }}</text>
      </view>

      <!-- 促销信息 -->
      <view v-if="merchant.promotion_info" class="merchant-promotion">
        <wd-icon name="gift" size="12" color="#ff5500" />
        <text>{{ merchant.promotion_info }}</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
/**
 * 商家卡片组件逻辑
 *
 * Props:
 * - merchant: 商家信息对象
 * - distance: 距离信息（可选）
 * - customClass: 自定义样式类（可选）
 *
 * Events:
 * - click: 点击事件，传递商家ID
 */

import type { Merchant } from '@/api/takeout.typings'

// Props定义
interface Props {
  /** 商家信息 */
  merchant: Merchant
  /** 距离信息 */
  distance?: string
  /** 自定义样式类 */
  customClass?: string
}

// Emits定义
interface Emits {
  /** 点击事件 */
  click: [merchantId: number]
}

const props = withDefaults(defineProps<Props>(), {
  distance: '',
  customClass: ''
})

const emit = defineEmits<Emits>()

/**
 * 处理卡片点击事件
 */
function handleClick() {
  emit('click', props.merchant.id)
}
</script>

<style lang="scss" scoped>
// 商家卡片
.merchant-card {
  display: flex;
  margin-bottom: 12px;
  padding: 16px;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s;

  &:active {
    transform: scale(0.98);
  }

  .merchant-logo {
    position: relative;
    width: 80px;
    height: 80px;
    margin-right: 12px;
    border-radius: 8px;
    overflow: hidden;

    image {
      width: 100%;
      height: 100%;
    }

    .closed-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: rgba(0, 0, 0, 0.6);

      .closed-text {
        font-size: 12px;
        color: #fff;
        font-weight: 500;
      }
    }
  }

  .merchant-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .merchant-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 6px;

      .merchant-name {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .merchant-status {
        padding: 2px 8px;
        font-size: 11px;
        border-radius: 8px;
        font-weight: 500;

        &.open {
          color: #52c41a;
          background-color: #f6ffed;
        }

        &.closed {
          color: #ff4d4f;
          background-color: #fff2f0;
        }
      }
    }

    .merchant-rating {
      display: flex;
      align-items: center;
      margin-bottom: 6px;

      .rating-text {
        margin-left: 4px;
        font-size: 12px;
        color: #ff9500;
        font-weight: 500;
      }

      .sales-text {
        margin-left: 8px;
        font-size: 12px;
        color: #999;
      }
    }

    .merchant-delivery {
      display: flex;
      align-items: center;
      margin-bottom: 6px;

      .delivery-item {
        display: flex;
        align-items: center;
        margin-right: 12px;
        font-size: 12px;
        color: #666;

        text {
          margin-left: 2px;
        }
      }
    }

    .merchant-meta {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 6px;

      .min-amount {
        font-size: 12px;
        color: #ff5500;
        font-weight: 500;
      }

      .category {
        font-size: 12px;
        color: #999;
      }
    }

    .merchant-promotion {
      display: flex;
      align-items: center;
      padding: 4px 8px;
      background-color: #fff0e6;
      border-radius: 4px;

      text {
        margin-left: 4px;
        font-size: 11px;
        color: #ff5500;
        font-weight: 500;
      }
    }
  }
}
</style>