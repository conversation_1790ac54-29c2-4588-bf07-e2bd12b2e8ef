<!--
 * 外卖商品详情组件
 * 
 * 功能特性：
 * - 支持弹窗和页面两种显示模式
 * - 商品信息展示：图片、名称、价格、描述等
 * - 规格选择：支持多规格商品的规格选择和数量控制
 * - 套餐选择：支持套餐商品的选项选择
 * - 购物车集成：与购物车状态同步，支持添加到购物车
 * - 收藏功能：支持商品收藏/取消收藏
 * - 响应式设计：适配移动端和桌面端
-->

<template>
  <view class="takeout-food-detail" :class="{ 'is-popup': isPopup, 'is-page': !isPopup }">
    <!-- 页面模式的导航栏 -->
    <wd-navbar v-if="!isPopup" :title="food?.name || '商品详情'" />

    <!-- 弹窗模式的头部 -->
    <view v-if="isPopup" class="popup-header">
      <text class="popup-title">商品详情</text>
      <view class="close-btn" @click="handleClose">
        <wd-icon name="close" size="20" color="#999" />
      </view>
    </view>

    <!-- 商品详情内容 -->
    <view
      v-if="food"
      class="content"
      :class="{ 'popup-content': isPopup, 'page-content': !isPopup }"
    >
      <!-- 商品图片 -->
      <view class="food-detail-image">
        <image :src="food.image" mode="aspectFill" />
      </view>

      <!-- 商品信息 -->
      <view class="food-detail-info">
        <view class="food-detail-name">{{ food.name }}</view>
        <view v-if="food.brief || food.description" class="food-detail-description">
          {{ food.brief || food.description }}
        </view>
        <view class="food-detail-price">
          <text class="current-price">¥{{ food.price.toFixed(2) }}</text>
          <text v-if="food.packaging_fee > 0" class="packaging-fee">
            +¥{{ food.packaging_fee.toFixed(2) }}
          </text>
          <text
            v-if="food.original_price && food.original_price > food.price"
            class="original-price"
          >
            ¥{{ food.original_price.toFixed(2) }}
          </text>
        </view>

        <!-- 商品标签 -->
        <view v-if="food.tags && food.tags.length > 0" class="food-tags">
          <text v-for="tag in food.tags" :key="tag" class="tag">{{ tag }}</text>
        </view>

        <!-- 销量和评分 -->
        <view class="food-stats">
          <text class="stat-item">月销 {{ food.total_sold || 0 }}</text>
          <text v-if="food.rating" class="stat-item">好评率 {{ food.rating }}%</text>
        </view>
      </view>

      <!-- 收藏按钮 -->
      <view v-if="!isPopup" class="favorite-section">
        <FavoriteButton
          :type="FavoriteType.TAKEOUT_FOOD"
          :target-id="food.id"
          :target-name="food.name"
          :target-image="food.image"
          :extra-data="{
            price: food.price,
            original_price: food.original_price,
            merchant_id: food.merchant_id,
            category_id: food.category_id,
          }"
          :size="24"
          show-text
        />
      </view>

      <!-- 规格选择 -->
      <view v-if="food.variants && food.variants.length > 0" class="variant-selection">
        <view class="section-title">选择规格</view>
        <view class="variants-list" :class="{ 'popup-variants-list': isPopup }">
          <view
            v-for="variant in food.variants"
            :key="variant.id"
            class="variant-item"
            :class="{ 'popup-variant-item': isPopup }"
          >
            <view class="variant-info" :class="{ 'popup-variant-info': isPopup }">
              <view class="variant-name" :class="{ 'popup-variant-name': isPopup }">
                {{ variant.name }}
              </view>
              <view class="variant-price" :class="{ 'popup-variant-price': isPopup }">
                <text class="current-price">¥{{ variant.price.toFixed(2) }}</text>
                <text v-if="food.packaging_fee > 0" class="packaging-fee">
                  +¥{{ food.packaging_fee.toFixed(2) }}
                </text>
                <text
                  v-if="variant.original_price && variant.original_price > variant.price"
                  class="original-price"
                >
                  ¥{{ variant.original_price.toFixed(2) }}
                </text>
              </view>
            </view>

            <!-- 规格的数量控制 -->
            <view class="variant-control" :class="{ 'popup-variant-control': isPopup }">
              <view v-if="getVariantQuantity(variant.id) > 0" class="quantity-control">
                <view class="quantity-btn" @click.stop="decreaseVariantQuantity(variant)">
                  <wd-icon name="remove" size="14" color="#ff5500" />
                </view>
                <text class="quantity">{{ getVariantQuantity(variant.id) }}</text>
                <view class="quantity-btn" @click.stop="increaseVariantQuantity(variant)">
                  <wd-icon name="add" size="14" color="#ff5500" />
                </view>
              </view>
              <view v-else class="add-btn" @click.stop="addVariantToCart(variant)">
                <wd-icon name="add" size="16" color="#fff" />
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 套餐选择 -->
      <view
        v-if="food.is_combination && food.combos && food.combos.length > 0"
        class="combo-selection"
      >
        <view class="section-title">选择套餐</view>
        <view class="combo-options">
          <view v-for="combo in food.combos" :key="combo.id" class="combo-option">
            <view class="combo-header">
              <text class="combo-name">{{ combo.name }}</text>
              <text class="combo-required">{{ combo.is_required ? '必选' : '可选' }}</text>
            </view>
            <view class="combo-items">
              <view
                v-for="option in combo.options"
                :key="option.id"
                class="combo-item"
                :class="{ active: isComboOptionSelected(combo.id, option.id) }"
                @click="toggleComboOption(combo.id, option.id, combo.is_required)"
              >
                <text class="item-name">{{ option.name }}</text>
                <text class="item-price">+¥{{ option.extra_price.toFixed(2) }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 数量选择 - 单规格商品显示 -->
      <view v-if="!food.has_variants || food.variants?.length === 0" class="quantity-selection">
        <view class="section-title">数量</view>
        <view class="quantity-control">
          <view class="quantity-btn" @click="decreaseQuantity">
            <wd-icon name="remove" size="16" color="#ff5500" />
          </view>
          <text class="quantity">{{ quantity }}</text>
          <view class="quantity-btn" @click="increaseQuantity">
            <wd-icon name="add" size="16" color="#ff5500" />
          </view>
        </view>
      </view>

      <!-- 多规格商品总数量显示 -->
      <view v-if="food.has_variants && food.variants?.length > 0" class="variants-quantity-display">
        <view class="section-title">合计数量：{{ getTotalVariantsQuantity() }}</view>
      </view>

      <!-- 商品详情图片 -->
      <view
        v-if="!isPopup && food.detail_images && food.detail_images.length > 0"
        class="detail-images-section"
      >
        <view class="section-title">商品详情</view>
        <view class="detail-images">
          <image
            v-for="(image, index) in food.detail_images"
            :key="index"
            :src="image"
            mode="widthFix"
            class="detail-image"
          />
        </view>
      </view>
    </view>

    <!-- 底部操作 - 仅对单规格商品或有套餐的商品显示 -->
    <view
      v-if="food && (!food.has_variants || food.variants?.length === 0 || food.is_combination)"
      class="footer"
      :class="{ 'popup-footer': isPopup, 'page-footer': !isPopup }"
    >
      <view class="total-price">
        <text>总计：¥{{ getTotalPrice().toFixed(2) }}</text>
      </view>
      <view v-if="!isPopup" class="action-buttons">
        <view class="add-to-cart-btn" @click="handleAddToCart">
          <text>加入购物车</text>
        </view>
      </view>
    </view>

    <!-- 多规格商品总计显示 -->
    <view
      v-if="food && food.has_variants && food.variants?.length > 0 && !food.is_combination"
      class="variants-total"
      :class="{ 'popup-variants-total': isPopup, 'page-variants-total': !isPopup }"
    >
      <view class="variants-total-info">
        <text class="total-label">当前商品总计：</text>
        <text class="total-amount">¥{{ getVariantsTotalPrice().toFixed(2) }}</text>
      </view>
      <view class="variants-tip">
        <text>包含商品价格和打包费</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
/**
 * 外卖商品详情组件逻辑
 *
 * 主要功能：
 * - 商品信息展示和交互
 * - 规格选择和数量控制
 * - 套餐选择和配置
 * - 购物车集成和状态同步
 * - 支持弹窗和页面两种模式
 */

import { ref, computed, watch, onMounted, nextTick } from 'vue'
import { useTakeoutStore } from '@/store/takeout'
import FavoriteButton from '@/components/FavoriteButton.vue'
import { FavoriteType } from '@/api/user.typings'
import type { ITakeoutFood } from '@/api/takeout.typings'

// Props定义
interface Props {
  /** 商品数据 */
  food: ITakeoutFood | null
  /** 是否为弹窗模式 */
  isPopup?: boolean
  /** 初始数量 */
  initialQuantity?: number
}

const props = withDefaults(defineProps<Props>(), {
  isPopup: false,
  initialQuantity: 1,
})

// Emits定义
interface Emits {
  /** 关闭事件 */
  (e: 'close'): void
  /** 添加到购物车事件 */
  (
    e: 'add-to-cart',
    data: {
      food: ITakeoutFood
      quantity: number
      variantId?: number
      comboSelections?: Record<number, number[]>
    },
  ): void
}

const emit = defineEmits<Emits>()

// Store
const takeoutStore = useTakeoutStore()

// 响应式数据
const quantity = ref(props.initialQuantity)
const comboSelections = ref<Record<number, number[]>>({})

// 计算属性
const variantQuantities = computed(() => {
  if (!props.food?.variants) return {}
  const quantities: Record<number, number> = {}

  // 确保依赖于购物车数据的变化
  const cartItems = takeoutStore.cart?.items || []

  console.log('🔄 [Component] 重新计算规格数量:', {
    foodId: props.food.id,
    cartItemsCount: cartItems.length,
    variantsCount: props.food.variants.length,
  })

  props.food.variants.forEach((variant) => {
    const item = cartItems.find(
      (item) => item.food_id === props.food!.id && item.variant_id === variant.id,
    )
    quantities[variant.id] = item ? item.quantity : 0

    console.log(`📊 [Component] 规格 ${variant.id} (${variant.name}) 数量:`, quantities[variant.id])
  })

  return quantities
})

// 方法
const handleClose = () => {
  emit('close')
}

const getVariantQuantity = (variantId: number): number => {
  return variantQuantities.value[variantId] || 0
}

const getTotalVariantsQuantity = (): number => {
  return Object.values(variantQuantities.value).reduce((total, qty) => total + qty, 0)
}

const increaseVariantQuantity = async (variant: any) => {
  if (!props.food) return

  try {
    console.log('🔄 [Component] 开始增加规格数量:', {
      foodId: props.food.id,
      variantId: variant.id,
    })

    const currentQuantity = getVariantQuantity(variant.id)
    console.log('📊 [Component] 当前数量:', currentQuantity)

    if (currentQuantity > 0) {
      // 购物车中已有该商品，使用更新方法
      console.log('🔄 [Component] 购物车中已有该商品，使用更新方法')
      await takeoutStore.updateCartItemQuantity(props.food.id, variant.id, currentQuantity + 1)
    } else {
      // 购物车中没有该商品，使用添加方法
      console.log('➕ [Component] 购物车中没有该商品，使用添加方法')
      await takeoutStore.addToCart({
        food_id: props.food.id,
        variant_id: variant.id,
        quantity: 1,
        remark: '',
        combo_selections: [],
      })
    }

    // 等待下一个tick确保响应式更新完成
    await nextTick()

    console.log('✅ [Component] 规格数量增加成功，当前购物车状态:', {
      cartItems: takeoutStore.cart?.items?.length,
      currentQuantity: getVariantQuantity(variant.id),
    })
  } catch (error) {
    console.error('❌ [Component] 增加规格数量失败:', error)
  }
}

const decreaseVariantQuantity = async (variant: any) => {
  if (!props.food) return

  try {
    const currentQuantity = getVariantQuantity(variant.id)
    console.log('🔄 [Component] 开始减少规格数量:', {
      foodId: props.food.id,
      variantId: variant.id,
      currentQuantity,
    })

    if (currentQuantity > 0) {
      await takeoutStore.updateCartItemQuantity(props.food.id, variant.id, currentQuantity - 1)

      // 等待下一个tick确保响应式更新完成
      await nextTick()

      console.log('✅ [Component] 规格数量减少成功，当前购物车状态:', {
        cartItems: takeoutStore.cart?.items?.length,
        newQuantity: getVariantQuantity(variant.id),
      })
    }
  } catch (error) {
    console.error('❌ [Component] 减少规格数量失败:', error)
  }
}

const addVariantToCart = async (variant: any) => {
  await increaseVariantQuantity(variant)
}

const increaseQuantity = async () => {
  if (!props.food) return

  try {
    console.log('🔄 [Component] 开始增加单规格商品数量:', { foodId: props.food.id })

    // 直接从购物车数据中获取当前数量
    const cartItems = takeoutStore.cart?.items || []
    const item = cartItems.find(
      (item) => item.food_id === props.food.id && (!item.variant_id || item.variant_id === 0),
    )
    const currentQuantity = item ? item.quantity : 0

    console.log('📊 [Component] 当前购物车数量:', currentQuantity)

    if (currentQuantity > 0) {
      // 购物车中已有该商品，使用更新方法
      console.log('🔄 [Component] 购物车中已有该商品，使用更新方法')
      await takeoutStore.updateCartItemQuantity(props.food.id, 0, currentQuantity + 1)
    } else {
      // 购物车中没有该商品，使用添加方法
      console.log('➕ [Component] 购物车中没有该商品，使用添加方法')
      await takeoutStore.addToCart({
        food_id: props.food.id,
        variant_id: 0,
        quantity: 1,
        remark: '',
        combo_selections: [],
      })
    }

    // 等待响应式更新
    await nextTick()

    console.log('✅ [Component] 单规格商品数量增加成功')
  } catch (error) {
    console.error('❌ [Component] 增加单规格商品数量失败:', error)
  }
}

const decreaseQuantity = async () => {
  if (!props.food) return

  try {
    console.log('🔄 [Component] 开始减少单规格商品数量:', { foodId: props.food.id })

    // 直接从购物车数据中获取当前数量
    const cartItems = takeoutStore.cart?.items || []
    const item = cartItems.find(
      (item) => item.food_id === props.food.id && (!item.variant_id || item.variant_id === 0),
    )
    const currentQuantity = item ? item.quantity : 0

    console.log('📊 [Component] 当前购物车数量:', currentQuantity)

    if (currentQuantity > 1) {
      // 减少数量
      console.log('🔄 [Component] 减少商品数量')
      await takeoutStore.updateCartItemQuantity(props.food.id, 0, currentQuantity - 1)
    } else if (currentQuantity === 1) {
      // 删除商品
      console.log('🗑️ [Component] 删除商品')
      await takeoutStore.updateCartItemQuantity(props.food.id, 0, 0)
    }

    // 等待响应式更新
    await nextTick()

    console.log('✅ [Component] 单规格商品数量减少成功')
  } catch (error) {
    console.error('❌ [Component] 减少单规格商品数量失败:', error)
  }
}

const isComboOptionSelected = (comboId: number, optionId: number): boolean => {
  return comboSelections.value[comboId]?.includes(optionId) || false
}

const toggleComboOption = (comboId: number, optionId: number, isRequired: boolean) => {
  if (!comboSelections.value[comboId]) {
    comboSelections.value[comboId] = []
  }

  const selections = comboSelections.value[comboId]
  const index = selections.indexOf(optionId)

  if (index > -1) {
    selections.splice(index, 1)
  } else {
    if (isRequired) {
      // 必选项只能选一个
      comboSelections.value[comboId] = [optionId]
    } else {
      selections.push(optionId)
    }
  }
}

const getTotalPrice = (): number => {
  if (!props.food) return 0

  let total = props.food.price * quantity.value

  // 添加套餐价格
  Object.entries(comboSelections.value).forEach(([comboId, optionIds]) => {
    const combo = props.food?.combos?.find((c) => c.id === parseInt(comboId))
    if (combo) {
      optionIds.forEach((optionId) => {
        const option = combo.options?.find((o) => o.id === optionId)
        if (option) {
          total += option.extra_price * quantity.value
        }
      })
    }
  })

  // 添加打包费
  total += (props.food.packaging_fee || 0) * quantity.value

  return total
}

const getVariantsTotalPrice = (): number => {
  if (!props.food?.variants) return 0

  let total = 0
  props.food.variants.forEach((variant) => {
    const qty = getVariantQuantity(variant.id)
    if (qty > 0) {
      total += variant.price * qty
      total += (props.food!.packaging_fee || 0) * qty
    }
  })

  return total
}

const handleAddToCart = () => {
  if (!props.food) return

  emit('add-to-cart', {
    food: props.food,
    quantity: quantity.value,
    comboSelections: comboSelections.value,
  })
}

// 监听购物车变化，同步数量
watch(
  () => takeoutStore.cart?.items,
  () => {
    if (props.food && !props.food.has_variants) {
      // 直接从购物车数据中查找
      const cartItems = takeoutStore.cart?.items || []
      const item = cartItems.find(
        (item) => item.food_id === props.food!.id && (!item.variant_id || item.variant_id === 0),
      )
      // 如果购物车中有该商品，同步数量；否则保持当前数量
      if (item) {
        quantity.value = item.quantity
      }

      console.log('🔄 [Component] 单规格商品数量同步:', {
        cartQuantity: item?.quantity || 0,
        displayQuantity: quantity.value,
      })
    }
  },
  { deep: true },
)

// 组件挂载时初始化
onMounted(() => {
  if (props.food && !props.food.has_variants) {
    // 直接从购物车数据中查找
    const cartItems = takeoutStore.cart?.items || []
    const item = cartItems.find(
      (item) => item.food_id === props.food!.id && (!item.variant_id || item.variant_id === 0),
    )
    // 如果购物车中有该商品，使用购物车数量；否则使用初始数量
    quantity.value = item ? item.quantity : props.initialQuantity

    console.log('🔄 [Component] 单规格商品初始化:', {
      cartQuantity: item?.quantity || 0,
      initialQuantity: props.initialQuantity,
      finalQuantity: quantity.value,
    })
  }
})
</script>

<style lang="scss" scoped>
.takeout-food-detail {
  background-color: #fff;

  &.is-popup {
    max-height: 70vh;
    margin-bottom: env(safe-area-inset-bottom);
  }

  &.is-page {
    min-height: 100vh;
  }
}

// 弹窗头部
.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;

  .popup-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
  }

  .close-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: #f5f7fa;
  }
}

// 内容区域
.content {
  &.popup-content {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
  }

  &.page-content {
    padding: 20px;
    padding-bottom: 100px; // 为底部操作栏留空间
  }
}

// 商品图片
.food-detail-image {
  width: 100%;
  height: 200px;
  margin-bottom: 16px;
  border-radius: 8px;
  overflow: hidden;

  image {
    width: 100%;
    height: 100%;
  }
}

// 商品信息
.food-detail-info {
  margin-bottom: 20px;

  .food-detail-name {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
  }

  .food-detail-description {
    font-size: 14px;
    color: #666;
    line-height: 1.5;
    margin-bottom: 12px;
  }

  .food-detail-price {
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    .current-price {
      font-size: 20px;
      font-weight: 600;
      color: #ff5500;
    }

    .packaging-fee {
      font-size: 16px;
      font-weight: 500;
      color: #666;
      margin-left: 2px;
    }

    .original-price {
      margin-left: 8px;
      font-size: 14px;
      color: #999;
      text-decoration: line-through;
    }
  }

  .food-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin-bottom: 12px;

    .tag {
      padding: 2px 8px;
      font-size: 12px;
      color: #ff5500;
      background-color: #fff0e6;
      border-radius: 4px;
    }
  }

  .food-stats {
    display: flex;
    gap: 16px;

    .stat-item {
      font-size: 14px;
      color: #666;
    }
  }
}

// 收藏区域
.favorite-section {
  margin-bottom: 20px;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 8px;
}

// 通用区域样式
.variant-selection,
.combo-selection,
.quantity-selection,
.variants-quantity-display,
.detail-images-section {
  margin-bottom: 20px;

  .section-title {
    font-size: 15px;
    font-weight: 500;
    color: #333;
    margin-bottom: 12px;
  }
}

// 规格选择
.variants-list {
  .variant-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    margin-bottom: 8px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    background-color: #fafafa;

    .variant-info {
      flex: 1;

      .variant-name {
        font-size: 14px;
        font-weight: 500;
        color: #333;
        margin-bottom: 4px;
      }

      .variant-price {
        display: flex;
        align-items: center;

        .current-price {
          font-size: 16px;
          font-weight: 600;
          color: #ff5500;
        }

        .packaging-fee {
          font-size: 14px;
          color: #666;
          margin-left: 2px;
        }

        .original-price {
          margin-left: 8px;
          font-size: 12px;
          color: #999;
          text-decoration: line-through;
        }
      }
    }

    .variant-control {
      .quantity-control {
        display: flex;
        align-items: center;

        .quantity-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 28px;
          height: 28px;
          border-radius: 50%;
          background-color: #fff;
          border: 1px solid #ff5500;
        }

        .quantity {
          margin: 0 12px;
          font-size: 14px;
          font-weight: 500;
          color: #333;
          min-width: 20px;
          text-align: center;
        }
      }

      .add-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 28px;
        height: 28px;
        border-radius: 50%;
        background-color: #ff5500;
      }
    }
  }
}

// 弹窗中的规格列表特殊样式
.popup-variants-list {
  .popup-variant-item {
    border-left: 3px solid #ff5500;
    border: none;
    border-left: 3px solid #ff5500;
  }
}

// 套餐选择
.combo-selection {
  .combo-options {
    .combo-option {
      margin-bottom: 16px;
      border: 1px solid #f0f0f0;
      border-radius: 8px;
      overflow: hidden;

      .combo-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 16px;
        background-color: #fafafa;
        border-bottom: 1px solid #f0f0f0;

        .combo-name {
          font-size: 14px;
          font-weight: 500;
          color: #333;
        }

        .combo-required {
          font-size: 12px;
          color: #ff5500;
          background-color: #fff0e6;
          padding: 2px 6px;
          border-radius: 4px;
        }
      }

      .combo-items {
        .combo-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 12px 16px;
          border-bottom: 1px solid #f0f0f0;
          transition: all 0.3s;

          &:last-child {
            border-bottom: none;
          }

          &.active {
            background-color: #fff0e6;
            border-left: 3px solid #ff5500;
          }

          .item-name {
            font-size: 14px;
            color: #333;
          }

          .item-price {
            font-size: 14px;
            color: #ff5500;
            font-weight: 500;
          }
        }
      }
    }
  }
}

// 数量选择
.quantity-selection {
  .quantity-control {
    display: flex;
    align-items: center;
    justify-content: center;

    .quantity-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 36px;
      height: 36px;
      border-radius: 50%;
      background-color: #fff;
      border: 1px solid #ff5500;
    }

    .quantity {
      margin: 0 20px;
      font-size: 16px;
      font-weight: 500;
      color: #333;
      min-width: 30px;
      text-align: center;
    }
  }
}

// 商品详情图片
.detail-images-section {
  .detail-images {
    .detail-image {
      width: 100%;
      margin-bottom: 8px;
      border-radius: 8px;
    }
  }
}

// 底部操作栏
.footer {
  &.popup-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    border-top: 1px solid #f0f0f0;
  }

  &.page-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    background-color: #fff;
    border-top: 1px solid #f0f0f0;
    z-index: 100;
  }

  .total-price {
    font-size: 16px;
    font-weight: 600;
    color: #333;
  }

  .action-buttons {
    .add-to-cart-btn {
      padding: 12px 24px;
      background-color: #ff5500;
      color: #fff;
      border-radius: 20px;
      font-size: 14px;
      font-weight: 500;
      text-align: center;

      &:active {
        transform: scale(0.95);
      }
    }
  }
}

// 多规格总计
.variants-total {
  &.popup-variants-total {
    padding: 16px 20px;
    border-top: 1px solid #f0f0f0;
    background-color: #fafafa;
  }

  &.page-variants-total {
    position: fixed;
    bottom: 50px;
    left: 0;
    right: 0;
    padding: 16px 20px;
    background-color: #fafafa;
    border-top: 1px solid #f0f0f0;
    z-index: 100;
  }

  .variants-total-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 4px;

    .total-label {
      font-size: 14px;
      color: #666;
    }

    .total-amount {
      font-size: 16px;
      font-weight: 600;
      color: #ff5500;
    }
  }

  .variants-tip {
    text-align: center;

    text {
      font-size: 12px;
      color: #999;
    }
  }
}
</style>
