<!--
虚拟滚动列表组件

用于优化大量数据的渲染性能，只渲染可视区域内的元素
-->
<template>
  <scroll-view
    :scroll-y="true"
    :scroll-top="scrollTop"
    :style="{ height: containerHeight + 'px' }"
    @scroll="handleScroll"
    @scrolltolower="handleScrollToLower"
    @refresherrefresh="handleRefresh"
    :refresher-enabled="refresherEnabled"
    :refresher-triggered="refresherTriggered"
    class="virtual-list"
  >
    <!-- 上方占位元素 -->
    <view :style="{ height: topPlaceholderHeight + 'px' }"></view>

    <!-- 可视区域内的元素 -->
    <view class="virtual-list-items">
      <view
        v-for="(item, index) in visibleItems"
        :key="getItemKey ? getItemKey(item, startIndex + index) : startIndex + index"
        :style="{ height: itemHeight + 'px' }"
        class="virtual-list-item"
      >
        <slot :item="item" :index="startIndex + index"></slot>
      </view>
    </view>

    <!-- 下方占位元素 -->
    <view :style="{ height: bottomPlaceholderHeight + 'px' }"></view>

    <!-- 加载更多提示 -->
    <view v-if="loading" class="loading-more">
      <view class="loading-icon"></view>
      <text class="loading-text">加载中...</text>
    </view>
  </scroll-view>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'

interface Props {
  // 数据列表
  items: any[]
  // 每个元素的高度
  itemHeight: number
  // 容器高度
  containerHeight: number
  // 缓冲区大小（额外渲染的元素数量）
  bufferSize?: number
  // 获取元素唯一键的函数
  getItemKey?: (item: any, index: number) => string | number
  // 是否启用下拉刷新
  refresherEnabled?: boolean
  // 下拉刷新状态
  refresherTriggered?: boolean
  // 是否正在加载更多
  loading?: boolean
}

interface Emits {
  (e: 'scroll', event: any): void
  (e: 'scrollToLower'): void
  (e: 'refresh'): void
}

const props = withDefaults(defineProps<Props>(), {
  bufferSize: 5,
  refresherEnabled: true,
  refresherTriggered: false,
  loading: false,
})

const emit = defineEmits<Emits>()

// 滚动位置
const scrollTop = ref(0)

// 可视区域内可显示的元素数量
const visibleCount = computed(() => {
  return Math.ceil(props.containerHeight / props.itemHeight) + props.bufferSize * 2
})

// 开始索引
const startIndex = computed(() => {
  const index = Math.floor(scrollTop.value / props.itemHeight) - props.bufferSize
  return Math.max(0, index)
})

// 结束索引
const endIndex = computed(() => {
  const index = startIndex.value + visibleCount.value
  return Math.min(props.items.length, index)
})

// 可视区域内的元素
const visibleItems = computed(() => {
  return props.items.slice(startIndex.value, endIndex.value)
})

// 上方占位元素高度
const topPlaceholderHeight = computed(() => {
  return startIndex.value * props.itemHeight
})

// 下方占位元素高度
const bottomPlaceholderHeight = computed(() => {
  const remainingItems = props.items.length - endIndex.value
  return remainingItems * props.itemHeight
})

// 处理滚动事件
function handleScroll(event: any) {
  scrollTop.value = event.detail.scrollTop
  emit('scroll', event)
}

// 处理滚动到底部事件
function handleScrollToLower() {
  emit('scrollToLower')
}

// 处理下拉刷新事件
function handleRefresh() {
  emit('refresh')
}

// 滚动到指定位置
function scrollToIndex(index: number) {
  const targetScrollTop = index * props.itemHeight
  scrollTop.value = targetScrollTop
}

// 滚动到顶部
function scrollToTop() {
  scrollTop.value = 0
}

// 滚动到底部
function scrollToBottom() {
  const maxScrollTop = props.items.length * props.itemHeight - props.containerHeight
  scrollTop.value = Math.max(0, maxScrollTop)
}

// 暴露方法给父组件
defineExpose({
  scrollToIndex,
  scrollToTop,
  scrollToBottom,
})
</script>

<style lang="scss" scoped>
.virtual-list {
  position: relative;
  overflow: hidden;
}

.virtual-list-items {
  position: relative;
}

.virtual-list-item {
  position: relative;
  overflow: hidden;
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;

  .loading-icon {
    width: 40rpx;
    height: 40rpx;
    border: 4rpx solid #f3f3f3;
    border-top: 4rpx solid #4095e5;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 20rpx;
  }

  .loading-text {
    font-size: 28rpx;
    color: #999;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
