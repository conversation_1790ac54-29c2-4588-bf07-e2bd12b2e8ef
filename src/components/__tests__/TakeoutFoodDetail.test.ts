/**
 * TakeoutFoodDetail 组件测试
 *
 * 测试外卖商品详情组件的基本功能：
 * - 组件渲染
 * - 商品信息显示
 * - 规格选择
 * - 购物车操作
 * - 弹窗和页面模式切换
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import TakeoutFoodDetail from '../TakeoutFoodDetail.vue'
import { useTakeoutStore } from '@/store/takeout'
import type { ITakeoutFood } from '@/api/takeout.typings'

// Mock store
vi.mock('@/store/takeout')
const mockTakeoutStore = {
  getCartItemCountWithVariant: vi.fn(() => 0),
  getCartItemCount: vi.fn(() => 0),
  addToCart: vi.fn(),
  updateCartItemQuantity: vi.fn(),
  cart: { items: [] },
}

// Mock FavoriteButton component
vi.mock('@/components/FavoriteButton.vue', () => ({
  default: {
    name: 'FavoriteButton',
    template: '<div data-testid="favorite-button">FavoriteButton</div>',
  },
}))

// Mock uni-app components
vi.mock('@dcloudio/uni-app', () => ({
  // Mock any uni-app specific functions if needed
}))

// 测试用的商品数据
const mockFood: ITakeoutFood = {
  id: 1,
  name: '测试商品',
  price: 15.8,
  original_price: 18.8,
  image: 'https://example.com/food.jpg',
  brief: '这是一个测试商品',
  description: '详细的商品描述',
  tags: ['热销', '推荐'],
  total_sold: 100,
  rating: 95,
  packaging_fee: 1.0,
  merchant_id: 1,
  category_id: 1,
  has_variants: true,
  variants: [
    {
      id: 1,
      name: '小份',
      price: 15.8,
      original_price: 18.8,
      is_default: true,
    },
    {
      id: 2,
      name: '大份',
      price: 22.8,
      original_price: 25.8,
      is_default: false,
    },
  ],
  is_combination: false,
  combos: [],
  detail_images: ['https://example.com/detail1.jpg', 'https://example.com/detail2.jpg'],
}

describe('TakeoutFoodDetail', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    ;(useTakeoutStore as any).mockReturnValue(mockTakeoutStore)
  })

  describe('基本渲染', () => {
    it('应该正确渲染商品信息', () => {
      const wrapper = mount(TakeoutFoodDetail, {
        props: {
          food: mockFood,
          isPopup: false,
        },
      })

      // 检查商品名称
      expect(wrapper.text()).toContain('测试商品')

      // 检查价格
      expect(wrapper.text()).toContain('¥15.80')

      // 检查描述
      expect(wrapper.text()).toContain('这是一个测试商品')

      // 检查标签
      expect(wrapper.text()).toContain('热销')
      expect(wrapper.text()).toContain('推荐')

      // 检查销量
      expect(wrapper.text()).toContain('月销 100')
    })

    it('应该在页面模式下显示导航栏', () => {
      const wrapper = mount(TakeoutFoodDetail, {
        props: {
          food: mockFood,
          isPopup: false,
        },
      })

      // 页面模式应该有导航栏
      expect(wrapper.find('wd-navbar').exists()).toBe(true)
    })

    it('应该在弹窗模式下显示关闭按钮', () => {
      const wrapper = mount(TakeoutFoodDetail, {
        props: {
          food: mockFood,
          isPopup: true,
        },
      })

      // 弹窗模式应该有关闭按钮
      expect(wrapper.find('.close-btn').exists()).toBe(true)
      expect(wrapper.find('.popup-title').text()).toBe('商品详情')
    })
  })

  describe('规格选择', () => {
    it('应该显示商品规格列表', () => {
      const wrapper = mount(TakeoutFoodDetail, {
        props: {
          food: mockFood,
          isPopup: false,
        },
      })

      // 检查规格选择区域
      expect(wrapper.find('.variant-selection').exists()).toBe(true)
      expect(wrapper.text()).toContain('选择规格')

      // 检查规格选项
      expect(wrapper.text()).toContain('小份')
      expect(wrapper.text()).toContain('大份')
    })

    it('应该正确显示规格价格', () => {
      const wrapper = mount(TakeoutFoodDetail, {
        props: {
          food: mockFood,
          isPopup: false,
        },
      })

      // 检查规格价格
      const variantItems = wrapper.findAll('.variant-item')
      expect(variantItems).toHaveLength(2)

      // 第一个规格（小份）
      expect(variantItems[0].text()).toContain('¥15.80')

      // 第二个规格（大份）
      expect(variantItems[1].text()).toContain('¥22.80')
    })
  })

  describe('购物车操作', () => {
    it('应该能够增加规格商品数量', async () => {
      const wrapper = mount(TakeoutFoodDetail, {
        props: {
          food: mockFood,
          isPopup: false,
        },
      })

      // 找到第一个规格的添加按钮
      const addBtn = wrapper.find('.add-btn')
      expect(addBtn.exists()).toBe(true)

      // 点击添加按钮
      await addBtn.trigger('click')

      // 验证调用了正确的store方法
      expect(mockTakeoutStore.addToCart).toHaveBeenCalledWith({
        food_id: 1,
        variant_id: 1,
        quantity: 1,
        remark: '',
        combo_selections: [],
      })
    })

    it('应该能够减少规格商品数量', async () => {
      // Mock当前数量为2
      mockTakeoutStore.getCartItemCountWithVariant.mockReturnValue(2)

      const wrapper = mount(TakeoutFoodDetail, {
        props: {
          food: mockFood,
          isPopup: false,
        },
      })

      // 找到减少按钮
      const decreaseBtn = wrapper.find('.quantity-btn')
      expect(decreaseBtn.exists()).toBe(true)

      // 点击减少按钮
      await decreaseBtn.trigger('click')

      // 验证调用了正确的store方法
      expect(mockTakeoutStore.updateCartItemQuantity).toHaveBeenCalledWith(1, 1, 1)
    })
  })

  describe('事件处理', () => {
    it('应该在弹窗模式下触发关闭事件', async () => {
      const wrapper = mount(TakeoutFoodDetail, {
        props: {
          food: mockFood,
          isPopup: true,
        },
      })

      // 点击关闭按钮
      const closeBtn = wrapper.find('.close-btn')
      await closeBtn.trigger('click')

      // 验证触发了关闭事件
      expect(wrapper.emitted('close')).toBeTruthy()
    })

    it('应该触发添加到购物车事件', async () => {
      const wrapper = mount(TakeoutFoodDetail, {
        props: {
          food: { ...mockFood, has_variants: false },
          isPopup: false,
        },
      })

      // 找到添加到购物车按钮
      const addToCartBtn = wrapper.find('.add-to-cart-btn')
      await addToCartBtn.trigger('click')

      // 验证触发了添加到购物车事件
      expect(wrapper.emitted('add-to-cart')).toBeTruthy()

      const emittedData = wrapper.emitted('add-to-cart')?.[0]?.[0]
      expect(emittedData).toMatchObject({
        food: expect.objectContaining({ id: 1 }),
        quantity: 1,
      })
    })
  })

  describe('收藏功能', () => {
    it('应该在页面模式下显示收藏按钮', () => {
      const wrapper = mount(TakeoutFoodDetail, {
        props: {
          food: mockFood,
          isPopup: false,
        },
      })

      // 页面模式应该显示收藏按钮
      expect(wrapper.find('[data-testid="favorite-button"]').exists()).toBe(true)
    })

    it('应该在弹窗模式下隐藏收藏按钮', () => {
      const wrapper = mount(TakeoutFoodDetail, {
        props: {
          food: mockFood,
          isPopup: true,
        },
      })

      // 弹窗模式不应该显示收藏按钮
      expect(wrapper.find('.favorite-section').exists()).toBe(false)
    })
  })

  describe('边界情况', () => {
    it('应该处理空商品数据', () => {
      const wrapper = mount(TakeoutFoodDetail, {
        props: {
          food: null,
          isPopup: false,
        },
      })

      // 组件应该正常渲染，不崩溃
      expect(wrapper.exists()).toBe(true)
    })

    it('应该处理没有规格的商品', () => {
      const foodWithoutVariants = {
        ...mockFood,
        has_variants: false,
        variants: [],
      }

      const wrapper = mount(TakeoutFoodDetail, {
        props: {
          food: foodWithoutVariants,
          isPopup: false,
        },
      })

      // 不应该显示规格选择区域
      expect(wrapper.find('.variant-selection').exists()).toBe(false)

      // 应该显示数量选择区域
      expect(wrapper.find('.quantity-selection').exists()).toBe(true)
    })
  })
})
