/** * 聊天输入组件 - 基础版 * * 提供基本的文本输入和发送功能 */
<template>
  <view class="chat-input-container">
    <view class="input-wrapper">
      <text class="iconfont icon-voice" @tap="toggleVoiceMode"></text>
      <textarea
        v-show="!isVoiceMode"
        class="text-input"
        v-model="message"
        :adjust-position="true"
        :cursor-spacing="20"
        :show-confirm-bar="false"
        :auto-height="true"
        :maxlength="-1"
        :placeholder="placeholder"
        @focus="onInputFocus"
        @blur="onInputBlur"
        @confirm="sendMessage"
      />
      <view
        v-show="isVoiceMode"
        class="voice-input"
        @touchstart="startRecordVoice"
        @touchend="stopRecordVoice"
        @touchcancel="cancelRecordVoice"
      >
        {{ recording ? '松开结束' : '按住说话' }}
      </view>
      <text class="iconfont icon-emoji" @tap="toggleEmojiPanel"></text>
      <text class="iconfont icon-more" @tap="toggleMorePanel"></text>
    </view>

    <view class="send-wrapper">
      <button
        class="send-btn"
        :disabled="!canSend"
        :class="{ 'send-btn-active': canSend }"
        @tap="sendMessage"
      >
        发送
      </button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// 组件属性
const props = defineProps({
  placeholder: {
    type: String,
    default: '请输入消息',
  },
})

// 组件事件
const emit = defineEmits(['send', 'focus', 'blur', 'moreClick', 'emojiClick'])

// 数据
const message = ref('')
const isVoiceMode = ref(false)
const recording = ref(false)
const showEmojiPanel = ref(false)
const showMorePanel = ref(false)

// 计算属性
const canSend = computed(() => message.value.trim().length > 0)

// 输入框获取焦点
function onInputFocus() {
  emit('focus')
  showEmojiPanel.value = false
  showMorePanel.value = false
}

// 输入框失去焦点
function onInputBlur() {
  emit('blur')
}

// 切换语音模式
function toggleVoiceMode() {
  isVoiceMode.value = !isVoiceMode.value
  if (isVoiceMode.value) {
    showEmojiPanel.value = false
    showMorePanel.value = false
  }
}

// 开始录音
function startRecordVoice() {
  recording.value = true
  // 实现开始录音功能
  console.log('开始录音')
}

// 结束录音
function stopRecordVoice() {
  recording.value = false
  // 实现结束录音并发送功能
  console.log('结束录音并发送')
}

// 取消录音
function cancelRecordVoice() {
  recording.value = false
  // 实现取消录音功能
  console.log('取消录音')
}

// 切换表情面板
function toggleEmojiPanel() {
  showEmojiPanel.value = !showEmojiPanel.value
  if (showEmojiPanel.value) {
    showMorePanel.value = false
    isVoiceMode.value = false
  }
  emit('emojiClick', showEmojiPanel.value)
}

// 切换更多功能面板
function toggleMorePanel() {
  showMorePanel.value = !showMorePanel.value
  if (showMorePanel.value) {
    showEmojiPanel.value = false
    isVoiceMode.value = false
  }
  emit('moreClick', showMorePanel.value)
}

// 发送消息
function sendMessage() {
  if (!canSend.value) return

  emit('send', message.value)
  message.value = ''
}
</script>

<style lang="scss" scoped>
.chat-input-container {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  background-color: #f5f7fa;
  border-top: 1rpx solid #e0e0e0;
}

.input-wrapper {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #ffffff;
  border-radius: 36rpx;
  padding: 0 20rpx;
  margin-right: 20rpx;

  .iconfont {
    font-size: 44rpx;
    color: #999;
    padding: 10rpx;

    &:active {
      color: #4095e5;
    }
  }

  .text-input {
    flex: 1;
    font-size: 30rpx;
    height: 72rpx;
    min-height: 36rpx;
    max-height: 160rpx;
    line-height: 1.5;
    padding: 16rpx 0;
  }

  .voice-input {
    flex: 1;
    text-align: center;
    color: #333;
    font-size: 30rpx;
    height: 72rpx;
    line-height: 72rpx;

    &:active {
      background-color: #e0e0e0;
    }
  }
}

.send-wrapper {
  .send-btn {
    width: 120rpx;
    height: 72rpx;
    line-height: 72rpx;
    font-size: 28rpx;
    text-align: center;
    background-color: #cccccc;
    color: #ffffff;
    border-radius: 36rpx;
    padding: 0;
    margin: 0;

    &-active {
      background-color: #4095e5;
    }

    &:active {
      opacity: 0.8;
    }
  }
}
</style>
