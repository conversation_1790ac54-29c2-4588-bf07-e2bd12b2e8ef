/** * 聊天消息组件 * * 此组件用于渲染不同类型的聊天消息（文本、图片、语音、视频、文件等） *
支持显示发送者信息、时间、消息状态等 */
<template>
  <view class="message-item" :class="{ self: isSelf }">
    <!-- 消息时间 -->
    <view v-if="showTime" class="message-time">
      {{ formatTime(message.sendTime) }}
    </view>

    <!-- 消息主体 -->
    <view class="message-body">
      <!-- 头像 -->
      <image
        class="avatar"
        :src="message.avatar || defaultAvatar"
        mode="aspectFill"
        @tap="onAvatarClick"
      ></image>

      <!-- 消息内容容器 -->
      <view class="content-wrapper">
        <!-- 发送者名称 (仅群聊且非自己发送的消息显示) -->
        <view v-if="showSender" class="sender-name">{{ message.senderName }}</view>

        <!-- 消息内容 -->
        <view
          class="message-content"
          :class="[`message-${message.type}`, { self: isSelf }]"
          @longpress="onMessageLongPress"
        >
          <!-- 文本消息 -->
          <text v-if="message.type === 'text'" class="text-content">{{ message.content }}</text>

          <!-- 图片消息 -->
          <image
            v-else-if="message.type === 'image'"
            class="image-content"
            :src="message.content"
            mode="widthFix"
            @tap="previewImage"
          ></image>

          <!-- 语音消息 -->
          <view v-else-if="message.type === 'voice'" class="voice-content" @tap="playVoice">
            <text class="iconfont icon-voice"></text>
            <text class="voice-duration">{{ message.duration }}''</text>
          </view>

          <!-- 视频消息 -->
          <view v-else-if="message.type === 'video'" class="video-content" @tap="playVideo">
            <image class="video-cover" :src="message.cover || ''" mode="aspectFill"></image>
            <text class="iconfont icon-play video-play-icon"></text>
          </view>

          <!-- 文件消息 -->
          <view v-else-if="message.type === 'file'" class="file-content" @tap="downloadFile">
            <text class="iconfont icon-file"></text>
            <view class="file-info">
              <text class="file-name">{{ message.fileName }}</text>
              <text class="file-size">{{ formatFileSize(message.fileSize) }}</text>
            </view>
          </view>

          <!-- 订单通知消息 -->
          <OrderNotificationMessage
            v-else-if="message.type === 'order_notification'"
            :notification="message.content.orderNotification"
            :created-at="message.sendTime"
            @action="handleOrderAction"
          />

          <!-- 其他类型消息 -->
          <view v-else class="unknown-content">
            <text>不支持的消息类型</text>
          </view>
        </view>

        <!-- 消息状态 -->
        <view v-if="isSelf" class="message-status">
          <text v-if="message.status === 'sending'" class="status-sending">发送中...</text>
          <text v-else-if="message.status === 'failed'" class="status-failed">发送失败</text>
          <text v-else-if="message.status === 'sent'" class="status-sent">已发送</text>
          <text v-else-if="message.status === 'delivered'" class="status-delivered">已送达</text>
          <text v-else-if="message.status === 'read'" class="status-read">已读</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import OrderNotificationMessage from './OrderNotificationMessage.vue'

// 消息数据接口
interface MessageProps {
  message: {
    id: string
    type: 'text' | 'image' | 'voice' | 'video' | 'file' | 'order_notification'
    content:
      | string
      | {
          orderNotification?: {
            type: string
            title: string
            content: string
            orderId: number
            orderNo: string
            amount?: number
            refundAmount?: number
            actionType?: string
            actionUrl?: string
            data?: any
          }
        }
    senderId: string
    senderName?: string
    avatar?: string
    sendTime: number | string
    status?: 'sending' | 'sent' | 'delivered' | 'read' | 'failed'
    duration?: number
    fileName?: string
    fileSize?: number
    cover?: string
  }
  isSelf: boolean
  isGroup: boolean
  showTime: boolean
}

// 组件属性
const props = withDefaults(defineProps<MessageProps>(), {
  isSelf: false,
  isGroup: false,
  showTime: false,
})

// 默认头像
const defaultAvatar = '/static/images/default-avatar.png'

// 事件
const emit = defineEmits(['avatarClick', 'messageLongPress'])

// 是否显示发送者名称（仅在群聊中显示非自己发送的消息的发送者名称）
const showSender = computed(() => {
  return props.isGroup && !props.isSelf && props.message.senderName
})

// 格式化时间
function formatTime(timestamp: number): string {
  if (!timestamp) return ''

  const msgDate = new Date(timestamp)
  const now = new Date()

  // 今天的消息只显示时间
  if (msgDate.toDateString() === now.toDateString()) {
    return msgDate.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
  }

  // 昨天的消息显示"昨天 HH:MM"
  const yesterday = new Date(now)
  yesterday.setDate(now.getDate() - 1)
  if (msgDate.toDateString() === yesterday.toDateString()) {
    const time = msgDate.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
    return `昨天 ${time}`
  }

  // 其他日期显示完整日期和时间
  return msgDate.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  })
}

// 格式化文件大小
function formatFileSize(bytes?: number): string {
  if (!bytes) return '0 B'

  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  let size = bytes
  let unitIndex = 0

  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024
    unitIndex++
  }

  return `${size.toFixed(2)} ${units[unitIndex]}`
}

// 头像点击事件
function onAvatarClick() {
  emit('avatarClick', props.message)
}

// 长按消息事件
function onMessageLongPress() {
  emit('messageLongPress', props.message)
}

// 预览图片
function previewImage() {
  if (props.message.type !== 'image') return

  uni.previewImage({
    current: props.message.content,
    urls: [props.message.content],
  })
}

// 播放语音
function playVoice() {
  if (props.message.type !== 'voice') return

  // 实现语音播放
  console.log('播放语音:', props.message.content)
}

// 播放视频
function playVideo() {
  if (props.message.type !== 'video') return

  uni.navigateTo({
    url: `/pages-sub/video-player/index?url=${encodeURIComponent(props.message.content)}`,
  })
}

// 下载文件
function downloadFile() {
  if (props.message.type !== 'file') return

  // 实现文件下载
  console.log('下载文件:', props.message.content)
}

// 处理订单操作
function handleOrderAction(notification: any) {
  console.log('处理订单操作:', notification)

  if (notification.actionUrl) {
    uni.navigateTo({
      url: notification.actionUrl,
    })
  }
}
</script>

<style lang="scss" scoped>
.message-item {
  margin: 20rpx 0;

  .message-time {
    text-align: center;
    margin: 20rpx 0;

    &-text {
      display: inline-block;
      padding: 4rpx 16rpx;
      font-size: 24rpx;
      color: #999;
      background: rgba(0, 0, 0, 0.05);
      border-radius: 8rpx;
    }
  }

  .message-body {
    display: flex;
    padding: 0 24rpx;

    .avatar {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      background-color: #eee;
      border: 1rpx solid #eaeaea;
    }

    .content-wrapper {
      max-width: 70%;
      margin: 0 20rpx;

      .sender-name {
        font-size: 24rpx;
        color: #999;
        margin-bottom: 6rpx;
      }

      .message-content {
        position: relative;
        border-radius: 12rpx;
        padding: 16rpx;
        background-color: #fff;
        box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
        word-break: break-all;

        &.self {
          background-color: #dcf0fc;
        }

        &::before {
          content: '';
          position: absolute;
          top: 16rpx;
          left: -14rpx;
          width: 0;
          height: 0;
          border: 8rpx solid transparent;
          border-right-color: #fff;
        }

        &.self::before {
          left: auto;
          right: -14rpx;
          border-right-color: transparent;
          border-left-color: #dcf0fc;
        }

        .text-content {
          font-size: 30rpx;
          line-height: 1.5;
          color: #333;
        }

        .image-content {
          max-width: 400rpx;
          border-radius: 8rpx;
        }

        .voice-content {
          display: flex;
          align-items: center;
          padding: 16rpx;

          .icon-voice {
            font-size: 40rpx;
            margin-right: 12rpx;
          }

          .voice-duration {
            font-size: 28rpx;
          }
        }

        .video-content {
          position: relative;
          width: 320rpx;
          height: 240rpx;

          .video-cover {
            width: 100%;
            height: 100%;
            border-radius: 8rpx;
          }

          .video-play-icon {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            font-size: 80rpx;
            color: #fff;
            text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
          }
        }

        .file-content {
          display: flex;
          align-items: center;
          padding: 16rpx;

          .icon-file {
            font-size: 48rpx;
            margin-right: 16rpx;
            color: #4095e5;
          }

          .file-info {
            .file-name {
              font-size: 28rpx;
              margin-bottom: 6rpx;
              display: block;
              color: #333;
              max-width: 400rpx;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }

            .file-size {
              font-size: 24rpx;
              color: #999;
            }
          }
        }

        .unknown-content {
          padding: 16rpx;
          color: #999;
          font-size: 28rpx;
        }
      }

      .message-status {
        text-align: right;
        margin-top: 8rpx;
        font-size: 24rpx;

        .status-sending {
          color: #999;
        }

        .status-failed {
          color: #ff4d4f;
        }

        .status-sent {
          color: #999;
        }

        .status-delivered {
          color: #999;
        }

        .status-read {
          color: #4095e5;
        }
      }
    }
  }

  // 自己发送的消息靠右显示
  &.self {
    .message-body {
      flex-direction: row-reverse;
    }
  }
}
</style>
