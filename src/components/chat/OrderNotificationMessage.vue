<template>
  <view class="order-notification-message">
    <view class="notification-card" :class="getCardClass()">
      <!-- 通知图标 -->
      <view class="notification-icon">
        <wd-icon :name="getIconName()" :color="getIconColor()" size="48" />
      </view>

      <!-- 通知内容 -->
      <view class="notification-content">
        <view class="notification-header">
          <text class="notification-title">{{ notification.title }}</text>
          <text class="notification-time">{{ formatTime(createdAt) }}</text>
        </view>

        <view class="notification-body">
          <text class="notification-text">{{ notification.content }}</text>
        </view>

        <view class="notification-footer">
          <view class="order-info">
            <text class="order-no">订单号：{{ notification.orderNo }}</text>
            <view v-if="notification.amount || notification.refundAmount" class="amount-info">
              <text v-if="notification.amount" class="amount">
                金额：¥{{ notification.amount }}
              </text>
              <text v-if="notification.refundAmount" class="refund-amount">
                退款：¥{{ notification.refundAmount }}
              </text>
            </view>
          </view>

          <!-- 操作按钮 -->
          <view v-if="notification.actionUrl" class="action-buttons">
            <wd-button 
              type="primary" 
              size="small" 
              @click="handleAction"
            >
              {{ getActionText() }}
            </wd-button>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { formatRelativeTime } from '@/utils/date'
import { WSNotificationEventType } from '@/api/chat.typings'

// Props
interface Props {
  notification: {
    type: WSNotificationEventType
    title: string
    content: string
    orderId: number
    orderNo: string
    amount?: number
    refundAmount?: number
    actionType?: string
    actionUrl?: string
    data?: any
  }
  createdAt: string
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  action: [notification: Props['notification']]
}>()

// 获取卡片样式类
const getCardClass = () => {
  switch (props.notification.type) {
    case WSNotificationEventType.ORDER_PAYMENT_SUCCESS:
    case WSNotificationEventType.USER_REFUND_RESULT:
      return 'success'
    case WSNotificationEventType.ORDER_PAYMENT_FAILED:
      return 'error'
    case WSNotificationEventType.ORDER_SHIPPED:
    case WSNotificationEventType.ORDER_DELIVERED:
      return 'info'
    case WSNotificationEventType.ORDER_CANCELLED:
      return 'warning'
    default:
      return 'default'
  }
}

// 获取图标名称
const getIconName = () => {
  switch (props.notification.type) {
    case WSNotificationEventType.ORDER_PAYMENT_SUCCESS:
      return 'check-circle'
    case WSNotificationEventType.ORDER_PAYMENT_FAILED:
      return 'close-circle'
    case WSNotificationEventType.USER_REFUND_RESULT:
      return 'refund'
    case WSNotificationEventType.ORDER_SHIPPED:
      return 'truck'
    case WSNotificationEventType.ORDER_DELIVERED:
      return 'location'
    case WSNotificationEventType.ORDER_CANCELLED:
      return 'close'
    default:
      return 'notification'
  }
}

// 获取图标颜色
const getIconColor = () => {
  switch (props.notification.type) {
    case WSNotificationEventType.ORDER_PAYMENT_SUCCESS:
    case WSNotificationEventType.USER_REFUND_RESULT:
      return '#52c41a'
    case WSNotificationEventType.ORDER_PAYMENT_FAILED:
      return '#ff4d4f'
    case WSNotificationEventType.ORDER_SHIPPED:
    case WSNotificationEventType.ORDER_DELIVERED:
      return '#1890ff'
    case WSNotificationEventType.ORDER_CANCELLED:
      return '#faad14'
    default:
      return '#666'
  }
}

// 获取操作按钮文本
const getActionText = () => {
  switch (props.notification.actionType) {
    case 'order_detail':
      return '查看订单'
    case 'payment':
      return '去支付'
    case 'refund_detail':
      return '查看退款'
    default:
      return '查看详情'
  }
}

// 格式化时间
const formatTime = (time: string) => {
  return formatRelativeTime(time)
}

// 处理操作按钮点击
const handleAction = () => {
  emit('action', props.notification)
}
</script>

<style lang="scss" scoped>
.order-notification-message {
  margin: 16rpx 0;
}

.notification-card {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  border-left: 8rpx solid #ddd;

  &.success {
    border-left-color: #52c41a;
  }

  &.error {
    border-left-color: #ff4d4f;
  }

  &.info {
    border-left-color: #1890ff;
  }

  &.warning {
    border-left-color: #faad14;
  }
}

.notification-icon {
  display: flex;
  justify-content: center;
  margin-bottom: 16rpx;
}

.notification-content {
  .notification-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12rpx;

    .notification-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
      flex: 1;
    }

    .notification-time {
      font-size: 24rpx;
      color: #999;
      margin-left: 16rpx;
    }
  }

  .notification-body {
    margin-bottom: 16rpx;

    .notification-text {
      font-size: 28rpx;
      color: #666;
      line-height: 1.5;
    }
  }

  .notification-footer {
    .order-info {
      margin-bottom: 16rpx;

      .order-no {
        font-size: 24rpx;
        color: #999;
        display: block;
        margin-bottom: 8rpx;
      }

      .amount-info {
        display: flex;
        gap: 24rpx;

        .amount,
        .refund-amount {
          font-size: 26rpx;
          font-weight: 500;
        }

        .amount {
          color: #ff4757;
        }

        .refund-amount {
          color: #52c41a;
        }
      }
    }

    .action-buttons {
      display: flex;
      justify-content: flex-end;
    }
  }
}
</style>
