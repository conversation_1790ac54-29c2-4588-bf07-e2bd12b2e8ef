<template>
  <view class="error-fallback">
    <view class="error-content">
      <wot-icon name="warning" size="60" color="#ff5500" />
      <text class="error-title">{{ title }}</text>
      <text class="error-message">{{ message }}</text>

      <view class="error-actions">
        <view v-if="showRetry" class="action-btn primary" @click="handleRetry">
          <text>重试</text>
        </view>
        <view v-if="showBack" class="action-btn secondary" @click="handleBack">
          <text>返回</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
interface Props {
  title?: string
  message?: string
  showRetry?: boolean
  showBack?: boolean
}

interface Emits {
  (e: 'retry'): void
  (e: 'back'): void
}

const props = withDefaults(defineProps<Props>(), {
  title: '出错了',
  message: '页面加载失败，请稍后重试',
  showRetry: true,
  showBack: false,
})

const emit = defineEmits<Emits>()

const handleRetry = () => {
  emit('retry')
}

const handleBack = () => {
  emit('back')
}
</script>

<style scoped lang="scss">
.error-fallback {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  padding: 40px 20px;

  .error-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;

    .error-title {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      margin: 15px 0 10px;
    }

    .error-message {
      font-size: 14px;
      color: #666;
      line-height: 1.5;
      margin-bottom: 30px;
    }

    .error-actions {
      display: flex;
      gap: 15px;

      .action-btn {
        padding: 12px 24px;
        border-radius: 20px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;

        &.primary {
          background: #ff5500;
          color: white;

          &:active {
            opacity: 0.8;
          }
        }

        &.secondary {
          background: #f5f5f5;
          color: #666;

          &:active {
            background: #e0e0e0;
          }
        }
      }
    }
  }
}
</style>
