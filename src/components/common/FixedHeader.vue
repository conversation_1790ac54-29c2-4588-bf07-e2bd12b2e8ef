<!--
固定顶部标题栏组件

用于在有TabBar的页面中提供固定在顶部的标题栏
主要功能：
1. 固定在页面顶部，不随滚动消失
2. 支持自定义标题和返回按钮
3. 支持状态栏适配
4. 支持自定义操作按钮
-->
<template>
  <view class="fixed-header" :class="{ 'with-status-bar': withStatusBar }">
    <!-- 状态栏占位 -->
    <view
      v-if="withStatusBar"
      class="status-bar"
      :style="{ height: statusBarHeight + 'px' }"
    ></view>

    <!-- 导航栏 -->
    <view class="nav-bar" :style="{ height: navBarHeight + 'px' }">
      <!-- 左侧区域 -->
      <view class="nav-left">
        <view v-if="showBack" class="back-btn" @click="handleBack">
          <wd-icon name="arrow-left" size="20" color="#333" />
        </view>
        <slot name="left"></slot>
      </view>

      <!-- 中间标题区域 -->
      <view class="nav-center">
        <text class="nav-title">{{ title }}</text>
        <slot name="center"></slot>
      </view>

      <!-- 右侧区域 -->
      <view class="nav-right">
        <slot name="right"></slot>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

interface Props {
  // 标题文本
  title?: string
  // 是否显示返回按钮
  showBack?: boolean
  // 是否包含状态栏
  withStatusBar?: boolean
  // 背景色
  backgroundColor?: string
  // 文字颜色
  textColor?: string
  // 是否透明
  transparent?: boolean
}

interface Emits {
  (e: 'back'): void
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  showBack: true,
  withStatusBar: true,
  backgroundColor: '#ffffff',
  textColor: '#333333',
  transparent: false,
})

const emit = defineEmits<Emits>()

// 状态栏高度
const statusBarHeight = ref(0)
// 导航栏高度
const navBarHeight = ref(44)

// 总高度
const totalHeight = computed(() => {
  return (props.withStatusBar ? statusBarHeight.value : 0) + navBarHeight.value
})

/**
 * 处理返回按钮点击
 */
function handleBack() {
  emit('back')

  // 如果没有监听back事件，默认执行返回操作
  if (!emit('back')) {
    uni.navigateBack({
      fail: () => {
        // 如果无法返回，跳转到首页
        uni.switchTab({
          url: '/pages/index/index',
        })
      },
    })
  }
}

/**
 * 获取系统信息
 */
function getSystemInfo() {
  try {
    const systemInfo = uni.getSystemInfoSync()
    statusBarHeight.value = systemInfo.statusBarHeight || 20

    // 根据平台调整导航栏高度
    // #ifdef H5
    navBarHeight.value = 44
    // #endif

    // #ifdef MP-WEIXIN
    navBarHeight.value = 44
    // #endif

    // #ifdef APP-PLUS
    navBarHeight.value = 44
    // #endif
  } catch (e) {
    console.warn('获取系统信息失败:', e)
    statusBarHeight.value = 20
    navBarHeight.value = 44
  }
}

onMounted(() => {
  getSystemInfo()
})

// 暴露总高度给父组件使用
defineExpose({
  totalHeight,
})
</script>

<style lang="scss" scoped>
.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: v-bind(backgroundColor);

  &.transparent {
    background-color: transparent;
  }
}

.status-bar {
  width: 100%;
  background-color: inherit;
}

.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;
  background-color: inherit;
  border-bottom: 1rpx solid #f0f0f0;

  .transparent & {
    border-bottom: none;
  }
}

.nav-left,
.nav-right {
  display: flex;
  align-items: center;
  min-width: 120rpx;
}

.nav-left {
  justify-content: flex-start;
}

.nav-right {
  justify-content: flex-end;
}

.nav-center {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.back-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  transition: background-color 0.3s ease;

  &:active {
    background-color: rgba(0, 0, 0, 0.05);
  }
}

.nav-title {
  font-size: 36rpx;
  font-weight: 600;
  color: v-bind(textColor);
  max-width: 400rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .fixed-header {
    background-color: #1a1a1a;

    .nav-bar {
      border-bottom-color: #333;
    }

    .nav-title {
      color: #ffffff;
    }

    .back-btn:active {
      background-color: rgba(255, 255, 255, 0.1);
    }
  }
}

/* 安全区域适配 */
.fixed-header {
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
}

/* 不同平台的适配 */
/* #ifdef H5 */
.fixed-header {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}
/* #endif */

/* #ifdef MP-WEIXIN */
.nav-bar {
  padding: 0 24rpx;
}
/* #endif */
</style>
