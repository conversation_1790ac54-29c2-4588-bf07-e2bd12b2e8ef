<!--
优化的加载更多组件

基于 wot-design-uni 的 wd-loadmore 组件，修复了触摸事件监听器的性能警告
主要优化：
1. 为所有触摸事件添加 passive 选项
2. 优化滚动性能
3. 减少不必要的事件监听器
4. 支持自定义触摸阈值
-->
<template>
  <view
    ref="loadmoreContainer"
    class="optimized-loadmore"
    :class="[`loadmore-${state}`, customClass]"
    :style="customStyle"
  >
    <!-- 加载中状态 -->
    <view v-if="state === 'loading'" class="loadmore-content loading">
      <view class="loadmore-spinner">
        <view class="spinner-dot" v-for="i in 3" :key="i"></view>
      </view>
      <text class="loadmore-text">{{ loadingText }}</text>
    </view>

    <!-- 加载完成状态 -->
    <view v-else-if="state === 'finished'" class="loadmore-content finished">
      <view class="loadmore-line"></view>
      <text class="loadmore-text">{{ finishedText }}</text>
      <view class="loadmore-line"></view>
    </view>

    <!-- 加载失败状态 -->
    <view v-else-if="state === 'error'" class="loadmore-content error">
      <text class="loadmore-text error-text" @click="handleRetry">{{ errorText }}</text>
    </view>

    <!-- 默认状态（可以加载更多） -->
    <view v-else class="loadmore-content default">
      <text class="loadmore-text">{{ defaultText }}</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { addAllPassiveTouchListeners } from '@/utils/touchEventFixSimple'

interface Props {
  // 加载状态：loading-加载中, finished-加载完成, error-加载失败, default-默认
  state?: 'loading' | 'finished' | 'error' | 'default'
  // 自定义文本
  loadingText?: string
  finishedText?: string
  errorText?: string
  defaultText?: string
  // 自定义样式
  customStyle?: string
  customClass?: string
  // 触摸阈值（向上滑动多少距离触发加载）
  threshold?: number
  // 是否启用触摸优化
  enableTouchOptimization?: boolean
}

interface Emits {
  (e: 'loadmore'): void
  (e: 'retry'): void
}

const props = withDefaults(defineProps<Props>(), {
  state: 'default',
  loadingText: '加载中...',
  finishedText: '没有更多了',
  errorText: '加载失败，点击重试',
  defaultText: '上拉加载更多',
  customStyle: '',
  customClass: '',
  threshold: 50,
  enableTouchOptimization: true,
})

const emit = defineEmits<Emits>()

// 组件引用
const loadmoreContainer = ref<HTMLElement>()

// 触摸相关状态
let touchStartY = 0
let touchMoveY = 0
let isScrolling = false
let scrollTimer: NodeJS.Timeout | null = null

/**
 * 处理重试
 */
function handleRetry() {
  emit('retry')
}

/**
 * 检查是否可以触发加载更多
 */
function canLoadMore(): boolean {
  return props.state === 'default' && !isScrolling
}

/**
 * 触发加载更多
 */
function triggerLoadMore() {
  if (canLoadMore()) {
    emit('loadmore')
  }
}

/**
 * 检查是否滚动到底部
 */
function isScrolledToBottom(): boolean {
  const scrollTop = document.documentElement.scrollTop || document.body.scrollTop
  const scrollHeight = document.documentElement.scrollHeight || document.body.scrollHeight
  const clientHeight = document.documentElement.clientHeight || window.innerHeight

  // 距离底部小于阈值时认为已滚动到底部
  return scrollHeight - scrollTop - clientHeight <= props.threshold
}

/**
 * 应用触摸优化
 */
function applyTouchOptimization() {
  if (!loadmoreContainer.value || !props.enableTouchOptimization) return

  // 使用优化的被动触摸事件监听器
  addAllPassiveTouchListeners(loadmoreContainer.value, {
    touchstart: (event: TouchEvent) => {
      touchStartY = event.touches[0].clientY
      isScrolling = false

      // 清除滚动定时器
      if (scrollTimer) {
        clearTimeout(scrollTimer)
        scrollTimer = null
      }
    },
    touchmove: (event: TouchEvent) => {
      touchMoveY = event.touches[0].clientY
      const deltaY = touchMoveY - touchStartY

      // 标记为正在滚动
      isScrolling = true

      // 如果向上滑动且滚动到底部，准备触发加载更多
      if (deltaY < -props.threshold && isScrolledToBottom() && canLoadMore()) {
        // 添加视觉反馈
        loadmoreContainer.value?.classList.add('pull-up-active')
      } else {
        loadmoreContainer.value?.classList.remove('pull-up-active')
      }
    },
    touchend: (event: TouchEvent) => {
      const deltaY = touchMoveY - touchStartY

      // 移除视觉反馈
      loadmoreContainer.value?.classList.remove('pull-up-active')

      // 如果向上滑动距离超过阈值且滚动到底部，触发加载更多
      if (deltaY < -props.threshold && isScrolledToBottom() && canLoadMore()) {
        triggerLoadMore()
      }

      // 设置滚动结束定时器
      scrollTimer = setTimeout(() => {
        isScrolling = false
      }, 150)
    },
    touchcancel: (event: TouchEvent) => {
      // 触摸取消时清理状态
      loadmoreContainer.value?.classList.remove('pull-up-active')
      isScrolling = false

      if (scrollTimer) {
        clearTimeout(scrollTimer)
        scrollTimer = null
      }
    },
  })

  // 添加滚动事件监听器（被动）
  window.addEventListener(
    'scroll',
    () => {
      // 自动检查是否需要加载更多
      if (isScrolledToBottom() && canLoadMore() && !isScrolling) {
        triggerLoadMore()
      }
    },
    { passive: true },
  )

  console.log('✅ 加载更多组件触摸优化已应用')
}

onMounted(() => {
  nextTick(() => {
    if (props.enableTouchOptimization) {
      applyTouchOptimization()
    }
  })
})

onUnmounted(() => {
  // 清理定时器
  if (scrollTimer) {
    clearTimeout(scrollTimer)
  }
})
</script>

<style lang="scss" scoped>
.optimized-loadmore {
  padding: 30rpx 0;
  text-align: center;
  transition: all 0.3s ease;

  // 启用硬件加速
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  will-change: transform;

  &.pull-up-active {
    transform: translateY(-10rpx);
    background-color: rgba(77, 142, 255, 0.05);
  }
}

.loadmore-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20rpx;

  &.loading {
    color: #4d8eff;
  }

  &.finished {
    color: #999;
    gap: 30rpx;
  }

  &.error {
    color: #f5222d;
  }

  &.default {
    color: #666;
  }
}

.loadmore-text {
  font-size: 28rpx;
  line-height: 1.4;

  &.error-text {
    cursor: pointer;
    text-decoration: underline;

    &:hover {
      color: #ff4d4f;
    }
  }
}

.loadmore-spinner {
  display: flex;
  gap: 8rpx;
}

.spinner-dot {
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  background-color: #4d8eff;
  animation: spinner-bounce 1.4s ease-in-out infinite both;

  &:nth-child(1) {
    animation-delay: -0.32s;
  }

  &:nth-child(2) {
    animation-delay: -0.16s;
  }
}

@keyframes spinner-bounce {
  0%,
  80%,
  100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

.loadmore-line {
  width: 60rpx;
  height: 1rpx;
  background-color: #ddd;
}

// 响应式优化
@media (max-width: 768px) {
  .optimized-loadmore {
    padding: 20rpx 0;
  }

  .loadmore-text {
    font-size: 26rpx;
  }
}

// 深色模式支持
@media (prefers-color-scheme: dark) {
  .loadmore-content.finished {
    color: #666;
  }

  .loadmore-content.default {
    color: #999;
  }

  .loadmore-line {
    background-color: #444;
  }
}
</style>
