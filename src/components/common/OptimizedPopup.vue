<!--
优化的弹窗组件

基于 wot-design-uni 的 wd-popup 组件，修复了触摸事件监听器的性能警告
主要优化：
1. 为 touchmove 事件添加 passive 选项
2. 优化滚动性能
3. 减少不必要的事件监听器
-->
<template>
  <wd-popup
    v-model="internalVisible"
    :position="position"
    :safe-area-inset-bottom="safeAreaInsetBottom"
    :z-index="zIndex"
    :close-on-click-modal="closeOnClickModal"
    :duration="duration"
    :custom-style="customStyle"
    :custom-class="customClass"
    @open="handleOpen"
    @close="handleClose"
    @click-modal="handleClickModal"
  >
    <view ref="popupContent" class="optimized-popup-content" :class="contentClass">
      <slot />
    </view>
  </wd-popup>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { addAllPassiveTouchListeners } from '@/utils/touchEventFixSimple'

interface Props {
  modelValue: boolean
  position?: 'top' | 'bottom' | 'left' | 'right' | 'center'
  safeAreaInsetBottom?: boolean
  zIndex?: number
  closeOnClickModal?: boolean
  duration?: number
  customStyle?: string
  customClass?: string
  contentClass?: string
  // 是否启用触摸优化
  enableTouchOptimization?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'open'): void
  (e: 'close'): void
  (e: 'click-modal'): void
}

const props = withDefaults(defineProps<Props>(), {
  position: 'center',
  safeAreaInsetBottom: true,
  zIndex: 9999,
  closeOnClickModal: true,
  duration: 300,
  customStyle: '',
  customClass: '',
  contentClass: '',
  enableTouchOptimization: true,
})

const emit = defineEmits<Emits>()

// 内部可见状态
const internalVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

// 弹窗内容引用
const popupContent = ref<HTMLElement>()

// 触摸事件处理器
let touchStartY = 0
let touchMoveHandler: ((event: TouchEvent) => void) | null = null

/**
 * 处理弹窗打开事件
 */
function handleOpen() {
  emit('open')

  // 如果启用了触摸优化，在弹窗打开后应用优化
  if (props.enableTouchOptimization) {
    nextTick(() => {
      applyTouchOptimization()
    })
  }
}

/**
 * 处理弹窗关闭事件
 */
function handleClose() {
  emit('close')

  // 清理触摸事件监听器
  if (touchMoveHandler && popupContent.value) {
    popupContent.value.removeEventListener('touchmove', touchMoveHandler)
    touchMoveHandler = null
  }
}

/**
 * 处理点击遮罩事件
 */
function handleClickModal() {
  emit('click-modal')
}

/**
 * 应用触摸优化
 */
function applyTouchOptimization() {
  if (!popupContent.value) return

  // 使用优化的被动触摸事件监听器
  addAllPassiveTouchListeners(popupContent.value, {
    touchstart: (event: TouchEvent) => {
      touchStartY = event.touches[0].clientY
    },
    touchmove: (event: TouchEvent) => {
      // 对于底部弹窗，允许向下滑动关闭
      if (props.position === 'bottom') {
        const touch = event.touches[0]
        const deltaY = touch.clientY - touchStartY

        // 如果向下滑动距离超过阈值，关闭弹窗
        if (deltaY > 50) {
          internalVisible.value = false
        }
      }
    },
    touchend: (event: TouchEvent) => {
      // 触摸结束时的处理
    },
    touchcancel: (event: TouchEvent) => {
      // 触摸取消时的处理
    },
  })

  console.log('✅ 弹窗触摸优化已应用')
}

/**
 * 监听可见状态变化
 */
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue && props.enableTouchOptimization) {
      // 弹窗显示时应用优化
      nextTick(() => {
        applyTouchOptimization()
      })
    }
  },
)

onMounted(() => {
  // 组件挂载时如果弹窗已显示，应用优化
  if (props.modelValue && props.enableTouchOptimization) {
    nextTick(() => {
      applyTouchOptimization()
    })
  }
})

onUnmounted(() => {
  // 组件卸载时清理事件监听器
  if (touchMoveHandler && popupContent.value) {
    popupContent.value.removeEventListener('touchmove', touchMoveHandler)
  }
})
</script>

<style lang="scss" scoped>
.optimized-popup-content {
  position: relative;

  // 优化滚动性能
  -webkit-overflow-scrolling: touch;
  overflow-scrolling: touch;

  // 启用硬件加速
  transform: translateZ(0);
  will-change: transform;
}

// 为不同位置的弹窗提供特定样式
.optimized-popup-content.bottom-popup {
  border-radius: 20rpx 20rpx 0 0;
}

.optimized-popup-content.top-popup {
  border-radius: 0 0 20rpx 20rpx;
}

.optimized-popup-content.center-popup {
  border-radius: 20rpx;
}
</style>
