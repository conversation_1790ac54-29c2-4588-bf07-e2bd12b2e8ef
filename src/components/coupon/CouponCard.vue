<template>
  <view class="coupon-card" :class="[statusClass, { 'can-claim': canClaim }]" @click="handleClick">
    <!-- 优惠券主体 -->
    <view class="coupon-main">
      <!-- 左侧金额区域 -->
      <view class="coupon-amount" :class="typeClass">
        <text class="amount">{{ formatAmount }}</text>
        <text class="unit">{{ amountUnit }}</text>
        <text class="type">{{ typeText }}</text>
      </view>

      <!-- 中间信息区域 -->
      <view class="coupon-info">
        <text class="coupon-name">{{ actualCoupon.name }}</text>
        <text class="coupon-desc">{{ description }}</text>
        <text class="expire-time">{{ expireText }}</text>

        <!-- 商家信息 -->
        <view v-if="merchantName || merchantLogo" class="merchant-info">
          <image
            v-if="merchantLogo"
            :src="merchantLogo"
            class="merchant-logo"
            mode="aspectFill"
            @error="handleLogoError"
          />
          <view v-else-if="merchantName" class="merchant-logo-placeholder">
            <text class="logo-text">{{ merchantName.charAt(0) }}</text>
          </view>
          <text v-if="merchantName" class="merchant-name">{{ merchantName }}</text>
        </view>
      </view>

      <!-- 右侧状态区域 -->
      <view class="coupon-status">
        <!-- 可领取状态 -->
        <view v-if="canClaim" class="claim-btn" @click.stop="handleClaim">
          <text>{{ claimText }}</text>
        </view>

        <!-- 已使用/已过期状态 -->
        <view v-else-if="isUsed || isExpired" class="status-badge">
          <text>{{ statusText }}</text>
        </view>

        <!-- 可使用状态 -->
        <view v-else-if="canUse" class="use-btn">
          <text>立即使用</text>
        </view>

        <!-- 不可用状态 -->
        <view v-else class="disabled-badge">
          <text>{{ disabledReason }}</text>
        </view>
      </view>
    </view>

    <!-- 优惠券装饰 -->
    <view class="coupon-decoration">
      <view class="left-circle"></view>
      <view class="right-circle"></view>
      <view class="dash-line"></view>
    </view>

    <!-- 标签 -->
    <view v-if="showTags" class="coupon-tags">
      <text v-if="isNew" class="tag new-tag">新人专享</text>
      <text v-if="isHot" class="tag hot-tag">热门</text>
      <text v-if="isLimited" class="tag limited-tag">限量</text>
      <text v-if="isExpiringSoon" class="tag expire-tag">即将过期</text>
    </view>

    <!-- 进度条（限量券） -->
    <view v-if="showProgress" class="progress-bar">
      <view class="progress-bg">
        <view class="progress-fill" :style="{ width: progressPercent + '%' }"></view>
      </view>
      <text class="progress-text">已领{{ claimedCount }}/{{ totalCount }}</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { ICoupon, IUserCoupon } from '@/api/coupon.typings'
import { CouponType, CouponStatus } from '@/api/coupon.typings'

interface Props {
  // 优惠券数据（可能是ICoupon或IUserCoupon）
  coupon: ICoupon | IUserCoupon
  // 是否可以领取
  canClaim?: boolean
  // 是否可以使用
  canUse?: boolean
  // 不可用原因
  disabledReason?: string
  // 是否显示标签
  showTags?: boolean
  // 是否显示进度条
  showProgress?: boolean
  // 点击模式：claim(领取) | use(使用) | select(选择) | view(查看)
  mode?: 'claim' | 'use' | 'select' | 'view'
}

interface Emits {
  (e: 'click', coupon: ICoupon | IUserCoupon): void
  (e: 'claim', coupon: ICoupon | IUserCoupon): void
  (e: 'use', coupon: ICoupon | IUserCoupon): void
}

const props = withDefaults(defineProps<Props>(), {
  canClaim: false,
  canUse: true,
  disabledReason: '',
  showTags: true,
  showProgress: false,
  mode: 'view',
})

const emit = defineEmits<Emits>()

// 获取实际的优惠券数据
const actualCoupon = computed(() => {
  return 'coupon' in props.coupon ? props.coupon.coupon : props.coupon
})

// 用户优惠券数据（如果是用户优惠券）
const userCoupon = computed(() => {
  return 'coupon' in props.coupon ? (props.coupon as IUserCoupon) : null
})

// 状态计算
const isUsed = computed(() => userCoupon.value?.status === CouponStatus.USED)

// 过期状态检查（包含实际过期时间检查）
const isExpired = computed(() => {
  // 如果后端状态已经是已过期，直接返回true
  if (userCoupon.value?.status === CouponStatus.EXPIRED) {
    return true
  }

  // 检查实际过期时间
  const expireTime =
    userCoupon.value?.expire_time ||
    userCoupon.value?.coupon?.end_time ||
    actualCoupon.value.end_time
  if (expireTime) {
    const now = new Date()
    const expireDate = new Date(expireTime)
    if (now > expireDate) {
      // 实际已过期，但后端状态未更新
      return true
    }
  }

  return false
})

const isNew = computed(() => userCoupon.value?.is_new || false)

// 样式类
const statusClass = computed(() => {
  if (isUsed.value) return 'used'
  if (isExpired.value) return 'expired'
  if (!props.canUse && !props.canClaim) return 'disabled'
  return 'available'
})

const typeClass = computed(() => {
  switch (actualCoupon.value.type) {
    case CouponType.DISCOUNT:
      return 'discount'
    case CouponType.PERCENTAGE:
      return 'percentage'
    case CouponType.FREE_DELIVERY:
      return 'free-delivery'
    case CouponType.GIFT:
      return 'gift'
    case CouponType.CASHBACK:
      return 'cashback'
    default:
      return 'default'
  }
})

// 金额格式化
const formatAmount = computed(() => {
  const { type, amount } = actualCoupon.value

  switch (type) {
    case CouponType.DISCOUNT:
      return amount.toString()
    case CouponType.PERCENTAGE:
      return (amount * 10).toString()
    case CouponType.FREE_DELIVERY:
      return '免费'
    case CouponType.CASHBACK:
      return amount.toString()
    default:
      return amount.toString()
  }
})

const amountUnit = computed(() => {
  const { type } = actualCoupon.value

  switch (type) {
    case CouponType.DISCOUNT:
    case CouponType.CASHBACK:
      return '¥'
    case CouponType.PERCENTAGE:
      return '折'
    case CouponType.FREE_DELIVERY:
      return ''
    default:
      return '¥'
  }
})

const typeText = computed(() => {
  switch (actualCoupon.value.type) {
    case CouponType.DISCOUNT:
      return '满减券'
    case CouponType.PERCENTAGE:
      return '折扣券'
    case CouponType.FREE_DELIVERY:
      return '免配送费'
    case CouponType.GIFT:
      return '赠品券'
    case CouponType.CASHBACK:
      return '返现券'
    default:
      return '优惠券'
  }
})

// 描述文本
const description = computed(() => {
  const { type, amount, min_order_amount } = actualCoupon.value

  switch (type) {
    case CouponType.DISCOUNT:
      return `满¥${min_order_amount}减¥${amount}`
    case CouponType.PERCENTAGE:
      return `满¥${min_order_amount}享${amount * 10}折`
    case CouponType.FREE_DELIVERY:
      return `满¥${min_order_amount}免配送费`
    default:
      return actualCoupon.value.description || `满¥${min_order_amount}可用`
  }
})

// 过期时间文本
const expireText = computed(() => {
  // 优先使用用户优惠券的过期时间，如果没有则使用优惠券模板的结束时间
  const expireTime =
    userCoupon.value?.expire_time ||
    userCoupon.value?.coupon?.end_time ||
    actualCoupon.value.end_time

  if (!expireTime) {
    return '时间未知'
  }

  // 处理不同的时间格式
  let date: Date
  if (typeof expireTime === 'string') {
    // 如果是字符串，尝试解析
    date = new Date(expireTime)
  } else if (typeof expireTime === 'number') {
    // 如果是时间戳
    date = new Date(expireTime * 1000) // 假设是秒级时间戳
  } else {
    date = new Date(expireTime)
  }

  // 检查日期是否有效
  if (isNaN(date.getTime())) {
    return '时间格式错误'
  }

  const now = new Date()
  const diffDays = Math.ceil((date.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))

  if (diffDays < 0) {
    return '已过期'
  } else if (diffDays === 0) {
    return '今日过期'
  } else if (diffDays <= 3) {
    return `${diffDays}天后过期`
  } else {
    return `有效期至${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
  }
})

// 商家名称
const merchantName = computed(() => {
  return actualCoupon.value.merchant_name || ''
})

// 商家Logo
const merchantLogo = computed(() => {
  return actualCoupon.value.merchant_logo || ''
})

// 状态文本
const statusText = computed(() => {
  if (isUsed.value) return '已使用'
  if (isExpired.value) return '已过期'
  return ''
})

const claimText = computed(() => {
  return props.mode === 'claim' ? '立即领取' : '领取'
})

// 标签相关
const isHot = computed(() => {
  // 可以根据业务逻辑判断是否为热门券
  return actualCoupon.value.claimed_quantity > actualCoupon.value.total_quantity * 0.5
})

const isLimited = computed(() => {
  return actualCoupon.value.total_quantity > 0 && actualCoupon.value.total_quantity <= 100
})

const isExpiringSoon = computed(() => {
  if (!userCoupon.value) return false

  // 如果已使用或已过期，不显示即将过期角标
  if (isUsed.value || isExpired.value) {
    return false
  }

  // 检查days_to_expire字段
  if (userCoupon.value.days_to_expire !== undefined) {
    return userCoupon.value.days_to_expire <= 3 && userCoupon.value.days_to_expire >= 0
  }

  // 如果没有days_to_expire字段，通过过期时间计算
  const expireTime = userCoupon.value.expire_time || userCoupon.value.coupon?.end_time
  if (expireTime) {
    const now = new Date()
    const expireDate = new Date(expireTime)
    const diffDays = Math.ceil((expireDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
    return diffDays <= 3 && diffDays >= 0
  }

  return false
})

// 进度相关
const claimedCount = computed(() => actualCoupon.value.claimed_quantity || 0)
const totalCount = computed(() => actualCoupon.value.total_quantity || 0)
const progressPercent = computed(() => {
  if (totalCount.value === 0) return 0
  return Math.min((claimedCount.value / totalCount.value) * 100, 100)
})

// 事件处理
const handleClick = () => {
  emit('click', props.coupon)
}

const handleClaim = () => {
  emit('claim', props.coupon)
}

const handleLogoError = () => {
  console.log('商家Logo加载失败')
}
</script>

<style scoped lang="scss">
.coupon-card {
  position: relative;
  background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  &:active {
    transform: translateY(0);
  }

  &.used {
    opacity: 0.6;
    background: #f5f5f5;
  }

  &.expired {
    opacity: 0.6;
    background: #f5f5f5;
  }

  &.disabled {
    opacity: 0.6;
    cursor: not-allowed;

    &:hover {
      transform: none;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }

  &.can-claim {
    border: 2px solid #ff5500;

    &:hover {
      border-color: #ff7700;
    }
  }

  .coupon-main {
    display: flex;
    align-items: center;
    padding: 15px;
    position: relative;
    z-index: 2;

    .coupon-amount {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 80px;
      height: 60px;
      border-radius: 6px;
      color: white;
      position: relative;

      &.discount {
        background: linear-gradient(135deg, #ff5500, #ff7700);
      }

      &.percentage {
        background: linear-gradient(135deg, #00c851, #00a843);
      }

      &.free-delivery {
        background: linear-gradient(135deg, #007bff, #0056b3);
      }

      &.gift {
        background: linear-gradient(135deg, #e91e63, #c2185b);
      }

      &.cashback {
        background: linear-gradient(135deg, #9c27b0, #7b1fa2);
      }

      &.default {
        background: linear-gradient(135deg, #6c757d, #545b62);
      }

      .amount {
        font-size: 20px;
        font-weight: 700;
        line-height: 1;
      }

      .unit {
        font-size: 12px;
        font-weight: 500;
        margin-top: -2px;
      }

      .type {
        font-size: 10px;
        margin-top: 2px;
        opacity: 0.9;
      }
    }

    .coupon-info {
      flex: 1;
      padding: 0 15px;
      display: flex;
      flex-direction: column;
      gap: 4px;

      .coupon-name {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        line-height: 1.2;
      }

      .coupon-desc {
        font-size: 13px;
        color: #666;
        line-height: 1.2;
      }

      .expire-time {
        font-size: 11px;
        color: #999;
      }

      .merchant-info {
        display: flex;
        align-items: center;
        gap: 6px;
        margin-top: 2px;

        .merchant-logo {
          width: 16px;
          height: 16px;
          border-radius: 50%;
          flex-shrink: 0;
        }

        .merchant-logo-placeholder {
          width: 16px;
          height: 16px;
          border-radius: 50%;
          background: #ff5500;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;

          .logo-text {
            font-size: 8px;
            color: white;
            font-weight: 600;
          }
        }

        .merchant-name {
          font-size: 11px;
          color: #ff5500;
          font-weight: 500;
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }

    .coupon-status {
      display: flex;
      align-items: center;

      .claim-btn {
        background: #ff5500;
        color: white;
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;

        &:hover {
          background: #ff7700;
        }

        &:active {
          transform: scale(0.95);
        }
      }

      .use-btn {
        background: transparent;
        color: #ff5500;
        border: 1px solid #ff5500;
        padding: 6px 12px;
        border-radius: 15px;
        font-size: 11px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;

        &:hover {
          background: #ff5500;
          color: white;
        }
      }

      .status-badge {
        background: #f5f5f5;
        color: #999;
        padding: 6px 12px;
        border-radius: 15px;
        font-size: 11px;
      }

      .disabled-badge {
        color: #999;
        font-size: 11px;
      }
    }
  }

  .coupon-decoration {
    position: absolute;
    top: 0;
    left: 95px;
    right: 0;
    height: 100%;
    pointer-events: none;

    .left-circle {
      position: absolute;
      left: -6px;
      top: 50%;
      transform: translateY(-50%);
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background: #f5f5f5;
    }

    .right-circle {
      position: absolute;
      right: -6px;
      top: 50%;
      transform: translateY(-50%);
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background: #f5f5f5;
    }

    .dash-line {
      position: absolute;
      left: 0;
      top: 50%;
      right: 0;
      height: 1px;
      background: repeating-linear-gradient(
        to right,
        #ddd 0,
        #ddd 4px,
        transparent 4px,
        transparent 8px
      );
    }
  }

  .coupon-tags {
    position: absolute;
    top: 8px;
    right: 8px;
    display: flex;
    gap: 4px;
    z-index: 3;

    .tag {
      padding: 2px 6px;
      border-radius: 8px;
      font-size: 10px;
      font-weight: 500;
      color: white;

      &.new-tag {
        background: #ff5500;
      }

      &.hot-tag {
        background: #e91e63;
      }

      &.limited-tag {
        background: #9c27b0;
      }

      &.expire-tag {
        background: #f44336;
      }
    }
  }

  .progress-bar {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 8px 15px;
    background: rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    gap: 8px;

    .progress-bg {
      flex: 1;
      height: 4px;
      background: #e0e0e0;
      border-radius: 2px;
      overflow: hidden;

      .progress-fill {
        height: 100%;
        background: #ff5500;
        border-radius: 2px;
        transition: width 0.3s ease;
      }
    }

    .progress-text {
      font-size: 10px;
      color: #666;
      white-space: nowrap;
    }
  }
}
</style>
