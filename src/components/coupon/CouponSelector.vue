<template>
  <view class="coupon-selector">
    <!-- 当前选中的优惠券显示 -->
    <view class="current-coupon" @click="showModal = true">
      <view v-if="!selectedCoupon" class="no-coupon">
        <wd-icon name="coupon" size="20" color="#ff5500" />
        <text class="coupon-text">选择优惠券</text>
        <text v-if="availableCount > 0" class="available-count">{{ availableCount }}张可用</text>
      </view>
      <view v-else class="selected-coupon">
        <wd-icon name="coupon" size="20" color="#ff5500" />
        <view class="coupon-info">
          <text class="coupon-name">{{ selectedCoupon.coupon.name }}</text>
          <text class="coupon-discount">
            -¥{{ selectedCoupon.discount_amount || selectedCoupon.coupon.amount }}
          </text>
        </view>
      </view>
      <wd-icon name="arrow-right" size="16" color="#999" />
    </view>

    <!-- 优惠券选择弹窗 - 使用优化的弹窗组件 -->
    <OptimizedPopup
      v-model="showModal"
      position="bottom"
      :safe-area-inset-bottom="true"
      :z-index="9999"
      content-class="bottom-popup"
      :enable-touch-optimization="true"
    >
      <view class="coupon-modal">
        <view class="modal-header">
          <text class="title">选择优惠券</text>
          <wd-icon name="close" size="20" color="#999" @click="showModal = false" />
        </view>

        <scroll-view class="coupon-list" scroll-y>
          <!-- 不使用优惠券选项 -->
          <view
            class="coupon-item no-coupon-option"
            :class="{ active: !selectedCoupon }"
            @click="selectCoupon(null)"
          >
            <view class="coupon-content">
              <text class="option-text">不使用优惠券</text>
            </view>
            <wd-icon
              :name="!selectedCoupon ? 'check-circle' : 'circle'"
              :color="!selectedCoupon ? '#ff5500' : '#ddd'"
              size="20"
            />
          </view>

          <!-- 可用优惠券列表 -->
          <view v-if="availableCoupons.length > 0" class="coupon-section">
            <text class="section-title">可用优惠券 ({{ availableCoupons.length }})</text>
            <view
              v-for="coupon in availableCoupons"
              :key="coupon.id"
              class="coupon-item"
              :class="{ active: selectedCoupon?.id === coupon.id }"
              @click="selectCoupon(coupon)"
            >
              <view class="coupon-card">
                <view class="coupon-amount" :class="getCouponTypeClass(coupon.coupon.type)">
                  <text class="amount">{{ formatCouponAmount(coupon) }}</text>
                  <text class="type">{{ getCouponTypeText(coupon.coupon.type) }}</text>
                </view>
                <view class="coupon-details">
                  <text class="coupon-name">{{ coupon.coupon.name }}</text>
                  <text class="coupon-desc">{{ getCouponDescription(coupon) }}</text>
                  <text class="expire-time">{{ getExpireText(coupon) }}</text>
                </view>
              </view>
              <wd-icon
                :name="selectedCoupon?.id === coupon.id ? 'check-circle' : 'circle'"
                :color="selectedCoupon?.id === coupon.id ? '#ff5500' : '#ddd'"
                size="20"
              />
            </view>
          </view>

          <!-- 不可用优惠券列表 -->
          <view v-if="unavailableCoupons.length > 0" class="coupon-section">
            <text class="section-title">不可用优惠券 ({{ unavailableCoupons.length }})</text>
            <view
              v-for="coupon in unavailableCoupons"
              :key="coupon.id"
              class="coupon-item disabled"
            >
              <view class="coupon-card">
                <view class="coupon-amount disabled">
                  <text class="amount">{{ formatCouponAmount(coupon) }}</text>
                  <text class="type">{{ getCouponTypeText(coupon.coupon.type) }}</text>
                </view>
                <view class="coupon-details">
                  <text class="coupon-name">{{ coupon.coupon.name }}</text>
                  <text class="coupon-desc">{{ getCouponDescription(coupon) }}</text>
                  <text class="unavailable-reason">{{ coupon.reason || '不满足使用条件' }}</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 空状态 -->
          <view
            v-if="availableCoupons.length === 0 && unavailableCoupons.length === 0 && !loading"
            class="empty-state"
          >
            <wd-icon name="coupon" size="60" color="#ddd" />
            <text class="empty-text">暂无可用优惠券</text>
            <text class="empty-tip">去优惠券中心看看吧</text>
          </view>

          <!-- 加载状态 -->
          <view v-if="loading" class="loading-state">
            <wd-loading size="30" />
            <text class="loading-text">加载中...</text>
          </view>
        </scroll-view>

        <!-- 底部操作区 -->
        <view class="modal-footer">
          <view class="coupon-center-btn" @click="goToCouponCenter">
            <wd-icon name="add" size="16" color="#ff5500" />
            <text>优惠券中心</text>
          </view>
        </view>
      </view>
    </OptimizedPopup>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useCouponStore } from '@/store/coupon'
import type { IUserCoupon } from '@/api/coupon.typings'
import { CouponType } from '@/api/coupon.typings'
import OptimizedPopup from '@/components/common/OptimizedPopup.vue'

interface Props {
  merchantId: number
  totalAmount: number
  foodIds?: string
}

interface Emits {
  (e: 'select', merchantId: number, coupon: IUserCoupon | null): void
}

const props = withDefaults(defineProps<Props>(), {
  foodIds: '',
})

const emit = defineEmits<Emits>()

const couponStore = useCouponStore()
const showModal = ref(false)

// 计算属性
const availableCoupons = computed(() =>
  couponStore.getAvailableCouponsForMerchant(props.merchantId),
)
const unavailableCoupons = computed(() =>
  couponStore.getUnavailableCouponsForMerchant(props.merchantId),
)
const selectedCoupon = computed(() => couponStore.getSelectedCouponForMerchant(props.merchantId))
const availableCount = computed(() => availableCoupons.value.length)
const loading = computed(() => couponStore.loading.availableCoupons)

// 方法
const selectCoupon = (coupon: IUserCoupon | null) => {
  couponStore.selectCouponForMerchant(props.merchantId, coupon)
  emit('select', props.merchantId, coupon)
  showModal.value = false
}

const formatCouponAmount = (coupon: IUserCoupon) => {
  switch (coupon.coupon.type) {
    case CouponType.DISCOUNT:
      return `¥${coupon.coupon.amount}`
    case CouponType.PERCENTAGE:
      return `${coupon.coupon.amount * 10}折`
    case CouponType.FREE_DELIVERY:
      return '免配送费'
    default:
      return `¥${coupon.coupon.amount}`
  }
}

const getCouponTypeText = (type: CouponType) => {
  switch (type) {
    case CouponType.DISCOUNT:
      return '满减券'
    case CouponType.PERCENTAGE:
      return '折扣券'
    case CouponType.FREE_DELIVERY:
      return '免配送费'
    case CouponType.GIFT:
      return '赠品券'
    case CouponType.CASHBACK:
      return '返现券'
    default:
      return '优惠券'
  }
}

const getCouponTypeClass = (type: CouponType) => {
  switch (type) {
    case CouponType.DISCOUNT:
      return 'discount'
    case CouponType.PERCENTAGE:
      return 'percentage'
    case CouponType.FREE_DELIVERY:
      return 'free-delivery'
    default:
      return 'default'
  }
}

const getCouponDescription = (coupon: IUserCoupon) => {
  const { type, amount, min_order_amount } = coupon.coupon

  switch (type) {
    case CouponType.DISCOUNT:
      return `满¥${min_order_amount}减¥${amount}`
    case CouponType.PERCENTAGE:
      return `满¥${min_order_amount}享${amount * 10}折`
    case CouponType.FREE_DELIVERY:
      return `满¥${min_order_amount}免配送费`
    default:
      return coupon.coupon.description || ''
  }
}

// 获取过期时间文本（与CouponCard保持一致的逻辑）
const getExpireText = (coupon: any) => {
  // 优先使用用户优惠券的过期时间，如果没有则使用优惠券模板的结束时间
  const expireTime = coupon.expire_time || coupon.coupon?.end_time

  console.log('🕒 CouponSelector 时间调试:', {
    coupon,
    expireTime,
    couponExpireTime: coupon.expire_time,
    couponEndTime: coupon.coupon?.end_time,
  })

  if (!expireTime) {
    console.warn('⚠️ CouponSelector 没有过期时间')
    return '时间未知'
  }

  // 处理不同的时间格式
  let date: Date
  if (typeof expireTime === 'string') {
    date = new Date(expireTime)
  } else if (typeof expireTime === 'number') {
    // 处理时间戳，检查是秒还是毫秒
    date = expireTime > 1000000000000 ? new Date(expireTime) : new Date(expireTime * 1000)
  } else {
    date = new Date(expireTime)
  }

  // 检查日期是否有效
  if (isNaN(date.getTime())) {
    console.error('❌ CouponSelector 无效的日期格式:', expireTime)
    return '时间格式错误'
  }

  const now = new Date()
  const diffDays = Math.ceil((date.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))

  console.log('📅 CouponSelector 时间计算结果:', {
    expireTime,
    date: date.toISOString(),
    now: now.toISOString(),
    diffDays,
  })

  if (diffDays < 0) {
    return '已过期'
  } else if (diffDays === 0) {
    return '今日过期'
  } else if (diffDays <= 3) {
    return `${diffDays}天后过期`
  } else {
    return `有效期至${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
  }
}

const formatDate = (dateStr: string | number | undefined) => {
  console.log('🕒 CouponSelector formatDate 调试:', { dateStr, type: typeof dateStr })

  if (!dateStr) {
    console.warn('⚠️ CouponSelector 没有日期数据')
    return '时间未知'
  }

  // 处理不同的时间格式
  let date: Date
  if (typeof dateStr === 'string') {
    date = new Date(dateStr)
  } else if (typeof dateStr === 'number') {
    // 处理时间戳，检查是秒还是毫秒
    date = dateStr > 1000000000000 ? new Date(dateStr) : new Date(dateStr * 1000)
  } else {
    date = new Date(dateStr)
  }

  // 检查日期是否有效
  if (isNaN(date.getTime())) {
    console.error('❌ CouponSelector 无效的日期格式:', dateStr)
    return '时间格式错误'
  }

  console.log('📅 CouponSelector 日期解析结果:', {
    input: dateStr,
    parsed: date.toISOString(),
    formatted: `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`,
  })

  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
}

const goToCouponCenter = () => {
  showModal.value = false
  uni.navigateTo({
    url: '/pages/coupon/center',
  })
}

// 加载优惠券方法
const loadCoupons = async () => {
  try {
    console.log('🎫 CouponSelector 加载优惠券:', {
      merchantId: props.merchantId,
      totalAmount: props.totalAmount,
      foodIds: props.foodIds,
    })

    await couponStore.fetchAvailableCouponsForOrder({
      merchant_id: props.merchantId,
      total_amount: props.totalAmount,
      food_ids: props.foodIds,
    })

    console.log('🎫 优惠券加载完成:', {
      available: availableCoupons.value.length,
      unavailable: unavailableCoupons.value.length,
    })
  } catch (error) {
    console.error('❌ 加载优惠券失败:', error)
    uni.showToast({
      title: '加载优惠券失败',
      icon: 'none',
      duration: 2000,
    })
  }
}

// 监听参数变化，重新加载优惠券
watch(
  [() => props.merchantId, () => props.totalAmount, () => props.foodIds],
  () => {
    if (props.merchantId && props.totalAmount > 0) {
      console.log('🎫 CouponSelector 参数变化，重新加载优惠券:', {
        merchantId: props.merchantId,
        totalAmount: props.totalAmount,
        foodIds: props.foodIds,
      })
      loadCoupons()
    }
  },
  { immediate: true },
)

// 检查是否需要加载优惠券数据
const shouldLoadCoupons = computed(() => {
  return (
    props.merchantId &&
    props.totalAmount > 0 &&
    availableCoupons.value.length === 0 &&
    unavailableCoupons.value.length === 0 &&
    !loading.value
  )
})

// 当组件显示时检查是否需要加载数据
watch(showModal, (newVal) => {
  if (newVal && shouldLoadCoupons.value) {
    console.log('🎫 打开优惠券选择器，检测到需要加载数据')
    loadCoupons()
  }
})

onMounted(() => {
  console.log('🎫 CouponSelector 组件挂载:', {
    merchantId: props.merchantId,
    totalAmount: props.totalAmount,
    foodIds: props.foodIds,
    shouldLoad: shouldLoadCoupons.value,
    availableCouponsCount: availableCoupons.value.length,
    unavailableCouponsCount: unavailableCoupons.value.length,
  })

  if (props.merchantId && props.totalAmount > 0) {
    loadCoupons()
  }
})
</script>

<style scoped lang="scss">
.coupon-selector {
  width: 100%;
}

.current-coupon {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px;
  background: #fff;
  border-radius: 8px;
  border: 1px solid #eee;
  cursor: pointer;
  transition: all 0.2s;

  &:active {
    background: #f8f8f8;
  }

  .no-coupon {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;

    .coupon-text {
      font-size: 14px;
      color: #333;
    }

    .available-count {
      background: #ff5500;
      color: white;
      padding: 2px 6px;
      border-radius: 10px;
      font-size: 12px;
    }
  }

  .selected-coupon {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;

    .coupon-info {
      display: flex;
      flex-direction: column;
      gap: 2px;

      .coupon-name {
        font-size: 14px;
        color: #333;
      }

      .coupon-discount {
        font-size: 12px;
        color: #ff5500;
        font-weight: 500;
      }
    }
  }
}

.coupon-modal {
  background: white;
  border-radius: 12px 12px 0 0;
  max-height: 80vh;
  display: flex;
  flex-direction: column;

  .modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 20px;
    border-bottom: 1px solid #eee;

    .title {
      font-size: 16px;
      font-weight: 500;
      color: #333;
    }
  }

  .coupon-list {
    flex: 1;
    max-height: 60vh;
    padding: 10px 0;
  }

  .no-coupon-option {
    padding: 15px 20px;
    border-bottom: 1px solid #f5f5f5;

    .coupon-content {
      flex: 1;

      .option-text {
        font-size: 14px;
        color: #333;
      }
    }
  }

  .coupon-section {
    margin-bottom: 20px;

    .section-title {
      display: block;
      padding: 10px 20px;
      font-size: 12px;
      color: #666;
      background: #f8f8f8;
    }
  }

  .coupon-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 20px;
    border-bottom: 1px solid #f5f5f5;
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover {
      background: #f8f8f8;
    }

    &.active {
      background: #fff0e6;
    }

    &.disabled {
      opacity: 0.6;
      cursor: not-allowed;

      &:hover {
        background: transparent;
      }
    }

    .coupon-card {
      display: flex;
      align-items: center;
      gap: 12px;
      flex: 1;

      .coupon-amount {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 60px;
        height: 50px;
        border-radius: 6px;
        color: white;

        &.discount {
          background: linear-gradient(135deg, #ff5500, #ff7700);
        }

        &.percentage {
          background: linear-gradient(135deg, #00c851, #00a843);
        }

        &.free-delivery {
          background: linear-gradient(135deg, #007bff, #0056b3);
        }

        &.default {
          background: linear-gradient(135deg, #6c757d, #545b62);
        }

        &.disabled {
          background: #ccc;
        }

        .amount {
          font-size: 14px;
          font-weight: 600;
          line-height: 1;
        }

        .type {
          font-size: 10px;
          line-height: 1;
          margin-top: 2px;
        }
      }

      .coupon-details {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 4px;

        .coupon-name {
          font-size: 14px;
          font-weight: 500;
          color: #333;
        }

        .coupon-desc {
          font-size: 12px;
          color: #666;
        }

        .expire-time {
          font-size: 11px;
          color: #999;
        }

        .unavailable-reason {
          font-size: 11px;
          color: #ff5500;
        }
      }
    }
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;

    .empty-text {
      font-size: 14px;
      color: #666;
      margin-top: 10px;
    }

    .empty-tip {
      font-size: 12px;
      color: #999;
      margin-top: 5px;
    }
  }

  .loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;

    .loading-text {
      font-size: 12px;
      color: #666;
      margin-top: 10px;
    }
  }

  .modal-footer {
    padding: 15px 20px;
    border-top: 1px solid #eee;

    .coupon-center-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 5px;
      padding: 10px;
      color: #ff5500;
      font-size: 14px;
      cursor: pointer;

      &:active {
        opacity: 0.7;
      }
    }
  }
}
</style>
