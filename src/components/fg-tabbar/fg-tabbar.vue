<template>
  <view class="custom-tabbar">
    <wd-tabbar
      fixed
      v-model="tabbarStore.curIdx"
      bordered
      safeAreaInsetBottom
      placeholder
      @change="selectTabBar"
      class="tabbar-container"
    >
      <block v-for="(item, idx) in tabbarList" :key="item.path">
        <wd-tabbar-item
          v-if="item.iconType === 'wot'"
          :title="item.text"
          :icon="item.icon"
          :class="{ 'special-item': item.isSpecial }"
        ></wd-tabbar-item>
        <wd-tabbar-item
          v-else-if="item.iconType === 'unocss' || item.iconType === 'iconfont'"
          :title="item.text"
          :class="{ 'special-item': item.isSpecial }"
        >
          <template #icon>
            <view
              h-40rpx
              w-40rpx
              :class="[item.icon, idx === tabbarStore.curIdx ? 'is-active' : 'is-inactive']"
            ></view>
          </template>
        </wd-tabbar-item>
        <wd-tabbar-item
          v-else-if="item.iconType === 'local'"
          :title="item.text"
          :class="{ 'special-item': item.isSpecial }"
        >
          <template #icon>
            <view
              :class="{
                'home-icon-container': item.isSpecial,
                active: item.isSpecial && idx === tabbarStore.curIdx,
              }"
            >
              <image :src="item.icon" :class="item.isSpecial ? 'home-icon' : 'normal-icon'" />
            </view>
          </template>
        </wd-tabbar-item>
      </block>
    </wd-tabbar>
  </view>
</template>

<script setup lang="ts">
// unocss icon 默认不生效，需要在这里写一遍才能生效！注释掉也是生效的，但是必须要有！
// i-carbon-code
import { ref } from 'vue'
import { tabBarList as _tabBarList } from '@/utils/index'
import { tabbarStore } from './tabbar'

const tabbar = ref(1)
/** tabbarList 里面的 path 从 pages.config.ts 得到 */
const tabbarList = _tabBarList.map((item) => ({ ...item, path: `/${item.pagePath}` }))

function selectTabBar({ value: index }: { value: number }) {
  const url = tabbarList[index].path
  tabbarStore.setCurIdx(index)
  uni.switchTab({ url })
}
onLoad(() => {
  // 解决原生 tabBar 未隐藏导致有2个 tabBar 的问题
  // #ifdef APP-PLUS | H5
  uni.hideTabBar({
    fail(err) {
      console.log('hideTabBar fail: ', err)
    },
    success(res) {
      console.log('hideTabBar success: ', res)
    },
  })
  // #endif
})
</script>

<style scoped>
.custom-tabbar {
  position: relative;
}

.tabbar-container {
  position: relative;
}

/* 主页特殊样式 */
.special-item {
  position: relative;
}

.home-icon-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  border-radius: 50%;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);
  transform: translateY(-10rpx);
  transition: all 0.3s ease;
}

.home-icon-container.active {
  background: linear-gradient(135deg, #ff4757, #ff6b6b);
  box-shadow: 0 6rpx 16rpx rgba(255, 107, 107, 0.4);
  transform: translateY(-12rpx) scale(1.05);
}

.home-icon-container::before {
  content: '';
  position: absolute;
  top: -8rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 80rpx;
  height: 20rpx;
  background: #fff;
  border-radius: 0 0 40rpx 40rpx;
  z-index: -1;
}

.home-icon {
  width: 32rpx !important;
  height: 32rpx !important;
  filter: brightness(0) invert(1);
}

.normal-icon {
  width: 40rpx;
  height: 40rpx;
}

/* 为主页项添加特殊的底部间距 */
.special-item :deep(.wd-tabbar-item__content) {
  padding-bottom: 8rpx;
}

/* 调整主页文字位置 */
.special-item :deep(.wd-tabbar-item__title) {
  margin-top: 4rpx;
  font-size: 20rpx;
  font-weight: 500;
}

/* 其他图标的样式调整 */
:deep(.wd-tabbar-item__icon) {
  margin-bottom: 4rpx;
}

:deep(.wd-tabbar-item__title) {
  font-size: 22rpx;
}
</style>
