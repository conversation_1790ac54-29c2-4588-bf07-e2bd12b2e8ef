<!--
TabBar 页面布局组件

为有TabBar的页面提供标准布局：
1. 固定顶部标题栏
2. 可滚动的内容区域
3. 固定底部TabBar
4. 自动处理安全区域和间距
-->
<template>
  <view class="tabbar-layout">
    <!-- 固定顶部标题栏 -->
    <FixedHeader
      v-if="showHeader"
      ref="headerRef"
      :title="title"
      :show-back="showBack"
      :with-status-bar="withStatusBar"
      :background-color="headerBgColor"
      :text-color="headerTextColor"
      :transparent="headerTransparent"
      @back="handleBack"
    >
      <template #left>
        <slot name="header-left"></slot>
      </template>
      <template #center>
        <slot name="header-center"></slot>
      </template>
      <template #right>
        <slot name="header-right"></slot>
      </template>
    </FixedHeader>

    <!-- 内容区域 -->
    <view
      class="content-area"
      :style="contentStyle"
      :class="{ 'with-header': showHeader, 'with-tabbar': showTabBar }"
    >
      <!-- 顶部占位，避免被固定标题栏遮挡 -->
      <view
        v-if="showHeader"
        class="header-placeholder"
        :style="{ height: headerHeight + 'px' }"
      ></view>

      <!-- 页面内容 -->
      <view class="page-content">
        <slot></slot>
      </view>

      <!-- 底部占位，避免被TabBar遮挡 -->
      <view
        v-if="showTabBar"
        class="tabbar-placeholder"
        :style="{ height: tabbarHeight + 'px' }"
      ></view>
    </view>

    <!-- TabBar组件 -->
    <fg-tabbar v-if="showTabBar" />
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import FixedHeader from '@/components/common/FixedHeader.vue'

interface Props {
  // 页面标题
  title?: string
  // 是否显示标题栏
  showHeader?: boolean
  // 是否显示返回按钮
  showBack?: boolean
  // 是否显示TabBar
  showTabBar?: boolean
  // 是否包含状态栏
  withStatusBar?: boolean
  // 标题栏背景色
  headerBgColor?: string
  // 标题栏文字颜色
  headerTextColor?: string
  // 标题栏是否透明
  headerTransparent?: boolean
  // 内容区域背景色
  contentBgColor?: string
  // 是否启用下拉刷新
  enablePullRefresh?: boolean
  // 是否启用上拉加载
  enableLoadMore?: boolean
}

interface Emits {
  (e: 'back'): void
  (e: 'pullRefresh'): void
  (e: 'loadMore'): void
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  showHeader: true,
  showBack: false,
  showTabBar: true,
  withStatusBar: true,
  headerBgColor: '#ffffff',
  headerTextColor: '#333333',
  headerTransparent: false,
  contentBgColor: '#f5f5f5',
  enablePullRefresh: false,
  enableLoadMore: false,
})

const emit = defineEmits<Emits>()

// 组件引用
const headerRef = ref()

// 高度相关
const headerHeight = ref(0)
const tabbarHeight = ref(100) // TabBar默认高度

// 内容区域样式
const contentStyle = computed(() => {
  return {
    backgroundColor: props.contentBgColor,
    minHeight: '100vh',
  }
})

/**
 * 处理返回事件
 */
function handleBack() {
  emit('back')
}

/**
 * 获取标题栏高度
 */
function getHeaderHeight() {
  nextTick(() => {
    if (headerRef.value && headerRef.value.totalHeight) {
      headerHeight.value = headerRef.value.totalHeight
    } else {
      // 默认高度：状态栏(20px) + 导航栏(44px)
      headerHeight.value = props.withStatusBar ? 64 : 44
    }
  })
}

/**
 * 获取TabBar高度
 */
function getTabBarHeight() {
  try {
    const systemInfo = uni.getSystemInfoSync()
    // TabBar高度 + 安全区域底部高度
    tabbarHeight.value = 100 + (systemInfo.safeAreaInsets?.bottom || 0)
  } catch (e) {
    console.warn('获取TabBar高度失败:', e)
    tabbarHeight.value = 100
  }
}

onMounted(() => {
  getHeaderHeight()
  getTabBarHeight()
})

// 暴露方法给父组件
defineExpose({
  headerHeight: computed(() => headerHeight.value),
  tabbarHeight: computed(() => tabbarHeight.value),
  refresh: () => {
    getHeaderHeight()
    getTabBarHeight()
  },
})
</script>

<style lang="scss" scoped>
.tabbar-layout {
  position: relative;
  width: 100%;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.content-area {
  position: relative;
  width: 100%;
  min-height: 100vh;

  &.with-header {
    // 有标题栏时的样式调整
  }

  &.with-tabbar {
    // 有TabBar时的样式调整
  }
}

.header-placeholder {
  width: 100%;
  flex-shrink: 0;
}

.page-content {
  position: relative;
  width: 100%;
  flex: 1;
  padding: 0;
}

.tabbar-placeholder {
  width: 100%;
  flex-shrink: 0;
}

/* 滚动优化 */
.content-area {
  -webkit-overflow-scrolling: touch;
  overflow-scrolling: touch;
}

/* 安全区域适配 */
.content-area {
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
}

/* 不同平台的适配 */
/* #ifdef H5 */
.tabbar-layout {
  max-width: 100vw;
  overflow-x: hidden;
}
/* #endif */

/* #ifdef MP-WEIXIN */
.content-area {
  padding-left: 0;
  padding-right: 0;
}
/* #endif */

/* #ifdef APP-PLUS */
.content-area {
  padding-bottom: env(safe-area-inset-bottom);
}
/* #endif */
</style>
