<!--
 * @file PaymentSelector.vue
 * @description 支付方式选择组件
 * <AUTHOR> Assistant
 * @date 2025-01-27
 * @features
 *   - 支持多种支付方式选择（微信支付、支付宝、余额支付）
 *   - 显示订单金额汇总
 *   - 支持自定义支付方式列表
 *   - 提供支付确认回调
 *   - 美观的UI设计和交互效果
-->

<template>
  <wd-popup
    v-model="visible"
    position="bottom"
    :safe-area-inset-bottom="true"
    :z-index="9999"
    :lock-scroll="true"
    @close="handleClose"
  >
    <view class="payment-popup">
      <view class="popup-header">
        <view class="popup-title">选择支付方式</view>
        <wd-icon name="close" size="20" color="#999" @click="handleClose" />
      </view>

      <view class="payment-list">
        <view
          v-for="method in paymentMethods"
          :key="method.code"
          class="payment-item"
          :class="{ selected: selectedPaymentMethod === method.code }"
          @click="selectPaymentMethod(method.code)"
        >
          <view class="payment-content">
            <view class="payment-icon">
              <image :src="method.icon" class="method-icon" mode="aspectFit" />
            </view>
            <view class="payment-info">
              <text class="method-name">{{ method.name }}</text>
              <text class="method-desc">{{ method.description }}</text>
            </view>
          </view>
          <view v-if="selectedPaymentMethod === method.code" class="payment-selected">
            <wd-icon name="check" color="#ff5500" size="20" />
          </view>
        </view>
      </view>

      <view class="payment-footer">
        <view class="order-summary">
          <view class="summary-row">
            <text class="summary-label">商品金额</text>
            <text class="summary-value">¥{{ orderSummary.itemAmount.toFixed(2) }}</text>
          </view>
          <view v-if="orderSummary.deliveryFee > 0" class="summary-row">
            <text class="summary-label">配送费</text>
            <text class="summary-value">¥{{ orderSummary.deliveryFee.toFixed(2) }}</text>
          </view>
          <view v-if="orderSummary.discount > 0" class="summary-row">
            <text class="summary-label">优惠金额</text>
            <text class="summary-value discount">-¥{{ orderSummary.discount.toFixed(2) }}</text>
          </view>
          <view class="summary-row total">
            <text class="summary-label">实付金额</text>
            <text class="summary-value">¥{{ orderSummary.totalAmount.toFixed(2) }}</text>
          </view>
        </view>
        <wd-button
          type="primary"
          :disabled="!selectedPaymentMethod || loading"
          :loading="loading"
          @click="handleConfirmPayment"
        >
          {{ loading ? '处理中...' : `确认支付 ¥${orderSummary.totalAmount.toFixed(2)}` }}
        </wd-button>
      </view>
    </view>
  </wd-popup>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

/**
 * 支付方式接口定义
 */
interface PaymentMethod {
  code: string
  name: string
  description: string
  icon: string
}

/**
 * 订单汇总接口定义
 */
interface OrderSummary {
  itemAmount: number // 商品金额
  deliveryFee: number // 配送费
  discount: number // 优惠金额
  totalAmount: number // 实付金额
}

/**
 * 组件属性定义
 */
interface Props {
  modelValue: boolean // 弹窗显示状态
  paymentMethods?: PaymentMethod[] // 支付方式列表
  orderSummary: OrderSummary // 订单汇总信息
  loading?: boolean // 加载状态
}

/**
 * 组件事件定义
 */
interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm', paymentMethod: string): void
  (e: 'close'): void
}

// 组件属性
const props = withDefaults(defineProps<Props>(), {
  paymentMethods: () => [
    {
      code: 'wechat',
      name: '微信支付',
      description: '推荐移动端用户使用',
      icon: '/static/images/payment/wechat.png',
    },
    {
      code: 'alipay',
      name: '支付宝',
      description: '适合所有平台用户',
      icon: '/static/images/payment/alipay.png',
    },
    {
      code: 'balance',
      name: '余额支付',
      description: '使用账户余额支付',
      icon: '/static/images/payment/balance.png',
    },
  ],
  loading: false,
})

// 组件事件
const emit = defineEmits<Emits>()

// 响应式数据
const selectedPaymentMethod = ref('')

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

/**
 * 选择支付方式
 * @param method 支付方式代码
 */
const selectPaymentMethod = (method: string) => {
  selectedPaymentMethod.value = method
}

/**
 * 处理关闭弹窗
 */
const handleClose = () => {
  selectedPaymentMethod.value = ''
  emit('close')
  emit('update:modelValue', false)
}

/**
 * 处理确认支付
 */
const handleConfirmPayment = () => {
  if (!selectedPaymentMethod.value) {
    uni.showToast({
      title: '请选择支付方式',
      icon: 'none',
    })
    return
  }

  emit('confirm', selectedPaymentMethod.value)
}

// 监听弹窗关闭，重置选择状态
watch(
  () => props.modelValue,
  (newVal) => {
    if (!newVal) {
      selectedPaymentMethod.value = ''
    }
  },
)
</script>

<style lang="scss" scoped>
// 支付方式弹窗样式
.payment-popup {
  max-height: 80vh;
  min-height: 400px;
  background-color: #fff;
  border-radius: 12px 12px 0 0;
  position: relative;
  z-index: 10000;

  .popup-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    position: sticky;
    top: 0;
    background-color: #fff;
    z-index: 10001;

    .popup-title {
      font-size: 16px;
      font-weight: 500;
      color: #333;
    }
  }

  .payment-list {
    padding: 20px;
    max-height: 40vh;
    overflow-y: auto;
  }

  .payment-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px;
    margin-bottom: 10px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    cursor: pointer;
    transition: all 0.2s;

    &:last-child {
      margin-bottom: 0;
    }

    &:hover {
      background-color: #f0f0f0;
    }

    &.selected {
      background-color: #fff0e6;
      border-color: #ff5500;
    }

    .payment-content {
      display: flex;
      align-items: center;
      flex: 1;

      .payment-icon {
        width: 40px;
        height: 40px;
        margin-right: 12px;
        display: flex;
        align-items: center;
        justify-content: center;

        .method-icon {
          width: 32px;
          height: 32px;
        }
      }

      .payment-info {
        flex: 1;

        .method-name {
          display: block;
          font-size: 16px;
          font-weight: 500;
          color: #333;
          margin-bottom: 4px;
        }

        .method-desc {
          display: block;
          font-size: 12px;
          color: #666;
        }
      }
    }

    .payment-selected {
      margin-left: 10px;
    }
  }

  .payment-footer {
    padding: 20px;
    border-top: 1px solid #eee;
    position: sticky;
    bottom: 0;
    background-color: #fff;
    z-index: 10001;

    .order-summary {
      margin-bottom: 15px;
      padding: 15px;
      background-color: #f8f9fa;
      border-radius: 8px;

      .summary-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }

        &.total {
          padding-top: 8px;
          border-top: 1px solid #e9ecef;
          font-weight: 500;

          .summary-label {
            font-size: 16px;
            color: #333;
          }

          .summary-value {
            font-size: 18px;
            color: #ff5500;
          }
        }

        .summary-label {
          font-size: 14px;
          color: #666;
        }

        .summary-value {
          font-size: 14px;
          color: #333;

          &.discount {
            color: #ff5500;
          }
        }
      }
    }
  }
}
</style>
