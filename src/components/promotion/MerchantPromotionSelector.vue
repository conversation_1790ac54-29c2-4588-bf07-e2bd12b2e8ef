<!--
  商家促销活动选择器组件
-->
<template>
  <view class="merchant-promotion-selector">
    <!-- 触发按钮 -->
    <view class="selector-trigger" @click="showModal = true">
      <view class="trigger-content">
        <text class="trigger-icon">🎉</text>
        <view class="trigger-text">
          <text v-if="selectedPromotion" class="selected-text">
            {{ selectedPromotion.name }}
          </text>
          <text v-else class="placeholder-text">选择促销活动</text>
        </view>
        <text class="trigger-count" v-if="availableCount > 0">{{ availableCount }}个可用</text>
      </view>
      <text class="trigger-arrow">></text>
    </view>

    <!-- 促销活动选择弹窗 - 使用优化的弹窗组件 -->
    <OptimizedPopup
      v-model="showModal"
      position="bottom"
      :close-on-click-modal="true"
      content-class="bottom-popup"
      :enable-touch-optimization="true"
    >
      <view class="promotion-modal">
        <view class="modal-header">
          <text class="modal-title">选择促销活动</text>
          <text class="modal-close" @click="showModal = false">×</text>
        </view>

        <view class="modal-content">
          <!-- 加载状态 -->
          <view v-if="loading" class="loading-state">
            <text class="loading-text">加载中...</text>
          </view>

          <!-- 不使用促销活动选项 -->
          <view class="no-promotion-section">
            <view
              class="promotion-item no-promotion-item"
              :class="{ 'promotion-item--selected': !selectedPromotion }"
              @click="handlePromotionSelect(null)"
            >
              <view class="promotion-info">
                <text class="promotion-name">不使用促销活动</text>
              </view>
              <view class="promotion-action">
                <text v-if="!selectedPromotion" class="selected-icon">✓</text>
                <text v-else class="select-text">选择</text>
              </view>
            </view>
          </view>

          <!-- 可用促销活动 -->
          <view v-if="applicablePromotions.length > 0" class="promotions-section">
            <text class="section-title">可用促销活动</text>
            <view
              v-for="result in applicablePromotions"
              :key="result.promotion.id"
              class="promotion-item"
              :class="{
                'promotion-item--selected': selectedPromotion?.id === result.promotion.id,
                'promotion-item--disabled': !result.applicable,
              }"
              @click="handlePromotionSelect(result)"
            >
              <view class="promotion-info">
                <text class="promotion-name">{{ result.promotion.name }}</text>
                <text class="promotion-desc">{{ result.promotion.description }}</text>
                <view class="promotion-rules">
                  <text class="rule-text">{{ getPromotionRuleText(result.promotion) }}</text>
                </view>
                <view v-if="result.applicable" class="promotion-discount">
                  <text class="discount-text">可优惠 ¥{{ result.discount_amount.toFixed(2) }}</text>
                </view>
                <view v-else class="promotion-reason">
                  <text class="reason-text">{{ result.reason }}</text>
                </view>
              </view>
              <view class="promotion-action">
                <text v-if="selectedPromotion?.id === result.promotion.id" class="selected-icon">
                  ✓
                </text>
                <text v-else-if="result.applicable" class="select-text">选择</text>
                <text v-else class="disabled-text">不可用</text>
              </view>
            </view>
          </view>

          <!-- 无可用促销活动 -->
          <view v-else-if="!loading" class="empty-state">
            <text class="empty-text">暂无可用的促销活动</text>
          </view>
        </view>

        <view class="modal-footer">
          <button class="confirm-btn" @click="handleConfirm">确定</button>
        </view>
      </view>
    </OptimizedPopup>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { usePromotionStore } from '@/store/promotion'
import type { IPromotion, IPromotionApplicationResult } from '@/api/promotion.typings'
import OptimizedPopup from '@/components/common/OptimizedPopup.vue'

// Props
interface Props {
  merchantId: number
  totalAmount: number
  foodIds?: string
}

const props = withDefaults(defineProps<Props>(), {
  foodIds: '',
})

// Emits
const emit = defineEmits<{
  select: [merchantId: number, promotion: IPromotion | null]
}>()

// Store
const promotionStore = usePromotionStore()

// 响应式数据
const showModal = ref(false)

// 计算属性
const applicablePromotions = computed(() =>
  promotionStore.getApplicablePromotions(props.merchantId),
)
const selectedPromotion = computed(() => promotionStore.getSelectedPromotion(props.merchantId))
const availableCount = computed(() => applicablePromotions.value.filter((p) => p.applicable).length)
const loading = computed(() => promotionStore.loading.validation)

// 方法
const loadPromotions = async () => {
  try {
    console.log('🎉 MerchantPromotionSelector 加载促销活动:', {
      merchantId: props.merchantId,
      totalAmount: props.totalAmount,
      foodIds: props.foodIds,
    })

    await promotionStore.validatePromotionsForOrder({
      merchant_id: props.merchantId,
      total_amount: props.totalAmount,
      food_ids: props.foodIds,
    })

    console.log('🎉 促销活动加载完成:', {
      available: availableCount.value,
      total: applicablePromotions.value.length,
    })
  } catch (error) {
    console.error('❌ 加载促销活动失败:', error)
    uni.showToast({
      title: '加载促销活动失败',
      icon: 'none',
      duration: 2000,
    })
  }
}

const handlePromotionSelect = (result: IPromotionApplicationResult | null) => {
  if (result === null) {
    // 选择不使用促销活动
    console.log('🎉 选择不使用促销活动')
    promotionStore.selectPromotion(props.merchantId, null)
    return
  }

  if (!result.applicable) {
    uni.showToast({
      title: result.reason || '促销活动不可用',
      icon: 'none',
      duration: 2000,
    })
    return
  }

  console.log('🎉 选择促销活动:', result.promotion)
  promotionStore.selectPromotion(props.merchantId, result.promotion)
}

const handleConfirm = () => {
  const selected = selectedPromotion.value
  console.log('🎉 确认选择促销活动:', selected)
  emit('select', props.merchantId, selected)
  showModal.value = false
}

const getPromotionRuleText = (promotion: IPromotion): string => {
  const rules = promotionStore.parsePromotionRules(promotion.rules)

  if (rules.coupon) {
    const coupon = rules.coupon
    return `满¥${coupon.min_order_amount}减¥${coupon.amount}`
  }

  return promotion.description || '促销活动'
}

// 监听参数变化，重新加载促销活动
watch(
  [() => props.merchantId, () => props.totalAmount, () => props.foodIds],
  () => {
    if (props.merchantId && props.totalAmount > 0) {
      console.log('🎉 MerchantPromotionSelector 参数变化，重新加载促销活动:', {
        merchantId: props.merchantId,
        totalAmount: props.totalAmount,
        foodIds: props.foodIds,
      })
      loadPromotions()
    }
  },
  { immediate: true },
)

onMounted(() => {
  console.log('🎉 MerchantPromotionSelector 组件挂载:', {
    merchantId: props.merchantId,
    totalAmount: props.totalAmount,
    foodIds: props.foodIds,
  })

  if (props.merchantId && props.totalAmount > 0) {
    loadPromotions()
  }
})
</script>

<style scoped lang="scss">
.merchant-promotion-selector {
  width: 100%;
}

.selector-trigger {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #fff;
  border-radius: 8px;
  border: 1px solid #e5e5e5;
}

.trigger-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.trigger-icon {
  font-size: 16px;
  margin-right: 8px;
}

.trigger-text {
  flex: 1;
}

.selected-text {
  color: #ff5500;
  font-size: 14px;
  font-weight: 500;
}

.placeholder-text {
  color: #999;
  font-size: 14px;
}

.trigger-count {
  color: #ff5500;
  font-size: 12px;
  margin-left: 8px;
}

.trigger-arrow {
  color: #ccc;
  font-size: 16px;
}

.promotion-modal {
  background: #fff;
  border-radius: 16px 16px 0 0;
  max-height: 70vh;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.modal-close {
  font-size: 24px;
  color: #999;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  flex: 1;
  overflow-y: auto;
  padding: 0 20px;
}

.loading-state,
.empty-state {
  padding: 40px 20px;
  text-align: center;
}

.loading-text,
.empty-text {
  color: #999;
  font-size: 14px;
}

.no-promotion-section,
.promotions-section {
  padding: 20px 0;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
  display: block;
}

.promotion-item {
  display: flex;
  align-items: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 12px;
  border: 2px solid transparent;

  &--selected {
    border-color: #ff5500;
    background: #fff5f0;
  }

  &--disabled {
    opacity: 0.6;
  }
}

.no-promotion-item {
  background: #f0f0f0;
}

.promotion-info {
  flex: 1;
}

.promotion-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 4px;
}

.promotion-desc {
  font-size: 12px;
  color: #666;
  display: block;
  margin-bottom: 8px;
}

.promotion-rules {
  margin-bottom: 8px;
}

.rule-text {
  font-size: 14px;
  color: #ff5500;
  background: #fff5f0;
  padding: 4px 8px;
  border-radius: 4px;
}

.promotion-discount {
  margin-top: 4px;
}

.discount-text {
  font-size: 14px;
  color: #52c41a;
  font-weight: 600;
}

.promotion-reason {
  margin-top: 4px;
}

.reason-text {
  font-size: 12px;
  color: #ff4d4f;
}

.promotion-action {
  margin-left: 16px;
}

.selected-icon {
  color: #ff5500;
  font-size: 18px;
  font-weight: bold;
}

.select-text {
  color: #ff5500;
  font-size: 14px;
}

.disabled-text {
  color: #ccc;
  font-size: 14px;
}

.modal-footer {
  display: flex;
  padding: 20px;
  border-top: 1px solid #f0f0f0;
}

.confirm-btn {
  width: 100%;
  height: 44px;
  border-radius: 8px;
  font-size: 16px;
  border: none;
  background: #ff5500;
  color: #fff;
}
</style>
