<template>
  <view class="promotion-selector">
    <!-- 当前选中的促销活动显示 -->
    <view class="current-promotion" @click="showModal = true">
      <view v-if="!selectedPromotion" class="no-promotion">
        <wd-icon name="gift" size="20" color="#fa8c16" />
        <text class="promotion-text">选择促销活动</text>
        <text v-if="availableCount > 0" class="available-count">{{ availableCount }}个可用</text>
      </view>
      <view v-else class="selected-promotion">
        <wd-icon name="gift" size="20" color="#fa8c16" />
        <view class="promotion-info">
          <text class="promotion-name">{{ selectedPromotion.type_name }}</text>
          <text class="promotion-desc">{{ selectedPromotion.description }}</text>
        </view>
      </view>
      <wd-icon name="arrow-right" size="16" color="#999" />
    </view>

    <!-- 促销活动选择弹窗 -->
    <wd-popup v-model="showModal" position="bottom" :safe-area-inset-bottom="true" :z-index="9999">
      <view class="promotion-modal">
        <view class="modal-header">
          <text class="title">选择促销活动</text>
          <wd-icon name="close" size="20" color="#999" @click="showModal = false" />
        </view>

        <scroll-view class="promotion-list" scroll-y>
          <!-- 不使用促销活动选项 -->
          <view
            class="promotion-item no-promotion-option"
            :class="{ active: !selectedPromotion }"
            @click="selectPromotion(null)"
          >
            <view class="promotion-content">
              <text class="option-text">不使用促销活动</text>
            </view>
            <wd-icon
              :name="!selectedPromotion ? 'check-circle' : 'circle'"
              :color="!selectedPromotion ? '#fa8c16' : '#ddd'"
              size="20"
            />
          </view>

          <!-- 可用促销活动列表 -->
          <view v-if="availablePromotions.length > 0" class="promotion-section">
            <text class="section-title">可用促销活动 ({{ availablePromotions.length }})</text>
            <view
              v-for="promotion in availablePromotions"
              :key="promotion.id"
              class="promotion-item"
              :class="{ active: selectedPromotion?.id === promotion.id }"
              @click="selectPromotion(promotion)"
            >
              <view class="promotion-card">
                <view class="promotion-icon">
                  <wd-icon name="gift" size="24" color="#fa8c16" />
                </view>
                <view class="promotion-details">
                  <text class="promotion-name">{{ promotion.type_name }}</text>
                  <text class="promotion-desc">{{ promotion.description }}</text>
                  <text class="promotion-condition">{{ getPromotionCondition(promotion) }}</text>
                </view>
              </view>
              <wd-icon
                :name="selectedPromotion?.id === promotion.id ? 'check-circle' : 'circle'"
                :color="selectedPromotion?.id === promotion.id ? '#fa8c16' : '#ddd'"
                size="20"
              />
            </view>
          </view>

          <!-- 空状态 -->
          <view v-if="availablePromotions.length === 0" class="empty-state">
            <wd-icon name="gift" size="60" color="#ddd" />
            <text class="empty-text">暂无可用促销活动</text>
          </view>
        </scroll-view>
      </view>
    </wd-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

interface Promotion {
  id: number
  type: string
  type_name: string
  description: string
  threshold: number
  discount: number
  max_discount?: number
}

interface Props {
  promotions: Promotion[]
  totalAmount: number
  selectedPromotionId?: number
}

interface Emits {
  (e: 'select', promotion: Promotion | null): void
}

const props = withDefaults(defineProps<Props>(), {
  promotions: () => [],
  totalAmount: 0,
})

const emit = defineEmits<Emits>()

const showModal = ref(false)

// 计算属性
const availablePromotions = computed(() => {
  return props.promotions.filter((promotion) => {
    // 检查是否满足使用条件
    return props.totalAmount >= promotion.threshold
  })
})

const selectedPromotion = computed(() => {
  if (!props.selectedPromotionId) return null
  return availablePromotions.value.find((p) => p.id === props.selectedPromotionId) || null
})

const availableCount = computed(() => availablePromotions.value.length)

// 方法
const selectPromotion = (promotion: Promotion | null) => {
  emit('select', promotion)
  showModal.value = false
}

const getPromotionCondition = (promotion: Promotion) => {
  if (promotion.type === 'full_reduction') {
    return `满¥${promotion.threshold}减¥${promotion.discount}`
  } else if (promotion.type === 'discount') {
    const discountText = `${promotion.discount}折`
    const maxDiscountText = promotion.max_discount ? `最高减¥${promotion.max_discount}` : ''
    return `满¥${promotion.threshold}享${discountText}${maxDiscountText ? ' ' + maxDiscountText : ''}`
  }
  return promotion.description
}
</script>

<style scoped lang="scss">
.promotion-selector {
  width: 100%;
}

.current-promotion {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px;
  background: #fff;
  border-radius: 8px;
  border: 1px solid #eee;
  cursor: pointer;
  transition: all 0.2s;

  &:active {
    background: #f8f8f8;
  }

  .no-promotion {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;

    .promotion-text {
      font-size: 14px;
      color: #333;
    }

    .available-count {
      background: #fa8c16;
      color: white;
      padding: 2px 6px;
      border-radius: 10px;
      font-size: 12px;
    }
  }

  .selected-promotion {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;

    .promotion-info {
      display: flex;
      flex-direction: column;
      gap: 2px;

      .promotion-name {
        font-size: 14px;
        color: #333;
        font-weight: 500;
      }

      .promotion-desc {
        font-size: 12px;
        color: #fa8c16;
      }
    }
  }
}

.promotion-modal {
  background: white;
  border-radius: 12px 12px 0 0;
  max-height: 80vh;
  display: flex;
  flex-direction: column;

  .modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 20px;
    border-bottom: 1px solid #eee;

    .title {
      font-size: 16px;
      font-weight: 500;
      color: #333;
    }
  }

  .promotion-list {
    flex: 1;
    max-height: 60vh;
    padding: 10px 0;
  }

  .no-promotion-option {
    padding: 15px 20px;
    border-bottom: 1px solid #f5f5f5;

    .promotion-content {
      flex: 1;

      .option-text {
        font-size: 14px;
        color: #333;
      }
    }
  }

  .promotion-section {
    margin-bottom: 20px;

    .section-title {
      display: block;
      padding: 10px 20px;
      font-size: 12px;
      color: #666;
      background: #f8f8f8;
    }
  }

  .promotion-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 20px;
    border-bottom: 1px solid #f5f5f5;
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover {
      background: #f8f8f8;
    }

    &.active {
      background: #fff7e6;
    }

    .promotion-card {
      display: flex;
      align-items: center;
      gap: 12px;
      flex: 1;

      .promotion-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        background: #fff7e6;
        border-radius: 6px;
      }

      .promotion-details {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 4px;

        .promotion-name {
          font-size: 14px;
          font-weight: 500;
          color: #333;
        }

        .promotion-desc {
          font-size: 12px;
          color: #666;
        }

        .promotion-condition {
          font-size: 11px;
          color: #fa8c16;
          font-weight: 500;
        }
      }
    }
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;

    .empty-text {
      font-size: 14px;
      color: #666;
      margin-top: 10px;
    }
  }
}
</style>
