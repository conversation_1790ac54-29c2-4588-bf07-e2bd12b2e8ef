/**
 * 历史记录 Hook
 * 用于在各个页面中方便地记录用户浏览历史
 */

import { ref, onMounted, onUnmounted, getCurrentInstance } from 'vue'
import { addHistory } from '@/api/user'
import { useUserStore } from '@/store/user'
import { HistoryType } from '@/api/user.typings'
import type { IAddHistoryRequest } from '@/api/user.typings'

interface UseHistoryOptions {
  autoTrack?: boolean // 是否自动追踪（页面进入时立即记录）
  trackOnLeave?: boolean // 是否在页面离开时记录停留时长
  source?: string // 访问来源
  requireLogin?: boolean // 是否需要登录才记录（默认true）
}

export function useHistory(
  type: HistoryType,
  targetId: number,
  targetName: string,
  targetImage?: string,
  extraData?: Record<string, any>,
  options: UseHistoryOptions = {},
) {
  const { autoTrack = true, trackOnLeave = true, source = 'direct', requireLogin = true } = options

  const userStore = useUserStore()
  const tracked = ref(false)
  let startTime = 0

  // 记录历史
  const track = async (duration?: number) => {
    console.log('🔍 [History] 尝试记录历史:', {
      type,
      target_id: targetId,
      target_name: targetName,
      isLoggedIn: userStore.isLoggedIn,
      tracked: tracked.value,
      duration,
    })

    // 检查用户是否已登录（如果需要）
    if (requireLogin && !userStore.isLoggedIn) {
      console.warn('⚠️ [History] 用户未登录，跳过历史记录')
      return
    }

    // 如果不需要登录但用户未登录，也跳过（因为后端需要认证）
    if (!userStore.isLoggedIn) {
      console.warn('⚠️ [History] 用户未登录，后端需要认证，跳过历史记录')
      return
    }

    // 避免重复记录（除非是记录停留时长）
    if (tracked.value && !duration) {
      console.log('📝 [History] 已记录过，跳过重复记录')
      return
    }

    try {
      const request: IAddHistoryRequest = {
        type,
        target_id: targetId,
        target_name: targetName,
        target_image: targetImage,
        extra_data: extraData,
        user_agent: getUserAgent(),
        platform: getPlatform(),
        source,
        duration: duration || 0,
      }

      console.log('📤 [History] 发送历史记录请求:', request)
      await addHistory(request)
      tracked.value = true
      console.log('✅ [History] 历史记录成功')
    } catch (error) {
      console.error('❌ [History] 记录历史失败:', error)

      // 详细的错误信息
      if (error && typeof error === 'object') {
        if (error.data && error.data.message) {
          console.error('❌ [History] 服务器错误:', error.data.message)
        }
        if (error.status) {
          console.error('❌ [History] HTTP状态码:', error.status)
        }
      }
    }
  }

  // 记录带停留时长的历史
  const trackWithDuration = () => {
    if (startTime > 0) {
      const duration = Math.floor((Date.now() - startTime) / 1000)
      track(duration)
    }
  }

  // 获取用户代理
  const getUserAgent = (): string => {
    if (typeof navigator !== 'undefined') {
      return navigator.userAgent
    }
    return 'Unknown'
  }

  // 获取平台信息
  const getPlatform = (): string => {
    // #ifdef H5
    return 'h5'
    // #endif

    // #ifdef MP-WEIXIN
    return 'weixin'
    // #endif

    // #ifdef MP-ALIPAY
    return 'alipay'
    // #endif

    // #ifdef APP-PLUS
    return 'app'
    // #endif

    return 'unknown'
  }

  // 页面可见性变化处理
  const handleVisibilityChange = () => {
    if (typeof document === 'undefined') return

    if (document.hidden) {
      // 页面隐藏时记录停留时长
      if (trackOnLeave) {
        trackWithDuration()
      }
    } else {
      // 页面显示时重新开始计时
      startTime = Date.now()
    }
  }

  // 页面卸载处理
  const handleBeforeUnload = () => {
    if (trackOnLeave) {
      trackWithDuration()
    }
  }

  // 初始化函数，可以手动调用
  const initialize = () => {
    startTime = Date.now()

    // 自动追踪
    if (autoTrack) {
      track()
    }

    // 监听页面可见性变化（仅在浏览器环境）
    if (typeof document !== 'undefined') {
      document.addEventListener('visibilitychange', handleVisibilityChange)
      window.addEventListener('beforeunload', handleBeforeUnload)
    }
  }

  // 清理函数，可以手动调用
  const cleanup = () => {
    // 页面卸载时记录停留时长
    if (trackOnLeave) {
      trackWithDuration()
    }

    // 移除事件监听
    if (typeof document !== 'undefined') {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      window.removeEventListener('beforeunload', handleBeforeUnload)
    }
  }

  // 尝试在组件生命周期中注册，如果失败则提供手动调用方式
  try {
    // 检查是否在组件上下文中
    if (getCurrentInstance()) {
      onMounted(initialize)
      onUnmounted(cleanup)
    } else {
      // 如果不在组件上下文中，立即初始化
      initialize()
    }
  } catch (error) {
    // 如果生命周期钩子注册失败，立即初始化
    console.warn('[useHistory] 生命周期钩子注册失败，使用手动初始化:', error.message)
    initialize()
  }

  return {
    tracked,
    track,
    trackWithDuration,
    initialize,
    cleanup,
  }
}

// 快捷方法：记录商品浏览历史
export function useProductHistory(
  productId: number,
  productName: string,
  productImage?: string,
  extraData?: Record<string, any>,
  options?: UseHistoryOptions,
) {
  return useHistory(
    HistoryType.MALL_PRODUCT,
    productId,
    productName,
    productImage,
    extraData,
    options,
  )
}

// 快捷方法：记录外卖商品浏览历史
export function useFoodHistory(
  foodId: number,
  foodName: string,
  foodImage?: string,
  extraData?: Record<string, any>,
  options?: UseHistoryOptions,
) {
  return useHistory(HistoryType.TAKEOUT_FOOD, foodId, foodName, foodImage, extraData, options)
}

// 快捷方法：记录商家浏览历史
export function useMerchantHistory(
  merchantId: number,
  merchantName: string,
  merchantImage?: string,
  extraData?: Record<string, any>,
  options?: UseHistoryOptions,
) {
  return useHistory(
    HistoryType.MERCHANT,
    merchantId,
    merchantName,
    merchantImage,
    extraData,
    options,
  )
}

// 快捷方法：记录搜索历史
export function useSearchHistory(
  keyword: string,
  searchType?: string,
  extraData?: Record<string, any>,
  options?: UseHistoryOptions,
) {
  return useHistory(
    HistoryType.SEARCH,
    Date.now(), // 使用时间戳作为唯一ID
    keyword,
    undefined,
    { search_type: searchType, ...extraData },
    options,
  )
}

// 快捷方法：记录页面访问历史
export function usePageHistory(
  pagePath: string,
  pageTitle: string,
  extraData?: Record<string, any>,
  options?: UseHistoryOptions,
) {
  return useHistory(
    HistoryType.PAGE,
    Date.now(), // 使用时间戳作为唯一ID
    pageTitle,
    undefined,
    { page_path: pagePath, ...extraData },
    options,
  )
}
