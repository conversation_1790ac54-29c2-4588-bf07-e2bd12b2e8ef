/**
 * 系统配置相关常量
 * 与后端 models/system_config.go 中的常量保持一致
 *
 * <AUTHOR>
 * @email <EMAIL>
 */

// ==================== 配置键名常量 ====================

/**
 * 网站基本信息配置键
 */
export const SYSTEM_CONFIG_KEYS = {
  // 网站基本信息
  SITE_NAME: 'site_name', // 网站名称
  SITE_VERSION: 'site_version', // 网站版本
  API_VERSION: 'api_version', // API版本
  CONFIG_VERSION: 'config_version', // 配置版本号
  SITE_LOGO: 'site_logo', // 网站Logo
  SITE_FAVICON: 'site_favicon', // 网站图标
  SITE_COPYRIGHT: 'site_copyright', // 版权信息
  SITE_REGION: 'site_region', // 地区

  // 联系方式
  CONTACT_EMAIL: 'contact_email', // 联系邮箱
  CONTACT_PHONE: 'contact_phone', // 联系电话
  CONTACT_ADDRESS: 'contact_address', // 联系地址
  CONTACT_WORK_TIME: 'contact_work_time', // 工作时间
  CONTACT_QQ: 'contact_qq', // QQ客服
  CONTACT_WECHAT: 'contact_wechat', // 微信客服
  COMPANY_NAME: 'company_name', // 公司名称
  CONTACT_LONGITUDE: 'contact_longitude', // 经度
  CONTACT_LATITUDE: 'contact_latitude', // 纬度

  // 系统设置
  MAINTENANCE_MODE: 'maintenance_mode', // 维护模式
  MAINTENANCE_MESSAGE: 'maintenance_message', // 维护消息
  MAINTENANCE_RECOVERY_TIME: 'maintenance_recovery_time', // 预计恢复时间
} as const

/**
 * 配置分类常量
 */
export const CONFIG_CATEGORIES = {
  SYSTEM: 'system', // 系统基础配置
  APP_UI: 'app_ui', // 小程序UI配置
  BANNER: 'banner', // Banner轮播图配置
  APPMENU: 'appmenu', // AppMenu配置
  AD: 'ad', // 广告配置
  MENU: 'menu', // 菜单配置
  NOTICE: 'notice', // 公告配置
  SMS: 'sms', // 短信配置
  UPLOAD: 'upload', // 上传配置
  PAYMENT: 'payment', // 支付配置
  WECHAT: 'wechat', // 微信配置
} as const

// ==================== 缓存键名常量 ====================

/**
 * 本地存储缓存键名
 */
export const CACHE_KEYS = {
  SYSTEM_BANNER_LIST: 'system_banner_list', // 轮播图列表缓存
  SYSTEM_APPMENU_LIST: 'system_appmenu_list', // 功能导航列表缓存
  SYSTEM_GLOBAL_CATEGORY_LIST: 'system_global_category_list', // 外卖全局分类列表缓存
  SYSTEM_INFO: 'system_info', // 系统信息缓存
  CONTACT_INFO: 'contact_info', // 联系信息缓存
  MAINTENANCE_MODE: 'maintenance_mode', // 维护模式缓存
  ADDRESS_OPTIONS: 'address_options', // 地址选项缓存
  SYSTEM_NOTICES: 'system_notices', // 系统公告缓存
} as const

// ==================== API 路径常量 ====================

/**
 * 系统配置相关API路径
 */
export const SYSTEM_API_PATHS = {
  // 基础信息
  SYSTEM_INFO: '/api/v1/system/info/basic',
  CONTACT_INFO: '/api/v1/system/info/contact',
  PUBLIC_CONFIG: '/api/v1/system/info',

  // 维护模式
  MAINTENANCE_MODE: '/api/v1/system/maintenance',

  // 配置详情
  CONFIG_DETAILS: '/api/v1/system/configs/details',

  // 外卖全局分类
  GLOBAL_CATEGORIES: '/api/v1/takeout/global-categories',

  // 公告
  ACTIVE_NOTICES: '/api/v1/system/notices',
  NOTICE_DETAIL: '/api/v1/system/notices',

  // 地址
  ADDRESS_OPTIONS: '/api/v1/system/addresses/options',

  // AI聊天
  CHAT_COMPLETION: '/api/v1/system/chat/completion',

  // UI生成器
  UI_GENERATE: '/api/v1/system/ui/generate',
  UI_GENERATE_CUSTOM: '/api/v1/system/ui/generate/custom',
} as const

// ==================== 默认值常量 ====================

/**
 * 默认配置值
 */
export const DEFAULT_VALUES = {
  SITE_NAME: '商城系统',
  CACHE_TIME: 10, // 默认缓存时间（分钟）
  PAGE_SIZE: 10, // 默认分页大小
} as const

/**
 * 状态常量
 */
export const STATUS = {
  ENABLED: 1, // 启用
  DISABLED: 0, // 禁用
} as const

/**
 * 维护模式状态
 */
export const MAINTENANCE_STATUS = {
  ENABLED: '1', // 开启维护模式
  DISABLED: '0', // 关闭维护模式
} as const

// ==================== 类型导出 ====================

/**
 * 配置键名类型
 */
export type SystemConfigKey = (typeof SYSTEM_CONFIG_KEYS)[keyof typeof SYSTEM_CONFIG_KEYS]

/**
 * 配置分类类型
 */
export type ConfigCategory = (typeof CONFIG_CATEGORIES)[keyof typeof CONFIG_CATEGORIES]

/**
 * 缓存键名类型
 */
export type CacheKey = (typeof CACHE_KEYS)[keyof typeof CACHE_KEYS]

/**
 * API路径类型
 */
export type SystemApiPath = (typeof SYSTEM_API_PATHS)[keyof typeof SYSTEM_API_PATHS]
