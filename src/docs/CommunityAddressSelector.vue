<!--
  /**
   * @file CommunityAddressSelector.vue
   * <AUTHOR>
   * @date 2025-01-27
   * @version 1.0.0
   * @description 社区地址选择器组件，支持动态加载小区、楼栋、单元，并返回完整地址信息及经纬度。使用Element Plus级联选择器实现。
   */
-->
<template>
  <div>
    <el-cascader
      v-model="selectedKeys"
      :options="options"
      :props="cascaderProps"
      placeholder="请选择地址"
      @change="handleChange"
      style="width: 100%"
      clearable
      filterable
      :show-all-levels="true"
    />
  </div>
</template>

<script setup>
/**
 * @file CommunityAddressSelector.vue
 * <AUTHOR>
 * @date 2025-01-27
 * @version 1.0.0
 * @description 社区地址选择器组件，支持动态加载小区、楼栋、单元，并返回完整地址信息及经纬度。使用Element Plus级联选择器实现。
 */
import { ref, onMounted } from 'vue'
import { ElCascader } from 'element-plus'
import { get, post } from '@/utils/request'

// API基础路径
const API_BASE_URL = '/v1/system'

/**
 * @typedef {object} AddressOption
 * @property {number|string} value - 选项的值 (地址ID)
 * @property {string} label - 选项的标签 (地址名称)
 * @property {number} level - 地址层级 (1:小区, 2:楼栋, 3:单元)
 * @property {boolean} isLeaf - 是否为叶子节点
 * @property {Array<AddressOption>} [children] - 子选项
 * @property {number} [longitude] - 经度 (可选，通常在叶子节点或全量加载时提供)
 * @property {number} [latitude] - 纬度 (可选)
 */

/**
 * @typedef {object} SelectedAddressInfo
 * @property {string} fullPath - 完整地址路径
 * @property {number} longitude - 经度
 * @property {number} latitude - 纬度
 * @property {number|null} communityId - 选定的小区ID
 * @property {number|null} buildingId - 选定的楼栋ID
 * @property {number|null} unitId - 选定的单元ID
 */

const props = defineProps({
  // 可以定义一些props，例如初始值等
  // initialValue: {
  //   type: Array,
  //   default: () => [],
  // },
})

const emit = defineEmits(['address-selected'])

/**
 * @description 级联选择器的选项数据
 * @type {import('vue').Ref<Array<AddressOption>>}
 */
const options = ref([])

/**
 * @description 用户当前选择的各级ID路径
 * @type {import('vue').Ref<Array<number|string>>}
 */
const selectedKeys = ref([])

/**
 * @description 将后端API返回的地址数据转换为级联选择器选项格式
 * @param {Array<object>} addresses - 后端返回的地址对象数组
 * @param {number} currentLevel - 当前正在加载的地址层级
 * @returns {Array<AddressOption>}
 */
const transformAddressData = (addresses, currentLevel) => {
  if (!Array.isArray(addresses)) {
    console.error('Invalid address data received:', addresses)
    return []
  }
  return addresses.map((addr) => {
    // 检查是否没有子节点
    const hasNoChildren =
      !addr.children || !Array.isArray(addr.children) || addr.children.length === 0

    return {
      value: addr.value || addr.id,
      label: addr.label || addr.name,
      level: addr.level || currentLevel, // 后端返回的level优先，否则使用当前层级
      longitude: addr.longitude,
      latitude: addr.latitude,
      // 如果是第三级或者没有子节点，则视为叶子节点
      // 如果是第二级地址，默认设置为可选叶子节点
      isLeaf:
        (addr.level || currentLevel) === 3 || hasNoChildren || (addr.level || currentLevel) === 2,
      children: addr.children
        ? transformAddressData(addr.children, (addr.level || currentLevel) + 1)
        : undefined,
    }
  })
}

/**
 * @description 动态加载子级地址数据
 * @param {object} node - 当前节点
 * @param {function} resolve - 解析函数
 */
const loadData = async (node, resolve) => {
  try {
    console.log('Node data:', node.data)
    console.log('Is leaf node?', node.isLeaf)

    // 如果节点是叶子节点，直接返回空数组
    if (node.isLeaf) {
      console.log('Node is leaf, returning empty array')
      resolve([])
      return
    }

    // 二级节点强制判定为叶子节点
    if (node.data && node.data.level === 2) {
      console.log('Level 2 node detected, treating as leaf')
      resolve([])
      return
    }

    let parentId = 0
    let nextLevel = 1

    if (node.data) {
      parentId = node.data.value
      nextLevel = node.data.level + 1
    }

    // 如果已经是第三级，不再请求下一级
    if (nextLevel > 3) {
      resolve([])
      return
    }

    // 验证parentId和nextLevel的有效性
    if (parentId === undefined || parentId === null || isNaN(parentId)) {
      console.error('Invalid parentId:', parentId)
      resolve([])
      return
    }

    if (nextLevel === undefined || nextLevel === null || isNaN(nextLevel)) {
      console.error('Invalid nextLevel:', nextLevel)
      nextLevel = 1 // 使用默认值
    }

    // 根据父级ID和下一层级获取子地址
    const response = await get(`${API_BASE_URL}/addresses/parent/${parentId}`, {
      level: nextLevel,
    })

    if (Array.isArray(response)) {
      const children = transformAddressData(response, nextLevel)
      resolve(children)
    } else {
      console.error('Failed to load children or invalid data format:', response)
      resolve([])
    }
  } catch (error) {
    console.error('Error loading children addresses:', error)
    resolve([])
  }
}

/**
 * @description Element Plus级联选择器配置
 */
const cascaderProps = {
  lazy: true,
  lazyLoad: loadData,
  value: 'value',
  label: 'label',
  children: 'children',
  leaf: 'isLeaf',
  checkStrictly: true, // 允许选择任意一级选项
  multiple: false,
  emitPath: true, // 把选中的路径数组作为返回值
  expandTrigger: 'hover', // 改为鼠标悬停时展开下级选项
}

/**
 * 根据选中的value路径获取对应的选项节点
 * @param {Array<number|string>} valuePath - 值路径数组
 * @returns {Array<AddressOption>} 选项节点数组
 */
const getSelectedNodes = (valuePath) => {
  if (!valuePath || !valuePath.length || !options.value || !options.value.length) {
    return []
  }

  const result = []
  let currentOptions = options.value
  let currentNode = null

  // 遍历值路径，查找每一级对应的节点
  for (let i = 0; i < valuePath.length; i++) {
    const value = valuePath[i]
    currentNode = currentOptions.find((node) => String(node.value) === String(value))

    if (currentNode) {
      result.push(currentNode)
      if (currentNode.children && currentNode.children.length > 0) {
        currentOptions = currentNode.children
      } else {
        break
      }
    } else {
      break
    }
  }

  return result
}

/**
 * @description 处理用户选择变化事件
 * @param {Array<number|string>} value - 当前选中的值数组 (各级ID)
 */
const handleChange = async (value) => {
  if (value && value.length > 0) {
    // 获取选中的所有ID，最多三级
    const communityId = value[0] || null
    const buildingId = value.length > 1 ? value[1] : null
    const unitId = value.length > 2 ? value[2] : null

    // 直接从已加载的数据中获取完整地址信息
    const selectedNodes = getSelectedNodes(value)
    const fullPath = selectedNodes.map((node) => node.label).join('/')

    // 获取最后一级节点的经纬度信息
    const lastNode = selectedNodes[selectedNodes.length - 1]

    // 构造完整的地址信息对象
    /** @type {SelectedAddressInfo} */
    const fullInfo = {
      fullPath,
      longitude: lastNode?.longitude || null,
      latitude: lastNode?.latitude || null,
      communityId,
      buildingId,
      unitId,
    }

    console.log('Selected address info:', fullInfo)
    emit('address-selected', fullInfo)
  } else {
    // 清空选择
    emit('address-selected', {
      fullPath: '',
      longitude: null,
      latitude: null,
      communityId: null,
      buildingId: null,
      unitId: null,
    })
  }
}

/**
 * @description 根据层级设置是否为叶子节点
 * @param {Array<AddressOption>} data - 地址选项数组
 * @returns {Array<AddressOption>}
 */
const markLeafNodes = (data) => {
  if (!Array.isArray(data)) return []

  return data.map((item) => {
    // 复制原项目
    const node = { ...item }

    // 如果是第二级或第三级，或者没有子节点，设置为叶子节点
    if (node.level === 2 || node.level === 3 || !node.children || !node.children.length) {
      node.isLeaf = true
    }

    // 如果有子节点，递归处理
    if (node.children && node.children.length > 0) {
      node.children = markLeafNodes(node.children)
    }

    return node
  })
}

/**
 * @description 组件挂载后，加载初始数据 (顶级小区列表)
 */
onMounted(async () => {
  try {
    // 使用options API获取全量数据
    const response = await get(`${API_BASE_URL}/addresses/options`)

    // 检查响应是否有效并包含 options 数组
    if (response && response.options && Array.isArray(response.options)) {
      // 处理数据，并标记叶子节点
      options.value = markLeafNodes(response.options)
      console.log('Processed options with leaf nodes marked:', options.value)
    } else if (response && Array.isArray(response)) {
      // 兼容在response直接是数组的情况
      const firstLevelAddresses = response.filter((addr) => addr.level === 1 || addr.parentId === 0)
      let transformedData = transformAddressData(firstLevelAddresses, 1)
      // 处理变换后的数据，确保叶子节点标记
      options.value = markLeafNodes(transformedData)
    } else {
      console.error('Failed to load initial community options or invalid data format:', response)
      options.value = []
    }
  } catch (error) {
    console.error('Error loading initial community options:', error)
    options.value = []
  }
})
</script>

<style scoped>
/* 可以添加一些组件的特定样式 */
</style>
