# 社区地址选择器组件开发设计指南

## 1. 组件概述

`CommunityAddressSelector.vue` 是一个基于 Element Plus 级联选择器实现的社区地址选择器组件，支持动态加载小区、楼栋、单元等多级地址数据，并能返回完整的地址信息和经纬度坐标。该组件主要用于地址选择、社区服务定位等场景。

**组件特性：**

- 多级联动选择（小区/楼栋/单元）
- 懒加载数据，优化性能
- 支持数据搜索和过滤
- 返回完整地址信息对象
- 包含地理位置坐标数据

## 2. 组件设计

### 2.1 架构设计

组件采用单文件组件（SFC）架构，基于 Vue 3 Composition API 实现：

```
CommunityAddressSelector.vue
├── Template - 基于 el-cascader 构建的选择器界面
├── Script - 组件核心逻辑
│   ├── 数据接口定义
│   ├── 数据转换逻辑
│   ├── 懒加载实现
│   └── 事件处理
└── Style - 组件样式
```

### 2.2 数据流设计

```
                  ┌─────────────┐
                  │  后端API    │
                  └──────┬──────┘
                         │
                         ▼
┌─────────────┐    ┌──────────────┐    ┌─────────────┐
│ 组件Props   │───▶│  组件状态     │───▶│  事件输出   │
└─────────────┘    └──────────────┘    └─────────────┘
                         │
                         ▼
                  ┌──────────────┐
                  │  UI渲染      │
                  └──────────────┘
```

### 2.3 状态管理

组件内部维护两个主要状态：

- `options`: 级联选择器的选项数据
- `selectedKeys`: 用户当前选择的值路径

## 3. 接口设计

### 3.1 Props

组件设计了以下Props接口（当前版本中部分注释未实现）：

```javascript
const props = defineProps({
  // initialValue: {
  //   type: Array,
  //   default: () => [],
  // },
})
```

**建议扩展的Props：**

```javascript
const props = defineProps({
  initialValue: {
    type: Array,
    default: () => [],
    description: '初始选中的地址值路径',
  },
  placeholder: {
    type: String,
    default: '请选择地址',
    description: '选择器的占位文本',
  },
  disabled: {
    type: Boolean,
    default: false,
    description: '是否禁用选择器',
  },
})
```

### 3.2 Events

组件输出以下事件：

```javascript
const emit = defineEmits(['address-selected'])
```

**address-selected**: 当用户选择地址时触发，传递一个包含完整地址信息的对象：

```typescript
interface SelectedAddressInfo {
  fullPath: string // 完整地址路径（如: "星河小区/1号楼/2单元"）
  longitude: number | null // 经度坐标
  latitude: number | null // 纬度坐标
  communityId: number | null // 小区ID
  buildingId: number | null // 楼栋ID
  unitId: number | null // 单元ID
}
```

### 3.3 数据结构

组件内部使用的主要数据结构：

```typescript
/**
 * 地址选项数据结构
 */
interface AddressOption {
  value: number | string // 选项的值 (地址ID)
  label: string // 选项的标签 (地址名称)
  level: number // 地址层级 (1:小区, 2:楼栋, 3:单元)
  isLeaf: boolean // 是否为叶子节点
  children?: AddressOption[] // 子选项
  longitude?: number // 经度 (可选)
  latitude?: number // 纬度 (可选)
}
```

## 4. 核心逻辑实现

### 4.1 数据加载策略

组件采用两种数据加载策略：

1. **初始化全量加载**：尝试获取完整的地址树结构
2. **按需懒加载**：如果全量加载失败或不支持，则使用懒加载方式加载子节点数据

```javascript
// 组件初始化时加载数据
onMounted(async () => {
  try {
    // 尝试使用options API获取全量数据
    const response = await get(`${API_BASE_URL}/addresses/options`)

    // 根据响应格式处理数据
    if (response && response.options && Array.isArray(response.options)) {
      options.value = markLeafNodes(response.options)
    } else if (response && Array.isArray(response)) {
      const firstLevelAddresses = response.filter((addr) => addr.level === 1 || addr.parentId === 0)
      let transformedData = transformAddressData(firstLevelAddresses, 1)
      options.value = markLeafNodes(transformedData)
    } else {
      options.value = []
    }
  } catch (error) {
    console.error('Error loading initial community options:', error)
    options.value = []
  }
})
```

### 4.2 懒加载实现

组件使用Element Plus级联选择器的懒加载功能，通过`loadData`函数动态加载子级数据：

```javascript
const loadData = async (node, resolve) => {
  try {
    // 根节点特殊处理
    if (node.level === 0) {
      // 加载顶级地址数据
      const response = await get(`${API_BASE_URL}/addresses`, {
        params: { level: 1, parentId: 0 },
      })
      resolve(transformAddressData(response.data || response, 1))
      return
    }

    // 如果是叶子节点，不再加载子级数据
    if (node.isLeaf) {
      resolve([])
      return
    }

    // 加载子级地址数据
    const id = node.data.value
    const url = `${API_BASE_URL}/addresses/children`
    const response = await get(url, { params: { parentId: id } })

    if (response && response.data && Array.isArray(response.data)) {
      const nextLevel = node.level + 1
      resolve(transformAddressData(response.data, nextLevel))
    } else if (response && Array.isArray(response)) {
      const nextLevel = node.level + 1
      resolve(transformAddressData(response, nextLevel))
    } else {
      resolve([])
    }
  } catch (error) {
    console.error('Error loading children addresses:', error)
    resolve([])
  }
}
```

### 4.3 数据转换

组件实现了两个关键的数据转换函数：

1. **transformAddressData**: 将后端API返回的地址数据转换为级联选择器可用的格式
2. **markLeafNodes**: 根据层级规则标记叶子节点

```javascript
// 将后端API返回的地址数据转换为级联选择器选项格式
const transformAddressData = (addresses, currentLevel) => {
  if (!Array.isArray(addresses)) {
    return []
  }
  return addresses.map((addr) => {
    const hasNoChildren =
      !addr.children || !Array.isArray(addr.children) || addr.children.length === 0

    return {
      value: addr.value || addr.id,
      label: addr.label || addr.name,
      level: addr.level || currentLevel,
      longitude: addr.longitude,
      latitude: addr.latitude,
      isLeaf:
        (addr.level || currentLevel) === 3 || hasNoChildren || (addr.level || currentLevel) === 2,
      children: addr.children
        ? transformAddressData(addr.children, (addr.level || currentLevel) + 1)
        : undefined,
    }
  })
}

// 根据层级设置是否为叶子节点
const markLeafNodes = (data) => {
  if (!Array.isArray(data)) return []

  return data.map((item) => {
    const node = { ...item }

    if (node.level === 2 || node.level === 3 || !node.children || !node.children.length) {
      node.isLeaf = true
    }

    if (node.children && node.children.length > 0) {
      node.children = markLeafNodes(node.children)
    }

    return node
  })
}
```

### 4.4 选择变更处理

当用户选择地址发生变化时，组件会构建完整的地址信息对象并通过事件传递给父组件：

```javascript
const handleChange = async (value) => {
  if (value && value.length > 0) {
    // 获取选中的所有ID
    const communityId = value[0] || null
    const buildingId = value.length > 1 ? value[1] : null
    const unitId = value.length > 2 ? value[2] : null

    // 获取完整地址路径
    const selectedNodes = getSelectedNodes(value)
    const fullPath = selectedNodes.map((node) => node.label).join('/')

    // 获取最后一级节点的经纬度
    const lastNode = selectedNodes[selectedNodes.length - 1]

    // 构造完整的地址信息对象
    const fullInfo = {
      fullPath,
      longitude: lastNode?.longitude || null,
      latitude: lastNode?.latitude || null,
      communityId,
      buildingId,
      unitId,
    }

    emit('address-selected', fullInfo)
  } else {
    // 清空选择
    emit('address-selected', {
      fullPath: '',
      longitude: null,
      latitude: null,
      communityId: null,
      buildingId: null,
      unitId: null,
    })
  }
}
```

## 5. API接口依赖

组件依赖以下后端接口：

### 5.1 获取地址选项树

```
GET /v1/system/addresses/options
```

**响应格式**：

```json
{
  "options": [
    {
      "value": 1,
      "label": "星河小区",
      "level": 1,
      "longitude": 114.123,
      "latitude": 22.456,
      "children": [
        {
          "value": 101,
          "label": "1号楼",
          "level": 2,
          "children": [
            {
              "value": 1001,
              "label": "1单元",
              "level": 3,
              "longitude": 114.1232,
              "latitude": 22.4563
            }
          ]
        }
      ]
    }
  ]
}
```

### 5.2 获取顶级地址

```
GET /v1/system/addresses
```

**参数**：

- level: 层级，值为1
- parentId: 父级ID，值为0表示顶级

**响应格式**：

```json
{
  "data": [
    {
      "id": 1,
      "name": "星河小区",
      "level": 1,
      "longitude": 114.123,
      "latitude": 22.456
    }
  ]
}
```

### 5.3 获取子级地址

```
GET /v1/system/addresses/children
```

**参数**：

- parentId: 父级ID

**响应格式**：

```json
{
  "data": [
    {
      "id": 101,
      "name": "1号楼",
      "level": 2,
      "parentId": 1
    }
  ]
}
```

## 6. 使用示例

### 6.1 基础用法

```vue
<template>
  <community-address-selector @address-selected="handleAddressSelected" />
</template>

<script setup>
import { ref } from 'vue'
import CommunityAddressSelector from '@/components/CommunityAddressSelector.vue'

const selectedAddress = ref(null)

const handleAddressSelected = (addressInfo) => {
  selectedAddress.value = addressInfo
  console.log('选中的地址:', addressInfo)
}
</script>
```

### 6.2 表单中使用

```vue
<template>
  <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
    <el-form-item label="所在社区" prop="address">
      <community-address-selector @address-selected="handleAddressSelected" />
    </el-form-item>

    <el-form-item>
      <el-button type="primary" @click="submitForm">提交</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup>
import { ref, reactive } from 'vue'
import CommunityAddressSelector from '@/components/CommunityAddressSelector.vue'

const formRef = ref(null)
const form = reactive({
  address: null,
})

const rules = {
  address: [{ required: true, message: '请选择社区地址', trigger: 'change' }],
}

const handleAddressSelected = (addressInfo) => {
  form.address = addressInfo
}

const submitForm = async () => {
  if (!formRef.value) return
  await formRef.value.validate()
  // 提交表单...
}
</script>
```

## 7. H5适配与优化建议

### 7.1 H5适配建议

针对H5环境的特殊性，推荐以下优化：

1. **移动端交互优化**：

   ```css
   /* 增强移动端点击区域 */
   .el-cascader {
     --el-cascader-menu-text-padding: 12px 20px;
   }

   /* 适配小屏幕 */
   @media screen and (max-width: 768px) {
     .el-cascader-panel {
       max-width: 100vw;
     }
   }
   ```

2. **触摸友好交互**：

   ```javascript
   // 修改级联选择器配置，优化移动端体验
   const cascaderProps = {
     // ...其他配置
     expandTrigger: 'click', // 移动端使用点击展开而非悬停
     checkStrictly: true, // 可选择任意层级
   }
   ```

3. **加载状态优化**：
   ```vue
   <template>
     <div>
       <el-cascader
         v-model="selectedKeys"
         :options="options"
         :props="cascaderProps"
         :loading="loading"
         placeholder="请选择地址"
         @change="handleChange"
         style="width: 100%;"
       />
       <p v-if="loading" class="loading-tip">正在加载地址数据...</p>
     </div>
   </template>
   ```

### 7.2 性能优化建议

1. **数据缓存**：

   ```javascript
   // 使用本地缓存存储顶级地址数据
   const loadTopLevelData = async () => {
     // 先尝试从缓存读取
     const cachedData = localStorage.getItem('community_address_options')
     if (cachedData) {
       try {
         options.value = JSON.parse(cachedData)
         return true
       } catch (e) {
         console.error('Failed to parse cached data')
       }
     }

     // 缓存不存在或无效，从API加载
     try {
       const response = await get(`${API_BASE_URL}/addresses/options`)
       if (response && response.options) {
         options.value = markLeafNodes(response.options)
         // 存入缓存，设置过期时间
         localStorage.setItem('community_address_options', JSON.stringify(options.value))
         localStorage.setItem('community_address_cache_time', Date.now())
         return true
       }
     } catch (error) {
       console.error('Failed to load address options:', error)
     }
     return false
   }

   // 定期清理缓存
   const clearCacheIfExpired = () => {
     const cacheTime = localStorage.getItem('community_address_cache_time')
     if (cacheTime && Date.now() - parseInt(cacheTime) > 24 * 60 * 60 * 1000) {
       localStorage.removeItem('community_address_options')
       localStorage.removeItem('community_address_cache_time')
     }
   }
   ```

2. **延迟加载**：

   ```javascript
   import { ref, onMounted, nextTick } from 'vue'

   // 组件可见时才加载数据
   const isVisible = ref(false)
   const observer = new IntersectionObserver((entries) => {
     if (entries[0].isIntersecting) {
       isVisible.value = true
       loadAddressData()
       observer.disconnect()
     }
   })

   onMounted(() => {
     nextTick(() => {
       observer.observe(document.querySelector('#community-selector'))
     })
   })
   ```

3. **批量处理与虚拟滚动**：
   ```javascript
   // 在大数据集情况下，使用Element Plus的虚拟滚动功能
   const cascaderProps = {
     // ...其他配置
     props: {
       virtualScroll: true, // 启用虚拟滚动
       height: 250, // 设置虚拟滚动区域高度
     },
   }
   ```

## 8. 组件扩展方向

### 8.1 功能增强建议

1. **地图集成**：

   ```vue
   <!-- 添加地图选点功能 -->
   <template>
     <div class="address-selector-with-map">
       <community-address-selector @address-selected="handleAddressSelected" />
       <button @click="showMap = true">地图选点</button>

       <el-dialog v-model="showMap" title="地图选点" width="90%">
         <div class="map-container" style="height: 400px;">
           <!-- 集成高德/百度地图组件 -->
         </div>
       </el-dialog>
     </div>
   </template>
   ```

2. **最近使用记录**：

   ```javascript
   // 添加最近使用记录功能
   const recentAddresses = ref([])

   const loadRecentAddresses = () => {
     try {
       const saved = localStorage.getItem('recent_addresses')
       if (saved) {
         recentAddresses.value = JSON.parse(saved).slice(0, 5) // 最多显示5条
       }
     } catch (e) {
       console.error('Failed to load recent addresses')
     }
   }

   const saveToRecentAddresses = (address) => {
     // 将当前选择添加到最近使用记录
     const list = [
       address,
       ...recentAddresses.value.filter((item) => item.communityId !== address.communityId),
     ].slice(0, 5)

     recentAddresses.value = list
     localStorage.setItem('recent_addresses', JSON.stringify(list))
   }
   ```

3. **自定义样式支持**：
   ```vue
   <!-- 添加主题定制支持 -->
   <template>
     <community-address-selector
       :theme="{
         activeColor: '#ff6700',
         borderRadius: '8px',
         fontSize: '14px',
       }"
     />
   </template>
   ```

### 8.2 架构优化建议

1. **状态管理集成**：

   ```javascript
   // 将地址数据纳入统一状态管理
   import { useAddressStore } from '@/stores/addressStore'

   const addressStore = useAddressStore()

   // 使用store中的方法加载数据
   const loadData = async (node, resolve) => {
     if (node.level === 0) {
       const data = await addressStore.getTopLevelAddresses()
       resolve(data)
       return
     }

     const data = await addressStore.getChildrenAddresses(node.data.value)
     resolve(data)
   }
   ```

2. **组件拆分**：
   ```
   components/
   ├── CommunityAddressSelector/
   │   ├── index.vue           // 主组件
   │   ├── AddressOption.vue   // 选项展示组件
   │   ├── AddressSearch.vue   // 搜索组件
   │   ├── MapPicker.vue       // 地图选点组件
   │   └── utils.js           // 工具函数
   ```

## 9. 测试策略

### 9.1 单元测试

为组件编写单元测试，确保核心功能正常运行：

```javascript
// CommunityAddressSelector.spec.js
import { mount } from '@vue/test-utils'
import CommunityAddressSelector from '@/components/CommunityAddressSelector.vue'
import { vi } from 'vitest'

describe('CommunityAddressSelector', () => {
  test('初始化时不应有选中值', () => {
    const wrapper = mount(CommunityAddressSelector)
    expect(wrapper.vm.selectedKeys).toEqual([])
  })

  test('选择地址后应触发address-selected事件', async () => {
    const wrapper = mount(CommunityAddressSelector)

    // 模拟数据和选择
    wrapper.vm.options = [
      {
        value: 1,
        label: '测试小区',
        level: 1,
        children: [],
      },
    ]

    await wrapper.vm.handleChange([1])

    expect(wrapper.emitted('address-selected')).toBeTruthy()
    expect(wrapper.emitted('address-selected')[0][0]).toHaveProperty('communityId', 1)
  })
})
```

### 9.2 集成测试

```javascript
// 模拟地址选择流程
test('完整地址选择流程测试', async () => {
  // 挂载组件
  const wrapper = mount(CommunityAddressSelector)

  // 模拟API响应
  vi.mock('@/utils/request', () => ({
    get: vi.fn().mockImplementation((url) => {
      if (url.includes('addresses/options')) {
        return Promise.resolve({
          options: [
            /* 模拟数据 */
          ],
        })
      }
      return Promise.resolve([])
    }),
  }))

  // 等待组件加载完成
  await flushPromises()

  // 模拟选择
  await wrapper.findComponent(ElCascader).vm.$emit('change', [1, 2, 3])

  // 验证输出
  expect(wrapper.emitted('address-selected')[0][0]).toMatchObject({
    communityId: 1,
    buildingId: 2,
    unitId: 3,
  })
})
```

## 10. 常见问题与解决方案

### 10.1 数据加载失败

**问题**：组件初始化时无法加载地址数据

**解决方案**：

1. 添加重试机制

   ```javascript
   const loadInitialData = async (retries = 3) => {
     try {
       const response = await get(`${API_BASE_URL}/addresses/options`)
       options.value = markLeafNodes(response.options || [])
     } catch (error) {
       if (retries > 0) {
         console.warn(`Loading failed, retrying... (${retries} attempts left)`)
         setTimeout(() => loadInitialData(retries - 1), 1000)
       } else {
         console.error('Failed to load address data after multiple attempts')
         options.value = []
       }
     }
   }
   ```

2. 提供离线备用数据

   ```javascript
   const fallbackData = [
     {
       value: 'offline',
       label: '离线模式 - 请稍后重试',
       level: 1,
       isLeaf: true,
     },
   ]

   // 加载失败时使用离线数据
   options.value = fallbackData
   ```

### 10.2 选择器样式问题

**问题**：移动端下选择器弹出层被截断或显示不完整

**解决方案**：

```css
/* 全局样式覆盖 */
.el-popper.is-pure {
  max-width: 95vw !important;
  max-height: 300px !important;
}

.el-cascader-panel {
  max-height: 300px !important;
}

@media screen and (max-width: 768px) {
  .el-cascader__dropdown {
    left: 50% !important;
    transform: translateX(-50%) !important;
    min-width: 280px !important;
  }
}
```

### 10.3 数据格式兼容性

**问题**：后端API返回格式可能变化，导致组件解析错误

**解决方案**：

```javascript
// 添加数据适配层
const adaptAddressData = (response) => {
  // 尝试识别不同的数据格式
  if (response && response.options && Array.isArray(response.options)) {
    return response.options
  } else if (response && response.data && Array.isArray(response.data)) {
    return response.data
  } else if (response && Array.isArray(response)) {
    return response
  } else if (response && response.result && Array.isArray(response.result)) {
    return response.result
  }

  console.error('Unrecognized data format:', response)
  return []
}
```

## 11. 结论与最佳实践

社区地址选择器是一个功能强大但逻辑复杂的组件，开发中应注意以下最佳实践：

1. **性能优先**：

   - 懒加载数据而非一次性加载全部
   - 合理使用缓存减少请求
   - 大数据集情况下启用虚拟滚动

2. **用户体验**：

   - 提供清晰的加载状态反馈
   - 适配移动端触摸交互
   - 支持搜索和过滤功能

3. **可靠性**：

   - 实现错误处理和重试机制
   - 提供离线备用数据
   - 添加合适的数据校验

4. **可维护性**：
   - 清晰的代码注释和类型定义
   - 组件功能单一，责任明确
   - 编写完善的测试用例

遵循这些指南，可以开发出高质量、可靠的社区地址选择器组件，为用户提供流畅的地址选择体验。
