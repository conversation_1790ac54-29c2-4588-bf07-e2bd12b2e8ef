# TakeoutMerchants.vue 详细开发指南

## 概述

`TakeoutMerchants.vue` 是外卖商家列表页面的核心组件，位于 `/src/modules/user/views/TakeoutMerchants.vue`。该组件基于 Vue 3 + TypeScript + Element Plus 技术栈开发，提供完整的商家浏览、筛选、搜索和地理位置服务功能。

### 主要功能特性

- 📍 **地理位置服务**: 自动获取用户位置，计算商家距离
- 🏪 **商家列表展示**: 支持分页的商家信息展示
- 🔍 **智能搜索**: 商家名称关键词搜索
- 📂 **分类筛选**: 全局分类导航和子分类筛选
- 🎯 **多维排序**: 默认、评分、距离、配送费排序
- 🎉 **促销展示**: 商家促销活动信息展示
- 📱 **响应式设计**: 适配移动端和桌面端

## 文件结构分析

### 核心文件

```
src/modules/user/views/TakeoutMerchants.vue     # 主组件
src/modules/user/stores/userStore.ts           # 用户状态管理
src/modules/user/stores/merchantStore.ts       # 商家状态管理
src/modules/user/service/takeoutService.ts     # 业务逻辑服务
src/modules/user/api/takeout.ts               # API接口定义
src/modules/user/types/index.ts               # 基础类型定义
src/modules/user/types/takeout.ts             # 外卖相关类型
```

## 组件实现分析

### 模板结构

```vue
<template>
  <div class="takeout-merchants-page">
    <!-- 页面顶部 -->
    <div class="page-header">
      <h2 class="page-title">外卖商家</h2>
      <div class="location-actions">
        <!-- 位置获取按钮/位置信息显示 -->
      </div>
    </div>

    <!-- 分类导航 -->
    <el-card class="category-card">
      <div class="category-nav">
        <!-- 全局分类列表 -->
        <div class="global-categories">
          <!-- 分类项 -->
        </div>
        <!-- 子分类列表 -->
        <div class="sub-categories">
          <!-- 子分类项 -->
        </div>
      </div>
    </el-card>

    <!-- 筛选栏 -->
    <div class="filter-bar">
      <!-- 搜索框 -->
      <div class="search-box">
        <el-input v-model="searchKeyword" />
      </div>
      <!-- 排序选项 -->
      <div class="sort-options">
        <el-radio-group v-model="sortOption">
          <!-- 排序选项 -->
        </el-radio-group>
      </div>
    </div>

    <!-- 商家列表 -->
    <div class="merchant-list">
      <el-card v-for="merchant in merchantStore.merchants" :key="merchant.id">
        <!-- 商家信息卡片 -->
      </el-card>
    </div>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination />
    </div>
  </div>
</template>
```

### 脚本逻辑

```typescript
<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import { Search, Timer, Location, Bicycle, Present } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { takeoutService } from '../service/takeoutService';
import { useUserStore } from '../stores/userStore';
import { useMerchantStore } from '../stores/merchantStore';

// 路由和状态管理
const router = useRouter();
const userStore = useUserStore();
const merchantStore = useMerchantStore();

// 响应式状态
const globalCategories = ref<any[]>([]);
const subCategories = ref<any[]>([]);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);
const searchKeyword = ref('');
const sortOption = ref('default');
const currentGlobalCategory = ref<number | string | null>(null);
const currentCategory = ref<number | string | null>(null);

// 核心方法
async function loadGlobalCategories() { /* 加载全局分类 */ }
function selectGlobalCategory(id: number | string) { /* 选择全局分类 */ }
async function loadMerchants() { /* 加载商家列表 */ }
function handleSearch() { /* 处理搜索 */ }
function getMerchantDistance(merchant: any): string | null { /* 计算距离 */ }
function getPromotionDesc(promotion: any): string | null { /* 解析促销 */ }
</script>
```

## API 接口详解

### 分类相关接口

#### 1. 获取商户分类列表

```typescript
// API: /api/v1/merchant/merchant-categories
export function getMerchantCategoryList() {
  return get('/api/v1/merchant/merchant-categories')
}

// 使用示例
const response = await takeoutService.category.getMerchantCategoryList()
console.log('商户分类列表:', response.list)
```

**响应数据结构:**

```typescript
interface CategoryResponse {
  list: Array<{
    id: number
    name: string
    icon?: string
    sort_order?: number
    status: number
  }>
  total: number
}
```

#### 2. 获取商家分类

```typescript
// API: /api/v1/user/takeout/merchants/{merchantId}/categories
export function getMerchantCategories(merchantId: string | number) {
  return request({
    url: `/api/v1/user/takeout/merchants/${merchantId}/categories`,
    method: 'get',
  })
}
```

### 商家相关接口

#### 1. 获取商家列表

```typescript
// API: /api/v1/user/takeout/merchants
export function getMerchants(params?: {
  categoryId?: string | number
  globalCategoryId?: string | number
  page?: number
  pageSize?: number
  sort?: string
  keyword?: string
  latitude?: number
  longitude?: number
}) {
  return request({
    url: '/api/v1/user/takeout/merchants',
    method: 'get',
    params,
  })
}
```

**请求参数说明:**

- `globalCategoryId`: 全局分类ID
- `categoryId`: 子分类ID
- `keyword`: 搜索关键词
- `sort`: 排序方式 (`default`, `rating`, `distance`, `delivery_fee`)
- `latitude/longitude`: 用户位置坐标
- `page/pageSize`: 分页参数

**响应数据结构:**

```typescript
interface MerchantListResponse {
  data: ExtendedMerchant[]
  total: number
  page: number
  pageSize: number
}

interface ExtendedMerchant {
  id: string | number
  name: string
  logo: string
  rating: number
  month_sales: number
  operation_status: number // 1: 营业中, 0: 休息中
  address: string
  latitude?: number
  longitude?: number
  deliveryFee: number
  deliveryTime: number
  min_order_amount: number
  category_name: string
  promotion_info?: string
  promotions?: Promotion[]
}
```

## TypeScript 类型定义详解

### 核心类型定义

#### 1. 商家相关类型

```typescript
// src/modules/user/types/takeout.ts

/**
 * 商家信息接口
 */
export interface Merchant {
  id: string
  name: string
  logo: string
  description?: string
  address: string
  phone: string
  latitude?: number
  longitude?: number
  rating: number
  month_sales: number
  operation_status: number
  business_hours: string
  delivery_fee: number
  min_order_amount: number
  delivery_time: number
  category_id: string
  category_name: string
  created_at: string
  updated_at: string
}

/**
 * 扩展商家信息（包含促销、距离等）
 */
export interface ExtendedMerchant extends Merchant {
  distance?: number // 距离（公里）
  promotion_info?: string // 促销信息
  promotions?: Promotion[] // 促销活动列表
  deliveryFee: number // 配送费
  deliveryTime: number // 配送时间
}
```

#### 2. 分类相关类型

```typescript
/**
 * 外卖分类接口
 */
export interface TakeoutCategory {
  id: string
  name: string
  icon?: string
  description?: string
  sort_order: number
  status: number
  parent_id?: string
  merchant_id?: string
  created_at: string
  updated_at: string
}

/**
 * 全局分类接口
 */
export interface GlobalCategory {
  id: string
  name: string
  icon?: string
  description?: string
  sort_order: number
  status: number
  created_at: string
  updated_at: string
}
```

#### 3. 促销活动类型

```typescript
/**
 * 促销活动接口
 */
export interface Promotion {
  id: string
  name: string
  description?: string
  type: number // 促销类型：1-折扣，2-满减，3-赠品，4-优惠券
  rules: string // JSON格式的促销规则
  start_time: string
  end_time: string
  status: number
  merchant_id: string
  created_at: string
  updated_at: string
}

/**
 * 促销规则接口（满减类型）
 */
export interface CouponRule {
  coupon: {
    min_order_amount: number // 最低订单金额
    amount: number // 减免金额
    per_user_limit: number // 每用户限用次数
  }
}
```

## 状态管理详解

### UserStore 用户状态管理

```typescript
// src/modules/user/stores/userStore.ts

export const useUserStore = defineStore('user', () => {
  // 地理位置相关状态
  const userLocation = ref<UserLocation | null>(null)
  const locationError = ref<string | null>(null)
  const isGettingLocation = ref(false)

  // 计算属性
  const hasLocation = computed(() => {
    return userLocation.value && userLocation.value.latitude && userLocation.value.longitude
  })

  const currentLatitude = computed(() => userLocation.value?.latitude)
  const currentLongitude = computed(() => userLocation.value?.longitude)

  // 获取当前位置
  const getCurrentLocation = async (): Promise<boolean> => {
    if (isGettingLocation.value) return false

    isGettingLocation.value = true
    locationError.value = null

    try {
      const position = await new Promise<GeolocationPosition>((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(resolve, reject, {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 300000, // 5分钟缓存
        })
      })

      userLocation.value = {
        latitude: position.coords.latitude,
        longitude: position.coords.longitude,
        accuracy: position.coords.accuracy,
        timestamp: Date.now(),
      }

      await saveLocationToStorage()
      return true
    } catch (error) {
      handleLocationError(error as GeolocationPositionError)
      return false
    } finally {
      isGettingLocation.value = false
    }
  }

  // 计算商家距离
  const calculateMerchantDistance = (lat: number, lng: number): number | null => {
    if (!hasLocation.value) return null
    return calculateDistance(currentLatitude.value!, currentLongitude.value!, lat, lng)
  }

  return {
    // 状态
    userLocation,
    locationError,
    isGettingLocation,
    // 计算属性
    hasLocation,
    currentLatitude,
    currentLongitude,
    // 方法
    getCurrentLocation,
    calculateMerchantDistance,
  }
})
```

### MerchantStore 商家状态管理

```typescript
// src/modules/user/stores/merchantStore.ts

export const useMerchantStore = defineStore('merchant', () => {
  // 状态
  const merchants = ref<ExtendedMerchant[]>([])
  const currentMerchant = ref<ExtendedMerchant | null>(null)
  const loading = ref(false)

  // 计算属性
  const merchantsMap = computed(() => {
    const map = new Map<string, ExtendedMerchant>()
    merchants.value.forEach((merchant) => {
      map.set(merchant.id.toString(), merchant)
    })
    return map
  })

  const activeMerchants = computed(() => {
    return merchants.value.filter((merchant) => merchant.operation_status === 1)
  })

  // 加载商家列表
  const loadMerchants = async (params?: any) => {
    loading.value = true
    try {
      const response = await merchantService.getMerchants(params)
      merchants.value = response.data || []
      return response
    } catch (error) {
      console.error('加载商家列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  return {
    // 状态
    merchants,
    currentMerchant,
    loading,
    // 计算属性
    merchantsMap,
    activeMerchants,
    // 方法
    loadMerchants,
  }
})
```

## 核心功能实现详解

### 1. 地理位置服务

#### 位置获取实现

```typescript
/**
 * 处理获取位置
 */
async function handleGetLocation() {
  try {
    const success = await userStore.getCurrentLocation()
    if (success) {
      ElMessage.success('位置获取成功')
      // 重新加载商家列表以显示距离信息
      await loadMerchants()
    } else {
      ElMessage.warning('位置获取失败，请检查浏览器权限设置')
    }
  } catch (error) {
    console.error('获取位置失败:', error)
    ElMessage.error('获取位置失败')
  }
}

/**
 * 处理刷新位置
 */
async function handleRefreshLocation() {
  try {
    const success = await userStore.getCurrentLocation()
    if (success) {
      ElMessage.success('位置刷新成功')
      await loadMerchants()
    } else {
      ElMessage.warning('位置刷新失败，请检查浏览器权限设置')
    }
  } catch (error) {
    console.error('刷新位置失败:', error)
    ElMessage.error('刷新位置失败')
  }
}
```

#### 距离计算实现

```typescript
/**
 * 获取商家距离当前位置的距离
 * @param merchant 商家信息
 * @returns 距离字符串或null
 */
function getMerchantDistance(merchant: any): string | null {
  if (!merchant.longitude || !merchant.latitude) {
    return null
  }

  const distance = userStore.calculateMerchantDistance(merchant.latitude, merchant.longitude)

  if (distance === null) {
    return null
  }

  // 根据距离显示不同的格式
  if (distance < 1) {
    return `${Math.round(distance * 1000)}m`
  } else {
    return `${distance.toFixed(1)}km`
  }
}
```

### 2. 分类筛选功能

#### 全局分类加载

```typescript
/**
 * 加载全局分类
 */
async function loadGlobalCategories() {
  try {
    const response: any = await takeoutService.category.getMerchantCategoryList()
    console.log('商户分类列表:', response)

    // 从商户分类列表中提取list字段
    globalCategories.value = response?.list || []

    // 默认选择第一个全局分类
    if (globalCategories.value.length > 0 && !currentGlobalCategory.value) {
      selectGlobalCategory(globalCategories.value[0].id)
    }
  } catch (error) {
    console.error('获取全局分类失败:', error)
    ElMessage.error('获取分类失败，请稍后重试')
  }
}

/**
 * 选择全局分类
 * @param id 分类ID
 */
function selectGlobalCategory(id: number | string) {
  currentGlobalCategory.value = id
  currentCategory.value = null
  loadSubCategories()
  currentPage.value = 1
  loadMerchants()
}
```

### 3. 搜索和排序功能

#### 搜索实现

```typescript
/**
 * 处理搜索
 */
function handleSearch() {
  currentPage.value = 1
  loadMerchants()
}

/**
 * 处理排序变更
 */
function handleSortChange() {
  currentPage.value = 1
  loadMerchants()
}
```

#### 商家列表加载

```typescript
/**
 * 加载商家列表
 */
async function loadMerchants() {
  try {
    const params: any = {
      page: currentPage.value,
      pageSize: pageSize.value,
    }

    // 添加分类过滤
    if (currentGlobalCategory.value) {
      params.globalCategoryId = currentGlobalCategory.value
    }

    if (currentCategory.value) {
      params.categoryId = currentCategory.value
    }

    // 添加搜索关键词
    if (searchKeyword.value) {
      params.keyword = searchKeyword.value
    }

    // 添加排序
    if (sortOption.value !== 'default') {
      params.sort = sortOption.value
    }

    // 添加用户位置信息用于距离计算
    if (userStore.hasLocation) {
      params.latitude = userStore.currentLatitude
      params.longitude = userStore.currentLongitude
    }

    const result = await merchantStore.loadMerchants(params)
    total.value = result.total
  } catch (error) {
    console.error('获取商家列表失败:', error)
    ElMessage.error('获取商家列表失败，请稍后重试')
  }
}
```

### 4. 促销信息解析

```typescript
/**
 * 解析促销规则并返回优惠描述
 * @param promotion 促销活动信息
 * @returns 优惠描述字符串或null
 */
function getPromotionDesc(promotion: any): string | null {
  if (!promotion.rules) {
    return null
  }

  try {
    const rules = JSON.parse(promotion.rules)

    // 处理满减活动
    if (promotion.type === 4 && rules.coupon) {
      const coupon = rules.coupon
      const minAmount = coupon.min_order_amount || 0
      const amount = coupon.amount || 0
      const perUserLimit = coupon.per_user_limit || 0

      let desc = `满${minAmount}元减${amount}元`

      if (perUserLimit > 0) {
        desc += `，每人限用${perUserLimit}次`
      }

      return desc
    }

    return null
  } catch (error) {
    console.error('解析促销规则失败:', error)
    return null
  }
}
```

## 样式设计详解

### 1. 整体布局

```scss
.takeout-merchants-page {
  padding: 10px 0;
  max-width: 1200px;
  margin: 0 auto;

  // 页面头部
  .page-header {
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .page-title {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
      color: #333;
    }

    .location-actions {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: 5px;

      .location-info {
        display: flex;
        align-items: center;
        gap: 5px;
        color: #67c23a;
        font-size: 14px;
      }

      .location-error {
        font-size: 12px;
      }
    }
  }
}
```

### 2. 分类导航样式

```scss
.category-card {
  margin-bottom: 20px;

  .category-nav {
    width: 100%;

    .global-categories {
      margin-bottom: 10px;

      .global-category-list {
        display: flex;
        gap: 15px;
        padding: 5px 0;

        .global-category-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          cursor: pointer;
          padding: 5px 15px;
          border-radius: 4px;
          transition: all 0.3s;

          &:hover {
            background-color: #f5f7fa;
          }

          &.active {
            color: var(--el-color-primary);
            background-color: var(--el-color-primary-light-9);
          }

          .category-icon {
            margin-bottom: 5px;
            font-size: 20px;
          }

          .category-name {
            font-size: 14px;
          }
        }
      }
    }

    .sub-categories {
      .sub-category-list {
        display: flex;
        gap: 10px;
        padding: 5px 0;
        flex-wrap: wrap;

        .sub-category-item {
          padding: 6px 15px;
          border-radius: 16px;
          background-color: #f5f7fa;
          cursor: pointer;
          transition: all 0.3s;
          font-size: 13px;

          &:hover {
            background-color: #e6e8eb;
          }

          &.active {
            color: #fff;
            background-color: var(--el-color-primary);
          }
        }
      }
    }
  }
}
```

### 3. 商家卡片样式

```scss
.merchant-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 20px;

  .merchant-card {
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      transform: translateY(-2px);
    }

    .merchant-info {
      display: flex;
      gap: 15px;

      .merchant-logo {
        width: 80px;
        height: 80px;
        overflow: hidden;
        border-radius: 4px;
        flex-shrink: 0;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .merchant-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 8px;

        .merchant-header {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .merchant-name {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
          }
        }

        .merchant-rating {
          display: flex;
          align-items: center;
          gap: 5px;

          .rating-count {
            color: #909399;
            font-size: 13px;
          }
        }

        .merchant-meta {
          display: flex;
          gap: 15px;

          .meta-item {
            display: flex;
            align-items: center;
            gap: 5px;
            color: #606266;
            font-size: 13px;
          }
        }

        .merchant-tags {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .min-amount {
            color: #f56c6c;
            font-size: 13px;
          }

          .category-tags {
            display: flex;
            gap: 5px;
            align-items: center;
            font-size: 13px;
            color: #909399;
          }
        }
      }
    }
  }
}
```

### 4. 促销活动样式

```scss
.merchant-promotion {
  margin: 8px 0;

  .promotion-banner {
    background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
    border-radius: 6px;
    padding: 8px 12px;
    color: white;
    font-size: 12px;

    .promotion-tag {
      display: flex;
      align-items: center;
      gap: 4px;
      font-weight: 600;
      margin-bottom: 4px;

      .el-icon {
        font-size: 14px;
      }
    }

    .promotion-content {
      display: flex;
      flex-direction: column;
      gap: 2px;

      .promotion-title {
        font-weight: 500;
        font-size: 13px;
        line-height: 1.3;
      }

      .promotion-details {
        display: flex;
        flex-direction: column;
        gap: 2px;

        .promotion-item {
          display: flex;
          flex-direction: column;
          gap: 1px;

          .promotion-name {
            font-weight: 500;
            font-size: 12px;
          }

          .promotion-desc {
            font-size: 11px;
            opacity: 0.9;
            line-height: 1.2;
          }
        }

        .more-promotions {
          font-size: 11px;
          opacity: 0.8;
          font-style: italic;
          margin-top: 2px;
        }
      }
    }
  }
}
```

## 生命周期和初始化

```typescript
// 监视全局分类变化
watch(currentGlobalCategory, (newVal) => {
  if (newVal) {
    loadSubCategories()
  }
})

// 组件挂载时的初始化逻辑
onMounted(async () => {
  // 1. 尝试获取用户当前位置
  try {
    const hasStoredLocation = await userStore.loadLocationFromStorage()
    if (!hasStoredLocation) {
      // 如果没有存储的位置，尝试获取当前位置
      await userStore.getCurrentLocation()
    }
  } catch (error) {
    console.log('获取位置信息失败，将不显示距离信息:', error)
  }

  // 2. 加载全局分类
  await loadGlobalCategories()

  // 3. 加载商家列表
  await loadMerchants()
})
```

## 错误处理和用户体验

### 1. API 错误处理

```typescript
// 在每个API调用中都包含错误处理
try {
  const response = await takeoutService.category.getMerchantCategoryList()
  globalCategories.value = response?.list || []
} catch (error) {
  console.error('获取全局分类失败:', error)
  ElMessage.error('获取分类失败，请稍后重试')
}
```

### 2. 位置权限处理

```typescript
// 位置获取失败时的友好提示
if (!success) {
  ElMessage.warning('位置获取失败，请检查浏览器权限设置')
}
```

### 3. 空状态处理

```vue
<!-- 商家列表为空时显示空状态 -->
<el-empty
  v-if="merchantStore.merchants.length === 0 && !merchantStore.loading"
  description="暂无相关商家"
/>
```

### 4. 加载状态

```vue
<!-- 商家列表加载状态 -->
<div class="merchant-list" v-loading="merchantStore.loading">
  <!-- 商家列表内容 -->
</div>
```

## 性能优化建议

### 1. 图片懒加载

```vue
<template>
  <img :src="merchant.logo" :alt="merchant.name" loading="lazy" @error="handleImageError" />
</template>

<script>
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement;
  img.src = '/images/default-merchant-logo.png';
};
</script>
```

### 2. 防抖搜索

```typescript
import { debounce } from 'lodash-es';

const debouncedSearch = debounce(() => {
  handleSearch();
}, 300);

// 在搜索输入框中使用
<el-input
  v-model="searchKeyword"
  @input="debouncedSearch"
/>
```

### 3. 虚拟滚动（大数据量时）

```vue
<template>
  <VirtualList :items="merchantStore.merchants" :item-height="120" key-field="id" v-slot="{ item }">
    <MerchantCard :merchant="item" />
  </VirtualList>
</template>
```

## 总结

`TakeoutMerchants.vue` 是一个功能完整、设计精良的外卖商家列表组件。它展示了现代 Vue 3 应用的最佳实践，包括：

1. **组合式API**: 使用 `<script setup>` 语法，代码更简洁
2. **TypeScript**: 完整的类型定义，提高代码质量
3. **状态管理**: 使用 Pinia 进行状态管理，逻辑清晰
4. **响应式设计**: 适配移动端和桌面端
5. **用户体验**: 完善的错误处理和加载状态
6. **性能优化**: 合理的API调用和数据处理

通过本指南，开发者可以深入理解该组件的实现原理，并在此基础上进行功能扩展和优化。
