# 外卖商家列表页面 H5 前端开发指南

## 目录

1. [概述](#概述)
2. [页面功能分析](#页面功能分析)
3. [组件结构](#组件结构)
4. [API 接口详解](#api-接口详解)
5. [TypeScript 类型定义](#typescript-类型定义)
6. [状态管理](#状态管理)
7. [核心功能实现](#核心功能实现)
8. [样式设计](#样式设计)
9. [最佳实践](#最佳实践)
10. [常见问题](#常见问题)

## 概述

`TakeoutMerchants.vue` 是外卖模块的商家列表页面，提供了完整的商家浏览、筛选、搜索和定位功能。该页面是外卖业务的核心入口，用户可以通过此页面发现和选择心仪的商家。

### 技术栈

- **框架**: Vue 3 + TypeScript
- **UI 组件库**:wot Ui
- **状态管理**: Pinia
- **路由**: Vue Router
- **HTTP 客户端**: Axios
- **地理位置**: Navigator Geolocation API

### 文件位置

```
src/modules/user/views/TakeoutMerchants.vue
```

## 页面功能分析

### 核心功能

1. **商家列表展示**

   - 商家基本信息（名称、logo、评分、月销量）
   - 营业状态标识
   - 配送信息（配送费、起送金额、配送时间）
   - 距离显示
   - 促销活动展示

2. **分类筛选**

   - 全局分类导航
   - 子分类筛选
   - 分类切换动画

3. **搜索功能**

   - 关键词搜索
   - 实时搜索建议
   - 搜索历史

4. **排序功能**

   - 综合排序
   - 距离排序
   - 销量排序
   - 评分排序

5. **地理位置**

   - 自动获取用户位置
   - 手动刷新位置
   - 距离计算
   - 位置权限处理

6. **分页加载**
   - 分页组件
   - 懒加载
   - 加载状态

### 用户交互流程

```mermaid
flowchart TD
    A[进入页面] --> B[获取用户位置]
    B --> C[加载全局分类]
    C --> D[加载商家列表]
    D --> E[用户操作]
    E --> F{操作类型}
    F -->|分类筛选| G[切换分类]
    F -->|搜索| H[关键词搜索]
    F -->|排序| I[重新排序]
    F -->|刷新位置| J[更新位置]
    F -->|点击商家| K[跳转详情]
    G --> L[重新加载列表]
    H --> L
    I --> L
    J --> L
    L --> E
```

## 组件结构

### 模板结构

```vue
<template>
  <div class="takeout-merchants">
    <!-- 头部区域 -->
    <div class="header">
      <div class="location-info">
        <!-- 位置信息和刷新按钮 -->
      </div>
    </div>

    <!-- 全局分类导航 -->
    <div class="global-categories">
      <!-- 分类标签 -->
    </div>

    <!-- 子分类导航 -->
    <div class="sub-categories" v-if="subCategories.length > 0">
      <!-- 子分类标签 -->
    </div>

    <!-- 筛选栏 -->
    <div class="filter-bar">
      <!-- 搜索框和排序选择 -->
    </div>

    <!-- 商家列表 -->
    <div class="merchant-list">
      <!-- 商家卡片 -->
    </div>

    <!-- 分页组件 -->
    <div class="pagination">
      <!-- 分页控件 -->
    </div>
  </div>
</template>
```

### 组件依赖

```typescript
// Vue 核心
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { useRouter } from 'vue-router'

// Element Plus 组件
import {
  ElInput,
  ElSelect,
  ElOption,
  ElButton,
  ElTag,
  ElRate,
  ElPagination,
  ElMessage,
} from 'element-plus'

// 业务模块
import { takeoutService } from '../service/takeoutService'
import { useUserStore } from '../stores/userStore'
import { useMerchantStore } from '../stores/merchantStore'

// 类型定义
import type { GlobalCategory, TakeoutCategory, ExtendedMerchant } from '../types'
```

## API 接口详解

### 分类相关 API

#### 1. 获取全局分类列表

```typescript
/**
 * 获取全局分类列表
 * @returns Promise<GlobalCategory[]>
 */
const getGlobalCategories = async (): Promise<GlobalCategory[]> => {
  const response = await api.get('/api/v1/takeout/global-categories')
  return response.data
}
```

**请求示例:**

```http
GET /api/v1/takeout/global-categories
Content-Type: application/json
Authorization: Bearer {token}
```

**响应示例:**

```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "name": "美食",
      "icon": "food-icon.png",
      "level": 1,
      "sortOrder": 1,
      "isActive": true,
      "children": [
        {
          "id": 11,
          "name": "中餐",
          "parentId": 1,
          "level": 2,
          "sortOrder": 1,
          "isActive": true
        }
      ]
    }
  ]
}
```

#### 2. 获取商家分类列表

```typescript
/**
 * 获取商家分类列表
 * @param merchantId 商家ID
 * @returns Promise<TakeoutCategory[]>
 */
const getMerchantCategories = async (merchantId: string | number): Promise<TakeoutCategory[]> => {
  const response = await api.get(`/api/v1/takeout/merchants/${merchantId}/categories`)
  return response.data
}
```

### 商家相关 API

#### 1. 获取商家列表

```typescript
/**
 * 获取商家列表
 * @param params 查询参数
 * @returns Promise<{list: ExtendedMerchant[], total: number}>
 */
interface MerchantListParams {
  page?: number // 页码，默认1
  pageSize?: number // 每页数量，默认10
  categoryId?: string | number // 分类ID
  keyword?: string // 搜索关键词
  sort?: string // 排序方式: 'distance' | 'sales' | 'rating' | 'default'
  latitude?: number // 用户纬度
  longitude?: number // 用户经度
}

const getMerchants = async (params: MerchantListParams) => {
  const response = await api.get('/api/v1/takeout/merchants', { params })
  return response.data
}
```

**请求示例:**

```http
GET /api/v1/takeout/merchants?page=1&pageSize=10&categoryId=1&sort=distance&latitude=39.9042&longitude=116.4074
Content-Type: application/json
Authorization: Bearer {token}
```

**响应示例:**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "美味餐厅",
        "logo": "logo.jpg",
        "cover": "cover.jpg",
        "description": "正宗川菜",
        "address": "北京市朝阳区xxx街道",
        "phone": "010-12345678",
        "businessHours": "09:00-22:00",
        "rating": 4.5,
        "ratingCount": 1234,
        "minDeliveryAmount": 20,
        "deliveryFee": 3,
        "deliveryTime": "30-45分钟",
        "distance": 1200,
        "isOpen": true,
        "isPaused": false,
        "latitude": 39.9042,
        "longitude": 116.4074,
        "month_sales": 2580,
        "operation_status": 1,
        "promotion_info": "满30减5",
        "promotions": [
          {
            "id": 1,
            "name": "满减优惠",
            "type": 1,
            "description": "满30减5",
            "isActive": true
          }
        ],
        "categories": [
          {
            "id": 1,
            "name": "川菜",
            "level": 2,
            "isActive": true
          }
        ]
      }
    ],
    "total": 156
  }
}
```

#### 2. 获取商家食品列表

```typescript
/**
 * 获取指定商家的食品列表
 * @param merchantId 商家ID
 * @param params 查询参数
 * @returns Promise<Food[]>
 */
interface MerchantFoodParams {
  categoryId?: string | number // 食品分类ID
  keyword?: string // 搜索关键词
  page?: number // 页码
  pageSize?: number // 每页数量
}

const getMerchantFoods = async (merchantId: string | number, params?: MerchantFoodParams) => {
  const response = await api.get(`/api/v1/takeout/merchants/${merchantId}/foods`, { params })
  return response.data
}
```

### 购物车相关 API

#### 1. 添加商品到购物车

```typescript
/**
 * 添加商品到购物车
 * @param params 添加参数
 * @returns Promise<CartItem>
 */
interface AddToCartParams {
  foodId: string | number
  quantity: number
  variants?: {
    variantId: string | number
    name: string
    price: number
  }[]
  comboItems?: {
    itemId: string | number
    name: string
    price: number
    quantity: number
  }[]
}

const addToCart = async (params: AddToCartParams) => {
  const response = await api.post('/api/v1/takeout/cart/add', params)
  return response.data
}
```

#### 2. 获取购物车列表

```typescript
/**
 * 获取购物车列表
 * @returns Promise<Cart>
 */
const getCartList = async () => {
  const response = await api.get('/api/v1/takeout/cart')
  return response.data
}
```

### 订单相关 API

#### 1. 创建订单

```typescript
/**
 * 创建外卖订单
 * @param params 订单参数
 * @returns Promise<TakeoutOrder>
 */
interface CreateOrderParams {
  merchantId: string | number
  items: {
    foodId: string | number
    quantity: number
    variants?: any[]
    comboItems?: any[]
  }[]
  addressId: string | number
  paymentMethod: string
  remark?: string
}

const createOrder = async (params: CreateOrderParams) => {
  const response = await api.post('/api/v1/takeout/orders', params)
  return response.data
}
```

#### 2. 获取订单列表

```typescript
/**
 * 获取用户订单列表
 * @param params 查询参数
 * @returns Promise<{list: TakeoutOrder[], total: number}>
 */
interface OrderListParams {
  page?: number
  pageSize?: number
  status?: TakeoutOrderStatus
  startDate?: string
  endDate?: string
}

const getOrderList = async (params?: OrderListParams) => {
  const response = await api.get('/api/v1/takeout/orders', { params })
  return response.data
}
```

## TypeScript 类型定义

### 核心接口定义

#### 1. 商家相关类型

```typescript
// 基础商家接口
export interface Merchant {
  id: string | number
  name: string
  logo: string
  cover: string
  description?: string
  address: string
  phone?: string
  businessHours?: string
  categories: TakeoutCategory[]
  rating: number
  ratingCount: number
  minDeliveryAmount: number
  deliveryFee: number
  deliveryTime: string
  distance?: number // 单位：米
  isOpen: boolean
  isPaused: boolean
  createdAt: string
  updatedAt: string
}

// 扩展商家接口（包含促销等信息）
export interface ExtendedMerchant extends Merchant {
  // 地理位置信息
  latitude?: number
  longitude?: number

  // 促销信息
  promotions?: MerchantPromotion[]
  promotion_info?: string

  // 运营状态
  operation_status?: number // 1: 营业中, 0: 休息中

  // 销售数据
  month_sales?: number

  // 分类信息
  category_name?: string

  // 最小订单金额
  min_order_amount?: number
}

// 商家促销信息
export interface MerchantPromotion {
  id: string | number
  merchantId: string | number
  name: string
  type: number
  description?: string
  rules?: string
  startTime?: string
  endTime?: string
  isActive: boolean
}
```

#### 2. 分类相关类型

```typescript
// 外卖分类接口
export interface TakeoutCategory {
  id: string | number
  name: string
  icon?: string
  image?: string
  description?: string
  parentId?: string | number
  level: number
  sortOrder: number
  isActive: boolean
  createdAt: string
  updatedAt: string
}

// 全局分类接口（支持树形结构）
export interface GlobalCategory extends TakeoutCategory {
  children?: GlobalCategory[]
}
```

#### 3. 食品相关类型

```typescript
// 食品接口
export interface Food {
  id: string | number
  merchantId: string | number
  categoryId: string | number
  name: string
  description?: string
  price: number
  originalPrice?: number
  image: string
  images?: string[]
  unit?: string
  stock: number
  salesCount: number
  rating: number
  ratingCount: number
  isActive: boolean
  isRecommended: boolean
  hasVariants: boolean
  hasCombo: boolean
  attributes?: Record<string, string> // 如 {"辣度": "中辣", "热量": "高"}
  tags?: string[]
  createdAt: string
  updatedAt: string
  variants?: Variant[]
}

// 规格接口
export interface Variant {
  id: string | number
  name: string
  price: number
  original_price?: number // 原价
  stock: number
  is_active: boolean
  is_default: boolean
}

// 食品规格组接口
export interface VariantGroup {
  id: string | number
  name: string
  required: boolean
  multiSelect: boolean
  min?: number
  max?: number
  options: VariantOption[]
}

// 规格选项接口
export interface VariantOption {
  id: string | number
  name: string
  price: number
  isDefault: boolean
  isActive: boolean
}
```

#### 4. 购物车相关类型

```typescript
// 购物车项接口
export interface CartItem {
  id: string | number
  userId: string | number
  foodId: string | number
  food: Food
  quantity: number
  selected: boolean
  // 商家信息，用于购物车按商家分组
  merchant_id?: string | number
  merchant_name?: string
  // 商家经纬度信息，用于计算配送距离
  merchant_latitude?: number
  merchant_longitude?: number
  variants?: {
    variantId: string | number
    name: string
    price: number
  }[]
  comboItems?: {
    itemId: string | number
    name: string
    price: number
    quantity: number
  }[]
  packaging_fee?: number // 包装费用
  totalPrice: number
  createdAt: string
  updatedAt: string
}

// 购物车接口
export interface Cart {
  items: CartItem[]
  totalPrice: number
  totalItems: number
  merchantId: string | number
  merchantName: string
}
```

#### 5. 订单相关类型

```typescript
// 外卖订单状态枚举
export enum TakeoutOrderStatus {
  PENDING_PAYMENT = 'pending_payment', // 待支付
  PAID = 'paid', // 已支付
  ACCEPTED = 'accepted', // 商家已接单
  PREPARING = 'preparing', // 备餐中
  READY = 'ready', // 准备完成
  DELIVERING = 'delivering', // 配送中
  DELIVERED = 'delivered', // 已送达
  COMPLETED = 'completed', // 已完成
  CANCELLED = 'cancelled', // 已取消
  REFUNDING = 'refunding', // 退款中
  REFUNDED = 'refunded', // 已退款
}

// 外卖订单接口
export interface TakeoutOrder {
  orderID: string | number
  orderNumber: string
  userId: string | number
  merchantId: string | number
  merchant: {
    id: string | number
    name: string
    logo: string
    phone?: string
  }
  items: TakeoutOrderItem[]
  address: {
    id: string | number
    name: string
    phone: string
    province: string
    city: string
    district: string
    detail: string
  }
  status: TakeoutOrderStatus
  totalAmount: number
  deliveryFee: number
  packagingFee?: number
  discountAmount?: number
  payableAmount: number
  paymentMethod?: string
  paidAt?: string
  acceptedAt?: string
  preparedAt?: string
  readyAt?: string
  deliveredAt?: string
  completedAt?: string
  cancelledAt?: string
  refundedAt?: string
  estimatedDeliveryTime?: string
  deliveryTime?: number // 配送时间（分钟）
  remark?: string
  createdAt: string
  updatedAt: string
}

// 外卖订单项接口
export interface TakeoutOrderItem {
  id: string | number
  orderId: string | number
  foodId: string | number
  foodName: string
  foodImage: string
  quantity: number
  price: number
  variants?: {
    variantId: string | number
    name: string
    price: number
  }[]
  comboItems?: {
    itemId: string | number
    name: string
    price: number
    quantity: number
  }[]
  totalPrice: number
  rating?: number
  comment?: string
  ratedAt?: string
}
```

#### 6. 用户相关类型

```typescript
// 用户地理位置接口
export interface UserLocation {
  longitude: number
  latitude: number
}

// 用户信息接口
export interface UserInfo {
  id: string
  username: string
  nickname: string
  avatar?: string
  email?: string
  phone?: string
  status: UserStatus
  createdAt: string
  updatedAt: string
  lastLoginAt?: string
  role: string
  balance?: number
  points?: number
  level?: number
  gender?: 'male' | 'female' | 'other' | 'unknown'
  birthday?: string
}

// 用户状态枚举
export enum UserStatus {
  ACTIVE = 'active', // 正常状态
  FROZEN = 'frozen', // 账户冻结
  DELETED = 'deleted', // 已注销
}
```

### 响应类型定义

```typescript
// API 响应基础接口
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp?: number
}

// 分页响应接口
export interface PaginatedResponse<T> {
  list: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// 商家列表响应
export type MerchantListResponse = ApiResponse<PaginatedResponse<ExtendedMerchant>>

// 分类列表响应
export type CategoryListResponse = ApiResponse<GlobalCategory[]>

// 购物车响应
export type CartResponse = ApiResponse<Cart>

// 订单响应
export type OrderResponse = ApiResponse<TakeoutOrder>
export type OrderListResponse = ApiResponse<PaginatedResponse<TakeoutOrder>>
```

## 状态管理

### UserStore（用户状态管理）

```typescript
// stores/userStore.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { UserInfo, UserLocation } from '../types'

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref<string | null>(null)
  const userInfo = ref<UserInfo | null>(null)
  const loginTime = ref<number | null>(null)
  const expiresIn = ref<number>(24 * 60 * 60 * 1000) // 默认24小时

  // 地理位置相关状态
  const userLocation = ref<UserLocation | null>(null)
  const locationError = ref<string | null>(null)
  const isGettingLocation = ref(false)

  // 计算属性
  const isLoggedIn = computed(() => {
    return !!token.value && !!userInfo.value
  })

  const hasLocation = computed(() => {
    return !!userLocation.value
  })

  const currentLongitude = computed(() => {
    return userLocation.value?.longitude || null
  })

  const currentLatitude = computed(() => {
    return userLocation.value?.latitude || null
  })

  // 方法
  const getCurrentLocation = async (): Promise<boolean> => {
    if (isGettingLocation.value) {
      console.log('正在获取位置中，请稍候')
      return false
    }

    if (!navigator.geolocation) {
      locationError.value = '浏览器不支持地理位置获取'
      return false
    }

    isGettingLocation.value = true
    locationError.value = null

    return new Promise((resolve) => {
      const options = {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 300000,
      }

      navigator.geolocation.getCurrentPosition(
        async (position) => {
          const { longitude, latitude } = position.coords
          userLocation.value = { longitude, latitude }
          await saveLocationToStorage()
          isGettingLocation.value = false
          resolve(true)
        },
        (error) => {
          let errorMessage = '获取位置失败'
          switch (error.code) {
            case error.PERMISSION_DENIED:
              errorMessage = '用户拒绝了位置权限请求'
              break
            case error.POSITION_UNAVAILABLE:
              errorMessage = '位置信息不可用'
              break
            case error.TIMEOUT:
              errorMessage = '获取位置超时'
              break
          }
          locationError.value = errorMessage
          isGettingLocation.value = false
          resolve(false)
        },
        options,
      )
    })
  }

  const calculateMerchantDistance = (merchantLat: number, merchantLon: number): number | null => {
    if (!userLocation.value) return null

    const R = 6371 // 地球半径（公里）
    const dLat = ((merchantLat - userLocation.value.latitude) * Math.PI) / 180
    const dLon = ((merchantLon - userLocation.value.longitude) * Math.PI) / 180
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos((userLocation.value.latitude * Math.PI) / 180) *
        Math.cos((merchantLat * Math.PI) / 180) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2)
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
    const distance = R * c

    return Number(distance.toFixed(3))
  }

  return {
    // 状态
    token,
    userInfo,
    userLocation,
    locationError,
    isGettingLocation,

    // 计算属性
    isLoggedIn,
    hasLocation,
    currentLongitude,
    currentLatitude,

    // 方法
    getCurrentLocation,
    calculateMerchantDistance,
  }
})
```

### MerchantStore（商家状态管理）

```typescript
// stores/merchantStore.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { ExtendedMerchant } from '../types'
import { takeoutService } from '../service/takeoutService'

export const useMerchantStore = defineStore('merchant', () => {
  // 状态
  const merchants = ref<ExtendedMerchant[]>([])
  const currentMerchant = ref<ExtendedMerchant | null>(null)
  const loading = ref(false)

  // 计算属性
  const merchantsMap = computed(() => {
    const map = new Map<string | number, ExtendedMerchant>()
    merchants.value.forEach((merchant) => {
      map.set(merchant.id, merchant)
    })
    return map
  })

  const activeMerchants = computed(() => {
    return merchants.value.filter((merchant) => merchant.operation_status === 1)
  })

  const nearbyMerchants = computed(() => {
    return merchants.value
      .filter((merchant) => merchant.latitude && merchant.longitude)
      .sort((a, b) => {
        if (a.distance !== undefined && b.distance !== undefined) {
          return a.distance - b.distance
        }
        return 0
      })
  })

  // 方法
  const loadMerchants = async (params?: {
    page?: number
    pageSize?: number
    categoryId?: string | number
    keyword?: string
    sort?: string
    latitude?: number
    longitude?: number
  }) => {
    loading.value = true
    try {
      const response = await takeoutService.merchant.getMerchants(params)
      const merchantList = response?.list || []

      // 更新商家列表
      merchantList.forEach((newMerchant: any) => {
        const existingMerchant = merchantsMap.value.get(newMerchant.id)
        if (existingMerchant) {
          Object.assign(existingMerchant, newMerchant)
        } else {
          merchants.value.push(newMerchant as ExtendedMerchant)
        }
      })

      return {
        data: merchantList,
        total: response?.total || 0,
      }
    } catch (error) {
      console.error('获取商家列表失败:', error)
      return { data: [], total: 0 }
    } finally {
      loading.value = false
    }
  }

  const updateMerchantInList = (
    merchantId: string | number,
    updates: Partial<ExtendedMerchant>,
  ) => {
    const index = merchants.value.findIndex((merchant) => merchant.id === merchantId)
    if (index !== -1) {
      merchants.value[index] = { ...merchants.value[index], ...updates }
      if (currentMerchant.value?.id === merchantId) {
        currentMerchant.value = { ...currentMerchant.value, ...updates }
      }
    }
  }

  return {
    // 状态
    merchants,
    currentMerchant,
    loading,

    // 计算属性
    merchantsMap,
    activeMerchants,
    nearbyMerchants,

    // 方法
    loadMerchants,
    updateMerchantInList,
  }
})
```

## 核心功能实现

### 1. 组件初始化

```typescript
// TakeoutMerchants.vue <script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { takeoutService } from '../service/takeoutService'
import { useUserStore } from '../stores/userStore'
import { useMerchantStore } from '../stores/merchantStore'
import type { GlobalCategory, TakeoutCategory, ExtendedMerchant } from '../types'

// 路由和状态管理
const router = useRouter()
const userStore = useUserStore()
const merchantStore = useMerchantStore()

// 响应式数据
const globalCategories = ref<GlobalCategory[]>([])
const subCategories = ref<TakeoutCategory[]>([])
const merchants = ref<ExtendedMerchant[]>([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const searchKeyword = ref('')
const sortOption = ref('default')
const currentGlobalCategory = ref<string | number | null>(null)
const currentCategory = ref<string | number | null>(null)
const loading = ref(false)

// 组件挂载时的初始化
onMounted(async () => {
  // 尝试获取用户位置
  if (!userStore.hasLocation) {
    await userStore.getCurrentLocation()
  }

  // 加载全局分类
  await loadGlobalCategories()

  // 加载商家列表
  await loadMerchants()
})
```

### 2. 分类管理

```typescript
/**
 * 加载全局分类列表
 */
const loadGlobalCategories = async () => {
  try {
    const response = await takeoutService.category.getGlobalCategories()
    globalCategories.value = response || []
    console.log('全局分类加载成功:', globalCategories.value)
  } catch (error) {
    console.error('加载全局分类失败:', error)
    ElMessage.error('加载分类失败，请稍后重试')
  }
}

/**
 * 选择全局分类
 * @param categoryId 分类ID
 */
const selectGlobalCategory = (categoryId: string | number | null) => {
  currentGlobalCategory.value = categoryId
  currentCategory.value = null // 重置子分类
  currentPage.value = 1 // 重置页码
  loadMerchants() // 重新加载商家列表
}

/**
 * 加载子分类（当前为空实现，可根据需要扩展）
 */
const loadSubCategories = async () => {
  // 根据当前全局分类加载子分类
  // 这里可以实现具体的子分类加载逻辑
  subCategories.value = []
}

/**
 * 选择子分类
 * @param categoryId 子分类ID
 */
const selectCategory = (categoryId: string | number | null) => {
  currentCategory.value = categoryId
  currentPage.value = 1
  loadMerchants()
}

// 监听全局分类变化，加载对应的子分类
watch(currentGlobalCategory, () => {
  loadSubCategories()
})
```

### 3. 商家列表加载

```typescript
/**
 * 加载商家列表
 */
const loadMerchants = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      categoryId: currentCategory.value || currentGlobalCategory.value,
      keyword: searchKeyword.value,
      sort: sortOption.value,
      latitude: userStore.currentLatitude,
      longitude: userStore.currentLongitude,
    }

    // 过滤掉空值参数
    const filteredParams = Object.fromEntries(
      Object.entries(params).filter(
        ([_, value]) => value !== null && value !== undefined && value !== '',
      ),
    )

    console.log('加载商家列表参数:', filteredParams)

    const response = await merchantStore.loadMerchants(filteredParams)
    merchants.value = response.data
    total.value = response.total

    // 计算商家距离
    if (userStore.hasLocation) {
      merchants.value.forEach((merchant) => {
        if (merchant.latitude && merchant.longitude) {
          merchant.distance = userStore.calculateMerchantDistance(
            merchant.latitude,
            merchant.longitude,
          )
        }
      })
    }

    console.log('商家列表加载成功:', merchants.value)
  } catch (error) {
    console.error('加载商家列表失败:', error)
    ElMessage.error('加载商家列表失败，请稍后重试')
  } finally {
    loading.value = false
  }
}
```

### 4. 搜索功能

```typescript
/**
 * 处理搜索
 */
const handleSearch = () => {
  currentPage.value = 1 // 重置页码
  loadMerchants()
}

/**
 * 清除搜索
 */
const clearSearch = () => {
  searchKeyword.value = ''
  handleSearch()
}

// 搜索防抖处理
let searchTimer: NodeJS.Timeout | null = null
const debouncedSearch = () => {
  if (searchTimer) {
    clearTimeout(searchTimer)
  }
  searchTimer = setTimeout(() => {
    handleSearch()
  }, 500)
}
```

### 5. 排序功能

```typescript
/**
 * 处理排序变更
 * @param sort 排序方式
 */
const handleSortChange = (sort: string) => {
  sortOption.value = sort
  currentPage.value = 1
  loadMerchants()
}

// 排序选项
const sortOptions = [
  { label: '综合排序', value: 'default' },
  { label: '距离最近', value: 'distance' },
  { label: '销量最高', value: 'sales' },
  { label: '评分最高', value: 'rating' },
]
```

### 6. 分页功能

```typescript
/**
 * 处理分页变更
 * @param page 页码
 */
const handlePageChange = (page: number) => {
  currentPage.value = page
  loadMerchants()

  // 滚动到顶部
  window.scrollTo({ top: 0, behavior: 'smooth' })
}

/**
 * 处理每页数量变更
 * @param size 每页数量
 */
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  loadMerchants()
}
```

### 7. 地理位置功能

```typescript
/**
 * 获取用户位置
 */
const handleGetLocation = async () => {
  const success = await userStore.getCurrentLocation()
  if (success) {
    ElMessage.success('位置获取成功')
    // 重新加载商家列表以更新距离信息
    await loadMerchants()
  } else {
    ElMessage.error(userStore.locationError || '位置获取失败')
  }
}

/**
 * 刷新用户位置
 */
const handleRefreshLocation = async () => {
  // 清除当前位置缓存
  userStore.userLocation = null
  await handleGetLocation()
}

/**
 * 格式化距离显示
 * @param merchant 商家信息
 * @returns 格式化的距离字符串
 */
const getMerchantDistance = (merchant: ExtendedMerchant): string => {
  if (!merchant.distance) {
    return '距离未知'
  }

  if (merchant.distance < 1) {
    return `${Math.round(merchant.distance * 1000)}m`
  } else {
    return `${merchant.distance.toFixed(1)}km`
  }
}
```

### 8. 促销信息处理

```typescript
/**
 * 解析促销规则并返回优惠描述
 * @param merchant 商家信息
 * @returns 促销描述
 */
const getPromotionDesc = (merchant: ExtendedMerchant): string => {
  if (merchant.promotion_info) {
    return merchant.promotion_info
  }

  if (merchant.promotions && merchant.promotions.length > 0) {
    const activePromotions = merchant.promotions.filter((p) => p.isActive)
    if (activePromotions.length > 0) {
      return activePromotions.map((p) => p.description || p.name).join('，')
    }
  }

  return ''
}

/**
 * 检查商家是否有促销活动
 * @param merchant 商家信息
 * @returns 是否有促销
 */
const hasPromotion = (merchant: ExtendedMerchant): boolean => {
  return !!(
    merchant.promotion_info ||
    (merchant.promotions && merchant.promotions.some((p) => p.isActive))
  )
}
```

### 9. 页面跳转

```typescript
/**
 * 跳转到商家详情页
 * @param merchant 商家信息
 */
const goToMerchantDetail = (merchant: ExtendedMerchant) => {
  // 保存当前商家到状态管理
  merchantStore.setCurrentMerchant(merchant)

  // 跳转到商家详情页
  router.push({
    name: 'TakeoutMerchantDetail',
    params: { id: merchant.id },
  })
}

/**
 * 跳转到购物车页面
 */
const goToCart = () => {
  router.push({ name: 'TakeoutCart' })
}

/**
 * 跳转到订单页面
 */
const goToOrders = () => {
  router.push({ name: 'Orders' })
}
```

## 样式设计

### 1. 整体布局样式

```scss
// TakeoutMerchants.vue <style scoped>
.takeout-merchants {
  min-height: 100vh;
  background-color: #f5f5f5;

  // 头部区域
  .header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 16px;
    position: sticky;
    top: 0;
    z-index: 100;

    .location-info {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .location-text {
        display: flex;
        align-items: center;
        font-size: 16px;

        .location-icon {
          margin-right: 8px;
          font-size: 18px;
        }

        .location-name {
          max-width: 200px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .refresh-btn {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        border-radius: 20px;
        padding: 8px 16px;
        font-size: 14px;
        transition: all 0.3s ease;

        &:hover {
          background: rgba(255, 255, 255, 0.3);
          transform: translateY(-1px);
        }

        &.loading {
          opacity: 0.7;
          cursor: not-allowed;
        }
      }
    }
  }
}
```

### 2. 分类导航样式

```scss
// 全局分类导航
.global-categories {
  background: white;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .category-scroll {
    display: flex;
    overflow-x: auto;
    gap: 12px;
    padding-bottom: 4px;

    &::-webkit-scrollbar {
      height: 4px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 2px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }

  .category-item {
    flex-shrink: 0;
    padding: 8px 16px;
    border-radius: 20px;
    background: #f8f9fa;
    color: #666;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid transparent;

    &:hover {
      background: #e9ecef;
      transform: translateY(-1px);
    }

    &.active {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border-color: #667eea;
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    }
  }
}

// 子分类导航
.sub-categories {
  background: white;
  padding: 12px 16px;
  border-bottom: 1px solid #eee;

  .sub-category-scroll {
    display: flex;
    overflow-x: auto;
    gap: 8px;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  .sub-category-item {
    flex-shrink: 0;
    padding: 6px 12px;
    border-radius: 16px;
    background: #f8f9fa;
    color: #666;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: #e9ecef;
    }

    &.active {
      background: #667eea;
      color: white;
    }
  }
}
```

### 3. 筛选栏样式

```scss
// 筛选栏
.filter-bar {
  background: white;
  padding: 16px;
  display: flex;
  gap: 12px;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

  .search-input {
    flex: 1;

    :deep(.el-input__wrapper) {
      border-radius: 24px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      border: none;

      &:hover,
      &.is-focus {
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
      }
    }

    :deep(.el-input__inner) {
      padding-left: 16px;
    }
  }

  .sort-select {
    width: 120px;

    :deep(.el-select__wrapper) {
      border-radius: 20px;
      border: 1px solid #ddd;

      &:hover,
      &.is-focus {
        border-color: #667eea;
      }
    }
  }
}
```

### 4. 商家卡片样式

```scss
// 商家列表
.merchant-list {
  padding: 16px;

  .merchant-card {
    background: white;
    border-radius: 12px;
    margin-bottom: 16px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    cursor: pointer;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    }

    &:last-child {
      margin-bottom: 0;
    }

    .merchant-header {
      display: flex;
      padding: 16px;

      .merchant-logo {
        width: 60px;
        height: 60px;
        border-radius: 8px;
        object-fit: cover;
        margin-right: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      .merchant-info {
        flex: 1;

        .merchant-name {
          font-size: 18px;
          font-weight: 600;
          color: #333;
          margin-bottom: 4px;
          display: flex;
          align-items: center;

          .status-tag {
            margin-left: 8px;
            font-size: 12px;
            padding: 2px 6px;
            border-radius: 4px;

            &.open {
              background: #e8f5e8;
              color: #52c41a;
            }

            &.closed {
              background: #fff2e8;
              color: #fa8c16;
            }
          }
        }

        .merchant-rating {
          display: flex;
          align-items: center;
          margin-bottom: 4px;

          .rating-stars {
            margin-right: 8px;

            :deep(.el-rate__item) {
              margin-right: 2px;
            }
          }

          .rating-text {
            font-size: 14px;
            color: #666;
          }
        }

        .merchant-stats {
          display: flex;
          align-items: center;
          gap: 16px;
          font-size: 13px;
          color: #999;

          .stat-item {
            display: flex;
            align-items: center;

            .stat-icon {
              margin-right: 4px;
              font-size: 14px;
            }
          }
        }
      }
    }

    .merchant-promotions {
      padding: 0 16px 8px;

      .promotion-tag {
        display: inline-block;
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        margin-right: 8px;
        margin-bottom: 4px;
      }
    }

    .merchant-delivery {
      padding: 12px 16px;
      background: #f8f9fa;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 13px;

      .delivery-info {
        display: flex;
        gap: 16px;
        color: #666;

        .delivery-item {
          display: flex;
          align-items: center;

          .delivery-icon {
            margin-right: 4px;
            color: #667eea;
          }
        }
      }

      .distance {
        color: #667eea;
        font-weight: 500;
      }
    }
  }
}
```

### 5. 分页组件样式

```scss
// 分页组件
.pagination {
  padding: 20px 16px;
  display: flex;
  justify-content: center;
  background: white;
  margin-top: 16px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

  :deep(.el-pagination) {
    .el-pager li {
      border-radius: 8px;
      margin: 0 2px;

      &.is-active {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
      }
    }

    .btn-prev,
    .btn-next {
      border-radius: 8px;

      &:hover {
        color: #667eea;
      }
    }
  }
}
```

### 6. 加载状态样式

```scss
// 加载状态
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px;

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 空状态
.empty-state {
  text-align: center;
  padding: 60px 20px;

  .empty-icon {
    font-size: 64px;
    color: #ddd;
    margin-bottom: 16px;
  }

  .empty-text {
    font-size: 16px;
    color: #999;
    margin-bottom: 8px;
  }

  .empty-desc {
    font-size: 14px;
    color: #ccc;
  }
}
```

### 7. 响应式设计

```scss
// 响应式设计
@media (max-width: 768px) {
  .takeout-merchants {
    .header {
      padding: 16px 12px;

      .location-info {
        .location-text {
          font-size: 14px;

          .location-name {
            max-width: 150px;
          }
        }

        .refresh-btn {
          padding: 6px 12px;
          font-size: 12px;
        }
      }
    }

    .global-categories {
      padding: 12px;

      .category-item {
        padding: 6px 12px;
        font-size: 13px;
      }
    }

    .filter-bar {
      padding: 12px;
      flex-direction: column;
      gap: 8px;

      .search-input {
        width: 100%;
      }

      .sort-select {
        width: 100%;
      }
    }

    .merchant-list {
      padding: 12px;

      .merchant-card {
        .merchant-header {
          padding: 12px;

          .merchant-logo {
            width: 50px;
            height: 50px;
          }

          .merchant-info {
            .merchant-name {
              font-size: 16px;
            }

            .merchant-stats {
              flex-direction: column;
              align-items: flex-start;
              gap: 4px;
            }
          }
        }

        .merchant-delivery {
          padding: 10px 12px;
          flex-direction: column;
          align-items: flex-start;
          gap: 8px;

          .delivery-info {
            flex-wrap: wrap;
            gap: 8px;
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .takeout-merchants {
    .merchant-list {
      .merchant-card {
        .merchant-header {
          .merchant-info {
            .merchant-stats {
              .stat-item {
                font-size: 12px;
              }
            }
          }
        }
      }
    }
  }
}
```

## 最佳实践

### 1. 性能优化

#### 列表虚拟化

对于大量商家数据，建议使用虚拟滚动：

```typescript
// 使用 vue-virtual-scroller
import { VirtualList } from 'vue-virtual-scroller'

// 在模板中使用
<VirtualList
  :items="merchants"
  :item-height="120"
  key-field="id"
  v-slot="{ item }"
>
  <MerchantCard :merchant="item" />
</VirtualList>
```

#### 图片懒加载

```typescript
// 使用 Intersection Observer API
const useImageLazyLoad = () => {
  const imageRef = ref<HTMLImageElement>()

  onMounted(() => {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const img = entry.target as HTMLImageElement
          img.src = img.dataset.src!
          observer.unobserve(img)
        }
      })
    })

    if (imageRef.value) {
      observer.observe(imageRef.value)
    }
  })

  return { imageRef }
}
```

#### 防抖和节流

```typescript
// 搜索防抖
import { debounce } from 'lodash-es'

const debouncedSearch = debounce(() => {
  handleSearch()
}, 300)

// 滚动节流
import { throttle } from 'lodash-es'

const throttledScroll = throttle(() => {
  // 处理滚动事件
}, 100)
```

### 2. 错误处理

#### API 错误处理

```typescript
const handleApiError = (error: any) => {
  console.error('API 错误:', error)

  if (error.response) {
    // 服务器响应错误
    const { status, data } = error.response
    switch (status) {
      case 401:
        ElMessage.error('登录已过期，请重新登录')
        router.push('/login')
        break
      case 403:
        ElMessage.error('没有权限访问')
        break
      case 404:
        ElMessage.error('请求的资源不存在')
        break
      case 500:
        ElMessage.error('服务器内部错误')
        break
      default:
        ElMessage.error(data?.message || '请求失败')
    }
  } else if (error.request) {
    // 网络错误
    ElMessage.error('网络连接失败，请检查网络设置')
  } else {
    // 其他错误
    ElMessage.error('发生未知错误')
  }
}
```

#### 位置获取错误处理

```typescript
const handleLocationError = (error: GeolocationPositionError) => {
  let message = ''
  switch (error.code) {
    case error.PERMISSION_DENIED:
      message = '位置权限被拒绝，请在浏览器设置中允许位置访问'
      break
    case error.POSITION_UNAVAILABLE:
      message = '位置信息不可用，请检查GPS设置'
      break
    case error.TIMEOUT:
      message = '获取位置超时，请重试'
      break
    default:
      message = '获取位置失败'
  }

  ElMessage.warning(message)
  locationError.value = message
}
```

### 3. 用户体验优化

#### 骨架屏加载

```vue
<template>
  <div class="merchant-skeleton" v-if="loading">
    <div class="skeleton-header">
      <div class="skeleton-avatar"></div>
      <div class="skeleton-info">
        <div class="skeleton-line skeleton-title"></div>
        <div class="skeleton-line skeleton-subtitle"></div>
      </div>
    </div>
    <div class="skeleton-content">
      <div class="skeleton-line"></div>
      <div class="skeleton-line"></div>
    </div>
  </div>
</template>

<style scoped>
.merchant-skeleton {
  padding: 16px;
  background: white;
  border-radius: 12px;
  margin-bottom: 16px;
}

.skeleton-line {
  height: 16px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4px;
  margin-bottom: 8px;
}

.skeleton-title {
  width: 60%;
  height: 20px;
}

.skeleton-subtitle {
  width: 40%;
  height: 14px;
}

.skeleton-avatar {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
</style>
```

#### 下拉刷新

```typescript
// 使用 better-scroll 实现下拉刷新
import BScroll from '@better-scroll/core'
import PullDown from '@better-scroll/pull-down'

BScroll.use(PullDown)

const initPullRefresh = () => {
  const scroll = new BScroll('.merchant-list', {
    pullDownRefresh: {
      threshold: 90,
      stop: 40,
    },
  })

  scroll.on('pullingDown', async () => {
    await loadMerchants()
    scroll.finishPullDown()
  })
}
```

### 4. 安全性考虑

#### 输入验证

```typescript
// 搜索关键词验证
const validateSearchKeyword = (keyword: string): boolean => {
  // 防止XSS攻击
  const sanitized = keyword.replace(/<script[^>]*>.*?<\/script>/gi, '')
  if (sanitized !== keyword) {
    ElMessage.warning('搜索内容包含非法字符')
    return false
  }

  // 长度限制
  if (keyword.length > 50) {
    ElMessage.warning('搜索关键词过长')
    return false
  }

  return true
}
```

#### 数据脱敏

```typescript
// 商家电话号码脱敏
const maskPhoneNumber = (phone: string): string => {
  if (!phone || phone.length < 7) return phone
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
}
```

### 5. 可访问性优化

#### 语义化标签

```vue
<template>
  <main class="takeout-merchants" role="main">
    <header class="header" role="banner">
      <nav class="location-info" aria-label="位置信息">
        <!-- 位置内容 -->
      </nav>
    </header>

    <nav class="global-categories" role="navigation" aria-label="商家分类">
      <!-- 分类内容 -->
    </nav>

    <section class="merchant-list" role="region" aria-label="商家列表">
      <article
        v-for="merchant in merchants"
        :key="merchant.id"
        class="merchant-card"
        role="article"
        :aria-label="`商家：${merchant.name}`"
      >
        <!-- 商家卡片内容 -->
      </article>
    </section>
  </main>
</template>
```

#### 键盘导航支持

```typescript
// 添加键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
  switch (event.key) {
    case 'Enter':
    case ' ':
      // 处理回车和空格键
      event.preventDefault()
      break
    case 'ArrowUp':
    case 'ArrowDown':
      // 处理上下箭头键
      event.preventDefault()
      break
  }
}
```

## 常见问题

### 1. 位置获取失败

**问题**: 用户位置获取失败或不准确

**解决方案**:

```typescript
// 提供手动选择位置的备选方案
const showLocationPicker = () => {
  // 显示地图选择器或地址列表
  ElMessageBox.prompt('请输入您的位置', '位置设置', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputPlaceholder: '请输入详细地址',
  }).then(({ value }) => {
    // 通过地址解析获取经纬度
    geocodeAddress(value)
  })
}

// 地址解析
const geocodeAddress = async (address: string) => {
  try {
    // 调用地图API进行地址解析
    const response = await mapService.geocode(address)
    if (response.location) {
      userStore.userLocation = response.location
      await loadMerchants()
    }
  } catch (error) {
    ElMessage.error('地址解析失败')
  }
}
```

### 2. 商家列表加载慢

**问题**: 商家列表加载速度慢

**解决方案**:

```typescript
// 实现分页加载和缓存
const merchantCache = new Map<string, ExtendedMerchant[]>()

const loadMerchantsWithCache = async (params: any) => {
  const cacheKey = JSON.stringify(params)

  // 检查缓存
  if (merchantCache.has(cacheKey)) {
    merchants.value = merchantCache.get(cacheKey)!
    return
  }

  // 加载数据
  const response = await merchantStore.loadMerchants(params)

  // 缓存结果
  merchantCache.set(cacheKey, response.data)
  merchants.value = response.data
}

// 预加载下一页数据
const preloadNextPage = () => {
  const nextPageParams = {
    ...currentParams,
    page: currentPage.value + 1,
  }

  // 在后台预加载
  setTimeout(() => {
    merchantStore.loadMerchants(nextPageParams)
  }, 1000)
}
```

### 3. 图片加载失败

**问题**: 商家logo或图片加载失败

**解决方案**:

```vue
<template>
  <img
    :src="merchant.logo"
    :alt="merchant.name"
    class="merchant-logo"
    @error="handleImageError"
    @load="handleImageLoad"
  />
</template>

<script setup>
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  // 使用默认图片
  img.src = '/images/default-merchant-logo.png'
}

const handleImageLoad = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.classList.add('loaded')
}
</script>

<style scoped>
.merchant-logo {
  opacity: 0;
  transition: opacity 0.3s ease;

  &.loaded {
    opacity: 1;
  }
}
</style>
```

### 4. 内存泄漏

**问题**: 长时间使用后页面卡顿

**解决方案**:

```typescript
// 组件卸载时清理资源
onUnmounted(() => {
  // 清理定时器
  if (searchTimer) {
    clearTimeout(searchTimer)
  }

  // 清理事件监听器
  window.removeEventListener('scroll', throttledScroll)

  // 清理缓存
  merchantCache.clear()

  // 重置状态
  merchants.value = []
  globalCategories.value = []
})

// 限制缓存大小
const MAX_CACHE_SIZE = 50
const limitCacheSize = () => {
  if (merchantCache.size > MAX_CACHE_SIZE) {
    const firstKey = merchantCache.keys().next().value
    merchantCache.delete(firstKey)
  }
}
```

### 5. 网络异常处理

**问题**: 网络不稳定导致请求失败

**解决方案**:

```typescript
// 实现请求重试机制
const retryRequest = async (fn: Function, maxRetries = 3) => {
  let retries = 0

  while (retries < maxRetries) {
    try {
      return await fn()
    } catch (error) {
      retries++
      if (retries === maxRetries) {
        throw error
      }

      // 指数退避
      const delay = Math.pow(2, retries) * 1000
      await new Promise((resolve) => setTimeout(resolve, delay))
    }
  }
}

// 使用重试机制
const loadMerchantsWithRetry = () => {
  return retryRequest(() => merchantStore.loadMerchants(params))
}
```

---

## 总结

本指南详细介绍了 `TakeoutMerchants.vue` 外卖商家列表页面的完整实现，包括：

1. **功能完整性**: 涵盖了商家浏览、分类筛选、搜索排序、地理位置等核心功能
2. **技术规范**: 提供了完整的 TypeScript 类型定义和 API 接口文档
3. **状态管理**: 详细说明了 Pinia 状态管理的使用方式
4. **样式设计**: 包含了响应式设计和现代化的 UI 样式
5. **最佳实践**: 涵盖了性能优化、错误处理、用户体验等方面
6. **问题解决**: 提供了常见问题的解决方案

通过遵循本指南，开发者可以快速理解和维护外卖商家列表页面，同时确保代码质量和用户体验。
