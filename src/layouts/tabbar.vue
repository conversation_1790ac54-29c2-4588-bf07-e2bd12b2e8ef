<template>
  <wd-config-provider :themeVars="themeVars">
    <slot />
    <!-- 注意下面，多了一个自定义tabbar -->
    <fg-tabbar />
    <wd-toast />
    <wd-message-box />
    <wd-notify />
  </wd-config-provider>
</template>

<script lang="ts" setup>
import { onMounted } from 'vue'
import type { ConfigProviderThemeVars } from 'wot-design-uni'
import { useSystemStore } from '@/store/system'

// 初始化系统配置store
const systemStore = useSystemStore()

const themeVars: ConfigProviderThemeVars = {
  // colorTheme: 'red',
  // buttonPrimaryBgColor: '#07c160',
  // buttonPrimaryColor: '#07c160',
}

// 组件挂载时检查并初始化系统配置
onMounted(async () => {
  try {
    if (!systemStore.systemInfo || !systemStore.contactInfo) {
      console.log('Layout：初始化系统配置数据')
      await systemStore.initSystemData()
    }
  } catch (error) {
    console.error('Layout：系统配置初始化失败', error)
  }
})
</script>
