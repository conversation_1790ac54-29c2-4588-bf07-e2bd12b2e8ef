<route lang="json5">
{
  style: {
    navigationBarTitleText: '编辑地址',
    navigationStyle: 'custom',
  },
}
</route>

<!--
 * @Description: 地址编辑页面（添加/编辑收货地址）
 * @Author: O_Mall Team
 * @Date: 2025-01-XX
 * @Update: 集成社区地址选择器组件
-->
<template>
  <view class="address-edit-page">
    <!-- 自定义导航栏 -->
    <wd-navbar
      :title="isEdit ? '编辑地址' : '添加地址'"
      left-text="返回"
      left-arrow
      @click-left="handleBack"
    />

    <!-- 表单内容 -->
    <wd-form ref="formRef" :model="formData" :rules="formRules">
      <!-- 联系人信息 -->
      <view class="form-section">
        <view class="section-title">联系人信息</view>

        <wd-cell-group :inset="true" :border="true">
          <wd-form-item prop="receiverName" label="收货人">
            <wd-input v-model="formData.receiverName" clearable placeholder="请输入收货人姓名" />
          </wd-form-item>

          <wd-form-item prop="receiverPhone" label="手机号">
            <wd-input
              v-model="formData.receiverPhone"
              clearable
              type="tel"
              placeholder="请输入收货人手机号"
            />
          </wd-form-item>
        </wd-cell-group>
      </view>

      <!-- 地址信息 -->
      <view class="form-section">
        <view class="section-title">地址信息</view>

        <wd-cell-group>
          <!-- 省市区选择 -->
          <wd-form-item prop="region" label="所在地区">
            <wd-col-picker
              v-model="formData.region"
              :columns="regionColumns"
              placeholder="请选择所在地区"
              @change="handleRegionChange"
            />
          </wd-form-item>

          <!-- 社区地址选择 -->
          <wd-form-item prop="communityAddress" label="社区地址">
            <view class="community-selector-wrapper">
              <CommunityAddressSelector
                mode="inline"
                :initial-value="formData.communityAddress?.selectedKeys || []"
                @address-selected="handleCommunityAddressSelected"
                placeholder="请选择社区地址"
              />
            </view>
          </wd-form-item>

          <!-- 详细地址 -->
          <wd-form-item prop="address" label="详细地址">
            <wd-input
              v-model="formData.address"
              textarea
              clearable
              show-word-count
              autosize
              placeholder="请输入详细地址信息（如楼栋门牌号等）"
            />
          </wd-form-item>
        </wd-cell-group>
      </view>

      <!-- 其他设置 -->
      <view class="form-section">
        <view class="section-title">其他设置</view>

        <wd-cell-group>
          <wd-form-item label="设为默认地址">
            <wd-switch v-model="formData.isDefault" size="large" />
          </wd-form-item>
        </wd-cell-group>
      </view>
    </wd-form>

    <!-- 保存按钮 -->
    <view class="save-btn">
      <wd-button type="primary" size="large" block :loading="saveLoading" @click="handleSave">
        {{ isEdit ? '保存修改' : '保存地址' }}
      </wd-button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAddressStore } from '@/store/address'
import { useSystemStore } from '@/store/system'
import type { IAddress, ICreateAddressParams, IUpdateAddressParams } from '@/api/address.typings'
import CommunityAddressSelector from '@/components/CommunityAddressSelector/index.vue'
import type { SelectedAddressInfo } from '@/components/CommunityAddressSelector/types'

const router = useRouter()
const route = useRoute()
const addressStore = useAddressStore()
const systemStore = useSystemStore()

// 响应式数据
const saveLoading = ref(false)
const isEdit = ref(false)
const addressId = ref<number | null>(null)

// 表单引用
const formRef = ref(null)

// 表单数据
// 自定义表单类型，包含社区地址信息
// 不再扩展ICreateAddressParams，而是定义独立的表单类型
// 在提交时再转换成API需要的格式
interface AddressForm {
  receiverName: string
  receiverPhone: string
  province: string
  city: string
  district: string
  address: string
  isDefault: boolean
  region?: any[]
  communityAddress?: {
    fullPath: string
    longitude: number | null
    latitude: number | null
    communityId: number | string | null
    buildingId: number | string | null
    unitId: number | string | null
    selectedKeys?: (string | number)[]
  }
}

const formData = reactive<AddressForm>({
  receiverName: '',
  receiverPhone: '',
  province: '',
  city: '',
  district: '',
  address: '',
  isDefault: false,
  region: [],
  communityAddress: undefined,
})

// 表单验证规则
const formRules = {
  receiverName: [{ required: true, message: '请输入收货人姓名' }],
  receiverPhone: [
    { required: true, message: '请输入收货人手机号' },
    { required: true, pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' },
  ],
  region: [{ required: true, message: '请选择所在地区' }],
  address: [{ required: true, message: '请输入详细地址' }],
}

// 地区数据（这里使用模拟数据，实际项目中应该从API获取）
const regionColumns = ref([
  [
    { label: '北京市', value: '110000' },
    { label: '上海市', value: '310000' },
    { label: '广东省', value: '440000' },
    { label: '浙江省', value: '330000' },
    { label: '江苏省', value: '320000' },
    { label: '山东省', value: '370000' },
  ],
  [
    { label: '北京市', value: '110100' },
    { label: '上海市', value: '310100' },
    { label: '广州市', value: '440100' },
    { label: '深圳市', value: '440300' },
    { label: '杭州市', value: '330100' },
    { label: '南京市', value: '320100' },
  ],
  [
    { label: '东城区', value: '110101' },
    { label: '西城区', value: '110102' },
    { label: '朝阳区', value: '110105' },
    { label: '丰台区', value: '110106' },
    { label: '石景山区', value: '110107' },
    { label: '海淀区', value: '110108' },
  ],
])

/**
 * 处理社区地址选择
 * @param addressInfo 选中的地址信息对象，包含完整路径、经纬度等
 */
const handleCommunityAddressSelected = (addressInfo: SelectedAddressInfo) => {
  // 如果是空值，清空社区地址数据
  if (!addressInfo.fullPath) {
    formData.communityAddress = undefined
    // 同时清空详细地址字段
    formData.address = ''
    uni.showToast({
      title: '社区地址已清空',
      icon: 'none',
      duration: 1500,
    })
    return
  }

  // 保存选中的社区地址信息，确保类型兼容
  formData.communityAddress = {
    fullPath: addressInfo.fullPath,
    longitude: addressInfo.longitude,
    latitude: addressInfo.latitude,
    communityId: addressInfo.communityId,
    buildingId: addressInfo.buildingId,
    unitId: addressInfo.unitId,
    // 保存选中的keys用于回显
    selectedKeys: [addressInfo.communityId, addressInfo.buildingId, addressInfo.unitId].filter(
      Boolean,
    ) as (string | number)[],
  }

  // 同步更新详细地址字段为社区地址的完整路径
  formData.address = addressInfo.fullPath

  // 在控制台输出选中的社区地址信息，方便调试
  console.log('选中的社区地址:', formData.communityAddress)
  console.log('同步更新详细地址为:', formData.address)

  // 显示选择成功提示
  uni.showToast({
    title: '社区地址已选择: ' + addressInfo.fullPath,
    icon: 'success',
    duration: 1500,
  })
}

// 页面加载时的处理
onMounted(async () => {
  // 初始化系统信息，确保region数据可用
  try {
    await systemStore.getSystemInfo()
    console.log('系统信息已加载:', systemStore.systemInfo)
  } catch (error) {
    console.error('获取系统信息失败:', error)
  }

  const { id } = route.query

  if (id) {
    isEdit.value = true
    addressId.value = Number(id)

    // 优先从store中获取当前地址数据
    const currentAddress = addressStore.currentAddress
    if (currentAddress && currentAddress.id === Number(id)) {
      // 直接使用store中的地址数据，无需loading
      loadAddressFromData(currentAddress)
    } else {
      // 如果store中没有数据，则从API获取，需要显示loading
      saveLoading.value = true
      try {
        await loadAddressDetail()
      } finally {
        saveLoading.value = false
      }
    }
  }
})

/**
 * 从传递的地址数据加载表单
 */
const loadAddressFromData = (address: any) => {
  Object.assign(formData, {
    receiverName: address.receiver_name || address.receiverName || '',
    receiverPhone: address.receiver_mobile || address.receiverPhone || '',
    province: address.province,
    city: address.city,
    district: address.district,
    address: address.detailed_address || address.address || '',
    isDefault: address.is_default ?? address.isDefault ?? false,
  })

  // 设置地区选择器的值
  formData.region = [
    { label: address.province, value: address.province },
    { label: address.city, value: address.city },
    { label: address.district, value: address.district },
  ]

  // 如果有社区地址数据，也进行恢复
  if (address.detailed_address) {
    // 从地址数据中提取社区地址相关的ID
    const communityId = address.communityId || null
    const buildingId = address.buildingId || null
    const unitId = address.unitId || null

    console.log('loadAddressFromData: 原始地址数据:', {
      detailed_address: address.detailed_address,
      communityId,
      buildingId,
      unitId,
      selectedKeys: address.selectedKeys,
    })

    // 优先使用store中计算的selectedKeys，如果没有则手动构造
    let selectedKeys: (string | number)[] = []
    if (
      address.selectedKeys &&
      Array.isArray(address.selectedKeys) &&
      address.selectedKeys.length > 0
    ) {
      selectedKeys = address.selectedKeys
      console.log('loadAddressFromData: 使用store中的selectedKeys:', selectedKeys)
    } else {
      // 兼容旧数据，手动构造selectedKeys
      selectedKeys = [communityId, buildingId, unitId].filter(Boolean) as (string | number)[]
      console.log('loadAddressFromData: 手动构造selectedKeys:', selectedKeys)
    }

    formData.communityAddress = {
      fullPath: address.detailed_address,
      communityId: communityId,
      buildingId: buildingId,
      unitId: unitId,
      longitude: address.location_longitude || address.locationLongitude || null,
      latitude: address.location_latitude || address.locationLatitude || null,
      selectedKeys: selectedKeys,
    }

    console.log('loadAddressFromData: 设置的formData.communityAddress:', formData.communityAddress)
  } else {
    console.log('loadAddressFromData: 没有detailed_address，清空社区地址')
    formData.communityAddress = undefined
  }
}

/**
 * 加载地址详情（从API获取）
 */
const loadAddressDetail = async () => {
  if (!addressId.value) return

  try {
    await addressStore.fetchAddressDetail(addressId.value)
    const address = addressStore.currentAddress
    if (address) {
      loadAddressFromData(address)

      // 地区信息已在 loadAddressFromData 中设置
    }
  } catch (error) {
    console.error('获取地址详情失败:', error)
    uni.showToast({
      title: '获取地址详情失败',
      icon: 'none',
    })
    throw error // 重新抛出错误，让调用方处理loading状态
  }
}

/**
 * 返回上一页
 */
const handleBack = () => {
  uni.navigateBack()
}

/**
 * 地区选择变化
 */
const handleRegionChange = (value: any) => {
  if (value && value.length >= 3) {
    const [province, city, district] = value
    formData.province = province.label
    formData.city = city.label
    formData.district = district.label
  }
}

/**
 * 保存地址
 */
const handleSave = async () => {
  if (saveLoading.value) return

  try {
    // 验证表单
    await formRef.value.validate()

    saveLoading.value = true

    // 获取系统配置中的地区信息
    let systemRegion = { province: '', city: '', district: '' }
    if (systemStore.systemInfo?.region) {
      try {
        systemRegion = JSON.parse(systemStore.systemInfo.region)
      } catch (error) {
        console.error('解析系统地区信息失败:', error)
        // 如果解析失败，使用表单中的地区信息作为备选
        systemRegion = {
          province: formData.province,
          city: formData.city,
          district: formData.district,
        }
      }
    } else {
      // 如果没有系统地区信息，使用表单中的地区信息
      systemRegion = {
        province: formData.province,
        city: formData.city,
        district: formData.district,
      }
    }

    if (isEdit.value && addressId.value) {
      // 编辑地址
      // 提取社区地址信息，避免类型问题
      const { communityAddress, region, ...baseFormData } = formData

      const updateData: IUpdateAddressParams = {
        receiver_name: formData.receiverName,
        receiver_mobile: formData.receiverPhone,
        province: systemRegion.province,
        city: systemRegion.city,
        district: systemRegion.district,
        detailed_address: formData.communityAddress?.fullPath || formData.address,
        is_default: formData.isDefault,
        location_longitude: formData.communityAddress?.longitude || null,
        location_latitude: formData.communityAddress?.latitude || null,
        address_tag: '', // 地址标签
        postal_code: '', // 邮政编码
        // 社区相关字段
        community_id: formData.communityAddress?.communityId || null,
        building_id: formData.communityAddress?.buildingId || null,
        unit_id: formData.communityAddress?.unitId || 0,
        // phone字段（与receiver_mobile保持一致）
        phone: formData.receiverPhone,
        // 兼容旧版本字段
        //receiverName: formData.receiverName,
        //receiverPhone: formData.receiverPhone,
        address: formData.address,
        //isDefault: formData.isDefault,
        //communityAddress: formData.communityAddress?.fullPath,
        //communityId: formData.communityAddress?.communityId,
        //buildingId: formData.communityAddress?.buildingId,
        //unitId: formData.communityAddress?.unitId,
        //locationLongitude: formData.communityAddress?.longitude || null,
        //locationLatitude: formData.communityAddress?.latitude || null,
      }
      await addressStore.updateAddressById(addressId.value, updateData)
    } else {
      // 添加地址
      // 提取社区地址信息，避免类型问题
      const { communityAddress, region, ...baseFormData } = formData

      const createData: ICreateAddressParams = {
        receiver_name: formData.receiverName,
        receiver_mobile: formData.receiverPhone,
        province: systemRegion.province,
        city: systemRegion.city,
        district: systemRegion.district,
        detailed_address: formData.communityAddress?.fullPath || formData.address,
        is_default: formData.isDefault,
        location_longitude: formData.communityAddress?.longitude || null,
        location_latitude: formData.communityAddress?.latitude || null,
        address_tag: '', // 地址标签
        postal_code: '', // 邮政编码
        // 社区相关字段
        community_id: formData.communityAddress?.communityId || null,
        building_id: formData.communityAddress?.buildingId || null,
        unit_id: formData.communityAddress?.unitId || 0,
        // phone字段（与receiver_mobile保持一致）
        phone: formData.receiverPhone,
        // 兼容旧版本字段
        receiverName: formData.receiverName,
        receiverPhone: formData.receiverPhone,
        address: formData.address,
        isDefault: formData.isDefault,
        communityAddress: formData.communityAddress?.fullPath,
        communityId: formData.communityAddress?.communityId,
        buildingId: formData.communityAddress?.buildingId,
        unitId: formData.communityAddress?.unitId,
        locationLongitude: formData.communityAddress?.longitude || null,
        locationLatitude: formData.communityAddress?.latitude || null,
      }
      await addressStore.createNewAddress(createData)
    }

    uni.showToast({
      title: isEdit.value ? '修改成功' : '添加成功',
      icon: 'success',
    })

    // 刷新地址列表以同步最新数据
    try {
      await addressStore.fetchAddressList()
      console.log('地址列表已刷新')
    } catch (error) {
      console.error('刷新地址列表失败:', error)
    }

    // 延迟返回上一页
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  } catch (error) {
    if (error?.message) {
      // 表单验证错误
      return
    }
    console.error('保存地址失败:', error)
    uni.showToast({
      title: isEdit.value ? '修改失败' : '添加失败',
      icon: 'none',
    })
  } finally {
    saveLoading.value = false
  }
}
</script>

<style lang="scss" scoped>
.address-edit-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.form-container {
  padding: 20rpx;
}

.form-section {
  margin-bottom: 32rpx;

  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 16rpx;
    padding: 0 16rpx;
  }
}

.save-btn {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx;
  background-color: #fff;
  border-top: 1rpx solid #eee;
}

// 错误状态样式
:deep(.error) {
  .wd-cell__title,
  .wd-cell__value {
    color: #fa4350 !important;
  }
}
</style>
