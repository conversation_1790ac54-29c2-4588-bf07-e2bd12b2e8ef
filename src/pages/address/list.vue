<route lang="json5">
{
  style: {
    navigationBarTitleText: '收货地址',
    navigationStyle: 'custom',
  },
}
</route>

<!--
 * @Description: 地址管理页面
 * @Author: O_Mall Team
 * @Date: 2025-01-XX
 * @Update: 增加社区地址信息展示
-->
<template>
  <view class="address-page">
    <!-- 自定义导航栏 -->
    <wd-navbar title="收货地址" left-text="返回" left-arrow @click-left="handleBack" />

    <!-- 地址列表 -->
    <view class="address-list" v-if="addressList.length > 0">
      <view
        class="address-item"
        v-for="address in addressList"
        :key="address.id"
        @click="handleSelectAddress(address)"
      >
        <!-- 默认标签 -->
        <view class="address-header">
          <view class="address-info">
            <text class="name">{{ address.receiver_name }}</text>
            <text class="phone">{{ address.receiver_mobile }}</text>
          </view>
          <wd-tag v-if="address.is_default" type="primary" size="small">默认</wd-tag>
        </view>

        <!-- 地址详情 -->
        <view class="address-detail">
          <text class="full-address">
            {{ address.province }}{{ address.city }}{{ address.district }}
            <template v-if="address.detailed_address">
              <text class="community-address">{{ address.detailed_address }}</text>
            </template>
          </text>
        </view>

        <!-- 操作按钮 -->
        <view class="address-actions">
          <wd-button type="text" size="small" @click.stop="handleEditAddress(address)">
            编辑
          </wd-button>
          <wd-button type="text" size="small" @click.stop="handleDeleteAddress(address)">
            删除
          </wd-button>
          <wd-button
            v-if="!address.is_default"
            type="text"
            size="small"
            @click.stop="handleSetDefault(address)"
          >
            设为默认
          </wd-button>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" v-else>
      <wd-img src="/static/images/empty-address.png" width="120" height="120" mode="aspectFit" />
      <text class="empty-text">暂无收货地址</text>
      <text class="empty-tip">添加收货地址，享受便捷购物体验</text>
    </view>

    <!-- 添加地址按钮 -->
    <view class="add-address-btn">
      <wd-button type="primary" size="large" block @click="handleAddAddress">添加新地址</wd-button>
    </view>

    <!-- 删除确认弹窗 -->
    <wd-message-box />

    <!-- 加载状态 -->
    <wd-loading v-if="loading" />
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAddressStore } from '@/store/address'
import { useMessage } from 'wot-design-uni'
import type { IAddress } from '@/api/address.typings'

const router = useRouter()
const addressStore = useAddressStore()
const message = useMessage()

// 响应式数据
const loading = ref(false)
// 直接使用 store 中的响应式数据，而不是本地副本
const addressList = computed(() => addressStore.addressList)

// 页面加载时获取地址列表
onMounted(() => {
  loadAddressList()
})

/**
 * 加载地址列表
 */
const loadAddressList = async () => {
  try {
    console.log('开始加载地址列表，当前 loading 状态:', loading.value)
    loading.value = true
    console.log('设置 loading=true 后的状态:', loading.value)

    // 等待store操作完成
    await addressStore.fetchAddressList()
    console.log('地址列表获取成功，当前loading状态:', loading.value)
    console.log('store中的地址列表:', addressStore.addressList)

    // 直接关闭加载状态
    loading.value = false
    console.log('设置 loading=false 后的状态:', loading.value)
  } catch (error) {
    console.error('获取地址列表失败:', error)
    uni.showToast({
      title: '获取地址列表失败',
      icon: 'none',
    })
    loading.value = false
    console.log('错误处理后，当前loading状态:', loading.value)
  }
}

/**
 * 返回上一页
 */
const handleBack = () => {
  uni.navigateBack()
}

/**
 * 选择地址（用于订单页面选择地址）
 */
const handleSelectAddress = (address: IAddress) => {
  // 如果是从订单页面跳转过来的，则选择地址后返回
  // 使用路由对象获取查询参数
  const route = router.currentRoute.value

  if (route.query.from === 'order') {
    // 通过事件总线或者其他方式传递选中的地址
    uni.$emit('selectAddress', address)
    uni.navigateBack()
  }
}

/**
 * 添加新地址
 */
const handleAddAddress = () => {
  router.push('/pages/address/edit')
}

/**
 * 编辑地址
 * @param address 地址对象
 */
const handleEditAddress = (address: IAddress) => {
  // 将地址数据存储到store中
  addressStore.setCurrentAddress(address)
  uni.navigateTo({
    url: `/pages/address/edit?id=${address.id}`,
  })
}

/**
 * 删除地址
 */
const handleDeleteAddress = (address: IAddress) => {
  message
    .confirm({
      title: '删除地址',
      msg: '确定要删除这个收货地址吗？',
    })
    .then(async () => {
      // 用户点击确认
      await confirmDelete(address.id)
    })
    .catch(() => {
      // 用户点击取消，不做任何操作
      console.log('用户取消删除')
    })
}

/**
 * 确认删除地址
 */
const confirmDelete = async (addressId: number) => {
  try {
    loading.value = true
    await addressStore.deleteAddressById(addressId)
    uni.showToast({
      title: '删除成功',
      icon: 'success',
    })
    // store 中的数据已自动更新，无需手动同步
  } catch (error) {
    console.error('删除地址失败:', error)
    uni.showToast({
      title: '删除失败',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}

/**
 * 设为默认地址
 */
const handleSetDefault = async (address: IAddress) => {
  try {
    loading.value = true
    await addressStore.setDefaultAddressById(address.id)
    uni.showToast({
      title: '设置成功',
      icon: 'success',
    })
    // store 中的数据已自动更新，无需手动同步
  } catch (error) {
    console.error('设置默认地址失败:', error)
    uni.showToast({
      title: '设置失败',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}
</script>

<style lang="scss" scoped>
.address-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.address-list {
  padding: 20rpx;
}

.address-item {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

  .address-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16rpx;

    .address-info {
      display: flex;
      align-items: center;
      gap: 20rpx;

      .name {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
      }

      .phone {
        font-size: 28rpx;
        color: #666;
      }
    }
  }

  .address-detail {
    margin-bottom: 24rpx;

    .full-address {
      font-size: 28rpx;
      color: #666;
      line-height: 1.5;

      .community-address {
        color: #0066cc;
        margin: 0 4rpx;
      }
    }
  }

  .address-actions {
    display: flex;
    justify-content: flex-end;
    gap: 20rpx;
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;

  .empty-text {
    font-size: 32rpx;
    color: #999;
    margin: 24rpx 0 12rpx;
  }

  .empty-tip {
    font-size: 28rpx;
    color: #ccc;
  }
}

.add-address-btn {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx;
  background-color: #fff;
  border-top: 1rpx solid #eee;
}
</style>
