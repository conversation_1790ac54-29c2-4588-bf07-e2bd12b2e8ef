<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '登录',
  },
}
</route>

<template>
  <view class="login-container" :style="{ paddingTop: safeAreaInsets?.top + 'px' }">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-left" @click="goBack">
        <wd-icon name="arrow-left" color="#333" size="20" />
      </view>
      <view class="navbar-title">登录</view>
      <view class="navbar-right"></view>
    </view>

    <!-- 登录表单 -->
    <view class="login-form">
      <!-- Logo -->
      <view class="logo-section">
        <image src="/static/images/logo.png" class="logo" mode="aspectFit" />
        <text class="app-name">O_Mall</text>
        <text class="welcome-text">欢迎回来</text>
      </view>

      <!-- 登录方式切换 -->
      <view class="login-tabs">
        <view
          class="tab-item"
          :class="{ active: loginType === 'password' }"
          @click="switchLoginType('password')"
        >
          密码登录
        </view>
        <view
          class="tab-item"
          :class="{ active: loginType === 'sms' }"
          @click="switchLoginType('sms')"
        >
          短信登录
        </view>
      </view>

      <!-- 密码登录 -->
      <view v-if="loginType === 'password'" class="form-content">
        <wd-input
          v-model="passwordForm.username"
          label="账号"
          placeholder="请输入手机号/用户名"
          clearable
          :error="errors.username"
          @blur="validateUsername"
        >
          <template #prefix>
            <wd-icon name="user" color="#999" size="18" />
          </template>
        </wd-input>

        <wd-input
          v-model="passwordForm.password"
          label="密码"
          type="password"
          placeholder="请输入密码"
          clearable
          :error="errors.password"
          @blur="validatePassword"
        >
          <template #prefix>
            <wd-icon name="lock" color="#999" size="18" />
          </template>
        </wd-input>

        <!-- 验证码（如果需要） -->
        <view v-if="showCaptcha" class="captcha-section">
          <wd-input
            v-model="passwordForm.code"
            label="验证码"
            placeholder="请输入验证码"
            clearable
            :error="errors.code"
          >
            <template #prefix>
              <wd-icon name="shield" color="#999" size="18" />
            </template>
            <template #suffix>
              <image :src="captchaImage" class="captcha-image" @click="refreshCaptcha" />
            </template>
          </wd-input>
        </view>

        <view class="form-actions">
          <view class="remember-section">
            <wd-checkbox v-model="rememberMe">记住密码</wd-checkbox>
          </view>
          <view class="forgot-password" @click="goToForgotPassword">忘记密码？</view>
        </view>

        <wd-button type="primary" block :loading="isLoading" @click="handlePasswordLogin">
          登录
        </wd-button>
      </view>

      <!-- 短信登录 -->
      <view v-else class="form-content">
        <wd-input
          v-model="smsForm.phone"
          label="手机号"
          placeholder="请输入手机号"
          clearable
          :error="errors.phone"
          @blur="validatePhone"
        >
          <template #prefix>
            <wd-icon name="phone" color="#999" size="18" />
          </template>
        </wd-input>

        <view class="sms-input-section">
          <wd-input
            v-model="smsForm.code"
            label="验证码"
            placeholder="请输入短信验证码"
            clearable
            :error="errors.smsCode"
          >
            <template #prefix>
              <wd-icon name="message" color="#999" size="18" />
            </template>
            <template #suffix>
              <wd-button
                type="default"
                size="small"
                :disabled="!canSendSms || smsCountdown > 0"
                :loading="isSendingSms"
                @click="sendSmsCode"
              >
                {{ smsCountdown > 0 ? `${smsCountdown}s` : '获取验证码' }}
              </wd-button>
            </template>
          </wd-input>
        </view>

        <wd-button type="primary" block :loading="isLoading" @click="handleSmsLogin">
          登录
        </wd-button>
      </view>

      <!-- 第三方登录 -->
      <view class="third-party-login">
        <view class="divider">
          <text>其他登录方式</text>
        </view>

        <view class="third-party-buttons">
          <!-- #ifdef MP-WEIXIN -->
          <view class="third-party-item" @click="handleWxLogin">
            <wd-icon name="wechat" color="#07c160" size="24" />
            <text>微信</text>
          </view>
          <!-- #endif -->

          <!-- #ifdef APP-PLUS -->
          <view class="third-party-item" @click="handleQQLogin">
            <wd-icon name="qq" color="#12b7f5" size="24" />
            <text>QQ</text>
          </view>
          <!-- #endif -->
        </view>
      </view>

      <!-- 注册链接 -->
      <view class="register-section">
        <text>还没有账号？</text>
        <text class="register-link" @click="goToRegister">立即注册</text>
      </view>
    </view>

    <!-- 用户协议 -->
    <view class="agreement-section">
      <wd-checkbox v-model="agreeTerms" :error="errors.agreeTerms">
        我已阅读并同意
        <text class="agreement-link" @click="showUserAgreement">《用户协议》</text>
        和
        <text class="agreement-link" @click="showPrivacyPolicy">《隐私政策》</text>
      </wd-checkbox>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { useUserStore } from '@/store/user'
import * as userApi from '@/api/user'

const { safeAreaInsets } = uni.getSystemInfoSync()
const userStore = useUserStore()

// 登录类型
const loginType = ref<'password' | 'sms'>('password')

// 表单数据
const passwordForm = ref({
  username: '',
  password: '',
  code: '',
  uuid: '',
})

const smsForm = ref({
  phone: '',
  code: '',
})

// 表单验证错误
const errors = ref<Record<string, string>>({})

// 状态
const isLoading = ref(false)
const isSendingSms = ref(false)
const rememberMe = ref(false)
const agreeTerms = ref(false)
const showCaptcha = ref(false)
const captchaImage = ref('')
const smsCountdown = ref(0)
let smsTimer: NodeJS.Timeout | null = null

// 计算属性
const canSendSms = computed(() => {
  return /^1[3-9]\d{9}$/.test(smsForm.value.phone)
})

/**
 * 返回上一页
 */
const goBack = () => {
  uni.navigateBack()
}

/**
 * 切换登录方式
 */
const switchLoginType = (type: 'password' | 'sms') => {
  loginType.value = type
  clearErrors()
}

/**
 * 验证用户名
 */
const validateUsername = () => {
  const { username } = passwordForm.value
  if (!username) {
    errors.value.username = '请输入账号'
    return false
  }
  if (username.length < 3) {
    errors.value.username = '账号长度不能少于3位'
    return false
  }
  delete errors.value.username
  return true
}

/**
 * 验证密码
 */
const validatePassword = () => {
  const { password } = passwordForm.value
  if (!password) {
    errors.value.password = '请输入密码'
    return false
  }
  if (password.length < 6) {
    errors.value.password = '密码长度不能少于6位'
    return false
  }
  delete errors.value.password
  return true
}

/**
 * 验证手机号
 */
const validatePhone = () => {
  const { phone } = smsForm.value
  if (!phone) {
    errors.value.phone = '请输入手机号'
    return false
  }
  if (!/^1[3-9]\d{9}$/.test(phone)) {
    errors.value.phone = '请输入正确的手机号'
    return false
  }
  delete errors.value.phone
  return true
}

/**
 * 清除错误信息
 */
const clearErrors = () => {
  errors.value = {}
}

/**
 * 密码登录
 */
const handlePasswordLogin = async () => {
  if (!validateForm()) return
  if (!agreeTerms.value) {
    errors.value.agreeTerms = '请先同意用户协议和隐私政策'
    return
  }

  try {
    isLoading.value = true

    const response = await userApi.login(passwordForm.value)

    if (response.code === 200) {
      // 保存登录信息
      if (rememberMe.value) {
        uni.setStorageSync('rememberedUsername', passwordForm.value.username)
      }

      uni.showToast({
        title: '登录成功',
        icon: 'success',
      })

      // 延迟跳转，让用户看到成功提示
      setTimeout(() => {
        uni.switchTab({
          url: '/pages/index/index',
        })
      }, 1500)
    }
  } catch (error: any) {
    console.error('登录失败:', error)

    // 处理特定错误
    if (error.message?.includes('验证码')) {
      showCaptcha.value = true
      refreshCaptcha()
    }

    uni.showToast({
      title: error.message || '登录失败',
      icon: 'error',
    })
  } finally {
    isLoading.value = false
  }
}

/**
 * 短信登录
 */
const handleSmsLogin = async () => {
  if (!validatePhone()) return
  if (!smsForm.value.code) {
    errors.value.smsCode = '请输入短信验证码'
    return
  }
  if (!agreeTerms.value) {
    errors.value.agreeTerms = '请先同意用户协议和隐私政策'
    return
  }

  try {
    isLoading.value = true

    const response = await userApi.phoneLogin(smsForm.value)

    if (response.code === 200) {
      uni.showToast({
        title: '登录成功',
        icon: 'success',
      })

      setTimeout(() => {
        uni.switchTab({
          url: '/pages/index/index',
        })
      }, 1500)
    }
  } catch (error: any) {
    console.error('短信登录失败:', error)
    uni.showToast({
      title: error.message || '登录失败',
      icon: 'error',
    })
  } finally {
    isLoading.value = false
  }
}

/**
 * 发送短信验证码
 */
const sendSmsCode = async () => {
  if (!validatePhone()) return

  try {
    isSendingSms.value = true

    await userApi.sendVerificationCode({
      phone: smsForm.value.phone,
      type: 'login',
    })

    uni.showToast({
      title: '验证码已发送',
      icon: 'success',
    })

    // 开始倒计时
    startSmsCountdown()
  } catch (error: any) {
    console.error('发送验证码失败:', error)
    uni.showToast({
      title: error.message || '发送失败',
      icon: 'error',
    })
  } finally {
    isSendingSms.value = false
  }
}

/**
 * 开始短信倒计时
 */
const startSmsCountdown = () => {
  smsCountdown.value = 60
  smsTimer = setInterval(() => {
    smsCountdown.value--
    if (smsCountdown.value <= 0) {
      clearInterval(smsTimer!)
      smsTimer = null
    }
  }, 1000)
}

/**
 * 微信登录
 */
const handleWxLogin = async () => {
  if (!agreeTerms.value) {
    errors.value.agreeTerms = '请先同意用户协议和隐私政策'
    return
  }

  try {
    // 获取微信授权
    const loginRes = await new Promise<any>((resolve, reject) => {
      uni.login({
        provider: 'weixin',
        success: resolve,
        fail: reject,
      })
    })

    if (loginRes.code) {
      const response = await userApi.wxLogin({ code: loginRes.code })

      if (response.code === 200) {
        uni.showToast({
          title: '登录成功',
          icon: 'success',
        })

        setTimeout(() => {
          uni.switchTab({
            url: '/pages/index/index',
          })
        }, 1500)
      }
    }
  } catch (error: any) {
    console.error('微信登录失败:', error)
    uni.showToast({
      title: error.message || '微信登录失败',
      icon: 'error',
    })
  }
}

/**
 * QQ登录
 */
const handleQQLogin = () => {
  uni.showToast({
    title: '暂未开放',
    icon: 'none',
  })
}

/**
 * 刷新验证码
 */
const refreshCaptcha = () => {
  const uuid = Date.now().toString()
  passwordForm.value.uuid = uuid
  captchaImage.value = `${import.meta.env.VITE_SERVER_BASEURL}/auth/captcha?uuid=${uuid}&t=${Date.now()}`
}

/**
 * 验证表单
 */
const validateForm = () => {
  if (loginType.value === 'password') {
    return validateUsername() && validatePassword()
  } else {
    return validatePhone()
  }
}

/**
 * 跳转到注册页面
 */
const goToRegister = () => {
  uni.navigateTo({
    url: '/pages/auth/register',
  })
}

/**
 * 跳转到忘记密码页面
 */
const goToForgotPassword = () => {
  uni.navigateTo({
    url: '/pages/auth/forgot-password',
  })
}

/**
 * 显示用户协议
 */
const showUserAgreement = () => {
  uni.navigateTo({
    url: '/pages/service/user-agreement',
  })
}

/**
 * 显示隐私政策
 */
const showPrivacyPolicy = () => {
  uni.navigateTo({
    url: '/pages/service/privacy-policy',
  })
}

// 页面加载
onLoad(() => {
  // 加载记住的用户名
  const rememberedUsername = uni.getStorageSync('rememberedUsername')
  if (rememberedUsername) {
    passwordForm.value.username = rememberedUsername
    rememberMe.value = true
  }
})

// 组件挂载
onMounted(() => {
  // 默认同意协议
  agreeTerms.value = true
})

// 页面卸载时清理定时器
onUnmounted(() => {
  if (smsTimer) {
    clearInterval(smsTimer)
  }
})
</script>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
}

// 自定义导航栏
.custom-navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  position: relative;
}

.navbar-left,
.navbar-right {
  width: 80rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.navbar-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #fff;
}

// 登录表单
.login-form {
  padding: 60rpx 40rpx 40rpx;
}

// Logo区域
.logo-section {
  text-align: center;
  margin-bottom: 80rpx;
}

.logo {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
}

.app-name {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 16rpx;
}

.welcome-text {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

// 登录方式切换
.login-tabs {
  display: flex;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 50rpx;
  padding: 8rpx;
  margin-bottom: 60rpx;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 20rpx;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.7);
  border-radius: 42rpx;
  transition: all 0.3s ease;

  &.active {
    background-color: #fff;
    color: #333;
    font-weight: bold;
  }
}

// 表单内容
.form-content {
  background-color: #fff;
  border-radius: 30rpx;
  padding: 60rpx 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);
}

// 验证码区域
.captcha-section {
  margin-top: 30rpx;
}

.captcha-image {
  width: 120rpx;
  height: 60rpx;
  border-radius: 8rpx;
  border: 1px solid #ddd;
}

// 表单操作
.form-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 40rpx 0;
}

.remember-section {
  font-size: 24rpx;
}

.forgot-password {
  font-size: 24rpx;
  color: #ff6b35;
}

// 短信输入区域
.sms-input-section {
  margin-top: 30rpx;
}

// 第三方登录
.third-party-login {
  margin: 60rpx 0;
}

.divider {
  text-align: center;
  position: relative;
  margin-bottom: 40rpx;

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background-color: rgba(255, 255, 255, 0.3);
  }

  text {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 0 30rpx;
    font-size: 24rpx;
    color: rgba(255, 255, 255, 0.8);
  }
}

.third-party-buttons {
  display: flex;
  justify-content: center;
  gap: 60rpx;
}

.third-party-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  padding: 20rpx;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  min-width: 120rpx;

  text {
    font-size: 24rpx;
    color: #fff;
  }
}

// 注册区域
.register-section {
  text-align: center;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.register-link {
  color: #fff;
  font-weight: bold;
  margin-left: 10rpx;
}

// 用户协议
.agreement-section {
  position: absolute;
  bottom: 60rpx;
  left: 40rpx;
  right: 40rpx;
  font-size: 24rpx;
}

.agreement-link {
  color: #fff;
  text-decoration: underline;
}
</style>
