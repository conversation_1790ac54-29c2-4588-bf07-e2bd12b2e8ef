<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '聊天详情',
    navigationStyle: 'custom',
  },
  layout: 'tabbar',
}
</route>

<template>
  <view class="chat-detail-page">
    <!-- 自定义导航栏 -->
    <wd-navbar :title="conversationTitle" left-text="返回" left-arrow @click-left="handleBack">
      <template #right>
        <wd-icon name="more" :size="24" @click="showMoreActions = true" />
      </template>
    </wd-navbar>

    <!-- 消息列表 -->
    <scroll-view
      ref="scrollView"
      class="message-list"
      scroll-y
      :scroll-top="scrollTop"
      :scroll-into-view="scrollIntoView"
      @scrolltoupper="handleLoadMore"
    >
      <!-- 加载更多提示 -->
      <view v-if="hasMore && !loading.messages" class="load-more-tip">
        <text>下拉加载更多消息</text>
      </view>

      <view v-if="loading.messages" class="loading-tip">
        <wd-loading :size="24" />
        <text>加载中...</text>
      </view>

      <!-- 消息项 -->
      <view
        v-for="(message, index) in messages"
        :key="message.id"
        :id="`message-${message.id}`"
        class="message-item"
        :class="{
          'message-self': message.senderId === currentUserId,
          'message-other': message.senderId !== currentUserId,
        }"
      >
        <!-- 时间分割线 -->
        <view v-if="shouldShowTime(message, index)" class="time-divider">
          <text>{{ formatMessageTime(message.createdAt) }}</text>
        </view>

        <!-- 消息内容 -->
        <view class="message-content">
          <!-- 头像 -->
          <view v-if="message.senderId !== currentUserId" class="message-avatar">
            <Avatar
              :src="getSenderAvatar(message)"
              :size="72"
              :text="getSenderName(message).charAt(0)"
            />
          </view>

          <!-- 消息气泡 -->
          <view class="message-bubble-container">
            <!-- 发送状态 -->
            <view v-if="message.senderId === currentUserId" class="message-status">
              <wd-loading v-if="message.status === 'sending'" :size="16" />
              <wd-icon
                v-else-if="message.status === 'failed'"
                name="warning"
                size="16"
                color="#ff4757"
                @click="handleResendMessage(message)"
              />
            </view>

            <!-- 消息气泡 -->
            <view
              class="message-bubble"
              :class="{
                'bubble-self': message.senderId === currentUserId,
                'bubble-other': message.senderId !== currentUserId,
              }"
              @longpress="handleMessageLongPress(message)"
            >
              <!-- 文本消息 -->
              <text v-if="message.type === 'text'" class="message-text" selectable>
                {{ message.content.text }}
              </text>

              <!-- 图片消息 -->
              <view
                v-else-if="message.type === 'image'"
                class="message-image"
                @click="handlePreviewImage(message)"
              >
                <image :src="message.content.imageUrl" :mode="'aspectFit'" class="image-content" />
              </view>

              <!-- 语音消息 -->
              <view
                v-else-if="message.type === 'voice'"
                class="message-voice"
                @click="handlePlayVoice(message)"
              >
                <wd-icon
                  :name="isPlayingVoice(message) ? 'pause' : 'play'"
                  size="24"
                  color="white"
                />
                <text class="voice-duration">{{ message.content.fileSize }}"</text>
                <view v-if="isPlayingVoice(message)" class="voice-animation">
                  <view class="wave"></view>
                  <view class="wave"></view>
                  <view class="wave"></view>
                </view>
              </view>

              <!-- 文件消息 -->
              <view
                v-else-if="message.type === 'file'"
                class="message-file"
                @click="handleDownloadFile(message)"
              >
                <wd-icon name="file" :size="32" color="#1890ff" />
                <view class="file-info">
                  <text class="file-name">{{ message.content.fileName }}</text>
                  <text class="file-size">{{ formatFileSize(message.content.fileSize || 0) }}</text>
                </view>
              </view>

              <!-- 位置消息 -->
              <view
                v-else-if="message.type === 'location'"
                class="message-location"
                @click="handleViewLocation(message)"
              >
                <wd-icon name="location" :size="24" color="#ff4757" />
                <view class="location-info">
                  <text class="location-name">{{ message.content.location?.address }}</text>
                  <text class="location-address">{{ message.content.location?.address }}</text>
                </view>
              </view>

              <!-- 订单消息 -->
              <view
                v-else-if="message.type === 'order'"
                class="message-order"
                @click="handleViewOrder(message)"
              >
                <view class="order-header">
                  <wd-icon name="order" :size="24" color="#1890ff" />
                  <text class="order-title">订单信息</text>
                </view>
                <view class="order-content">
                  <text class="order-number">订单号：{{ message.content.orderInfo?.orderNo }}</text>
                  <text class="order-amount">金额：¥{{ message.content.orderInfo?.amount }}</text>
                </view>
              </view>

              <!-- 商品消息 -->
              <view
                v-else-if="message.type === 'goods'"
                class="message-goods"
                @click="handleViewGoods(message)"
              >
                <image
                  :src="message.content.goodsInfo?.goodsImage"
                  class="goods-image"
                  mode="aspectFill"
                />
                <view class="goods-info">
                  <text class="goods-name">{{ message.content.goodsInfo?.goodsName }}</text>
                  <text class="goods-price">¥{{ message.content.goodsInfo?.price }}</text>
                </view>
              </view>

              <!-- 系统消息 -->
              <text v-else-if="message.type === 'system'" class="message-system">
                {{ message.content.text }}
              </text>
            </view>
          </view>

          <!-- 头像（自己的消息） -->
          <view v-if="message.senderId === currentUserId" class="message-avatar">
            <Avatar :src="currentUserAvatar" :size="72" :text="currentUserName.charAt(0)" />
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 输入区域 -->
    <view class="input-area">
      <!-- 快捷回复 -->
      <view v-if="showQuickReplies && quickReplies.length > 0" class="quick-replies">
        <scroll-view scroll-x class="quick-replies-scroll">
          <view class="quick-replies-list">
            <view
              v-for="reply in quickReplies"
              :key="reply.id"
              class="quick-reply-item"
              @click="handleQuickReply(reply)"
            >
              <text>{{ reply.content }}</text>
            </view>
          </view>
        </scroll-view>
      </view>

      <!-- 输入框 -->
      <view class="input-container">
        <view class="input-left">
          <wd-icon
            name="voice"
            size="24"
            :color="inputMode === 'voice' ? '#1890ff' : '#999'"
            @click="toggleInputMode"
          />
        </view>

        <!-- 文本输入 -->
        <view v-if="inputMode === 'text'" class="text-input-container">
          <textarea
            v-model="inputText"
            class="text-input"
            placeholder="请输入消息..."
            :auto-height="true"
            :max-height="200"
            @input="handleInputChange"
            @focus="handleInputFocus"
            @blur="handleInputBlur"
          />
        </view>

        <!-- 语音输入 -->
        <view v-else class="voice-input-container">
          <view
            class="voice-input-button"
            :class="{ recording: isRecording }"
            @touchstart="startRecording"
            @touchend="stopRecording"
            @touchcancel="cancelRecording"
          >
            <text>{{ isRecording ? '松开发送' : '按住说话' }}</text>
          </view>
        </view>

        <view class="input-right">
          <!-- 表情按钮 -->
          <wd-icon
            v-if="inputMode === 'text'"
            name="emoji"
            size="24"
            color="#999"
            @click="showEmojiPanel = !showEmojiPanel"
          />

          <!-- 更多按钮 -->
          <wd-icon name="add" :size="24" color="#999" @click="showMorePanel = !showMorePanel" />

          <!-- 发送按钮 -->
          <wd-button
            v-if="inputText.trim() || inputMode === 'voice'"
            type="primary"
            size="small"
            @click="handleSendMessage"
          >
            发送
          </wd-button>
        </view>
      </view>

      <!-- 表情面板 -->
      <view v-if="showEmojiPanel" class="emoji-panel">
        <view class="emoji-grid">
          <view
            v-for="emoji in emojiList"
            :key="emoji"
            class="emoji-item"
            @click="handleEmojiClick(emoji)"
          >
            <text>{{ emoji }}</text>
          </view>
        </view>
      </view>

      <!-- 更多功能面板 -->
      <view v-if="showMorePanel" class="more-panel">
        <view class="more-grid">
          <view class="more-item" @click="handleSelectImage">
            <wd-icon name="image" :size="32" color="#1890ff" />
            <text>相册</text>
          </view>
          <view class="more-item" @click="handleTakePhoto">
            <wd-icon name="camera" :size="32" color="#52c41a" />
            <text>拍照</text>
          </view>
          <view class="more-item" @click="handleSelectFile">
            <wd-icon name="file" :size="32" color="#fa8c16" />
            <text>文件</text>
          </view>
          <view class="more-item" @click="handleSelectLocation">
            <wd-icon name="location" :size="32" color="#ff4757" />
            <text>位置</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 更多操作弹窗 -->
    <wd-action-sheet
      v-model="showMoreActions"
      :actions="moreActionsList"
      @select="handleMoreActionSelect"
    />

    <!-- 消息操作弹窗 -->
    <wd-action-sheet
      v-model="showMessageActions"
      :actions="messageActionsList"
      @select="handleMessageActionSelect"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useChatStore } from '@/store/chat'
import { useWebSocketStore } from '@/store/websocket'
import { formatTime, formatRelativeTime } from '@/utils/date'
import { formatFileSize } from '@/utils/format'
import Avatar from '@/components/Avatar/index.vue'
import type { IChatMessage, IQuickReply } from '@/api/chat.typings'

const route = useRoute()
const router = useRouter()
const chatStore = useChatStore()
const wsStore = useWebSocketStore()

// 路由参数
const conversationId = (route.query.conversationId || route.query.sessionId) as string
console.log('🎯 [ChatDetail] 获取到的会话ID:', conversationId, '类型:', typeof conversationId)
console.log('🎯 [ChatDetail] 路由参数:', route.query)

// 响应式数据
const scrollTop = ref(0)
const scrollIntoView = ref('')
const inputText = ref('')
const inputMode = ref<'text' | 'voice'>('text')
const isRecording = ref(false)
const showEmojiPanel = ref(false)
const showMorePanel = ref(false)
const showQuickReplies = ref(true)
const showMoreActions = ref(false)
const showMessageActions = ref(false)
const currentActionMessage = ref<IChatMessage | null>(null)
const playingVoiceId = ref('')

// 当前用户信息
const currentUserId = ref('current_user_id') // 从用户store获取
const currentUserName = ref('我') // 从用户store获取
const currentUserAvatar = ref('') // 从用户store获取

// 计算属性
const conversation = computed(() => chatStore.currentConversation)
const messages = computed(() => chatStore.messages)
const loading = computed(() => chatStore.loading)
const hasMore = computed(() => chatStore.hasMoreMessages)
const quickReplies = computed(() => chatStore.quickReplies)

const conversationTitle = computed(() => {
  if (!conversation.value) return '聊天'
  return conversation.value.title
})

// 表情列表
const emojiList = ref([
  '😀',
  '😃',
  '😄',
  '😁',
  '😆',
  '😅',
  '😂',
  '🤣',
  '😊',
  '😇',
  '🙂',
  '🙃',
  '😉',
  '😌',
  '😍',
  '🥰',
  '😘',
  '😗',
  '😙',
  '😚',
  '😋',
  '😛',
  '😝',
  '😜',
  '🤪',
  '🤨',
  '🧐',
  '🤓',
  '😎',
  '🤩',
  '🥳',
  '😏',
  '😒',
  '😞',
  '😔',
  '😟',
  '😕',
  '🙁',
  '☹️',
  '😣',
  '😖',
  '😫',
  '😩',
  '🥺',
  '😢',
  '😭',
  '😤',
  '😠',
  '😡',
  '🤬',
  '🤯',
  '😳',
  '🥵',
  '🥶',
  '😱',
  '😨',
])

// 更多操作列表
const moreActionsList = computed(() => {
  const actions = [
    { name: '聊天信息', value: 'info' },
    { name: '查找聊天记录', value: 'search' },
    { name: '清空聊天记录', value: 'clear' },
  ]

  if (conversation.value?.type === 'customer_service') {
    actions.push({ name: '转人工客服', value: 'transfer' }, { name: '结束会话', value: 'end' })
  }

  return actions
})

// 消息操作列表
const messageActionsList = computed(() => {
  if (!currentActionMessage.value) return []

  const message = currentActionMessage.value
  const actions = []

  if (message.type === 'text') {
    actions.push({ name: '复制', value: 'copy' })
  }

  if (message.senderId === currentUserId.value) {
    actions.push({ name: '撤回', value: 'recall' })
  }

  actions.push(
    { name: '转发', value: 'forward' },
    { name: '删除', value: 'delete', color: '#ff4757' },
  )

  return actions
})

// 获取发送者头像
const getSenderAvatar = (message: IChatMessage) => {
  const sender = conversation.value?.participants.find((p) => p.id === message.senderId)
  return sender?.avatar || ''
}

// 获取发送者名称
const getSenderName = (message: IChatMessage) => {
  const sender = conversation.value?.participants.find((p) => p.id === message.senderId)
  return sender?.nickname || '未知用户'
}

// 判断是否显示时间
const shouldShowTime = (message: IChatMessage, index: number) => {
  if (index === 0) return true

  const prevMessage = messages.value[index - 1]
  const currentTime = new Date(message.createdAt).getTime()
  const prevTime = new Date(prevMessage.createdAt).getTime()

  // 超过5分钟显示时间
  return currentTime - prevTime > 5 * 60 * 1000
}

// 格式化消息时间
const formatMessageTime = (time: string | undefined | null) => {
  // 检查时间是否有效
  if (!time) {
    return '刚刚'
  }

  const now = new Date()
  const messageTime = new Date(time)

  // 检查消息时间是否有效
  if (isNaN(messageTime.getTime())) {
    return '刚刚'
  }

  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const messageDate = new Date(
    messageTime.getFullYear(),
    messageTime.getMonth(),
    messageTime.getDate(),
  )

  if (messageDate.getTime() === today.getTime()) {
    // 今天，显示时间
    return formatTime(time, 'HH:mm')
  } else if (messageDate.getTime() === today.getTime() - 24 * 60 * 60 * 1000) {
    // 昨天
    return `昨天 ${formatTime(time, 'HH:mm')}`
  } else {
    // 其他日期
    return formatTime(time, 'MM-dd HH:mm')
  }
}

// 判断语音是否正在播放
const isPlayingVoice = (message: IChatMessage) => {
  return playingVoiceId.value === message.id
}

// 切换输入模式
const toggleInputMode = () => {
  inputMode.value = inputMode.value === 'text' ? 'voice' : 'text'
  showEmojiPanel.value = false
  showMorePanel.value = false
}

// 处理输入变化
const handleInputChange = () => {
  // 发送输入状态
  chatStore.sendTypingStatus(conversationId, true)
}

// 输入框获得焦点
const handleInputFocus = () => {
  showEmojiPanel.value = false
  showMorePanel.value = false
  scrollToBottom()
}

// 输入框失去焦点
const handleInputBlur = () => {
  // 发送停止输入状态
  chatStore.sendTypingStatus(conversationId, false)
}

// 发送消息
const handleSendMessage = async () => {
  if (!inputText.value.trim()) return

  console.log('🎯 [SendMessage] 准备发送消息:', {
    conversationId,
    conversationIdType: typeof conversationId,
    messageText: inputText.value.trim(),
  })

  try {
    await chatStore.sendMessage({
      conversationId,
      receiverId:
        conversation.value?.participants.find((p) => p.id !== currentUserId.value)?.id || '',
      type: 'text',
      content: {
        text: inputText.value.trim(),
      },
    })

    inputText.value = ''
    showEmojiPanel.value = false
    showMorePanel.value = false

    nextTick(() => {
      scrollToBottom()
    })
  } catch (error) {
    console.error('发送消息失败:', error)
    uni.showToast({
      title: '发送失败',
      icon: 'none',
    })
  }
}

// 重发消息
const handleResendMessage = async (message: IChatMessage) => {
  try {
    await chatStore.sendMessage({
      conversationId: message.conversationId,
      receiverId: message.receiverId,
      type: message.type,
      content: message.content,
    })
  } catch (error) {
    console.error('重发消息失败:', error)
    uni.showToast({
      title: '重发失败',
      icon: 'none',
    })
  }
}

// 表情点击
const handleEmojiClick = (emoji: string) => {
  inputText.value += emoji
}

// 快捷回复
const handleQuickReply = async (reply: IQuickReply) => {
  try {
    await chatStore.sendMessage({
      conversationId,
      receiverId:
        conversation.value?.participants.find((p) => p.id !== currentUserId.value)?.id || '',
      type: 'text',
      content: {
        text: reply.content,
      },
    })

    nextTick(() => {
      scrollToBottom()
    })
  } catch (error) {
    console.error('发送快捷回复失败:', error)
  }
}

// 开始录音
const startRecording = () => {
  isRecording.value = true
  // 调用录音API
}

// 停止录音
const stopRecording = () => {
  isRecording.value = false
  // 停止录音并发送
}

// 取消录音
const cancelRecording = () => {
  isRecording.value = false
  // 取消录音
}

// 选择图片
const handleSelectImage = () => {
  uni.chooseImage({
    count: 9,
    sizeType: ['original', 'compressed'],
    sourceType: ['album'],
    success: (res) => {
      handleUploadImages(res.tempFilePaths)
    },
  })
  showMorePanel.value = false
}

// 拍照
const handleTakePhoto = () => {
  uni.chooseImage({
    count: 1,
    sizeType: ['original', 'compressed'],
    sourceType: ['camera'],
    success: (res) => {
      handleUploadImages(res.tempFilePaths)
    },
  })
  showMorePanel.value = false
}

// 上传图片
const handleUploadImages = async (filePaths: string[]) => {
  for (const filePath of filePaths) {
    try {
      // 上传图片并发送消息
      const uploadResult = await chatStore.uploadChatFile(filePath, 'image')

      await chatStore.sendMessage({
        conversationId,
        receiverId:
          conversation.value?.participants.find((p) => p.id !== currentUserId.value)?.id || '',
        type: 'image',
        content: {
          imageUrl: uploadResult.url,
          width: (uploadResult as any).width,
          height: (uploadResult as any).height,
        },
      })
    } catch (error) {
      console.error('上传图片失败:', error)
      uni.showToast({
        title: '图片发送失败',
        icon: 'none',
      })
    }
  }

  nextTick(() => {
    scrollToBottom()
  })
}

// 选择文件
const handleSelectFile = () => {
  // 调用文件选择API
  showMorePanel.value = false
}

// 选择位置
const handleSelectLocation = () => {
  uni.chooseLocation({
    success: async (res) => {
      try {
        await chatStore.sendMessage({
          conversationId,
          receiverId:
            conversation.value?.participants.find((p) => p.id !== currentUserId.value)?.id || '',
          type: 'location',
          content: {
            location: {
              name: res.name,
              address: res.address,
              latitude: res.latitude,
              longitude: res.longitude,
            },
          },
        })

        nextTick(() => {
          scrollToBottom()
        })
      } catch (error) {
        console.error('发送位置失败:', error)
      }
    },
  })
  showMorePanel.value = false
}

// 预览图片
const handlePreviewImage = (message: IChatMessage) => {
  const imageUrls = messages.value
    .filter((msg) => msg.type === 'image')
    .map((msg) => msg.content.imageUrl || '')

  uni.previewImage({
    urls: imageUrls,
    current: message.content.imageUrl || '',
  })
}

// 播放语音
const handlePlayVoice = (message: IChatMessage) => {
  if (isPlayingVoice(message)) {
    // 停止播放
    playingVoiceId.value = ''
    // 调用停止播放API
  } else {
    // 开始播放
    playingVoiceId.value = message.id
    // 调用播放API
  }
}

// 下载文件
const handleDownloadFile = (message: IChatMessage) => {
  uni.downloadFile({
    url: message.content.fileUrl || '',
    success: (res) => {
      uni.openDocument({
        filePath: res.tempFilePath,
      })
    },
  })
}

// 查看位置
const handleViewLocation = (message: IChatMessage) => {
  uni.openLocation({
    latitude: message.content.location?.latitude || 0,
    longitude: message.content.location?.longitude || 0,
    name: message.content.location?.name || '',
    address: message.content.location?.address || '',
  })
}

// 查看订单
const handleViewOrder = (message: IChatMessage) => {
  router.push({
    path: '/order/detail',
    query: {
      orderId: message.content.orderInfo?.orderId,
    },
  })
}

// 查看商品
const handleViewGoods = (message: IChatMessage) => {
  router.push({
    path: '/goods/detail',
    query: {
      goodsId: message.content.goodsInfo?.goodsId,
    },
  })
}

// 消息长按
const handleMessageLongPress = (message: IChatMessage) => {
  currentActionMessage.value = message
  showMessageActions.value = true
}

// 处理消息操作
const handleMessageActionSelect = async (action: any) => {
  if (!currentActionMessage.value) return

  const message = currentActionMessage.value

  try {
    switch (action.value) {
      case 'copy':
        if (message.type === 'text') {
          uni.setClipboardData({
            data: message.content.text,
            success: () => {
              uni.showToast({
                title: '已复制',
                icon: 'success',
              })
            },
          })
        }
        break

      case 'recall':
        // 撤回消息功能暂未实现
        console.log('撤回消息:', message.id)
        uni.showToast({
          title: '已撤回',
          icon: 'success',
        })
        break

      case 'forward':
        // 转发消息
        break

      case 'delete':
        // 删除消息功能暂未实现
        console.log('删除消息:', message.id)
        uni.showToast({
          title: '已删除',
          icon: 'success',
        })
        break
    }
  } catch (error) {
    console.error('消息操作失败:', error)
    uni.showToast({
      title: '操作失败',
      icon: 'none',
    })
  }

  showMessageActions.value = false
  currentActionMessage.value = null
}

// 处理更多操作
const handleMoreActionSelect = async (action: any) => {
  try {
    switch (action.value) {
      case 'info':
        // 跳转到聊天信息页面
        break

      case 'search':
        // 跳转到搜索页面
        break

      case 'clear':
        const result = await uni.showModal({
          title: '清空聊天记录',
          content: '确定要清空所有聊天记录吗？此操作不可恢复。',
        })

        if (result.confirm) {
          // 清空消息功能暂未实现
          console.log('清空消息:', conversationId)
          uni.showToast({
            title: '已清空',
            icon: 'success',
          })
        }
        break

      case 'transfer':
        // 转人工客服功能暂未实现
        console.log('转人工客服:', conversationId)
        uni.showToast({
          title: '正在为您转接人工客服',
          icon: 'none',
        })
        break

      case 'end':
        // 结束会话
        const endResult = await uni.showModal({
          title: '结束会话',
          content: '确定要结束当前会话吗？',
        })

        if (endResult.confirm) {
          // 结束会话功能暂未实现
          console.log('结束会话:', conversationId)
          uni.showToast({
            title: '会话已结束',
            icon: 'success',
          })
        }
        break
    }
  } catch (error) {
    console.error('操作失败:', error)
    uni.showToast({
      title: '操作失败',
      icon: 'none',
    })
  }

  showMoreActions.value = false
}

// 加载更多消息
const handleLoadMore = async () => {
  if (loading.value.messages || !hasMore.value) return

  try {
    await chatStore.fetchMessages(conversationId)
  } catch (error) {
    console.error('加载更多消息失败:', error)
  }
}

// 滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    if (messages.value.length > 0) {
      const lastMessage = messages.value[messages.value.length - 1]
      scrollIntoView.value = `message-${lastMessage.id}`
    }
  })
}

// 返回
const handleBack = () => {
  uni.navigateBack()
}

// 初始化数据
const initData = async () => {
  try {
    await chatStore.setCurrentConversation(conversationId)
    await chatStore.fetchMessages(conversationId)
    await chatStore.fetchQuickReplies()

    // 标记消息为已读
    await chatStore.markMessagesAsRead(conversationId)

    nextTick(() => {
      scrollToBottom()
    })
  } catch (error) {
    console.error('初始化聊天详情失败:', error)
  }
}

// 监听新消息
watch(
  () => messages.value.length,
  (newLength, oldLength) => {
    if (newLength > oldLength) {
      nextTick(() => {
        scrollToBottom()
      })
    }
  },
)

onMounted(() => {
  // 确保WebSocket连接已建立
  if (!wsStore.isConnected) {
    console.log('🔌 [ChatDetail] WebSocket未连接，正在初始化连接...')
    wsStore.initWebSocket()
  } else {
    console.log('✅ [ChatDetail] WebSocket已连接')
  }

  initData()
})

onUnmounted(() => {
  // 清理当前会话
  chatStore.setCurrentConversation('')
})
</script>

<style lang="scss" scoped>
.chat-detail-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.message-list {
  flex: 1;
  padding: 20rpx;

  .load-more-tip,
  .loading-tip {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20rpx;
    color: #999;
    font-size: 24rpx;

    text {
      margin-left: 8rpx;
    }
  }
}

.message-item {
  margin-bottom: 32rpx;

  .time-divider {
    text-align: center;
    margin-bottom: 24rpx;

    text {
      background-color: rgba(0, 0, 0, 0.1);
      color: white;
      padding: 8rpx 16rpx;
      border-radius: 16rpx;
      font-size: 24rpx;
    }
  }

  .message-content {
    display: flex;
    align-items: flex-end;

    &.message-self {
      flex-direction: row-reverse;

      .message-bubble-container {
        align-items: flex-end;
      }
    }

    &.message-other {
      flex-direction: row;

      .message-bubble-container {
        align-items: flex-start;
      }
    }
  }

  .message-avatar {
    margin: 0 16rpx;
  }

  .message-bubble-container {
    display: flex;
    flex-direction: column;
    max-width: 60%;

    .message-status {
      margin-bottom: 8rpx;
      text-align: right;
    }
  }

  .message-bubble {
    padding: 16rpx 20rpx;
    border-radius: 16rpx;
    word-wrap: break-word;

    &.bubble-self {
      background-color: #1890ff;
      color: white;
      border-bottom-right-radius: 4rpx;
    }

    &.bubble-other {
      background-color: white;
      color: #333;
      border-bottom-left-radius: 4rpx;
    }
  }

  .message-text {
    font-size: 28rpx;
    line-height: 1.4;
  }

  .message-image {
    .image-content {
      max-width: 400rpx;
      max-height: 400rpx;
      border-radius: 8rpx;
    }
  }

  .message-voice {
    display: flex;
    align-items: center;
    min-width: 120rpx;

    .voice-duration {
      margin-left: 16rpx;
      font-size: 24rpx;
    }

    .voice-animation {
      display: flex;
      align-items: center;
      margin-left: 16rpx;

      .wave {
        width: 4rpx;
        height: 20rpx;
        background-color: currentColor;
        margin: 0 2rpx;
        border-radius: 2rpx;
        animation: wave 1s infinite;

        &:nth-child(2) {
          animation-delay: 0.1s;
        }

        &:nth-child(3) {
          animation-delay: 0.2s;
        }
      }
    }
  }

  .message-file {
    display: flex;
    align-items: center;

    .file-info {
      margin-left: 16rpx;

      .file-name {
        display: block;
        font-size: 28rpx;
        margin-bottom: 8rpx;
      }

      .file-size {
        font-size: 24rpx;
        color: #999;
      }
    }
  }

  .message-location {
    display: flex;
    align-items: center;

    .location-info {
      margin-left: 16rpx;

      .location-name {
        display: block;
        font-size: 28rpx;
        margin-bottom: 8rpx;
      }

      .location-address {
        font-size: 24rpx;
        color: #999;
      }
    }
  }

  .message-order {
    .order-header {
      display: flex;
      align-items: center;
      margin-bottom: 16rpx;

      .order-title {
        margin-left: 8rpx;
        font-size: 28rpx;
        font-weight: 500;
      }
    }

    .order-content {
      .order-number,
      .order-amount {
        display: block;
        font-size: 24rpx;
        margin-bottom: 8rpx;
      }
    }
  }

  .message-goods {
    display: flex;

    .goods-image {
      width: 120rpx;
      height: 120rpx;
      border-radius: 8rpx;
    }

    .goods-info {
      flex: 1;
      margin-left: 16rpx;

      .goods-name {
        display: block;
        font-size: 28rpx;
        margin-bottom: 16rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .goods-price {
        font-size: 32rpx;
        color: #ff4757;
        font-weight: 600;
      }
    }
  }

  .message-system {
    font-size: 24rpx;
    color: #999;
    text-align: center;
    background-color: rgba(0, 0, 0, 0.1);
    padding: 8rpx 16rpx;
    border-radius: 16rpx;
  }
}

.input-area {
  background-color: white;
  border-top: 1px solid #f0f0f0;

  .quick-replies {
    border-bottom: 1px solid #f0f0f0;

    .quick-replies-scroll {
      white-space: nowrap;
    }

    .quick-replies-list {
      display: flex;
      padding: 20rpx;

      .quick-reply-item {
        background-color: #f5f5f5;
        padding: 12rpx 24rpx;
        border-radius: 32rpx;
        margin-right: 16rpx;
        font-size: 24rpx;
        color: #666;
        white-space: nowrap;

        &:active {
          background-color: #e0e0e0;
        }
      }
    }
  }

  .input-container {
    display: flex;
    align-items: flex-end;
    padding: 20rpx;

    .input-left,
    .input-right {
      display: flex;
      align-items: center;
      gap: 16rpx;
    }

    .text-input-container {
      flex: 1;
      margin: 0 16rpx;

      .text-input {
        width: 100%;
        min-height: 72rpx;
        max-height: 200rpx;
        padding: 16rpx 20rpx;
        border: 1px solid #e0e0e0;
        border-radius: 36rpx;
        font-size: 28rpx;
        line-height: 1.4;
        background-color: #f8f9fa;
      }
    }

    .voice-input-container {
      flex: 1;
      margin: 0 16rpx;

      .voice-input-button {
        height: 72rpx;
        background-color: #f8f9fa;
        border: 1px solid #e0e0e0;
        border-radius: 36rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28rpx;
        color: #666;

        &.recording {
          background-color: #ff4757;
          color: white;
        }

        &:active {
          opacity: 0.8;
        }
      }
    }
  }

  .emoji-panel {
    border-top: 1px solid #f0f0f0;
    padding: 20rpx;

    .emoji-grid {
      display: grid;
      grid-template-columns: repeat(8, 1fr);
      gap: 20rpx;

      .emoji-item {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 80rpx;
        font-size: 48rpx;

        &:active {
          background-color: #f0f0f0;
          border-radius: 8rpx;
        }
      }
    }
  }

  .more-panel {
    border-top: 1px solid #f0f0f0;
    padding: 40rpx 20rpx;

    .more-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 40rpx;

      .more-item {
        display: flex;
        flex-direction: column;
        align-items: center;

        text {
          margin-top: 16rpx;
          font-size: 24rpx;
          color: #666;
        }

        &:active {
          opacity: 0.6;
        }
      }
    }
  }
}

@keyframes wave {
  0%,
  100% {
    transform: scaleY(1);
  }
  50% {
    transform: scaleY(1.5);
  }
}
</style>
