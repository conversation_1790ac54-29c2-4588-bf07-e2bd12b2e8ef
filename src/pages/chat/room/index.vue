/** * 聊天室页面 * * 此页面用于显示与特定用户或群组的聊天对话 *
支持文本、图片、语音、视频、文件等多种消息类型 */
<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '聊天',
    navigationStyle: 'custom',
  },
  layout: 'tabbar',
}
</route>

<template>
  <view class="chat-room-container">
    <!-- 自定义导航栏 -->
    <wd-navbar :title="chatTitle" left-text="返回" left-arrow @click-left="handleBack">
      <template #right>
        <wd-icon name="more" size="24" @click="showMoreActions = true" />
      </template>
    </wd-navbar>

    <!-- 消息列表 -->
    <scroll-view
      class="message-list"
      scroll-y
      :scroll-top="scrollTop"
      :scroll-into-view="scrollIntoView"
      @scrolltoupper="loadMoreHistory"
      :refresher-enabled="true"
      :refresher-threshold="80"
      :refresher-triggered="refreshing"
      @refresherrefresh="onRefresh"
      :upper-threshold="50"
    >
      <!-- 加载更多提示 -->
      <view v-if="loading" class="loading-more">
        <wd-loading :size="24" color="#4095e5" />
        <text class="loading-text">加载历史消息...</text>
      </view>

      <!-- 无更多历史消息提示 -->
      <view v-if="noMoreHistory" class="no-more-history">
        <text class="no-more-text">没有更多消息了</text>
      </view>

      <!-- 消息列表 -->
      <view class="message-container">
        <template v-for="(message, index) in messageList" :key="message.id">
          <!-- 消息组件 -->
          <view :id="`msg_${message.id}`" class="message-wrapper">
            <chat-message
              :message="message"
              :is-self="message.senderId === currentUserId"
              :is-group="chatType === 'group'"
              :show-time="shouldShowTime(message, index)"
              @avatar-click="onAvatarClick"
              @message-long-press="onMessageLongPress"
            ></chat-message>
          </view>
        </template>
      </view>
    </scroll-view>

    <!-- 输入区域 -->
    <chat-input-basic
      @send="sendMessage"
      @focus="onInputFocus"
      @blur="onInputBlur"
      @more-click="onMoreClick"
      @emoji-click="onEmojiClick"
      @imageSend="sendImageMessage"
      @videoSend="sendVideoMessage"
      @fileSend="sendFileMessage"
    ></chat-input-basic>

    <!-- 消息操作菜单 -->
    <wd-action-sheet
      v-model="showMessageActions"
      :actions="messageActions"
      title="消息操作"
      cancel-text="取消"
      @select="handleMessageAction"
    ></wd-action-sheet>

    <!-- 用户资料弹窗 -->
    <wd-popup
      v-model="showUserInfo"
      position="bottom"
      :closable="true"
      close-icon-position="top-right"
      class="user-info-popup"
    >
      <view class="user-info-content">
        <image
          class="user-avatar"
          :src="selectedUser?.avatar || defaultAvatar"
          mode="aspectFill"
        ></image>
        <view class="user-name">
          {{ selectedUser?.nickname || selectedUser?.username || '未知用户' }}
        </view>
        <view class="user-actions">
          <view class="action-item" @tap="startPrivateChat">
            <text class="iconfont icon-message"></text>
            <text class="action-text">发起会话</text>
          </view>
          <view class="action-item" @tap="viewUserProfile">
            <text class="iconfont icon-profile"></text>
            <text class="action-text">查看资料</text>
          </view>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, watch, nextTick } from 'vue'
import { onLoad, onUnload, onShow, onHide } from '@dcloudio/uni-app'
import ChatMessage from '@/components/chat/ChatMessage.vue'
import ChatInputBasic from '@/components/chat/ChatInputBasic.vue'
import { useUserStore } from '@/store/user'
import { WebSocketService, WebSocketMessage, WebSocketStatus } from '@/service/websocket'
import { useWebSocketStore } from '@/store/websocket'
import { toast } from '@/utils/toast'
import {
  joinSession as apiJoinSession,
  joinGroupSession as apiJoinGroupSession,
  getHistoryMessages as apiGetHistoryMessages,
  sendMessage as apiSendMessage,
  convertToUIMessage,
} from '@/api/chat/room'

// 用户信息
const userStore = useUserStore()
const currentUserId = computed(() => userStore.userInfo?.id || 0)

// 初始化WebSocket实例和状态
const websocketStore = useWebSocketStore()
const websocketService = WebSocketService.getInstance()

// 页面参数
const chatId = ref('') // 聊天对象ID
const sessionId = ref('') // 实际的会话ID
const chatType = ref('user') // 聊天类型：user 或 group
const chatTitle = ref('') // 聊天标题
const content = ref('')
const userProfile = ref(null)
const showUserProfile = ref(false)
const menuVisible = ref(false)
const selectedMessage = ref(null)
const shareDialogVisible = ref(false)

// 消息列表
const messageList = ref<any[]>([])
const loading = ref(false)
const refreshing = ref(false)
const noMoreHistory = ref(false)
const scrollTop = ref(0)
const scrollIntoView = ref('')
const defaultAvatar = '/static/images/default-avatar.png' // 默认头像
const pageSize = ref(20) // 分页大小
const currentPage = ref(1) // 当前页码
const totalPages = ref(1) // 总页数

// 消息操作
const showMessageActions = ref(false)
const messageActions = [
  { value: 'copy', name: '复制' },
  { value: 'forward', name: '转发' },
  { value: 'delete', name: '删除', color: '#E64340' },
]

// 用户信息
const showUserInfo = ref(false)
const selectedUser = ref(null)

// 导航栏相关
const showMoreActions = ref(false)

// 返回按钮处理
const handleBack = () => {
  uni.navigateBack()
}

// 生命周期钩子 - 页面加载
onLoad((option) => {
  // 清空消息列表，防止旧消息残留
  messageList.value = []

  console.log('聊天室页面接收到的参数:', option)

  // 获取路由参数
  chatId.value = option.id || ''
  chatType.value = option.type || 'user'
  chatTitle.value = decodeURIComponent(option.name || option.targetName || '')

  // 如果传递了 sessionId，直接使用它
  if (option.sessionId) {
    sessionId.value = option.sessionId
    console.log('从URL参数获取到会话ID:', sessionId.value)
  } else {
    console.log('URL参数中没有sessionId，将使用chatId作为fallback:', chatId.value)
  }

  console.log(
    '解析后的参数 - chatId:',
    chatId.value,
    'sessionId:',
    sessionId.value,
    'chatTitle:',
    chatTitle.value,
  )

  // 设置导航栏标题
  uni.setNavigationBarTitle({
    title: chatTitle.value,
  })

  // 初始化WebSocket连接
  initWebSocket()

  // 如果已经有sessionId（从会话列表跳转），直接加载消息历史
  if (sessionId.value) {
    console.log('已有会话ID，直接加载消息历史:', sessionId.value)
    loadHistoryMessages()
    subscribeToSession()
    loadMessageHistory()
  } else {
    // 如果没有sessionId，需要先加入会话
    console.log('没有会话ID，需要先加入会话')
    joinSession()
      .then(() => {
        // 向WebSocket服务器发送会话订阅消息
        loadHistoryMessages()
        subscribeToSession()
        // 加载消息历史记录
        loadMessageHistory()
      })
      .catch((err) => {
        console.error('加入会话失败:', err)
        toast.error('加入会话失败，请重试')
      })
  }

  console.log('聊天室页面加载完成，ID:', chatId.value, '类型:', chatType.value)
})

// 生命周期钩子 - 页面卸载
onUnload(() => {
  console.log('页面卸载，清理资源')

  // 移除所有事件监听器
  websocketService.removeEventListener('message', handleWebSocketMessage)
  websocketService.removeEventListener('connection', handleConnectionChange)
  websocketService.removeEventListener('error', handleWebSocketError)

  // WebSocket服务在全局单例模式下应保持连接
  // 如果需要断开连接，请解除下面注释
  // websocketService.disconnect()
})

// 生命周期钩子 - 页面显示
onShow(() => {
  // 检查WebSocket连接状态
  checkWebSocketConnection()
})

/**
 * 初始化WebSocket连接
 */
function initWebSocket() {
  console.log('初始化WebSocket服务:', websocketService)

  // 先断开已有的连接
  websocketService.disconnect()

  // 清除旧监听器，避免重复
  websocketService.removeEventListener('message', handleWebSocketMessage)
  websocketService.removeEventListener('connection', handleConnectionChange)
  websocketService.removeEventListener('error', handleWebSocketError)

  // 添加新监听器
  websocketService.addEventListener('message', handleWebSocketMessage)
  websocketService.addEventListener('connection', handleConnectionChange)
  websocketService.addEventListener('error', handleWebSocketError)

  // 获取令牌
  const token = userStore.userInfo?.token || ''
  if (!token) {
    toast.error('未登录，无法连接聊天服务器')
    return
  }

  // 获取设备ID
  const deviceId = uni.getStorageSync('device_id') || uni.getStorageSync('current_device_id')

  // 创建WebSocket连接
  console.log('尝试连接WebSocket服务...', '设备ID:', deviceId)
  websocketService
    .connect(token, deviceId)
    .then(() => {
      console.log('WebSocket连接成功')
    })
    .catch((err) => {
      console.error('WebSocket连接失败:', err)
      toast.error('连接聊天服务器失败')
    })
}

/**
 * 检查WebSocket连接状态
 */
function checkWebSocketConnection() {
  // 如果连接已断开，尝试重新连接
  if (websocketService.status.value === WebSocketStatus.DISCONNECTED) {
    // 重新连接
    initWebSocket()
  }
}

/**
 * 加入聊天会话
 */
async function joinSession() {
  try {
    // 验证会话ID - 优先使用 sessionId，如果没有则使用 chatId
    const currentSessionId = sessionId.value || chatId.value
    if (!currentSessionId) {
      console.error('会话ID为空，无法加入会话')
      toast.error('会话ID错误')
      return Promise.reject('会话ID为空')
    }

    console.log(
      '尝试加入会话:',
      currentSessionId,
      '类型:',
      chatType.value,
      'sessionId:',
      sessionId.value,
      'chatId:',
      chatId.value,
    )

    // 如果已经有 sessionId（从会话列表跳转过来），直接使用，不需要调用加入会话API
    if (sessionId.value) {
      console.log('已有会话ID，直接使用:', sessionId.value)
      return Promise.resolve()
    }

    // 如果没有 sessionId，需要调用API创建或加入会话
    let respData

    // 根据聊天类型调用不同的API
    if (chatType.value === 'group') {
      // 群聊：调用群聊会话API
      console.log('加入群聊会话:', currentSessionId)
      respData = await apiJoinGroupSession(currentSessionId)
    } else {
      // 私聊：调用原有的会话API
      const params = {
        receiver_id: currentSessionId, // 将聊天对象ID作为接收者ID传递
        receiver_type: 'user', // 私聊类型
      }
      console.log('加入私聊会话参数:', params)
      respData = await apiJoinSession(currentSessionId, params)
    }

    console.log('加入会话响应:', respData)

    if (respData.code === 200) {
      // 保存真实的会话ID
      if (respData.data && respData.data.session_id) {
        sessionId.value = respData.data.session_id.toString()
        console.log('成功加入会话，获取到会话ID:', sessionId.value)
        // 调用api获取历史消息
      } else {
        console.warn('服务器返回数据中未包含会话ID，将使用聊天对象ID作为回退')
        sessionId.value = currentSessionId
      }
      return Promise.resolve()
    } else {
      console.error('加入会话失败:', respData)
      toast.error('加入聊天失败')
      return Promise.reject(respData)
    }
  } catch (error) {
    console.error('加入会话失败:', error)
    toast.error('加入聊天失败')
    return Promise.reject(error)
  }
}

/**
 * 订阅WebSocket会话
 * 向WebSocket服务器发送会话订阅消息，使当前用户添加到会话组
 */
function subscribeToSession() {
  if (!sessionId.value || !websocketService.isConnected.value) {
    console.warn('无法订阅会话，会话ID不存在或WebSocket未连接')
    return false
  }

  // 构造订阅消息
  const subscribeMessage = {
    type: 'subscribe',
    event: 'join_session',
    session_id: parseInt(sessionId.value),
    timestamp: Date.now(),
    data: {
      session_id: parseInt(sessionId.value),
    },
  }

  // 发送消息到服务器
  const success = websocketService.sendMessage(subscribeMessage)

  if (success) {
    console.log('成功发送会话订阅消息:', sessionId.value)
  } else {
    console.error('发送会话订阅消息失败')
    toast.error('订阅会话失败，实时消息可能无法正常接收')
  }

  return success
}

/**
 * 处理WebSocket消息
 */
function handleWebSocketMessage(wsMessage: WebSocketMessage) {
  if (!wsMessage) {
    console.log('收到空消息')
    return
  }

  console.log('收到WebSocket消息:', JSON.stringify(wsMessage, null, 2))

  try {
    // 处理不同类型的消息
    switch (wsMessage.type) {
      case 'message':
        // 根据event字段判断具体操作
        switch (wsMessage.event) {
          case 'new_message':
          case 'text_message':
            console.log('收到新消息事件，数据:', wsMessage.data)
            if (wsMessage.data) {
              // 生成消息ID
              const messageId =
                wsMessage.data.message_id || wsMessage.data.id || `msg_${Date.now()}`

              // 检查是否是自己发送的消息（通过比对发送者ID）
              const isSelfMessage = wsMessage.data.sender_id === currentUserId.value

              // 如果是自己发送的消息，先检查是否是当前设备刚发送的（有临时消息）
              if (isSelfMessage) {
                // 查找是否有对应的临时消息
                const tempMsgIndex = messageList.value.findIndex((msg) => {
                  // 添加类型检查，确保id是字符串且是临时ID
                  const isStringId = typeof msg.id === 'string'
                  const isTempId = isStringId && msg.id.startsWith('temp_')

                  return (
                    isTempId &&
                    msg.content === wsMessage.data.content &&
                    msg.senderId === wsMessage.data.sender_id &&
                    Math.abs(msg.sendTime - (wsMessage.timestamp || Date.now())) < 30000
                  )
                })

                // 如果找到匹配的临时消息，说明是当前设备发送的，只需要更新ID
                if (tempMsgIndex !== -1) {
                  console.log(
                    '当前设备发送的消息，更新临时消息ID:',
                    messageList.value[tempMsgIndex].id,
                    '->',
                    messageId,
                  )
                  messageList.value[tempMsgIndex].id = messageId
                  messageList.value[tempMsgIndex].status = 'received'

                  // 滚动到底部显示最新消息
                  setTimeout(() => {
                    scrollToBottom()
                  }, 100)

                  return // 跳过后续处理
                } else {
                  // 没有找到临时消息，说明是其他设备发送的，需要添加到聊天历史
                  console.log('其他设备发送的消息，添加到聊天历史:', messageId)
                }
              }

              // 如果不是自己的消息，继续检查是否已存在
              const existingMsgIndex = messageList.value.findIndex(
                (msg) =>
                  msg.id === messageId ||
                  (msg.content === wsMessage.data.content &&
                    msg.senderId === wsMessage.data.sender_id &&
                    Math.abs(msg.sendTime - (wsMessage.timestamp || Date.now())) < 10000),
              )

              // 如果消息已存在，只更新状态
              if (existingMsgIndex !== -1) {
                console.log('消息已存在，只更新状态:', messageId)
                messageList.value[existingMsgIndex].status = 'received'
                // 即使是更新状态，也滚动到底部显示最新消息
                setTimeout(() => {
                  scrollToBottom()
                }, 100)
                return
              }

              // 如果是对方发送的新消息，才添加到消息列表
              // 使用统一的转换函数处理消息格式
              const uiMessage = convertToUIMessage(
                {
                  ...wsMessage.data,
                  id: messageId,
                  created_at: new Date(wsMessage.timestamp || Date.now()).toISOString(),
                },
                defaultAvatar,
              )
              addMessage(uiMessage)
              console.log('添加对方发送的新消息:', messageId)
            } else {
              console.error('消息数据为空')
            }
            break
          case 'message_read':
            // 消息已读状态更新
            console.log('消息状态更新:', wsMessage.data)
            updateMessageStatus(wsMessage.data)
            break
          default:
            console.log('未处理的消息事件:', wsMessage.event)
        }
        break
      case 'notification':
        // 处理通知消息
        handleNotification(wsMessage)
        break
      case 'heartbeat':
        // 心跳包无需处理
        console.log('收到心跳包')
        break
      case 'status':
        // 可能是用户上线/下线状态
        console.log('状态变更:', wsMessage.data)
        break
      default:
        console.log('未知消息类型:', wsMessage.type)
    }
  } catch (error) {
    console.error('处理WebSocket消息错误:', error)
  }
}

// 标识是否为首次加载消息
let isFirstLoad = true

/**
 * 加载历史消息
 */
async function loadHistoryMessages() {
  const currentSessionId = sessionId.value || chatId.value
  console.log(
    'loadhistory',
    loading.value,
    noMoreHistory.value,
    'sessionId:',
    sessionId.value,
    'chatId:',
    chatId.value,
  )
  if (loading.value || noMoreHistory.value || !currentSessionId) {
    console.log('跳过加载历史消息，原因:', {
      loading: loading.value,
      noMoreHistory: noMoreHistory.value,
      currentSessionId,
    })
    return
  }

  try {
    loading.value = true
    console.log(
      '请求历史消息, 聊天对象ID:',
      chatId.value,
      '实际会话ID:',
      sessionId.value,
      '页码:',
      currentPage.value,
    )

    // 使用API服务获取历史消息，使用真实的会话ID
    const response = await apiGetHistoryMessages(
      currentSessionId,
      currentPage.value,
      pageSize.value,
    )

    console.log('收到历史消息响应:', response)

    // 处理响应 - 适配后端返回格式
    if (response && response.code === 200 && response.data) {
      const messageItems = response.data.list || []
      console.log('收到历史消息数据:', messageItems.length)

      // 检查是否还有更多消息
      if (!messageItems.length || response.data.page >= response.data.page_count) {
        console.log('没有更多历史消息了')
        noMoreHistory.value = true
      } else {
        currentPage.value += 1
      }

      // 处理并添加消息
      const historyMessages = messageItems.map((msg: any) => {
        // 使用转换工具函数将后端消息格式转换为前端显示格式
        return convertToUIMessage(msg, defaultAvatar)
      })

      // 将历史消息添加到消息列表前面
      messageList.value = [...historyMessages.reverse(), ...messageList.value]

      // 如果是首次加载，滚动到最近消息
      if (isFirstLoad) {
        isFirstLoad = false
        nextTick(() => {
          scrollToBottom()
        })
      }
    } else {
      console.error('加载历史消息失败:', response)
      toast.error('加载聊天记录失败')
    }
  } catch (error) {
    console.error('加载历史消息错误:', error)
    toast.error('加载聊天记录失败')
  } finally {
    loading.value = false
  }
}

/**
 * 加载消息历史记录
 */
async function loadMessageHistory() {
  if (loading.value || noMoreHistory.value) return

  loading.value = true

  try {
    // 调用新的历史消息加载函数
    await loadHistoryMessages()

    loading.value = false
    refreshing.value = false
  } catch (error) {
    console.error('加载历史消息失败', error)
    loading.value = false
    refreshing.value = false
    toast.error('加载历史消息失败') // 使用error类型的toast
  }
}

/**
 * 加载更多历史消息
 */
function loadMoreHistory() {
  if (loading.value || noMoreHistory.value) return
  loadMessageHistory()
}

/**
 * 刷新
 */
function onRefresh() {
  refreshing.value = true
  loadMessageHistory()
}

/**
 * 添加消息到列表
 */
function addMessage(message) {
  // 确保消息ID是字符串类型
  if (message.id !== undefined && message.id !== null && typeof message.id !== 'string') {
    message.id = String(message.id)
  }

  // 检查是否已存在相同消息，防止重复
  const existingMsg = messageList.value.find((msg) => {
    // 对消息列表中的消息ID也进行类型检查
    const msgId =
      msg.id !== undefined && msg.id !== null
        ? typeof msg.id === 'string'
          ? msg.id
          : String(msg.id)
        : ''
    const messageId = message.id || ''

    // 通过ID匹配
    return (
      msgId === messageId ||
      // 通过内容、发送者和时间匹配（允许10秒误差）
      (msg.content === message.content &&
        msg.senderId === message.senderId &&
        Math.abs(msg.sendTime - message.sendTime) < 10000) ||
      // 特殊处理：如果是自己发送的消息，还要检查是否在时间范围内
      (message.senderId === currentUserId.value &&
        msg.senderId === currentUserId.value &&
        msg.content === message.content &&
        Math.abs(msg.sendTime - message.sendTime) < 30000)
    ) // 对自己发送的消息允许更大的时间误差
  })

  if (existingMsg) {
    console.log('消息已存在，不重复添加:', message.id, '已有消息ID:', existingMsg.id)
    return
  }

  // 添加新消息
  messageList.value.push(message)
  console.log('添加新消息到列表:', message.id)

  // 滚动到底部，确保显示最新消息
  scrollToBottom()
}

/**
 * 更新消息状态
 */
function updateMessageStatus(statusMessage) {
  const { messageId, status } = statusMessage

  // 查找并更新消息状态
  const index = messageList.value.findIndex((item) => item.id === messageId)
  if (index !== -1) {
    messageList.value[index].status = status
  }
}

/**
 * 处理输入状态
 */
function handleTypingStatus(typingMessage) {
  // 实现“对方正在输入”的提示
  console.log('对方正在输入:', typingMessage)
}

/**
 * 处理WebSocket连接状态变化
 * @param data 连接状态信息
 */
function handleConnectionChange(data: any) {
  console.log('WebSocket连接状态变化:', data)

  // 如果连接成功，处理会话
  if (data.status === 'connected') {
    console.log('WebSocket已连接，准备加入会话:', sessionId.value || chatId.value)

    // 如果已经有sessionId（从会话列表跳转），直接订阅会话
    if (sessionId.value) {
      console.log('已有会话ID，直接订阅会话:', sessionId.value)
      subscribeToSession()
    } else {
      // 如果没有sessionId，需要先加入会话
      console.log('没有会话ID，需要先加入会话')
      joinSession()
    }
  } else if (data.status === 'disconnected') {
    console.log('WebSocket连接断开')
  }
}

/**
 * 处理WebSocket错误
 * @param error 错误信息
 */
function handleWebSocketError(error: any) {
  console.error('WebSocket错误:', error)
  toast.error('WebSocket连接出现错误')
}

/**
 * 处理通知类消息
 * @param wsMessage WebSocket通知消息
 */
function handleNotification(wsMessage: WebSocketMessage) {
  // 应用可能接收到的通知事件包括：连接状态、用户状态变化、加入会话等
  if (!wsMessage.event) return

  switch (wsMessage.event) {
    case 'connected':
      console.log('与服务器连接成功')
      break
    case 'join_session':
      console.log('已加入会话:', wsMessage.session_id)
      // 当加入会话成功后自动滚动到最新消息
      nextTick(() => scrollToBottom())
      break
    case 'user_online':
      console.log('用户上线:', wsMessage.data)
      toast.info(`${wsMessage.data.username || '对方'}已上线`)
      break
    case 'user_offline':
      console.log('用户离线:', wsMessage.data)
      break
    default:
      console.log('未处理的通知类型:', wsMessage.event, wsMessage.data)
  }
}

/**
 * 判断是否应显示时间
 */
function shouldShowTime(message, index) {
  if (index === 0) return true

  const prevMessage = messageList.value[index - 1]
  if (!prevMessage) return true

  // 如果与前一条消息间隔超过5分钟，则显示时间
  const timeDiff = message.sendTime - prevMessage.sendTime
  return timeDiff > 5 * 60 * 1000
}

/**
 * 发送图片消息
 */
async function sendImageMessage(imageInfo) {
  console.log('发送图片消息:', imageInfo)

  // 创建一个临时ID
  const tempId = `temp_${Date.now()}`

  // 创建图片消息对象
  const message = {
    id: tempId,
    type: 'image',
    content: imageInfo.url,
    imageInfo: {
      url: imageInfo.url,
      name: imageInfo.name,
      size: imageInfo.size,
      width: imageInfo.width,
      height: imageInfo.height,
    },
    senderId: currentUserId.value,
    senderName: userStore.userInfo?.nickname || userStore.userInfo?.username,
    avatar: userStore.userInfo?.avatar,
    targetId: chatId.value,
    targetType: chatType.value,
    sendTime: Date.now(),
    status: 'sending',
  }

  // 添加到消息列表并滚动到底部
  addMessage(message)
  scrollToBottom()

  try {
    // 验证会话ID
    if (!sessionId.value && !chatId.value) {
      console.error('会话ID为空，无法发送消息')
      throw new Error('会话ID错误')
    }

    // 发送图片消息
    const respData = await apiSendMessage(sessionId.value || chatId.value, imageInfo.url, 'image', {
      fileName: imageInfo.name,
      fileSize: imageInfo.size,
      fileType: imageInfo.response?.data?.file_type || imageInfo.type,
      fileExt: imageInfo.response?.data?.file_ext,
    })
    console.log('图片消息发送响应:', respData)

    if (respData && respData.code === 200 && respData.data) {
      // 更新消息状态
      const index = messageList.value.findIndex((item) => item.id === tempId)
      if (index !== -1) {
        const messageId = respData.data.id || (respData.data.data && respData.data.data.id)
        if (messageId) {
          messageList.value[index].id = messageId
          messageList.value[index].status = 'sent'
          console.log('更新图片消息ID:', tempId, '->', messageId)
        }
      }
    } else {
      throw new Error(`图片消息发送失败: ${respData?.message || '未知错误'}`)
    }
  } catch (error) {
    console.error('发送图片消息失败:', error)
    // 更新消息状态为失败
    const index = messageList.value.findIndex((item) => item.id === tempId)
    if (index !== -1) {
      messageList.value[index].status = 'failed'
    }
    toast.error('图片消息发送失败')
  }
}

/**
 * 发送视频消息
 */
async function sendVideoMessage(videoInfo) {
  console.log('发送视频消息:', videoInfo)

  // 创建一个临时ID
  const tempId = `temp_${Date.now()}`

  // 创建视频消息对象
  const message = {
    id: tempId,
    type: 'video',
    content: videoInfo.url,
    videoInfo: {
      url: videoInfo.url,
      name: videoInfo.name,
      size: videoInfo.size,
      duration: videoInfo.duration,
      width: videoInfo.width,
      height: videoInfo.height,
    },
    senderId: currentUserId.value,
    senderName: userStore.userInfo?.nickname || userStore.userInfo?.username,
    avatar: userStore.userInfo?.avatar,
    targetId: chatId.value,
    targetType: chatType.value,
    sendTime: Date.now(),
    status: 'sending',
  }

  // 添加到消息列表并滚动到底部
  addMessage(message)
  scrollToBottom()

  try {
    // 验证会话ID
    if (!sessionId.value && !chatId.value) {
      console.error('会话ID为空，无法发送消息')
      throw new Error('会话ID错误')
    }

    // 发送视频消息
    const respData = await apiSendMessage(sessionId.value || chatId.value, videoInfo.url, 'video', {
      fileName: videoInfo.name,
      fileSize: videoInfo.size,
      fileType: videoInfo.response?.data?.file_type || videoInfo.type,
      fileExt: videoInfo.response?.data?.file_ext,
    })
    console.log('视频消息发送响应:', respData)

    if (respData && respData.code === 200 && respData.data) {
      // 更新消息状态
      const index = messageList.value.findIndex((item) => item.id === tempId)
      if (index !== -1) {
        const messageId = respData.data.id || (respData.data.data && respData.data.data.id)
        if (messageId) {
          messageList.value[index].id = messageId
          messageList.value[index].status = 'sent'
          console.log('更新视频消息ID:', tempId, '->', messageId)
        }
      }
    } else {
      throw new Error(`视频消息发送失败: ${respData?.message || '未知错误'}`)
    }
  } catch (error) {
    console.error('发送视频消息失败:', error)
    // 更新消息状态为失败
    const index = messageList.value.findIndex((item) => item.id === tempId)
    if (index !== -1) {
      messageList.value[index].status = 'failed'
    }
    toast.error('视频消息发送失败')
  }
}

/**
 * 发送文件消息
 */
async function sendFileMessage(fileInfo) {
  console.log('发送文件消息:', fileInfo)

  // 创建一个临时ID
  const tempId = `temp_${Date.now()}`

  // 创建文件消息对象
  const message = {
    id: tempId,
    type: 'file',
    content: fileInfo.url,
    fileInfo: {
      url: fileInfo.url,
      name: fileInfo.name,
      size: fileInfo.size,
      type: fileInfo.type,
    },
    senderId: currentUserId.value,
    senderName: userStore.userInfo?.nickname || userStore.userInfo?.username,
    avatar: userStore.userInfo?.avatar,
    targetId: chatId.value,
    targetType: chatType.value,
    sendTime: Date.now(),
    status: 'sending',
  }

  // 添加到消息列表并滚动到底部
  addMessage(message)
  scrollToBottom()

  try {
    // 验证会话ID
    if (!sessionId.value && !chatId.value) {
      console.error('会话ID为空，无法发送消息')
      throw new Error('会话ID错误')
    }

    // 发送文件消息
    const respData = await apiSendMessage(sessionId.value || chatId.value, fileInfo.url, 'file', {
      fileName: fileInfo.name,
      fileSize: fileInfo.size,
      fileType: fileInfo.response?.data?.file_type || fileInfo.type,
      fileExt: fileInfo.response?.data?.file_ext,
    })
    console.log('文件消息发送响应:', respData)

    if (respData && respData.code === 200 && respData.data) {
      // 更新消息状态
      const index = messageList.value.findIndex((item) => item.id === tempId)
      if (index !== -1) {
        const messageId = respData.data.id || (respData.data.data && respData.data.data.id)
        if (messageId) {
          messageList.value[index].id = messageId
          messageList.value[index].status = 'sent'
          console.log('更新文件消息ID:', tempId, '->', messageId)
        }
      }
    } else {
      throw new Error(`文件消息发送失败: ${respData?.message || '未知错误'}`)
    }
  } catch (error) {
    console.error('发送文件消息失败:', error)
    // 更新消息状态为失败
    const index = messageList.value.findIndex((item) => item.id === tempId)
    if (index !== -1) {
      messageList.value[index].status = 'failed'
    }
    toast.error('文件消息发送失败')
  }
}

/**
 * 发送消息
 */
async function sendMessage(content) {
  if (!content.trim()) return

  // 创建一个临时ID，使用当前时间戳确保唯一性
  const tempId = `temp_${Date.now()}`

  // 创建消息对象
  const message = {
    id: tempId,
    type: 'text',
    content,
    senderId: currentUserId.value,
    senderName: userStore.userInfo?.nickname || userStore.userInfo?.username,
    avatar: userStore.userInfo?.avatar,
    targetId: chatId.value,
    targetType: chatType.value,
    sendTime: Date.now(),
    status: 'sending',
  }

  // 添加到消息列表并滚动到底部
  addMessage(message)
  scrollToBottom()

  try {
    // 验证会话ID
    if (!sessionId.value && !chatId.value) {
      console.error('会话ID为空，无法发送消息')
      throw new Error('会话ID错误')
    }

    console.log('准备发送消息到会话:', sessionId.value || chatId.value, '消息内容:', content)

    // 使用API服务发送消息，优先使用真实会话ID
    const respData = await apiSendMessage(sessionId.value || chatId.value, content, 'text')
    console.log('消息发送响应:', respData)

    if (respData && respData.code === 200 && respData.data) {
      // 消息发送成功后更新消息状态，但不创建新消息
      const index = messageList.value.findIndex((item) => item.id === tempId)
      if (index !== -1) {
        // 获取消息ID，可能在data.id或data.data.id中
        const messageId = respData.data.id || (respData.data.data && respData.data.data.id)
        if (messageId) {
          messageList.value[index].id = messageId
          messageList.value[index].status = 'sent' // 使用'sent'而不是'received'，区分本地发送和websocket接收的状态
          console.log('更新本地发送消息ID:', tempId, '->', messageId)
        }
      }
      // 不需要toast提示，因为消息已经在界面上显示
      // toast.success(respData.data.message || '消息发送成功')
    } else {
      throw new Error(`消息发送失败: ${respData?.message || '未知错误'}`)
    }
  } catch (error) {
    console.error('发送消息失败:', error)
    // 更新消息状态为失败
    const index = messageList.value.findIndex((item) => item.id === tempId)
    if (index !== -1) {
      messageList.value[index].status = 'failed'
    }
    toast.error('消息发送失败')
  }
}

/**
 * 滚动到底部
 */
const scrollToBottom = () => {
  nextTick(() => {
    const lastMessage = messageList.value[messageList.value.length - 1]
    if (lastMessage) {
      // 用消息ID作为跳转目标，确保滚动到最新消息
      const messageId = typeof lastMessage.id === 'string' ? lastMessage.id : String(lastMessage.id)
      const targetId = `msg_${messageId}`

      console.log('准备滚动到最新消息:', messageId, '目标ID:', targetId)

      // 清空之前的scrollIntoView值，然后设置新值，确保触发滚动
      scrollIntoView.value = ''

      // 使用setTimeout确保DOM更新完成后再执行滚动
      setTimeout(() => {
        scrollIntoView.value = targetId
        console.log('设置scrollIntoView:', targetId)

        // 双重保险：如果scrollIntoView不生效，使用scrollTop
        setTimeout(() => {
          // 获取scroll-view的高度，滚动到最底部
          uni
            .createSelectorQuery()
            .select('.message-list')
            .boundingClientRect((rect) => {
              if (rect) {
                // 处理 rect 可能是对象或数组的情况
                const rectHeight = Array.isArray(rect) ? rect[0]?.height || 0 : rect.height
                scrollTop.value = rectHeight + 10000 // 设置一个足够大的值确保滚动到底部
                console.log('备用滚动方案执行，scrollTop设置为:', scrollTop.value)
              }
            })
            .exec()
        }, 200)
      }, 50)
    }
  })
}

/**
 * 输入框获取焦点
 */
function onInputFocus() {
  // 滚动到底部
  scrollToBottom()
}

/**
 * 输入框失去焦点
 */
function onInputBlur() {
  // 可以在此处实现一些逻辑
}

/**
 * 点击"更多"按钮
 */
function onMoreClick(show) {
  // 实现"更多"功能面板逻辑
  console.log('更多功能面板:', show)
}

/**
 * 点击表情按钮
 */
function onEmojiClick(show) {
  // 实现表情面板逻辑
  console.log('表情面板:', show)
}

/**
 * 头像点击事件
 */
function onAvatarClick(message) {
  // 只有在群聊中且不是自己的消息才显示用户信息
  if (chatType.value === 'group' && message.senderId !== currentUserId.value) {
    selectedUser.value = {
      id: message.senderId,
      username: message.senderName,
      avatar: message.avatar,
    }
    showUserInfo.value = true
  }
}

/**
 * 消息长按事件
 */
function onMessageLongPress(message) {
  selectedMessage.value = message
  showMessageActions.value = true
}

/**
 * 处理消息操作
 */
function handleMessageAction(action: { value: string; name: string }) {
  if (!selectedMessage.value) return

  switch (action.value) {
    case 'copy':
      // 复制消息文本
      if (selectedMessage.value.type === 'text') {
        uni.setClipboardData({
          data: selectedMessage.value.content,
          success: () => {
            toast.success('已复制到剪贴板')
          },
        })
      }
      break
    case 'forward':
      // 转发消息
      // 实现转发逻辑
      toast.info('转发功能待实现')
      break
    case 'delete':
      // 删除消息
      deleteMessage(selectedMessage.value.id)
      break
  }
}

/**
 * 删除消息
 */
function deleteMessage(messageId) {
  // 从列表中删除消息
  const index = messageList.value.findIndex((item) => item.id === messageId)
  if (index !== -1) {
    messageList.value.splice(index, 1)
    toast.success('消息已删除')
  }
}

/**
 * 发起私聊
 */
function startPrivateChat() {
  if (!selectedUser.value) return

  // 如果当前已经是私聊，则不需要操作
  if (chatType.value === 'user' && chatId.value === selectedUser.value.id) {
    showUserInfo.value = false
    return
  }

  // 跳转到私聊页面
  uni.navigateTo({
    url: `/pages/chat/room/index?id=${selectedUser.value.id}&type=user&name=${encodeURIComponent(selectedUser.value.username)}`,
  })
}

/**
 * 查看用户资料
 */
function viewUserProfile() {
  if (!selectedUser.value) return

  showUserInfo.value = false

  // 跳转到用户资料页面
  uni.navigateTo({
    url: `/pages-sub/user-profile/index?id=${selectedUser.value.id}`,
  })
}

/**
 * 获取模拟消息数据
 */
function getMockMessages(targetId, targetType, pageSize, page) {
  // 模拟历史消息
  const baseTime = Date.now() - page * 3600000
  const messages = []

  for (let i = 0; i < pageSize; i++) {
    const isSelf = i % 2 === 0
    const messageTime = baseTime + i * 60000

    messages.push({
      id: `msg_${messageTime}`,
      type: 'text',
      content: `这是第${page}页的第${i + 1}条测试消息`,
      senderId: isSelf ? currentUserId.value : targetId,
      senderName: isSelf ? '我' : targetType === 'user' ? chatTitle.value : '群成员',
      avatar: isSelf ? userStore.userInfo?.avatar : null,
      targetId,
      targetType,
      sendTime: messageTime,
      status: 'sent',
    })
  }

  return messages
}
</script>

<style lang="scss" scoped>
.chat-room-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
  position: relative;

  /* 确保导航栏固定在顶部 */
  :deep(.wd-navbar) {
    flex-shrink: 0;
    z-index: 100;
  }

  /* 确保输入框固定在底部 */
  :deep(.chat-input-basic) {
    flex-shrink: 0;
    z-index: 100;
  }
}

.message-list {
  flex: 1;
  padding: 20rpx 0;
  overflow-y: auto;
  /* 确保消息列表不会超出容器 */
  min-height: 0;
  /* 为导航栏和输入框预留空间 */
  margin-top: 0;
  margin-bottom: 0;
  /* 确保滚动区域正确 */
  height: 0;
}

.loading-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20rpx 0;

  .loading-text {
    font-size: 24rpx;
    color: #999;
    margin-left: 10rpx;
  }
}

.no-more-history {
  display: flex;
  justify-content: center;
  padding: 20rpx 0;

  .no-more-text {
    font-size: 24rpx;
    color: #999;
  }
}

.message-container {
  padding: 0 20rpx;
}

.message-wrapper {
  width: 100%;
}

.user-info-popup {
  .user-info-content {
    padding: 50rpx 30rpx;
    display: flex;
    flex-direction: column;
    align-items: center;

    .user-avatar {
      width: 160rpx;
      height: 160rpx;
      border-radius: 50%;
      margin-bottom: 20rpx;
    }

    .user-name {
      font-size: 36rpx;
      font-weight: 500;
      margin-bottom: 40rpx;
    }

    .user-actions {
      display: flex;
      width: 100%;
      justify-content: space-around;

      .action-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 20rpx 30rpx;

        .iconfont {
          font-size: 48rpx;
          color: #4095e5;
          margin-bottom: 10rpx;
        }

        .action-text {
          font-size: 28rpx;
          color: #333;
        }
      }
    }
  }
}
</style>
