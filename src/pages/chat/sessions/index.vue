<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5" type="page">
{
  layout: 'tabbar',
  style: {
    navigationBarTitleText: '聊天',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view class="chat-container" @click="handleContainerClick">
    <!-- 自定义导航栏 -->
    <wd-navbar title="聊天" left-text="返回" left-arrow @click-left="handleBack">
      <template #right>
        <wd-icon name="add" size="24" @click="showServiceSelector = true" />
      </template>
    </wd-navbar>

    <!-- 搜索栏 -->
    <view class="search-bar-wrapper" @click.stop="">
      <wd-search
        v-model="searchKeyword"
        placeholder="搜索聊天会话"
        round
        :light="true"
        @search="handleSearch"
      ></wd-search>
    </view>

    <!-- 刷新组件 -->
    <scroll-view
      scroll-y
      @scrolltolower="loadMore"
      @refresherrefresh="onRefresh"
      refresher-enabled
      :refresher-triggered="isRefreshing"
      class="sessions-scroll-view"
      @click.stop=""
    >
      <!-- 有数据时显示会话列表 -->
      <template v-if="sessionList && sessionList.length > 0">
        <view class="session-list">
          <!-- 会话项 -->
          <view
            v-for="(session, index) in sessionList"
            :key="session.id"
            class="session-item"
            :class="{ unread: session.unread_count > 0 }"
            hover-class="session-item-hover"
            @tap="navigateToChat(session)"
            @click.stop=""
          >
            <!-- 头像 -->
            <view class="avatar-container">
              <image class="avatar" :src="getSessionAvatar(session)" mode="aspectFill"></image>
              <!-- 未读消息数 -->
              <view v-if="session.unread_count > 0" class="unread-badge">
                {{ session.unread_count > 99 ? '99+' : session.unread_count }}
              </view>
            </view>

            <!-- 会话内容 -->
            <view class="session-content">
              <view class="session-header">
                <text class="session-name">{{ getSessionName(session) }}</text>
                <text class="session-time">
                  {{
                    formatTime(parseDate(session.last_message?.created_at || session.updated_at))
                  }}
                </text>
              </view>
              <view class="session-body">
                <text class="last-message">{{ formatLastMessage(session) }}</text>
              </view>
            </view>
          </view>
        </view>
      </template>

      <!-- 无数据展示 -->
      <view v-else class="empty-container">
        <image class="empty-image" src="/static/images/empty-chat.png" mode="aspectFit"></image>
        <text class="empty-text">暂无聊天会话</text>
      </view>

      <!-- 加载中提示 -->
      <view v-if="isLoading && !isRefreshing" class="loading-container">
        <view class="loading-icon"></view>
        <text class="loading-text">加载中...</text>
      </view>
    </scroll-view>

    <!-- 悬浮按钮 -->
    <wd-fab
      v-model:active="fabActive"
      position="right-bottom"
      direction="top"
      type="primary"
      inactive-icon="add"
      active-icon="close"
      :gap="{ bottom: 120, right: 30 }"
      @click.stop=""
    >
      <!-- 添加好友按钮 -->
      <wd-button @click="navigateToAddFriend" custom-class="fab-button" type="success" round>
        <wd-icon name="add-circle" :size="20"></wd-icon>
      </wd-button>

      <!-- 新建群组按钮 -->
      <wd-button @click="navigateToCreateGroup" custom-class="fab-button" type="warning" round>
        <wd-icon name="group-filled" :size="20"></wd-icon>
      </wd-button>

      <!-- 好友列表按钮 -->
      <wd-button @click="navigateToFriendsList" custom-class="fab-button" type="info" round>
        <wd-icon name="user-circle" :size="20"></wd-icon>
      </wd-button>
    </wd-fab>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { getSessions, readSession } from '@/api/chat/session'
// 由于后端返回数据与前端定义不匹配，此处使用自定义类型

/**
 * 定义API通用响应格式
 */
interface ApiResponse<T> {
  code: number
  message: string
  data: T
}

/**
 * 分页数据格式
 */
interface PageData<T> {
  list: T[]
  total: number
  page: number
  page_size: number
  page_count: number
}
import { useUserStore } from '@/store/user'
import { toast } from '@/utils/toast'
import { useWebSocketStore } from '@/store/websocket'
// 引入useQueue hook用于关闭悬浮按钮
// import { useQueue } from 'wot-design-uni'

/**
 * 后端返回的会话数据类型（与前端定义的Session接口有差异）
 */
interface BackendSession {
  id: string
  type: string // 'user_to_user' | 'group'
  creator_id: number
  creator_type: string
  receiver_id?: number
  receiver_type?: string
  receiver_name?: string
  receiver_avatar?: string
  group_id?: number
  group_name?: string
  group_avatar?: string
  target_name?: string // 新增：目标名称（对方用户名或群名）
  target_avatar?: string // 新增：目标头像（对方头像或群头像）
  status: number
  unread_count: number
  last_message?: {
    id: number
    type: string
    content: string
    created_at: string
    sender_id: number
    sender_type: string
    session_id: number
    status: number
  }
  created_at: string
  updated_at: string
}

// 用户信息
const userStore = useUserStore()
const wsStore = useWebSocketStore()
const searchKeyword = ref('') // 搜索关键字
const sessionList = ref<BackendSession[]>([]) // 会话列表
const isLoading = ref(false) // 是否加载中
const isRefreshing = ref(false) // 是否刷新中
const pageNo = ref(1) // 当前页码
const pageSize = ref(20) // 每页条数
const hasMore = ref(true) // 是否还有更多数据
const defaultAvatar = '/static/images/default-avatar.png' // 默认头像
const fabActive = ref(false) // 悬浮按钮激活状态
const showServiceSelector = ref(false) // 服务选择器状态

// 返回按钮处理
const handleBack = () => {
  uni.navigateBack()
}

// 当前用户ID
const currentUserId = computed(() => userStore.userInfo?.id)

// 获取会话列表
const getSessionList = async (reset: boolean = false) => {
  if (reset) {
    // 重置页码
    pageNo.value = 1
    hasMore.value = true
  }

  // 如果没有更多数据且不是刷新，直接返回
  if (!hasMore.value && !reset) {
    return
  }

  // 正在加载中时不重复请求
  if (isLoading.value) {
    return
  }

  isLoading.value = true
  console.log('加载会话列表，页码:', pageNo.value, '条数:', pageSize.value)

  try {
    // 构造请求参数
    const params = {
      pageNo: pageNo.value,
      pageSize: pageSize.value,
      keyword: searchKeyword.value,
    }

    console.log('请求参数:', params)

    // 调用API获取会话列表
    const response = await getSessions(params)
    console.log('获取会话列表响应:', response)

    // 类型断言，将响应转换为正确的类型
    const apiResponse = response as unknown as ApiResponse<PageData<BackendSession>>

    if (apiResponse && apiResponse.code === 200 && apiResponse.data) {
      // 解析响应数据
      const list = apiResponse.data.list || []
      const total = apiResponse.data.total || 0

      console.log('处理后的会话数据:', list, '总数:', total)

      // 更新是否有更多数据
      hasMore.value = list.length === pageSize.value

      // 更新会话列表
      if (reset) {
        sessionList.value = list
      } else {
        sessionList.value = [...sessionList.value, ...list]
      }

      // 增加页码
      pageNo.value++

      return list
    } else {
      console.error('响应数据结构异常:', apiResponse)
      uni.showToast({
        title: '获取会话列表失败',
        icon: 'none',
      })
      return []
    }
  } catch (error) {
    console.error('获取会话列表失败:', error)
    uni.showToast({
      title: '获取会话列表失败',
      icon: 'none',
    })
    return []
  } finally {
    // 完成加载状态
    isLoading.value = false
    isRefreshing.value = false
  }
}

// 搜索会话
function handleSearch() {
  // 执行搜索时重置列表并重新加载
  getSessionList(true)
}

// 下拉刷新会话列表
const onRefresh = async () => {
  isRefreshing.value = true
  await getSessionList(true)
  isRefreshing.value = false
}

// 加载更多会话
const loadMore = async () => {
  if (!isLoading.value && hasMore.value) {
    await getSessionList()
  }
}

// 格式化最后消息时间
function formatTime(timestamp: number): string {
  if (!timestamp) return ''

  const now = new Date()
  const msgDate = new Date(timestamp)

  // 今天的消息只显示时间
  if (msgDate.toDateString() === now.toDateString()) {
    return msgDate.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
  }

  // 昨天的消息显示"昨天"
  const yesterday = new Date(now)
  yesterday.setDate(now.getDate() - 1)
  if (msgDate.toDateString() === yesterday.toDateString()) {
    return '昨天'
  }

  // 一周内的消息显示星期几
  const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
  const diffDays = Math.floor((now.getTime() - msgDate.getTime()) / (24 * 3600 * 1000))
  if (diffDays < 7) {
    return weekDays[msgDate.getDay()]
  }

  // 其他情况显示日期
  return msgDate.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' })
}

// 格式化最后一条消息
function formatLastMessage(session: any): string {
  if (!session.last_message) return ''

  switch (session.last_message.type) {
    case 'text':
      return session.last_message.content
    case 'image':
      return '[图片]'
    case 'voice':
      return '[语音]'
    case 'video':
      return '[视频]'
    case 'file':
      return '[文件]'
    case 'system':
      return '[系统消息]'
    default:
      return session.last_message.content
  }
}

// 获取会话名称
function getSessionName(session: any): string {
  // 优先使用target_name字段
  if (session.target_name) {
    return session.target_name
  }

  // 兼容旧字段：根据会话类型获取名称
  if (session.type === 'user_to_user') {
    // 单聊场景，显示对方用户名
    return session.receiver_name || '对方用户'
  } else if (session.type === 'group') {
    // 群聊场景，显示群名称
    return session.group_name || '群聊'
  }
  return '聊天'
}

// 获取会话头像
function getSessionAvatar(session: any): string {
  // 优先使用target_avatar字段
  if (session.target_avatar) {
    return session.target_avatar
  }

  // 兼容旧字段：根据会话类型获取头像
  if (session.type === 'user_to_user') {
    // 单聊场景，显示对方头像
    return session.receiver_avatar || defaultAvatar
  } else if (session.type === 'group') {
    // 群聊场景，显示群头像
    return session.group_avatar || defaultAvatar
  }
  return defaultAvatar
}

// 将ISO日期字符串转换为时间戳
function parseDate(dateStr: string): number {
  if (!dateStr) return 0
  return new Date(dateStr).getTime()
}

// 跳转到聊天详情页
function navigateToChat(session: any) {
  // 将会话标记为已读
  readSession(session.id).catch((error) => {
    console.error('标记会话已读失败:', error)
  })

  const name = getSessionName(session)

  // 转换会话类型为前端使用的格式
  let type = 'user'
  let chatId = session.id // 默认使用session.id

  if (session.type === 'group') {
    type = 'group'
    // 对于群聊，使用group_id而不是session_id
    chatId = session.group_id || session.receiver_id || session.id
  }

  // 跳转到聊天页面
  uni.navigateTo({
    url: `/pages/chat/room/index?id=${chatId}&type=${type}&name=${encodeURIComponent(name)}`,
  })
}

// 收到新消息时刷新列表
function setupWebSocketListeners() {
  // 监听新消息事件
  wsStore.registerCallback('message:received', (message) => {
    console.log('收到新消息，刷新会话列表', message)
    // 立即刷新会话列表
    getSessionList(true)
  })

  // 监听会话更新事件
  wsStore.registerCallback('session:updated', () => {
    console.log('会话更新，刷新列表')
    // 立即刷新会话列表
    getSessionList(true)
  })
}

/**
 * 处理容器点击事件，用于关闭悬浮按钮
 */
function handleContainerClick() {
  // 点击容器其他地方时关闭悬浮按钮
  if (fabActive.value) {
    fabActive.value = false
  }
}

/**
 * 导航到添加好友页面
 */
function navigateToAddFriend() {
  fabActive.value = false
  uni.navigateTo({
    url: '/pages/friends/add/index',
  })
}

/**
 * 导航到创建群组页面
 */
function navigateToCreateGroup() {
  fabActive.value = false
  uni.navigateTo({
    url: '/pages/groups/create/index',
  })
}

/**
 * 导航到好友列表页面
 */
function navigateToFriendsList() {
  fabActive.value = false
  uni.switchTab({
    url: '/pages/friends/index',
  })
}

// 页面加载完毕
onMounted(() => {
  // 设置WebSocket监听器
  setupWebSocketListeners()

  // 初始化时加载会话列表
  getSessionList(true)
})
</script>

<style lang="scss" scoped>
.chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
}

.search-bar-wrapper {
  padding: 20rpx 30rpx;
  background-color: #fff;
  position: sticky;
  top: 0;
  z-index: 99;
}

.session-list {
  background-color: #fff;
}

.session-item {
  display: flex;
  padding: 24rpx 30rpx;
  background-color: #ffffff;
  border-bottom: 1px solid #f0f0f0;
  position: relative;

  &-hover {
    background-color: #f9f9f9;
  }

  &.unread::after {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 6rpx;
    height: 40rpx;
    background-color: #4095e5;
    border-radius: 6rpx;
  }
}

.avatar-container {
  position: relative;
  margin-right: 24rpx;

  .avatar {
    width: 90rpx;
    height: 90rpx;
    border-radius: 50%;
    background-color: #eee;
    border: 1px solid #eaeaea;
  }

  .unread-badge {
    position: absolute;
    top: -10rpx;
    right: -10rpx;
    min-width: 36rpx;
    height: 36rpx;
    padding: 0 6rpx;
    background-color: #ff4d4f;
    color: #fff;
    border-radius: 18rpx;
    font-size: 22rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.session-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  overflow: hidden;
}

.session-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;

  .session-name {
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
    max-width: 400rpx;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .session-time {
    font-size: 24rpx;
    color: #999;
  }
}

.session-body {
  width: 100%;

  .last-message {
    font-size: 28rpx;
    color: #666;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    max-width: 480rpx;
  }
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 200rpx;

  .empty-image {
    width: 240rpx;
    height: 240rpx;
    margin-bottom: 30rpx;
  }

  .empty-text {
    font-size: 28rpx;
    color: #999;
  }
}

// 悬浮按钮样式
:deep(.fab-button) {
  min-width: auto !important;
  box-sizing: border-box;
  width: 48rpx !important;
  height: 48rpx !important;
  border-radius: 24rpx !important;
  margin: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

// 加载动画
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx;

  .loading-icon {
    width: 40rpx;
    height: 40rpx;
    border: 4rpx solid #f3f3f3;
    border-top: 4rpx solid #4095e5;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20rpx;
  }

  .loading-text {
    font-size: 28rpx;
    color: #999;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 会话滚动视图
.sessions-scroll-view {
  flex: 1;
  height: 0;
}
</style>
