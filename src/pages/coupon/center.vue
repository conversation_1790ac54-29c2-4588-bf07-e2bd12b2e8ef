<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '优惠券中心',
    navigationStyle: 'default',
  },
  layout: 'tabbar',
}
</route>

<template>
  <view class="coupon-center">
    <!-- 页面头部 -->
    <view class="page-header">
      <text class="page-title">优惠券中心</text>
      <view class="header-actions">
        <view class="my-coupons-btn" @click="goToMyCoupons">
          <wd-icon name="coupon" size="16" color="#ff5500" />
          <text>我的优惠券</text>
          <view v-if="myCouponCount > 0" class="coupon-count-badge">
            {{ myCouponCount }}
          </view>
        </view>
      </view>
    </view>

    <!-- 统计信息卡片 -->
    <view class="stats-cards">
      <view class="stats-card">
        <view class="stats-icon">
          <wd-icon name="coupon" size="24" color="#ff5500" />
        </view>
        <view class="stats-info">
          <text class="stats-number">{{ totalCoupons }}</text>
          <text class="stats-label">可领取</text>
        </view>
      </view>
      <view class="stats-card" @click="goToMyCoupons">
        <view class="stats-icon">
          <wd-icon name="gift" size="24" color="#00c851" />
        </view>
        <view class="stats-info">
          <text class="stats-number">{{ myCouponCount }}</text>
          <text class="stats-label">我的优惠券</text>
        </view>
        <wd-icon name="arrow-right" size="14" color="#999" />
      </view>
      <view class="stats-card" @click="goToExpiringSoon">
        <view class="stats-icon">
          <wd-icon name="time" size="24" color="#ffa500" />
        </view>
        <view class="stats-info">
          <text class="stats-number">{{ expiringSoonCount }}</text>
          <text class="stats-label">即将过期</text>
        </view>
        <wd-icon name="arrow-right" size="14" color="#999" />
      </view>
    </view>

    <!-- 横幅区域 -->
    <view v-if="banners.length > 0" class="banner-section">
      <swiper class="banner-swiper" indicator-dots circular autoplay>
        <swiper-item v-for="banner in banners" :key="banner.id">
          <image
            :src="banner.image"
            mode="aspectFill"
            class="banner-image"
            @click="handleBannerClick(banner)"
          />
        </swiper-item>
      </swiper>
    </view>

    <!-- 搜索栏 -->
    <view class="search-section">
      <view class="search-bar">
        <wd-icon name="search" size="16" color="#999" />
        <input
          v-model="searchKeyword"
          placeholder="搜索优惠券"
          class="search-input"
          @input="handleSearch"
        />
        <view v-if="searchKeyword" class="clear-btn" @click="clearSearch">
          <wd-icon name="close" size="14" color="#999" />
        </view>
      </view>
    </view>

    <!-- 状态筛选标签 -->
    <view class="status-filter">
      <view class="filter-tabs">
        <view
          v-for="tab in statusTabs"
          :key="tab.value"
          class="filter-tab"
          :class="{ active: selectedStatus === tab.value }"
          @click="selectStatus(tab.value)"
        >
          <text class="tab-text">{{ tab.label }}</text>
          <text v-if="tab.count > 0" class="tab-count">{{ tab.count }}</text>
        </view>
      </view>
    </view>

    <!-- 分类筛选 -->
    <view class="category-filter">
      <scroll-view scroll-x class="category-scroll">
        <view class="category-list">
          <view
            v-for="category in categories"
            :key="category.value"
            class="category-item"
            :class="{ active: selectedCategory === category.value }"
            @click="selectCategory(category.value)"
          >
            <text class="category-name">{{ category.label }}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 优惠券列表 -->
    <view class="coupon-list">
      <!-- 错误状态 -->
      <!-- <ErrorFallback
        v-if="hasError && coupons.length === 0"
        :title="'加载失败'"
        :message="errorMessage"
        :show-retry="true"
        :show-back="false"
        @retry="loadCoupons(true)"
      /> -->
      <view v-if="hasError && coupons.length === 0" class="error-state">
        <wd-icon name="warning" size="60" color="#ff5500" />
        <text class="error-text">加载失败</text>
        <text class="error-message">{{ errorMessage }}</text>
        <view class="error-actions">
          <view class="action-btn" @click="loadCoupons(true)">
            <text>重试</text>
          </view>
        </view>
      </view>

      <!-- 加载状态 -->
      <view v-else-if="loading && coupons.length === 0" class="loading-state">
        <wd-loading size="30" />
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 空状态 -->
      <view v-else-if="coupons.length === 0" class="empty-state">
        <wd-icon name="coupon" size="60" color="#ddd" />
        <text class="empty-text">暂无优惠券</text>
        <text class="empty-tip">换个分类看看吧</text>
      </view>

      <view v-else class="coupon-items">
        <view
          v-for="coupon in coupons"
          :key="coupon.id"
          class="coupon-item-wrapper"
          :class="{ claimed: !coupon.can_claim }"
        >
          <CouponCard
            :coupon="coupon"
            :can-claim="coupon.can_claim"
            :claim-status-text="coupon.claim_status_text"
            :mode="coupon.can_claim ? 'claim' : 'view'"
            @claim="handleClaimCoupon"
          />

          <!-- 已领取状态遮罩 -->
          <view v-if="!coupon.can_claim" class="claimed-overlay">
            <view class="claimed-badge">
              <wd-icon name="check" size="16" color="white" />
              <text class="claimed-text">已领取</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view v-if="hasMore" class="load-more">
        <view v-if="loading" class="loading-more">
          <wd-loading size="20" />
          <text>加载更多...</text>
        </view>
        <view v-else class="load-more-btn" @click="loadMore">
          <text>点击加载更多</text>
        </view>
      </view>

      <!-- 没有更多 -->
      <view v-else-if="coupons.length > 0" class="no-more">
        <text>没有更多优惠券了</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { onReachBottom } from '@dcloudio/uni-app'
import { useCouponStore } from '@/store/coupon'
import CouponCard from '@/components/coupon/CouponCard.vue'
// import ErrorFallback from '@/components/common/ErrorFallback.vue'
import type {
  ICoupon,
  ICouponCategory,
  ICouponBanner,
  ICouponCenterItem,
} from '@/api/coupon.typings'

// 状态管理
const couponStore = useCouponStore()

// 响应式数据
const selectedCategory = ref<string>('all')
const selectedStatus = ref<string>('available') // 默认显示可领取的优惠券
const banners = ref<ICouponBanner[]>([])
const categories = ref<{ value: string; label: string }[]>([
  { value: 'all', label: '全部' },
  { value: 'discount', label: '满减券' },
  { value: 'percentage', label: '折扣券' },
  { value: 'delivery', label: '配送券' },
])

// 状态筛选标签
const statusTabs = computed(() => [
  {
    value: 'available',
    label: '可领取',
    count: availableCouponsCount.value,
  },
  {
    value: 'claimed',
    label: '已领取',
    count: claimedCouponsCount.value,
  },
])

// 统计数据
const myCouponCount = computed(() => couponStore.myCoupons?.length || 0)
const totalCoupons = computed(() => couponStore.pagination.centerCoupons.total || 0)
const expiringSoonCount = ref(0)

// 搜索功能
const searchKeyword = ref('')

// 错误状态
const hasError = ref(false)
const errorMessage = ref('')

// 计算属性
const allCoupons = computed(() => couponStore.centerCoupons as ICouponCenterItem[])
const loading = computed(() => couponStore.loading.centerCoupons)
const hasMore = computed(() => couponStore.pagination.centerCoupons.hasMore)

// 根据状态筛选优惠券
const coupons = computed(() => {
  if (selectedStatus.value === 'available') {
    // 可领取的优惠券：can_claim 为 true
    return allCoupons.value.filter((coupon) => coupon.can_claim === true)
  } else if (selectedStatus.value === 'claimed') {
    // 已领取的优惠券：can_claim 为 false 且 claim_status_text 为 "已领取"
    return allCoupons.value.filter(
      (coupon) => coupon.can_claim === false && coupon.claim_status_text === '已领取',
    )
  }
  return allCoupons.value
})

// 统计数量
const availableCouponsCount = computed(
  () => allCoupons.value.filter((coupon) => coupon.can_claim === true).length,
)

const claimedCouponsCount = computed(
  () =>
    allCoupons.value.filter(
      (coupon) => coupon.can_claim === false && coupon.claim_status_text === '已领取',
    ).length,
)

// 方法
const selectCategory = (categoryValue: string) => {
  selectedCategory.value = categoryValue
  loadCoupons(true)
}

const selectStatus = (statusValue: string) => {
  selectedStatus.value = statusValue
  // 状态切换不需要重新加载数据，因为是前端筛选
}

const loadCoupons = async (refresh = false) => {
  hasError.value = false
  errorMessage.value = ''

  try {
    await couponStore.fetchCouponCenter({
      category: selectedCategory.value === 'all' ? undefined : selectedCategory.value,
      refresh,
    })
  } catch (error: any) {
    console.error('加载优惠券失败:', error)
    hasError.value = true
    errorMessage.value = error.message || '网络连接失败，请检查网络设置'

    if (!refresh) {
      uni.showToast({
        title: '加载失败',
        icon: 'error',
      })
    }
  }
}

const loadMore = () => {
  if (!loading.value && hasMore.value) {
    loadCoupons(false)
  }
}

const handleBannerClick = (banner: ICouponBanner) => {
  // 处理横幅点击事件
  console.log('点击横幅:', banner)
}

const goToMyCoupons = () => {
  uni.navigateTo({
    url: '/pages/coupon/my-coupons',
  })
}

const goToExpiringSoon = () => {
  uni.navigateTo({
    url: '/pages/coupon/expiring-soon',
  })
}

/**
 * 领取单个优惠券
 */
const handleClaimCoupon = async (coupon: any) => {
  if (!coupon.can_claim) {
    uni.showToast({
      title: coupon.claim_status_text || '无法领取',
      icon: 'none',
    })
    return
  }

  try {
    await couponStore.claimCoupon(coupon.id)
    // 刷新优惠券列表
    await loadCoupons(true)
    // 更新统计数据
    await loadStatistics()
  } catch (error: any) {
    console.error('领取优惠券失败:', error)
    uni.showToast({
      title: error.message || '领取失败',
      icon: 'error',
    })
  }
}

/**
 * 加载统计数据
 */
const loadStatistics = async () => {
  try {
    // 加载即将过期的优惠券数量
    try {
      await couponStore.fetchExpiringSoonCoupons()
      expiringSoonCount.value = couponStore.expiringSoonCoupons?.length || 0
    } catch (error) {
      console.warn('加载即将过期优惠券失败:', error)
      expiringSoonCount.value = 0
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

/**
 * 处理搜索
 */
const handleSearch = () => {
  // 防抖处理
  clearTimeout(searchTimer)
  searchTimer = setTimeout(() => {
    loadCoupons(true)
  }, 500)
}

/**
 * 清除搜索
 */
const clearSearch = () => {
  searchKeyword.value = ''
  loadCoupons(true)
}

let searchTimer: any = null

// 生命周期
onMounted(async () => {
  try {
    await loadCoupons(true)
    await loadStatistics()
  } catch (error) {
    console.error('页面初始化失败:', error)
  }
})

// 触底加载更多
onReachBottom(() => {
  loadMore()
})

// 页面配置
defineOptions({
  navigationBarTitleText: '优惠券中心',
})
</script>

<style scoped lang="scss">
.coupon-center {
  min-height: 100vh;
  background: #f5f5f5;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 20px;
  background: white;
  border-bottom: 1px solid #eee;

  .page-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
  }

  .header-actions {
    .my-coupons-btn {
      display: flex;
      align-items: center;
      gap: 4px;
      padding: 6px 12px;
      background: #fff0e6;
      border-radius: 15px;
      font-size: 12px;
      color: #ff5500;
      cursor: pointer;
      position: relative;

      &:active {
        opacity: 0.7;
      }

      .coupon-count-badge {
        position: absolute;
        top: -5px;
        right: -5px;
        background: #ff5500;
        color: white;
        font-size: 10px;
        min-width: 16px;
        height: 16px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 4px;
      }
    }
  }
}

.stats-cards {
  display: flex;
  padding: 10px;
  gap: 10px;

  .stats-card {
    flex: 1;
    background: white;
    border-radius: 12px;
    padding: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    cursor: pointer;
    transition: all 0.2s;

    &:active {
      opacity: 0.8;
      transform: scale(0.98);
    }

    .stats-icon {
      width: 40px;
      height: 40px;
      border-radius: 20px;
      background: rgba(255, 85, 0, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .stats-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 2px;

      .stats-number {
        font-size: 18px;
        font-weight: 600;
        color: #333;
      }

      .stats-label {
        font-size: 12px;
        color: #666;
      }
    }
  }
}

.search-section {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 15px;
  background: white;
  border-bottom: 1px solid #eee;

  .search-bar {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 8px;
    background: #f5f5f5;
    border-radius: 20px;
    padding: 8px 12px;

    .search-input {
      flex: 1;
      font-size: 14px;
      color: #333;
      background: transparent;
      border: none;
      outline: none;

      &::placeholder {
        color: #999;
      }
    }

    .clear-btn {
      padding: 2px;
      cursor: pointer;

      &:active {
        opacity: 0.7;
      }
    }
  }

  .filter-btn {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 8px 12px;
    background: #fff0e6;
    border-radius: 15px;
    font-size: 12px;
    color: #ff5500;
    cursor: pointer;

    &:active {
      opacity: 0.7;
    }
  }
}

.status-filter {
  background: white;
  padding: 15px 20px;
  border-bottom: 1px solid #eee;

  .filter-tabs {
    display: flex;
    gap: 20px;

    .filter-tab {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 16px;
      border-radius: 20px;
      cursor: pointer;
      transition: all 0.2s;
      background: #f5f5f5;

      &.active {
        background: #fff0e6;
        color: #ff5500;

        .tab-count {
          background: #ff5500;
          color: white;
        }
      }

      &:active {
        opacity: 0.7;
      }

      .tab-text {
        font-size: 14px;
        font-weight: 500;
      }

      .tab-count {
        background: #e0e0e0;
        color: #666;
        font-size: 12px;
        min-width: 18px;
        height: 18px;
        border-radius: 9px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 6px;
        font-weight: 600;
      }
    }
  }
}

.banner-section {
  margin: 10px;
  border-radius: 8px;
  overflow: hidden;

  .banner-swiper {
    height: 120px;

    .banner-image {
      width: 100%;
      height: 100%;
    }
  }
}

.category-filter {
  background: white;
  padding: 15px 0;
  border-bottom: 1px solid #eee;

  .category-scroll {
    white-space: nowrap;

    .category-list {
      display: flex;
      padding: 0 20px;
      gap: 20px;

      .category-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 5px;
        padding: 8px 12px;
        border-radius: 20px;
        cursor: pointer;
        transition: all 0.2s;
        white-space: nowrap;

        &.active {
          background: #fff0e6;
          color: #ff5500;
        }

        &:active {
          opacity: 0.7;
        }

        .category-icon {
          width: 24px;
          height: 24px;
        }

        .category-name {
          font-size: 12px;
          font-weight: 500;
        }

        .category-count {
          font-size: 10px;
          color: #999;
        }
      }
    }
  }
}

.coupon-list {
  padding: 10px;

  .loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;

    .loading-text {
      font-size: 12px;
      color: #666;
      margin-top: 10px;
    }
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 80px 20px;

    .empty-text {
      font-size: 14px;
      color: #666;
      margin-top: 10px;
    }

    .empty-tip {
      font-size: 12px;
      color: #999;
      margin-top: 5px;
    }
  }

  .error-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 80px 20px;

    .error-text {
      font-size: 16px;
      color: #333;
      margin-top: 15px;
      font-weight: 600;
    }

    .error-message {
      font-size: 14px;
      color: #666;
      margin-top: 10px;
      text-align: center;
      line-height: 1.5;
    }

    .error-actions {
      margin-top: 20px;

      .action-btn {
        background: #ff5500;
        color: white;
        padding: 12px 24px;
        border-radius: 20px;
        font-size: 14px;
        cursor: pointer;

        &:active {
          opacity: 0.8;
        }
      }
    }
  }

  .coupon-items {
    display: flex;
    flex-direction: column;
    gap: 10px;

    .coupon-item-wrapper {
      position: relative;
      transition: all 0.3s ease;

      &.claimed {
        opacity: 0.7;
      }

      .claimed-overlay {
        position: absolute;
        top: 0;
        right: 0;
        z-index: 10;

        .claimed-badge {
          display: flex;
          align-items: center;
          gap: 4px;
          background: rgba(0, 200, 81, 0.9);
          color: white;
          padding: 6px 12px;
          border-radius: 0 8px 0 12px;
          font-size: 12px;
          font-weight: 500;
          box-shadow: 0 2px 8px rgba(0, 200, 81, 0.3);

          .claimed-text {
            font-size: 12px;
          }
        }
      }
    }
  }

  .load-more {
    padding: 20px;
    text-align: center;

    .loading-more {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      font-size: 12px;
      color: #666;
    }

    .load-more-btn {
      padding: 10px 20px;
      background: white;
      border-radius: 20px;
      font-size: 12px;
      color: #666;
      cursor: pointer;

      &:active {
        opacity: 0.7;
      }
    }
  }

  .no-more {
    padding: 20px;
    text-align: center;
    font-size: 12px;
    color: #999;
  }
}

.batch-claim-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 20px;
  background: white;
  border-top: 1px solid #eee;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  z-index: 100;

  .selected-info {
    font-size: 14px;
    color: #333;
  }

  .batch-claim-btn {
    background: #ff5500;
    color: white;
    padding: 10px 20px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;

    &:active {
      opacity: 0.8;
    }
  }
}
</style>
