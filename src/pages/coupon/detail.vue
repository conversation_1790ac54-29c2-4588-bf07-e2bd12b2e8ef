<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '优惠券详情',
    navigationStyle: 'default',
  },
}
</route>

<template>
  <view class="coupon-detail">
    <view v-if="loading" class="loading-state">
      <wd-loading size="40" />
      <text class="loading-text">加载中...</text>
    </view>

    <view v-else-if="!coupon" class="error-state">
      <wd-icon name="warning" size="60" color="#ddd" />
      <text class="error-text">优惠券不存在</text>
      <view class="error-actions">
        <view class="action-btn" @click="goBack">
          <text>返回</text>
        </view>
      </view>
    </view>

    <view v-else class="detail-content">
      <!-- 优惠券卡片 -->
      <view class="coupon-card-section">
        <CouponCard
          :coupon="coupon"
          :can-use="coupon.can_use"
          :disabled-reason="coupon.reason"
          :show-tags="true"
          mode="view"
        />
      </view>

      <!-- 优惠券信息 -->
      <view class="coupon-info-section">
        <view class="info-header">
          <text class="section-title">优惠券详情</text>
        </view>

        <view class="info-list">
          <view class="info-item">
            <text class="info-label">优惠券名称</text>
            <text class="info-value">{{ coupon.coupon.name }}</text>
          </view>

          <view class="info-item">
            <text class="info-label">优惠类型</text>
            <text class="info-value">{{ getCouponTypeText(coupon.coupon.type) }}</text>
          </view>

          <view class="info-item">
            <text class="info-label">优惠金额</text>
            <text class="info-value">{{ formatCouponAmount(coupon) }}</text>
          </view>

          <view class="info-item">
            <text class="info-label">使用条件</text>
            <text class="info-value">{{ getCouponDescription(coupon) }}</text>
          </view>

          <view v-if="coupon.coupon.merchant_name" class="info-item">
            <text class="info-label">适用商家</text>
            <view class="merchant-info-value">
              <image
                v-if="coupon.coupon.merchant_logo"
                :src="coupon.coupon.merchant_logo"
                class="merchant-logo-small"
                mode="aspectFill"
                @error="handleLogoError"
              />
              <view v-else-if="coupon.coupon.merchant_name" class="merchant-logo-placeholder-small">
                <text class="logo-text-small">{{ coupon.coupon.merchant_name.charAt(0) }}</text>
              </view>
              <text class="merchant-name-text">{{ coupon.coupon.merchant_name }}</text>
            </view>
          </view>

          <view class="info-item">
            <text class="info-label">有效期</text>
            <text class="info-value">{{ formatExpireTime(coupon.coupon.end_time) }}</text>
          </view>

          <view class="info-item">
            <text class="info-label">领取时间</text>
            <text class="info-value">{{ formatDate(coupon.claimed_at) }}</text>
          </view>

          <view v-if="isUsed(coupon)" class="info-item">
            <text class="info-label">使用时间</text>
            <text class="info-value">{{ formatDate(coupon.used_at) }}</text>
          </view>

          <view v-if="coupon.order_id" class="info-item">
            <text class="info-label">使用订单</text>
            <text class="info-value link-text" @click="goToOrder(coupon.order_id)">
              #{{ coupon.order_id }}
            </text>
          </view>
        </view>
      </view>

      <!-- 使用说明 -->
      <view class="usage-rules-section">
        <view class="rules-header">
          <text class="section-title">使用说明</text>
        </view>

        <view class="rules-content">
          <text class="rule-item">1. 优惠券仅限本人使用，不可转让</text>
          <text class="rule-item">2. 优惠券过期后自动失效，无法使用</text>
          <text class="rule-item">3. 部分商品可能不参与优惠活动</text>
          <text class="rule-item">4. 优惠券不可与其他优惠同时使用</text>
          <text class="rule-item">5. 如有疑问请联系客服</text>
        </view>
      </view>

      <!-- 底部操作按钮 -->
      <view class="bottom-actions">
        <view v-if="coupon.can_use" class="action-btn primary" @click="handleUseCoupon">
          <text>立即使用</text>
        </view>
        <view v-else class="action-btn disabled">
          <text>{{ getDisabledText() }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { useCouponStore } from '@/store/coupon'
import CouponCard from '@/components/coupon/CouponCard.vue'
import type { IUserCoupon } from '@/api/coupon.typings'
import { CouponStatus } from '@/api/coupon.typings'
import {
  formatCouponAmount,
  getCouponTypeText,
  getCouponDescription,
  formatExpireTime,
} from '@/utils/coupon'

// 状态管理
const couponStore = useCouponStore()

// 响应式数据
const couponId = ref<number>(0)
const coupon = ref<IUserCoupon | null>(null)
const loading = ref(true)

// 方法
const loadCouponDetail = async () => {
  if (!couponId.value) return

  loading.value = true
  try {
    // 从store中查找优惠券
    const foundCoupon = couponStore.myCoupons.find((c) => c.id === couponId.value)
    if (foundCoupon) {
      coupon.value = foundCoupon
    } else {
      // 如果store中没有，可以调用API获取详情
      // const response = await getCouponDetail(couponId.value)
      // coupon.value = response.data
      coupon.value = null
    }
  } catch (error) {
    console.error('加载优惠券详情失败:', error)
    coupon.value = null
  } finally {
    loading.value = false
  }
}

const formatDate = (dateStr: string) => {
  if (!dateStr || dateStr === '0001-01-01T00:00:00Z') {
    return '未使用'
  }
  const date = new Date(dateStr)
  if (isNaN(date.getTime())) {
    return '无效日期'
  }
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
}

const isUsed = (coupon: IUserCoupon) => {
  return (
    coupon.status === CouponStatus.USED &&
    coupon.used_at &&
    coupon.used_at !== '0001-01-01T00:00:00Z'
  )
}

const getDisabledText = () => {
  if (!coupon.value) return '不可用'

  switch (coupon.value.status) {
    case CouponStatus.USED:
      return '已使用'
    case CouponStatus.EXPIRED:
      return '已过期'
    default:
      return coupon.value.reason || '不可用'
  }
}

const handleUseCoupon = () => {
  if (!coupon.value || !coupon.value.can_use) return

  // 根据优惠券类型跳转到相应页面
  if (coupon.value.coupon.merchant_id) {
    // 商家券，跳转到商家页面
    uni.navigateTo({
      url: `/pages/takeout/merchant-detail?id=${coupon.value.coupon.merchant_id}`,
    })
  } else {
    // 平台券，跳转到首页
    uni.switchTab({
      url: '/pages/index/index',
    })
  }
}

const goToOrder = (orderId: number) => {
  uni.navigateTo({
    url: `/pages/order/detail?id=${orderId}`,
  })
}

const goBack = () => {
  uni.navigateBack()
}

const handleLogoError = () => {
  console.log('商家Logo加载失败')
}

// 页面加载
onLoad((options) => {
  if (options?.id) {
    couponId.value = parseInt(options.id)
  }
})

onMounted(() => {
  loadCouponDetail()
})

// 页面配置
defineOptions({
  navigationBarTitleText: '优惠券详情',
})
</script>

<style scoped lang="scss">
.coupon-detail {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 80px;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100px 20px;

  .loading-text {
    font-size: 14px;
    color: #666;
    margin-top: 15px;
  }
}

.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100px 20px;

  .error-text {
    font-size: 16px;
    color: #666;
    margin-top: 15px;
    margin-bottom: 30px;
  }

  .error-actions {
    .action-btn {
      background: #ff5500;
      color: white;
      padding: 12px 24px;
      border-radius: 20px;
      font-size: 14px;
      cursor: pointer;

      &:active {
        opacity: 0.8;
      }
    }
  }
}

.detail-content {
  .coupon-card-section {
    padding: 15px;
  }

  .coupon-info-section,
  .usage-rules-section {
    background: white;
    margin: 10px 15px;
    border-radius: 8px;
    overflow: hidden;

    .info-header,
    .rules-header {
      padding: 15px;
      border-bottom: 1px solid #eee;

      .section-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
      }
    }

    .info-list {
      padding: 0 15px;

      .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 0;
        border-bottom: 1px solid #f5f5f5;

        &:last-child {
          border-bottom: none;
        }

        .info-label {
          font-size: 14px;
          color: #666;
        }

        .info-value {
          font-size: 14px;
          color: #333;
          text-align: right;
          flex: 1;
          margin-left: 20px;

          &.link-text {
            color: #ff5500;
            cursor: pointer;

            &:active {
              opacity: 0.7;
            }
          }
        }

        .merchant-info-value {
          display: flex;
          align-items: center;
          justify-content: flex-end;
          flex: 1;
          margin-left: 20px;
          gap: 8px;

          .merchant-logo-small {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            flex-shrink: 0;
          }

          .merchant-logo-placeholder-small {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #ff5500;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;

            .logo-text-small {
              font-size: 10px;
              color: white;
              font-weight: 600;
            }
          }

          .merchant-name-text {
            font-size: 14px;
            color: #333;
            flex: 1;
            text-align: right;
          }
        }
      }
    }

    .rules-content {
      padding: 15px;

      .rule-item {
        display: block;
        font-size: 13px;
        color: #666;
        line-height: 1.6;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 15px 20px;
  background: white;
  border-top: 1px solid #eee;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);

  .action-btn {
    width: 100%;
    padding: 15px;
    border-radius: 25px;
    text-align: center;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;

    &.primary {
      background: #ff5500;
      color: white;

      &:active {
        opacity: 0.8;
      }
    }

    &.disabled {
      background: #f5f5f5;
      color: #999;
      cursor: not-allowed;
    }
  }
}
</style>
