<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '即将过期',
    navigationStyle: 'default',
  },
}
</route>

<template>
  <view class="expiring-soon">
    <!-- 页面头部 -->
    <view class="page-header">
      <text class="page-title">即将过期优惠券</text>
      <text class="page-subtitle">抓紧时间使用，避免过期浪费</text>
    </view>

    <!-- 过期时间筛选 -->
    <view class="time-filter">
      <view
        v-for="filter in timeFilters"
        :key="filter.value"
        class="filter-item"
        :class="{ active: selectedTimeFilter === filter.value }"
        @click="selectTimeFilter(filter.value)"
      >
        <text class="filter-text">{{ filter.label }}</text>
        <text v-if="filter.count > 0" class="filter-count">{{ filter.count }}</text>
      </view>
    </view>

    <!-- 优惠券列表 -->
    <view class="coupon-list">
      <view v-if="loading && coupons.length === 0" class="loading-state">
        <wd-loading size="30" />
        <text class="loading-text">加载中...</text>
      </view>

      <view v-else-if="coupons.length === 0" class="empty-state">
        <wd-icon name="time" size="60" color="#ddd" />
        <text class="empty-text">暂无即将过期的优惠券</text>
        <text class="empty-tip">去优惠券中心看看吧</text>
        <view class="empty-actions">
          <view class="action-btn" @click="goToCouponCenter">
            <text>优惠券中心</text>
          </view>
        </view>
      </view>

      <view v-else class="coupon-items">
        <CouponCard
          v-for="coupon in coupons"
          :key="coupon.id"
          :coupon="coupon"
          :can-use="coupon.can_use"
          :disabled-reason="coupon.reason"
          mode="use"
          @click="handleCouponClick"
          @use="handleUseCoupon"
        />
      </view>

      <!-- 加载更多 -->
      <view v-if="hasMore" class="load-more">
        <view v-if="loading" class="loading-more">
          <wd-loading size="20" />
          <text>加载更多...</text>
        </view>
        <view v-else class="load-more-btn" @click="loadMore">
          <text>点击加载更多</text>
        </view>
      </view>

      <!-- 没有更多 -->
      <view v-else-if="coupons.length > 0" class="no-more">
        <text>没有更多优惠券了</text>
      </view>
    </view>

    <!-- 温馨提示 -->
    <view class="tips-section">
      <view class="tips-header">
        <wd-icon name="info" size="16" color="#ff5500" />
        <text class="tips-title">温馨提示</text>
      </view>
      <view class="tips-content">
        <text class="tip-item">• 优惠券过期后将无法使用，请及时使用</text>
        <text class="tip-item">• 部分优惠券可能有使用条件限制</text>
        <text class="tip-item">• 建议优先使用即将过期的优惠券</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { onReachBottom } from '@dcloudio/uni-app'
import { useCouponStore } from '@/store/coupon'
import CouponCard from '@/components/coupon/CouponCard.vue'
import type { IUserCoupon } from '@/api/coupon.typings'

// 状态管理
const couponStore = useCouponStore()

// 响应式数据
const selectedTimeFilter = ref(7) // 默认7天内过期

// 时间筛选选项
const timeFilters = computed(() => [
  {
    value: 1,
    label: '1天内',
    count: coupons.value.filter((coupon) => (coupon.days_to_expire || 0) <= 1).length,
  },
  {
    value: 3,
    label: '3天内',
    count: coupons.value.filter((coupon) => (coupon.days_to_expire || 0) <= 3).length,
  },
  {
    value: 7,
    label: '7天内',
    count: coupons.value.filter((coupon) => (coupon.days_to_expire || 0) <= 7).length,
  },
  {
    value: 30,
    label: '30天内',
    count: coupons.value.filter((coupon) => (coupon.days_to_expire || 0) <= 30).length,
  },
])

// 计算属性
const coupons = computed(() => {
  return couponStore.expiringSoonCoupons.filter(
    (coupon) => (coupon.days_to_expire || 0) <= selectedTimeFilter.value,
  )
})

const loading = computed(() => couponStore.loading.myCoupons)
const hasMore = computed(() => couponStore.pagination.myCoupons.hasMore)

// 方法
const selectTimeFilter = (days: number) => {
  selectedTimeFilter.value = days
}

const loadCoupons = async (refresh = false) => {
  try {
    await couponStore.fetchExpiringSoonCoupons(selectedTimeFilter.value)
  } catch (error) {
    console.error('加载即将过期优惠券失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'error',
    })
  }
}

const loadMore = () => {
  if (!loading.value && hasMore.value) {
    loadCoupons(false)
  }
}

const handleCouponClick = (coupon: IUserCoupon) => {
  // 查看优惠券详情
  uni.navigateTo({
    url: `/pages/coupon/detail?id=${coupon.id}`,
  })
}

const handleUseCoupon = (coupon: IUserCoupon) => {
  if (!coupon.can_use) {
    uni.showToast({
      title: coupon.reason || '优惠券不可用',
      icon: 'none',
    })
    return
  }

  // 根据优惠券类型跳转到相应页面
  if (coupon.coupon.merchant_id) {
    // 商家券，跳转到商家页面
    uni.navigateTo({
      url: `/pages/takeout/merchant-detail?id=${coupon.coupon.merchant_id}`,
    })
  } else {
    // 平台券，跳转到首页
    uni.switchTab({
      url: '/pages/index/index',
    })
  }
}

const goToCouponCenter = () => {
  uni.navigateTo({
    url: '/pages/coupon/center',
  })
}

// 生命周期
onMounted(() => {
  loadCoupons(true)
})

// 触底加载更多
onReachBottom(() => {
  loadMore()
})

// 页面配置
defineOptions({
  navigationBarTitleText: '即将过期',
})
</script>

<style scoped lang="scss">
.expiring-soon {
  min-height: 100vh;
  background: #f5f5f5;
}

.page-header {
  background: linear-gradient(135deg, #ff5500, #ff7700);
  padding: 20px 15px;
  color: white;

  .page-title {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 5px;
    display: block;
  }

  .page-subtitle {
    font-size: 12px;
    opacity: 0.9;
  }
}

.time-filter {
  display: flex;
  background: white;
  padding: 15px;
  gap: 10px;
  border-bottom: 1px solid #eee;

  .filter-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    padding: 10px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s;

    &.active {
      background: #fff0e6;
      color: #ff5500;
    }

    &:active {
      opacity: 0.7;
    }

    .filter-text {
      font-size: 14px;
      font-weight: 500;
    }

    .filter-count {
      background: #ff5500;
      color: white;
      font-size: 10px;
      min-width: 16px;
      height: 16px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 4px;
    }
  }
}

.coupon-list {
  padding: 10px;

  .loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;

    .loading-text {
      font-size: 12px;
      color: #666;
      margin-top: 10px;
    }
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 80px 20px;

    .empty-text {
      font-size: 14px;
      color: #666;
      margin-top: 10px;
      margin-bottom: 5px;
    }

    .empty-tip {
      font-size: 12px;
      color: #999;
      margin-bottom: 20px;
    }

    .empty-actions {
      .action-btn {
        background: #ff5500;
        color: white;
        padding: 10px 20px;
        border-radius: 20px;
        font-size: 14px;
        cursor: pointer;

        &:active {
          opacity: 0.8;
        }
      }
    }
  }

  .coupon-items {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .load-more {
    padding: 20px;
    text-align: center;

    .loading-more {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      font-size: 12px;
      color: #666;
    }

    .load-more-btn {
      padding: 10px 20px;
      background: white;
      border-radius: 20px;
      font-size: 12px;
      color: #666;
      cursor: pointer;

      &:active {
        opacity: 0.7;
      }
    }
  }

  .no-more {
    padding: 20px;
    text-align: center;
    font-size: 12px;
    color: #999;
  }
}

.tips-section {
  background: white;
  margin: 10px;
  border-radius: 8px;
  padding: 15px;

  .tips-header {
    display: flex;
    align-items: center;
    gap: 5px;
    margin-bottom: 10px;

    .tips-title {
      font-size: 14px;
      font-weight: 500;
      color: #333;
    }
  }

  .tips-content {
    display: flex;
    flex-direction: column;
    gap: 5px;

    .tip-item {
      font-size: 12px;
      color: #666;
      line-height: 1.4;
    }
  }
}
</style>
