<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '我的优惠券',
    navigationStyle: 'default',
  },
}
</route>

<template>
  <view class="my-coupons">
    <!-- 统计信息 -->
    <view class="stats-section">
      <view class="stats-card">
        <text class="stats-number">{{ couponStats?.unused_coupons || 0 }}</text>
        <text class="stats-label">未使用</text>
      </view>
      <view class="stats-card">
        <text class="stats-number">{{ couponStats?.used_coupons || 0 }}</text>
        <text class="stats-label">已使用</text>
      </view>
      <view class="stats-card">
        <text class="stats-number">¥{{ couponStats?.total_saved || 0 }}</text>
        <text class="stats-label">已节省</text>
      </view>
      <view class="stats-card" @click="goToExpiringSoon">
        <text class="stats-number">{{ couponStats?.expiring_soon || 0 }}</text>
        <text class="stats-label">即将过期</text>
      </view>
    </view>

    <!-- 快捷操作 -->
    <view class="quick-actions">
      <view class="action-item" @click="goToCouponCenter">
        <wd-icon name="gift" size="20" color="#ff5500" />
        <text class="action-text">优惠券中心</text>
      </view>
      <view class="action-item" @click="goToExpiringSoon">
        <wd-icon name="time" size="20" color="#ffa500" />
        <text class="action-text">即将过期</text>
      </view>
      <view class="action-item" @click="goToUsageHistory">
        <wd-icon name="history" size="20" color="#00c851" />
        <text class="action-text">使用记录</text>
      </view>
    </view>

    <!-- 状态筛选 -->
    <view class="status-filter">
      <view
        v-for="tab in statusTabs"
        :key="tab.value"
        class="filter-tab"
        :class="{ active: selectedStatus === tab.value }"
        @click="selectStatus(tab.value)"
      >
        <text class="tab-text">{{ tab.label }}</text>
        <text v-if="tab.count > 0" class="tab-count">{{ tab.count }}</text>
      </view>
    </view>

    <!-- 优惠券列表 -->
    <view class="coupon-list">
      <view v-if="loading && coupons.length === 0" class="loading-state">
        <wd-loading size="30" />
        <text class="loading-text">加载中...</text>
      </view>

      <view v-else-if="coupons.length === 0" class="empty-state">
        <wd-icon name="coupon" size="60" color="#ddd" />
        <text class="empty-text">{{ getEmptyText() }}</text>
        <view class="empty-actions">
          <view class="action-btn" @click="goToCouponCenter">
            <text>去领券</text>
          </view>
        </view>
      </view>

      <view v-else class="coupon-items">
        <CouponCard
          v-for="coupon in coupons"
          :key="coupon.id"
          :coupon="coupon"
          :can-use="coupon.can_use"
          :disabled-reason="coupon.reason"
          mode="use"
          @click="handleCouponClick"
          @use="handleUseCoupon"
        />
      </view>

      <!-- 加载更多 -->
      <view v-if="hasMore" class="load-more">
        <view v-if="loading" class="loading-more">
          <wd-loading size="20" />
          <text>加载更多...</text>
        </view>
        <view v-else class="load-more-btn" @click="loadMore">
          <text>点击加载更多</text>
        </view>
      </view>

      <!-- 没有更多 -->
      <view v-else-if="coupons.length > 0" class="no-more">
        <text>没有更多优惠券了</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { onReachBottom } from '@dcloudio/uni-app'
import { useCouponStore } from '@/store/coupon'
import CouponCard from '@/components/coupon/CouponCard.vue'
import type { IUserCoupon } from '@/api/coupon.typings'
import { CouponStatus } from '@/api/coupon.typings'

// 状态管理
const couponStore = useCouponStore()

// 响应式数据
const selectedStatus = ref<CouponStatus | null>(null)

// 状态选项
const statusTabs = computed(() => [
  {
    value: null,
    label: '全部',
    count: couponStore.myCoupons?.length || 0,
  },
  {
    value: CouponStatus.UNUSED,
    label: '未使用',
    count: couponStore.unusedCoupons?.length || 0,
  },
  {
    value: CouponStatus.USED,
    label: '已使用',
    count: couponStore.usedCoupons?.length || 0,
  },
  {
    value: CouponStatus.EXPIRED,
    label: '已过期',
    count: couponStore.expiredCoupons?.length || 0,
  },
])

// 计算属性
const coupons = computed(() => {
  switch (selectedStatus.value) {
    case null:
      return couponStore.myCoupons || []
    case CouponStatus.UNUSED:
      return couponStore.unusedCoupons || []
    case CouponStatus.USED:
      return couponStore.usedCoupons || []
    case CouponStatus.EXPIRED:
      return couponStore.expiredCoupons || []
    default:
      return couponStore.myCoupons || []
  }
})

const loading = computed(() => couponStore.loading.myCoupons)
const hasMore = computed(() => couponStore.pagination.myCoupons.hasMore)
const couponStats = computed(() => couponStore.couponStats)

// 方法
const selectStatus = (status: CouponStatus | null) => {
  selectedStatus.value = status
  loadCoupons(true)
}

const loadCoupons = async (refresh = false) => {
  try {
    await couponStore.fetchMyCoupons({
      status: selectedStatus.value || undefined,
      refresh,
    })
  } catch (error) {
    console.error('加载优惠券失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'error',
    })
  }
}

const loadMore = () => {
  if (!loading.value && hasMore.value) {
    loadCoupons(false)
  }
}

const handleCouponClick = (coupon: IUserCoupon) => {
  // 查看优惠券详情
  uni.navigateTo({
    url: `/pages/coupon/detail?id=${coupon.id}`,
  })
}

const handleUseCoupon = (coupon: IUserCoupon) => {
  if (!coupon.can_use) {
    uni.showToast({
      title: coupon.reason || '优惠券不可用',
      icon: 'none',
    })
    return
  }

  // 根据优惠券类型跳转到相应页面
  if (coupon.coupon.merchant_id) {
    // 商家券，跳转到商家页面
    uni.navigateTo({
      url: `/pages/takeout/merchant-detail?id=${coupon.coupon.merchant_id}`,
    })
  } else {
    // 平台券，跳转到首页
    uni.switchTab({
      url: '/pages/index/index',
    })
  }
}

const getEmptyText = () => {
  switch (selectedStatus.value) {
    case CouponStatus.UNUSED:
      return '暂无未使用的优惠券'
    case CouponStatus.USED:
      return '暂无已使用的优惠券'
    case CouponStatus.EXPIRED:
      return '暂无已过期的优惠券'
    default:
      return '暂无优惠券'
  }
}

const goToCouponCenter = () => {
  uni.navigateTo({
    url: '/pages/coupon/center',
  })
}

const goToExpiringSoon = () => {
  uni.navigateTo({
    url: '/pages/coupon/expiring-soon',
  })
}

const goToUsageHistory = () => {
  uni.navigateTo({
    url: '/pages/coupon/usage-history',
  })
}

// 生命周期
onMounted(() => {
  loadCoupons(true)
})

// 触底加载更多
onReachBottom(() => {
  loadMore()
})

// 页面配置
defineOptions({
  navigationBarTitleText: '我的优惠券',
})
</script>

<style scoped lang="scss">
.my-coupons {
  min-height: 100vh;
  background: #f5f5f5;
}

.stats-section {
  display: flex;
  padding: 15px 10px;
  background: white;
  margin-bottom: 10px;

  .stats-card {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    padding: 10px;
    cursor: pointer;

    &:active {
      opacity: 0.7;
    }

    .stats-number {
      font-size: 20px;
      font-weight: 600;
      color: #ff5500;
    }

    .stats-label {
      font-size: 12px;
      color: #666;
    }
  }
}

.quick-actions {
  display: flex;
  background: white;
  margin-bottom: 10px;
  border-radius: 8px;
  overflow: hidden;

  .action-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 15px 10px;
    cursor: pointer;
    transition: all 0.2s;

    &:not(:last-child) {
      border-right: 1px solid #f5f5f5;
    }

    &:active {
      background: #f8f8f8;
    }

    .action-text {
      font-size: 12px;
      color: #666;
    }
  }
}

.status-filter {
  display: flex;
  background: white;
  border-bottom: 1px solid #eee;

  .filter-tab {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    padding: 15px 10px;
    cursor: pointer;
    position: relative;

    &.active {
      color: #ff5500;

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 30px;
        height: 2px;
        background: #ff5500;
      }
    }

    &:active {
      opacity: 0.7;
    }

    .tab-text {
      font-size: 14px;
      font-weight: 500;
    }

    .tab-count {
      background: #ff5500;
      color: white;
      padding: 1px 5px;
      border-radius: 8px;
      font-size: 10px;
      min-width: 16px;
      text-align: center;
    }
  }
}

.coupon-list {
  padding: 10px;

  .loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;

    .loading-text {
      font-size: 12px;
      color: #666;
      margin-top: 10px;
    }
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 80px 20px;

    .empty-text {
      font-size: 14px;
      color: #666;
      margin-top: 10px;
      margin-bottom: 20px;
    }

    .empty-actions {
      .action-btn {
        background: #ff5500;
        color: white;
        padding: 10px 20px;
        border-radius: 20px;
        font-size: 14px;
        cursor: pointer;

        &:active {
          opacity: 0.8;
        }
      }
    }
  }

  .coupon-items {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .load-more {
    padding: 20px;
    text-align: center;

    .loading-more {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      font-size: 12px;
      color: #666;
    }

    .load-more-btn {
      padding: 10px 20px;
      background: white;
      border-radius: 20px;
      font-size: 12px;
      color: #666;
      cursor: pointer;

      &:active {
        opacity: 0.7;
      }
    }
  }

  .no-more {
    padding: 20px;
    text-align: center;
    font-size: 12px;
    color: #999;
  }
}
</style>
