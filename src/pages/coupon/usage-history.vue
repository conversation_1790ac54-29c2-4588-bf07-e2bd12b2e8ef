<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '使用记录',
    navigationStyle: 'default',
  },
}
</route>

<template>
  <view class="usage-history">
    <!-- 统计概览 -->
    <view class="stats-overview">
      <view class="overview-card">
        <text class="stats-number">{{ totalUsed }}</text>
        <text class="stats-label">累计使用</text>
      </view>
      <view class="overview-card">
        <text class="stats-number">¥{{ totalSaved }}</text>
        <text class="stats-label">累计节省</text>
      </view>
      <view class="overview-card">
        <text class="stats-number">{{ thisMonthUsed }}</text>
        <text class="stats-label">本月使用</text>
      </view>
    </view>

    <!-- 时间筛选 -->
    <view class="time-filter">
      <view
        v-for="filter in timeFilters"
        :key="filter.value"
        class="filter-item"
        :class="{ active: selectedTimeFilter === filter.value }"
        @click="selectTimeFilter(filter.value)"
      >
        <text class="filter-text">{{ filter.label }}</text>
      </view>
    </view>

    <!-- 使用记录列表 -->
    <view class="history-list">
      <view v-if="loading && usageHistory.length === 0" class="loading-state">
        <wd-loading size="30" />
        <text class="loading-text">加载中...</text>
      </view>

      <view v-else-if="usageHistory.length === 0" class="empty-state">
        <wd-icon name="history" size="60" color="#ddd" />
        <text class="empty-text">暂无使用记录</text>
        <text class="empty-tip">去使用一些优惠券吧</text>
        <view class="empty-actions">
          <view class="action-btn" @click="goToCouponCenter">
            <text>优惠券中心</text>
          </view>
        </view>
      </view>

      <view v-else class="history-items">
        <view
          v-for="record in usageHistory"
          :key="record.id"
          class="history-item"
          @click="handleRecordClick(record)"
        >
          <view class="record-left">
            <view class="coupon-info">
              <text class="coupon-name">{{ record.coupon.name }}</text>
              <text class="coupon-desc">{{ getCouponDescription(record) }}</text>
            </view>
            <view class="usage-info">
              <text class="usage-time">{{ formatDate(record.used_at) }}</text>
              <text v-if="record.order_id" class="order-info">订单 #{{ record.order_id }}</text>
            </view>
          </view>
          <view class="record-right">
            <text class="saved-amount">-¥{{ record.discount_amount || record.coupon.amount }}</text>
            <wd-icon name="arrow-right" size="14" color="#999" />
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view v-if="hasMore" class="load-more">
        <view v-if="loading" class="loading-more">
          <wd-loading size="20" />
          <text>加载更多...</text>
        </view>
        <view v-else class="load-more-btn" @click="loadMore">
          <text>点击加载更多</text>
        </view>
      </view>

      <!-- 没有更多 -->
      <view v-else-if="usageHistory.length > 0" class="no-more">
        <text>没有更多记录了</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { onReachBottom } from '@dcloudio/uni-app'
import { useCouponStore } from '@/store/coupon'
import type { IUserCoupon } from '@/api/coupon.typings'
import { getCouponDescription } from '@/utils/coupon'

// 状态管理
const couponStore = useCouponStore()

// 响应式数据
const selectedTimeFilter = ref('all')
const usageHistory = ref<IUserCoupon[]>([])
const loading = ref(false)
const hasMore = ref(true)

// 时间筛选选项
const timeFilters = [
  { value: 'all', label: '全部' },
  { value: 'week', label: '最近一周' },
  { value: 'month', label: '最近一月' },
  { value: 'quarter', label: '最近三月' },
]

// 计算属性
const totalUsed = computed(() => {
  return couponStore.usedCoupons.length
})

const totalSaved = computed(() => {
  return couponStore.usedCoupons
    .reduce((total, coupon) => {
      return total + (coupon.discount_amount || coupon.coupon.amount)
    }, 0)
    .toFixed(2)
})

const thisMonthUsed = computed(() => {
  const now = new Date()
  const thisMonth = now.getMonth()
  const thisYear = now.getFullYear()

  return couponStore.usedCoupons.filter((coupon) => {
    if (!coupon.used_at) return false
    const usedDate = new Date(coupon.used_at)
    return usedDate.getMonth() === thisMonth && usedDate.getFullYear() === thisYear
  }).length
})

// 方法
const selectTimeFilter = (value: string) => {
  selectedTimeFilter.value = value
  loadUsageHistory(true)
}

const loadUsageHistory = async (refresh = false) => {
  if (refresh) {
    usageHistory.value = []
    hasMore.value = true
  }

  loading.value = true
  try {
    // 从store中获取已使用的优惠券
    let filteredCoupons = couponStore.usedCoupons

    // 根据时间筛选
    if (selectedTimeFilter.value !== 'all') {
      const now = new Date()
      let startDate = new Date()

      switch (selectedTimeFilter.value) {
        case 'week':
          startDate.setDate(now.getDate() - 7)
          break
        case 'month':
          startDate.setMonth(now.getMonth() - 1)
          break
        case 'quarter':
          startDate.setMonth(now.getMonth() - 3)
          break
      }

      filteredCoupons = filteredCoupons.filter((coupon) => {
        if (!coupon.used_at) return false
        const usedDate = new Date(coupon.used_at)
        return usedDate >= startDate
      })
    }

    // 按使用时间倒序排列
    filteredCoupons.sort((a, b) => {
      const dateA = new Date(a.used_at || 0)
      const dateB = new Date(b.used_at || 0)
      return dateB.getTime() - dateA.getTime()
    })

    usageHistory.value = filteredCoupons
    hasMore.value = false // 这里简化处理，实际应该支持分页
  } catch (error) {
    console.error('加载使用记录失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'error',
    })
  } finally {
    loading.value = false
  }
}

const loadMore = () => {
  if (!loading.value && hasMore.value) {
    loadUsageHistory(false)
  }
}

const handleRecordClick = (record: IUserCoupon) => {
  if (record.order_id) {
    // 跳转到订单详情
    uni.navigateTo({
      url: `/pages/order/detail?id=${record.order_id}`,
    })
  } else {
    // 跳转到优惠券详情
    uni.navigateTo({
      url: `/pages/coupon/detail?id=${record.id}`,
    })
  }
}

const formatDate = (dateStr: string) => {
  const date = new Date(dateStr)
  const now = new Date()
  const diffDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24))

  if (diffDays === 0) {
    return `今天 ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
  } else if (diffDays === 1) {
    return `昨天 ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
  } else if (diffDays < 7) {
    return `${diffDays}天前`
  } else {
    return `${date.getMonth() + 1}-${date.getDate()}`
  }
}

const goToCouponCenter = () => {
  uni.navigateTo({
    url: '/pages/coupon/center',
  })
}

// 生命周期
onMounted(async () => {
  // 确保优惠券数据已加载
  if (couponStore.myCoupons.length === 0) {
    await couponStore.fetchMyCoupons({ refresh: true })
  }
  loadUsageHistory(true)
})

// 触底加载更多
onReachBottom(() => {
  loadMore()
})

// 页面配置
defineOptions({
  navigationBarTitleText: '使用记录',
})
</script>

<style scoped lang="scss">
.usage-history {
  min-height: 100vh;
  background: #f5f5f5;
}

.stats-overview {
  display: flex;
  padding: 15px;
  gap: 10px;

  .overview-card {
    flex: 1;
    background: white;
    border-radius: 8px;
    padding: 20px 15px;
    text-align: center;

    .stats-number {
      display: block;
      font-size: 20px;
      font-weight: 600;
      color: #ff5500;
      margin-bottom: 5px;
    }

    .stats-label {
      font-size: 12px;
      color: #666;
    }
  }
}

.time-filter {
  display: flex;
  background: white;
  margin: 0 15px 10px;
  border-radius: 8px;
  overflow: hidden;

  .filter-item {
    flex: 1;
    padding: 12px;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s;

    &.active {
      background: #ff5500;
      color: white;
    }

    &:not(.active):active {
      background: #f8f8f8;
    }

    .filter-text {
      font-size: 14px;
      font-weight: 500;
    }
  }
}

.history-list {
  .loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;

    .loading-text {
      font-size: 12px;
      color: #666;
      margin-top: 10px;
    }
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 80px 20px;

    .empty-text {
      font-size: 14px;
      color: #666;
      margin-top: 10px;
      margin-bottom: 5px;
    }

    .empty-tip {
      font-size: 12px;
      color: #999;
      margin-bottom: 20px;
    }

    .empty-actions {
      .action-btn {
        background: #ff5500;
        color: white;
        padding: 10px 20px;
        border-radius: 20px;
        font-size: 14px;
        cursor: pointer;

        &:active {
          opacity: 0.8;
        }
      }
    }
  }

  .history-items {
    padding: 0 15px;

    .history-item {
      background: white;
      border-radius: 8px;
      padding: 15px;
      margin-bottom: 10px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      cursor: pointer;
      transition: all 0.2s;

      &:active {
        opacity: 0.8;
      }

      .record-left {
        flex: 1;

        .coupon-info {
          margin-bottom: 8px;

          .coupon-name {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            display: block;
            margin-bottom: 2px;
          }

          .coupon-desc {
            font-size: 12px;
            color: #666;
          }
        }

        .usage-info {
          display: flex;
          align-items: center;
          gap: 10px;

          .usage-time {
            font-size: 12px;
            color: #999;
          }

          .order-info {
            font-size: 12px;
            color: #ff5500;
          }
        }
      }

      .record-right {
        display: flex;
        align-items: center;
        gap: 8px;

        .saved-amount {
          font-size: 16px;
          font-weight: 600;
          color: #00c851;
        }
      }
    }
  }

  .load-more {
    padding: 20px;
    text-align: center;

    .loading-more {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      font-size: 12px;
      color: #666;
    }

    .load-more-btn {
      padding: 10px 20px;
      background: white;
      border-radius: 20px;
      font-size: 12px;
      color: #666;
      cursor: pointer;

      &:active {
        opacity: 0.7;
      }
    }
  }

  .no-more {
    padding: 20px;
    text-align: center;
    font-size: 12px;
    color: #999;
  }
}
</style>
