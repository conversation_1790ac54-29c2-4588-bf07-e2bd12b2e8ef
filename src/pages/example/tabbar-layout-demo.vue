<!--
TabBar 布局示例页面

展示如何使用 TabBarLayout 组件创建有固定顶部标题栏和底部TabBar的页面
-->
<template>
  <TabBarLayout
    title="TabBar布局示例"
    :show-back="true"
    :show-header="true"
    :show-tabbar="true"
    @back="handleBack"
  >
    <!-- 自定义标题栏右侧按钮 -->
    <template #header-right>
      <view class="header-btn" @click="handleSearch">
        <wd-icon name="search" size="20" color="#333" />
      </view>
    </template>

    <!-- 页面内容 -->
    <view class="demo-content">
      <view class="section">
        <text class="section-title">固定标题栏演示</text>
        <text class="section-desc">顶部标题栏固定在页面顶部，不会随滚动消失</text>
      </view>

      <view class="section">
        <text class="section-title">内容区域</text>
        <text class="section-desc">这里是页面的主要内容区域，可以正常滚动</text>
      </view>

      <!-- 模拟长内容 -->
      <view class="long-content">
        <view v-for="i in 50" :key="i" class="content-item">
          <text>内容项 {{ i }} - 这是一个很长的内容项，用来测试页面滚动效果</text>
        </view>
      </view>

      <view class="section">
        <text class="section-title">TabBar 演示</text>
        <text class="section-desc">底部TabBar固定在页面底部，不会随滚动消失</text>
      </view>

      <view class="feature-list">
        <view class="feature-item">
          <wd-icon name="check" size="16" color="#00c851" />
          <text>固定顶部标题栏</text>
        </view>
        <view class="feature-item">
          <wd-icon name="check" size="16" color="#00c851" />
          <text>可滚动内容区域</text>
        </view>
        <view class="feature-item">
          <wd-icon name="check" size="16" color="#00c851" />
          <text>固定底部TabBar</text>
        </view>
        <view class="feature-item">
          <wd-icon name="check" size="16" color="#00c851" />
          <text>自动安全区域适配</text>
        </view>
      </view>
    </view>
  </TabBarLayout>
</template>

<script setup lang="ts">
import TabBarLayout from '@/components/layout/TabBarLayout.vue'

/**
 * 处理返回按钮
 */
function handleBack() {
  uni.navigateBack({
    fail: () => {
      uni.switchTab({
        url: '/pages/index/index',
      })
    },
  })
}

/**
 * 处理搜索按钮
 */
function handleSearch() {
  uni.showToast({
    title: '搜索功能',
    icon: 'none',
  })
}
</script>

<style lang="scss" scoped>
.demo-content {
  padding: 32rpx;
}

.section {
  margin-bottom: 48rpx;

  .section-title {
    display: block;
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 16rpx;
  }

  .section-desc {
    display: block;
    font-size: 28rpx;
    color: #666;
    line-height: 1.6;
  }
}

.long-content {
  margin: 48rpx 0;

  .content-item {
    padding: 24rpx;
    margin-bottom: 16rpx;
    background-color: #fff;
    border-radius: 16rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

    text {
      font-size: 28rpx;
      color: #333;
      line-height: 1.5;
    }
  }
}

.feature-list {
  .feature-item {
    display: flex;
    align-items: center;
    padding: 24rpx 0;
    border-bottom: 1rpx solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    text {
      margin-left: 16rpx;
      font-size: 28rpx;
      color: #333;
    }
  }
}

.header-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  transition: background-color 0.3s ease;

  &:active {
    background-color: rgba(0, 0, 0, 0.05);
  }
}
</style>
