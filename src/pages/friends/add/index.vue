<!-- 添加好友页面 - 支持通过用户名、手机号、二维码等方式添加好友 -->
<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '添加好友',
    navigationStyle: 'default',
  },
}
</route>

<template>
  <view class="add-friend-container">
    <!-- 搜索栏 -->
    <view class="search-section">
      <wd-search
        v-model="searchKeyword"
        placeholder="输入用户名、手机号或邮箱"
        light
        @search="handleSearch"
        @clear="handleClear"
      >
        <template #suffix>
          <wd-button type="primary" size="small" @click="handleSearch">搜索</wd-button>
        </template>
      </wd-search>
    </view>

    <!-- 快捷添加方式 -->
    <view class="quick-actions">
      <view class="action-item" @tap="scanQRCode">
        <view class="action-icon">
          <wd-icon name="scan" :size="24" color="#4095e5" />
        </view>
        <text class="action-text">扫一扫</text>
      </view>
      <view class="action-item" @tap="showMyQRCode">
        <view class="action-icon">
          <wd-icon name="qr-code" :size="24" color="#4095e5" />
        </view>
        <text class="action-text">我的二维码</text>
      </view>
      <view class="action-item" @tap="addFromContacts">
        <view class="action-icon">
          <wd-icon name="phone" :size="24" color="#4095e5" />
        </view>
        <text class="action-text">手机联系人</text>
      </view>
    </view>

    <!-- 搜索结果 -->
    <view v-if="searchResults.length > 0" class="search-results">
      <view class="section-title">搜索结果</view>
      <view
        v-for="user in searchResults"
        :key="user.id"
        class="user-item"
        hover-class="user-item-hover"
      >
        <image class="user-avatar" :src="user.avatar || defaultAvatar" mode="aspectFill"></image>
        <view class="user-info">
          <view class="user-name">{{ user.nickname || user.username }}</view>
          <view v-if="user.signature" class="user-signature">{{ user.signature }}</view>
        </view>
        <wd-button
          v-if="!user.isFriend"
          type="primary"
          size="small"
          :loading="user.adding"
          @click="addFriend(user)"
        >
          添加
        </wd-button>
        <text v-else class="friend-status">已是好友</text>
      </view>
    </view>

    <!-- 推荐好友 -->
    <view v-if="recommendedFriends.length > 0" class="recommended-section">
      <view class="section-title">推荐好友</view>
      <view
        v-for="user in recommendedFriends"
        :key="user.id"
        class="user-item"
        hover-class="user-item-hover"
      >
        <image class="user-avatar" :src="user.avatar || defaultAvatar" mode="aspectFill"></image>
        <view class="user-info">
          <view class="user-name">{{ user.nickname || user.username }}</view>
          <view v-if="user.reason" class="recommend-reason">{{ user.reason }}</view>
        </view>
        <wd-button type="primary" size="small" :loading="user.adding" @click="addFriend(user)">
          添加
        </wd-button>
      </view>
    </view>

    <!-- 空状态 -->
    <view v-if="!loading && searchResults.length === 0 && searchKeyword" class="empty-state">
      <Empty description="未找到相关用户" />
    </view>

    <!-- 加载状态 -->
    <view v-if="loading" class="loading-state">
      <wd-loading :size="24" />
      <text class="loading-text">搜索中...</text>
    </view>

    <!-- 二维码弹窗 -->
    <wd-popup v-model="showQRCode" position="center" :closable="true">
      <view class="qr-code-popup">
        <view class="qr-header">
          <text class="qr-title">我的二维码</text>
        </view>
        <view class="qr-content">
          <image class="qr-image" :src="myQRCodeUrl" mode="aspectFit"></image>
          <view class="user-card">
            <image
              class="card-avatar"
              :src="userInfo?.avatar || defaultAvatar"
              mode="aspectFill"
            ></image>
            <view class="card-info">
              <text class="card-name">{{ userInfo?.nickname || userInfo?.username }}</text>
              <text v-if="userInfo?.signature" class="card-signature">
                {{ userInfo.signature }}
              </text>
            </view>
          </view>
        </view>
        <view class="qr-actions">
          <wd-button type="primary" @click="saveQRCode">保存到相册</wd-button>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useUserStore } from '@/store/user'
import { toast } from '@/utils/toast'
import Empty from '@/components/Empty/index.vue'

// 用户信息
const userStore = useUserStore()
const userInfo = computed(() => userStore.userInfo)

// 响应式数据
const searchKeyword = ref('')
const searchResults = ref([])
const recommendedFriends = ref([])
const loading = ref(false)
const showQRCode = ref(false)
const myQRCodeUrl = ref('')
const defaultAvatar = '/static/images/default-avatar.png'

// 搜索用户
const handleSearch = async () => {
  if (!searchKeyword.value.trim()) {
    toast.warning('请输入搜索关键词')
    return
  }

  loading.value = true
  try {
    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // 模拟搜索结果
    searchResults.value = [
      {
        id: 1,
        username: 'user123',
        nickname: '张三',
        avatar: '',
        signature: '这是一个测试用户',
        isFriend: false,
        adding: false,
      },
    ]
  } catch (error) {
    console.error('搜索用户失败:', error)
    toast.error('搜索失败，请重试')
  } finally {
    loading.value = false
  }
}

// 清空搜索
const handleClear = () => {
  searchResults.value = []
}

// 添加好友
const addFriend = async (user: any) => {
  user.adding = true
  try {
    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 1000))

    toast.success('好友申请已发送')
    user.isFriend = true
  } catch (error) {
    console.error('添加好友失败:', error)
    toast.error('添加失败，请重试')
  } finally {
    user.adding = false
  }
}

// 扫描二维码
const scanQRCode = () => {
  uni.scanCode({
    success: (res) => {
      console.log('扫描结果:', res.result)
      // 处理扫描结果
      handleQRCodeResult(res.result)
    },
    fail: (err) => {
      console.error('扫描失败:', err)
      toast.error('扫描失败')
    },
  })
}

// 处理二维码扫描结果
const handleQRCodeResult = (result: string) => {
  // 解析二维码内容，提取用户ID
  try {
    const data = JSON.parse(result)
    if (data.type === 'user' && data.userId) {
      // 根据用户ID获取用户信息并添加好友
      getUserInfoAndAdd(data.userId)
    } else {
      toast.error('无效的二维码')
    }
  } catch (error) {
    toast.error('二维码格式错误')
  }
}

// 根据用户ID获取信息并添加
const getUserInfoAndAdd = async (userId: string) => {
  try {
    // 模拟API调用获取用户信息
    const userInfo = {
      id: userId,
      username: 'scanned_user',
      nickname: '扫描用户',
      avatar: '',
      signature: '通过二维码添加',
      isFriend: false,
      adding: false,
    }

    // 添加到搜索结果中
    searchResults.value = [userInfo]
  } catch (error) {
    console.error('获取用户信息失败:', error)
    toast.error('获取用户信息失败')
  }
}

// 显示我的二维码
const showMyQRCode = () => {
  // 生成二维码数据
  const qrData = {
    type: 'user',
    userId: userInfo.value?.id,
    username: userInfo.value?.username,
  }

  // 这里应该调用二维码生成API
  myQRCodeUrl.value = generateQRCode(JSON.stringify(qrData))
  showQRCode.value = true
}

// 生成二维码（模拟）
const generateQRCode = (data: string) => {
  // 实际项目中应该调用二维码生成服务
  return `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(data)}`
}

// 保存二维码到相册
const saveQRCode = () => {
  uni.saveImageToPhotosAlbum({
    filePath: myQRCodeUrl.value,
    success: () => {
      toast.success('已保存到相册')
    },
    fail: () => {
      toast.error('保存失败')
    },
  })
}

// 从手机联系人添加
const addFromContacts = () => {
  // 获取手机联系人权限并导入
  uni.authorize({
    scope: 'scope.addressBook',
    success: () => {
      // 实际项目中应该调用联系人API
      toast.info('功能开发中')
    },
    fail: () => {
      toast.error('需要联系人权限')
    },
  })
}

// 加载推荐好友
const loadRecommendedFriends = async () => {
  try {
    // 模拟API调用
    recommendedFriends.value = [
      {
        id: 2,
        username: 'recommend1',
        nickname: '李四',
        avatar: '',
        reason: '可能认识的人',
        adding: false,
      },
      {
        id: 3,
        username: 'recommend2',
        nickname: '王五',
        avatar: '',
        reason: '共同好友推荐',
        adding: false,
      },
    ]
  } catch (error) {
    console.error('加载推荐好友失败:', error)
  }
}

// 页面加载
onMounted(() => {
  loadRecommendedFriends()
})
</script>

<style lang="scss" scoped>
.add-friend-container {
  background-color: #f5f7fa;
  min-height: 100vh;
}

.search-section {
  padding: 20rpx 30rpx;
  background-color: #fff;
  margin-bottom: 20rpx;
}

.quick-actions {
  display: flex;
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  justify-content: space-around;

  .action-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20rpx;

    .action-icon {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      background-color: #f0f8ff;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 16rpx;
    }

    .action-text {
      font-size: 28rpx;
      color: #333;
    }
  }
}

.search-results,
.recommended-section {
  background-color: #fff;
  margin-bottom: 20rpx;

  .section-title {
    padding: 30rpx;
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
    border-bottom: 1px solid #f0f0f0;
  }
}

.user-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }

  &-hover {
    background-color: #f9f9f9;
  }

  .user-avatar {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    margin-right: 24rpx;
    background-color: #eee;
  }

  .user-info {
    flex: 1;

    .user-name {
      font-size: 32rpx;
      color: #333;
      margin-bottom: 8rpx;
    }

    .user-signature,
    .recommend-reason {
      font-size: 28rpx;
      color: #999;
    }
  }

  .friend-status {
    font-size: 28rpx;
    color: #999;
  }
}

.empty-state,
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 30rpx;

  .loading-text {
    margin-top: 20rpx;
    font-size: 28rpx;
    color: #999;
  }
}

.qr-code-popup {
  width: 600rpx;
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;

  .qr-header {
    padding: 40rpx 30rpx 20rpx;
    text-align: center;
    border-bottom: 1px solid #f0f0f0;

    .qr-title {
      font-size: 36rpx;
      font-weight: 500;
      color: #333;
    }
  }

  .qr-content {
    padding: 40rpx;
    text-align: center;

    .qr-image {
      width: 400rpx;
      height: 400rpx;
      margin-bottom: 30rpx;
    }

    .user-card {
      display: flex;
      align-items: center;
      padding: 20rpx;
      background-color: #f9f9f9;
      border-radius: 16rpx;

      .card-avatar {
        width: 60rpx;
        height: 60rpx;
        border-radius: 50%;
        margin-right: 20rpx;
        background-color: #eee;
      }

      .card-info {
        flex: 1;
        text-align: left;

        .card-name {
          font-size: 30rpx;
          color: #333;
          margin-bottom: 8rpx;
        }

        .card-signature {
          font-size: 26rpx;
          color: #999;
        }
      }
    }
  }

  .qr-actions {
    padding: 20rpx 40rpx 40rpx;
  }
}
</style>
