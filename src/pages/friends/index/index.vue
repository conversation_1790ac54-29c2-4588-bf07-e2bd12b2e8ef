/** * 好友列表页面 * * 此页面用于展示用户的好友列表，并提供添加好友、删除好友等功能 *
通过字母索引可以快速定位到相应好友 */
<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '好友',
    navigationStyle: 'default',
  },
  layout: 'tabbar', // 使用tabbar布局
}
</route>

<template>
  <view class="friends-container">
    <!-- 搜索栏 -->
    <view class="search-bar-wrapper">
      <wd-search
        v-model="searchKeyword"
        placeholder="搜索好友"
        @search="handleSearch"
        @clear="handleSearch"
      />
    </view>

    <!-- 好友列表 -->
    <z-paging
      ref="pagingRef"
      @query="queryFriendList"
      v-model="friendList"
      :refresher-threshold="80"
      :show-scrollbar="false"
    >
      <template #default="{ list }">
        <block v-if="list.length">
          <!-- 操作菜单 -->
          <view class="menu-section">
            <view class="menu-item" @tap="navigateToNewFriends">
              <text class="iconfont icon-add-friend menu-icon"></text>
              <text class="menu-text">新的好友</text>
              <view v-if="newFriendCount > 0" class="menu-badge">{{ newFriendCount }}</view>
              <text class="menu-arrow iconfont icon-arrow-right"></text>
            </view>
            <view class="menu-item" @tap="navigateToFriendGroups">
              <text class="iconfont icon-group menu-icon"></text>
              <text class="menu-text">好友分组</text>
              <text class="menu-arrow iconfont icon-arrow-right"></text>
            </view>
            <view class="menu-item" @tap="navigateToBlacklist">
              <text class="iconfont icon-blacklist menu-icon"></text>
              <text class="menu-text">黑名单</text>
              <text class="menu-arrow iconfont icon-arrow-right"></text>
            </view>
          </view>

          <!-- 好友列表 -->
          <view class="friends-list">
            <template v-for="(group, index) in groupedFriends" :key="group.letter">
              <view class="letter-section">
                <view class="letter-header">{{ group.letter }}</view>
                <view
                  v-for="friend in group.friends"
                  :key="friend.id"
                  class="friend-item"
                  hover-class="friend-item-hover"
                  @tap="handleFriendClick(friend)"
                >
                  <image
                    class="friend-avatar"
                    :src="friend.avatar || defaultAvatar"
                    mode="aspectFill"
                  ></image>
                  <view class="friend-info">
                    <view class="friend-name">{{ friend.nickname || friend.username }}</view>
                    <view v-if="friend.signature" class="friend-signature">
                      {{ friend.signature }}
                    </view>
                  </view>
                </view>
              </view>
            </template>
          </view>
        </block>

        <!-- 无好友展示 -->
        <view v-else class="empty-container">
          <image
            class="empty-image"
            src="/static/images/empty-friends.png"
            mode="aspectFit"
          ></image>
          <text class="empty-text">暂无好友</text>
          <wd-button size="small" type="primary" @tap="navigateToNewFriends">添加好友</wd-button>
        </view>
      </template>
    </z-paging>

    <!-- 好友操作弹窗 -->
    <wd-action-sheet
      v-model="showActionSheet"
      :actions="friendActions"
      title="好友操作"
      cancel-text="取消"
      @select="handleActionSelect"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, computed, reactive, onMounted } from 'vue'
import { toast } from '@/utils/toast'
// 移除自定义 SearchBar 导入

// 好友数据接口
interface FriendItem {
  id: string
  username: string
  nickname?: string
  avatar?: string
  signature?: string
  pinyin?: string // 拼音首字母，用于索引排序
  letter?: string // 分组字母
}

// 数据定义
const searchKeyword = ref('')
const friendList = ref<FriendItem[]>([])
const pagingRef = ref(null)
const defaultAvatar = '/static/images/default-avatar.png'
const newFriendCount = ref(2) // 新好友请求数量
const selectedFriend = ref<FriendItem | null>(null)
const showActionSheet = ref(false)

/**
 * 获取好友列表
 * @param pageNo 页码
 * @param pageSize 每页大小
 */
async function queryFriendList(pageNo, pageSize) {
  try {
    // 模拟API调用，实际项目中应替换为真实接口调用
    const mockData = getMockFriendData()

    setTimeout(() => {
      friendList.value = mockData
      if (pagingRef.value) {
        pagingRef.value.complete(mockData)
      }
    }, 500)
  } catch (error) {
    console.error('获取好友列表失败', error)
    toast('获取好友列表失败，请重试')
    if (pagingRef.value) {
      pagingRef.value.complete(false)
    }
  }
}

/**
 * 处理搜索
 */
function handleSearch() {
  if (!searchKeyword.value) {
    return
  }
  // 实现搜索逻辑
  toast(`搜索: ${searchKeyword.value}`)
}

/**
 * 处理好友点击
 */
function handleFriendClick(friend: FriendItem) {
  selectedFriend.value = friend
  showActionSheet.value = true
}

// 好友操作菜单
const friendActions = [
  { value: 'chat', name: '发送消息' },
  { value: 'detail', name: '查看资料' },
  { value: 'delete', name: '删除好友', color: '#E64340' },
]

/**
 * 处理操作选择
 */
function handleActionSelect(item: { value: string; name: string }) {
  if (!selectedFriend.value) return

  switch (item.value) {
    case 'chat':
      uni.navigateTo({
        url: `/pages/chat/room/index?id=${selectedFriend.value.id}&type=user&name=${encodeURIComponent(selectedFriend.value.nickname || selectedFriend.value.username)}`,
      })
      break
    case 'detail':
      uni.navigateTo({
        url: `/pages/friends/detail/index?id=${selectedFriend.value.id}`,
      })
      break
    case 'delete':
      uni.showModal({
        title: '删除好友',
        content: `确定要删除好友"${selectedFriend.value.nickname || selectedFriend.value.username}"吗？`,
        confirmColor: '#E64340',
        success: (res) => {
          if (res.confirm) {
            deleteFriend(selectedFriend.value!.id)
          }
        },
      })
      break
  }

  showActionSheet.value = false
}

/**
 * 删除好友
 */
async function deleteFriend(friendId: string) {
  try {
    // 模拟API调用，实际项目中应替换为真实接口
    toast('好友删除成功')

    // 从列表中移除
    const index = friendList.value.findIndex((f) => f.id === friendId)
    if (index !== -1) {
      friendList.value.splice(index, 1)
    }
  } catch (error) {
    console.error('删除好友失败', error)
    toast('删除好友失败，请重试')
  }
}

/**
 * 导航到新好友页面
 */
function navigateToNewFriends() {
  uni.navigateTo({
    url: '/pages/friends/new/index',
  })
}

/**
 * 导航到好友分组页面
 */
function navigateToFriendGroups() {
  uni.navigateTo({
    url: '/pages/friends/groups/index',
  })
}

/**
 * 导航到黑名单页面
 */
function navigateToBlacklist() {
  uni.navigateTo({
    url: '/pages/friends/blacklist/index',
  })
}

// 按字母分组的好友列表
const groupedFriends = computed(() => {
  const groups: { letter: string; friends: FriendItem[] }[] = []
  const letterMap: Record<string, FriendItem[]> = {}

  // 根据拼音首字母分组
  friendList.value.forEach((friend) => {
    const letter = friend.letter || '#'
    if (!letterMap[letter]) {
      letterMap[letter] = []
    }
    letterMap[letter].push(friend)
  })

  // 按字母排序
  const letters = Object.keys(letterMap).sort((a, b) => {
    if (a === '#') return 1
    if (b === '#') return -1
    return a.localeCompare(b)
  })

  // 构建分组数据
  letters.forEach((letter) => {
    groups.push({
      letter,
      friends: letterMap[letter],
    })
  })

  return groups
})

/**
 * 获取模拟好友数据
 */
function getMockFriendData(): FriendItem[] {
  return [
    {
      id: '1',
      username: 'zhangsan',
      nickname: '张三',
      avatar:
        'https://img0.baidu.com/it/u=3011519953,1456955339&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500',
      signature: '这是我的个性签名',
      letter: 'Z',
    },
    {
      id: '2',
      username: 'lisi',
      nickname: '李四',
      avatar:
        'https://img0.baidu.com/it/u=1184207853,800554581&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500',
      letter: 'L',
    },
    {
      id: '3',
      username: 'wangwu',
      nickname: '王五',
      avatar:
        'https://img1.baidu.com/it/u=3580597430,1650396106&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500',
      signature: '努力工作，努力生活',
      letter: 'W',
    },
    {
      id: '4',
      username: 'zhaoliu',
      nickname: '赵六',
      letter: 'Z',
    },
    {
      id: '5',
      username: 'sunqi',
      nickname: '孙七',
      avatar:
        'https://img0.baidu.com/it/u=2799437447,3897745936&fm=253&fmt=auto&app=138&f=JPEG?w=512&h=500',
      letter: 'S',
    },
  ]
}
</script>

<style lang="scss" scoped>
.friends-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
}

.search-bar-wrapper {
  padding: 20rpx 30rpx;
  background-color: #fff;
}

.menu-section {
  margin-bottom: 20rpx;
  background-color: #fff;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx 40rpx;
  position: relative;

  &:not(:last-child)::after {
    content: '';
    position: absolute;
    left: 120rpx;
    right: 0;
    bottom: 0;
    height: 1px;
    background-color: #f0f0f0;
    transform: scaleY(0.5);
  }

  .menu-icon {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    background-color: #4095e5;
    color: #fff;
    font-size: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 24rpx;
  }

  .menu-text {
    flex: 1;
    font-size: 32rpx;
    color: #333;
  }

  .menu-badge {
    min-width: 40rpx;
    height: 40rpx;
    padding: 0 10rpx;
    background-color: #ff4d4f;
    color: #fff;
    font-size: 24rpx;
    border-radius: 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16rpx;
  }

  .menu-arrow {
    font-size: 32rpx;
    color: #ccc;
  }
}

.friends-list {
  background-color: #fff;
}

.letter-section {
  .letter-header {
    padding: 16rpx 40rpx;
    background-color: #f5f5f5;
    font-size: 28rpx;
    color: #666;
    font-weight: 500;
    position: sticky;
    top: 0;
    z-index: 10;
  }
}

.friend-item {
  display: flex;
  padding: 20rpx 40rpx;
  background-color: #fff;
  align-items: center;

  &-hover {
    background-color: #f9f9f9;
  }

  .friend-avatar {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    background-color: #eee;
    margin-right: 24rpx;
  }

  .friend-info {
    flex: 1;
    overflow: hidden;

    .friend-name {
      font-size: 32rpx;
      color: #333;
      margin-bottom: 8rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .friend-signature {
      font-size: 26rpx;
      color: #999;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 200rpx;

  .empty-image {
    width: 240rpx;
    height: 240rpx;
    margin-bottom: 30rpx;
  }

  .empty-text {
    font-size: 28rpx;
    color: #999;
    margin-bottom: 30rpx;
  }
}
</style>
