<!-- 创建群组页面 - 支持选择好友创建群聊 -->
<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '创建群组',
    navigationStyle: 'default',
  },
}
</route>

<template>
  <view class="create-group-container">
    <!-- 群组信息设置 -->
    <view class="group-info-section">
      <view class="section-title">群组信息</view>

      <!-- 群头像 -->
      <view class="group-avatar-item">
        <text class="item-label">群头像</text>
        <view class="avatar-upload" @tap="selectGroupAvatar">
          <image
            v-if="groupInfo.avatar"
            class="group-avatar"
            :src="groupInfo.avatar"
            mode="aspectFill"
          ></image>
          <view v-else class="avatar-placeholder">
            <wd-icon name="camera" :size="32" color="#999" />
            <text class="placeholder-text">设置群头像</text>
          </view>
        </view>
      </view>

      <!-- 群名称 -->
      <view class="form-item">
        <text class="item-label">群名称</text>
        <wd-input
          v-model="groupInfo.name"
          placeholder="请输入群名称"
          :maxlength="20"
          :show-word-limit="true"
          clearable
        />
      </view>

      <!-- 群介绍 -->
      <view class="form-item">
        <text class="item-label">群介绍</text>
        <wd-textarea
          v-model="groupInfo.description"
          placeholder="请输入群介绍（可选）"
          :maxlength="100"
          :show-word-limit="true"
          :auto-height="true"
        />
      </view>
    </view>

    <!-- 选择成员 -->
    <view class="member-section">
      <view class="section-header">
        <text class="section-title">选择成员</text>
        <text class="member-count">已选择 {{ selectedMembers.length }} 人</text>
      </view>

      <!-- 已选成员展示 -->
      <view v-if="selectedMembers.length > 0" class="selected-members">
        <view
          v-for="member in selectedMembers"
          :key="member.id"
          class="selected-member"
          @tap="removeMember(member)"
        >
          <image
            class="member-avatar"
            :src="member.avatar || defaultAvatar"
            mode="aspectFill"
          ></image>
          <text class="member-name">{{ member.nickname || member.username }}</text>
          <view class="remove-icon">
            <wd-icon name="close" :size="16" color="#fff" />
          </view>
        </view>
        <view class="add-more-member" @tap="showMemberSelector = true">
          <view class="add-icon">
            <wd-icon name="add" :size="24" color="#4095e5" />
          </view>
          <text class="add-text">添加</text>
        </view>
      </view>

      <!-- 选择成员按钮 -->
      <view v-else class="select-member-btn" @tap="showMemberSelector = true">
        <wd-icon name="add" :size="24" color="#4095e5" />
        <text class="select-text">选择群成员</text>
      </view>
    </view>

    <!-- 群设置 -->
    <view class="group-settings">
      <view class="section-title">群设置</view>

      <view class="setting-item">
        <text class="setting-label">群聊邀请确认</text>
        <wd-switch v-model="groupInfo.inviteConfirm" />
      </view>

      <view class="setting-item">
        <text class="setting-label">全员禁言</text>
        <wd-switch v-model="groupInfo.muteAll" />
      </view>

      <view class="setting-item">
        <text class="setting-label">群聊置顶</text>
        <wd-switch v-model="groupInfo.isTop" />
      </view>
    </view>

    <!-- 创建按钮 -->
    <view class="create-button-section">
      <wd-button
        type="primary"
        size="large"
        :loading="creating"
        :disabled="!canCreate"
        @click="createGroup"
      >
        创建群组
      </wd-button>
    </view>

    <!-- 成员选择弹窗 -->
    <wd-popup
      v-model="showMemberSelector"
      position="bottom"
      :safe-area-inset-bottom="true"
      :closable="true"
    >
      <view class="member-selector">
        <view class="selector-header">
          <text class="selector-title">选择群成员</text>
          <wd-button type="text" @click="confirmMemberSelection">确定</wd-button>
        </view>

        <!-- 搜索栏 -->
        <view class="search-section">
          <wd-search
            v-model="memberSearchKeyword"
            placeholder="搜索好友"
            light
            @search="searchMembers"
          />
        </view>

        <!-- 好友列表 -->
        <scroll-view class="friend-list" scroll-y>
          <view
            v-for="friend in filteredFriends"
            :key="friend.id"
            class="friend-item"
            :class="{ selected: isMemberSelected(friend) }"
            @tap="toggleMemberSelection(friend)"
          >
            <view class="friend-checkbox">
              <wd-checkbox
                :model-value="isMemberSelected(friend)"
                @change="toggleMemberSelection(friend)"
              />
            </view>
            <image
              class="friend-avatar"
              :src="friend.avatar || defaultAvatar"
              mode="aspectFill"
            ></image>
            <view class="friend-info">
              <text class="friend-name">{{ friend.nickname || friend.username }}</text>
              <text v-if="friend.signature" class="friend-signature">
                {{ friend.signature }}
              </text>
            </view>
          </view>
        </scroll-view>
      </view>
    </wd-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/store/user'
import { toast } from '@/utils/toast'

// 用户信息
const userStore = useUserStore()

// 响应式数据
const groupInfo = ref({
  name: '',
  description: '',
  avatar: '',
  inviteConfirm: true,
  muteAll: false,
  isTop: false,
})

const selectedMembers = ref([])
const friendList = ref([])
const memberSearchKeyword = ref('')
const showMemberSelector = ref(false)
const creating = ref(false)
const defaultAvatar = '/static/images/default-avatar.png'

// 计算属性
const canCreate = computed(() => {
  return groupInfo.value.name.trim() && selectedMembers.value.length >= 2
})

const filteredFriends = computed(() => {
  if (!memberSearchKeyword.value) {
    return friendList.value
  }
  return friendList.value.filter((friend) => {
    const name = friend.nickname || friend.username
    return name.toLowerCase().includes(memberSearchKeyword.value.toLowerCase())
  })
})

// 选择群头像
const selectGroupAvatar = () => {
  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: (res) => {
      const tempFilePath = res.tempFilePaths[0]
      // 这里应该上传图片到服务器
      uploadAvatar(tempFilePath)
    },
    fail: (err) => {
      console.error('选择图片失败:', err)
      toast.error('选择图片失败')
    },
  })
}

// 上传头像
const uploadAvatar = async (filePath: string) => {
  try {
    // 模拟上传
    await new Promise((resolve) => setTimeout(resolve, 1000))
    groupInfo.value.avatar = filePath
    toast.success('头像设置成功')
  } catch (error) {
    console.error('上传头像失败:', error)
    toast.error('上传失败，请重试')
  }
}

// 检查成员是否已选择
const isMemberSelected = (friend: any) => {
  return selectedMembers.value.some((member) => member.id === friend.id)
}

// 切换成员选择状态
const toggleMemberSelection = (friend: any) => {
  const index = selectedMembers.value.findIndex((member) => member.id === friend.id)
  if (index > -1) {
    selectedMembers.value.splice(index, 1)
  } else {
    selectedMembers.value.push(friend)
  }
}

// 移除已选成员
const removeMember = (member: any) => {
  const index = selectedMembers.value.findIndex((m) => m.id === member.id)
  if (index > -1) {
    selectedMembers.value.splice(index, 1)
  }
}

// 确认成员选择
const confirmMemberSelection = () => {
  showMemberSelector.value = false
  memberSearchKeyword.value = ''
}

// 搜索成员
const searchMembers = () => {
  // 搜索逻辑已在计算属性中实现
}

// 创建群组
const createGroup = async () => {
  if (!canCreate.value) {
    toast.warning('请完善群组信息并选择至少2名成员')
    return
  }

  creating.value = true
  try {
    // 构建创建群组的数据
    const createData = {
      name: groupInfo.value.name.trim(),
      description: groupInfo.value.description.trim(),
      avatar: groupInfo.value.avatar,
      members: selectedMembers.value.map((member) => member.id),
      settings: {
        inviteConfirm: groupInfo.value.inviteConfirm,
        muteAll: groupInfo.value.muteAll,
        isTop: groupInfo.value.isTop,
      },
    }

    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 2000))

    toast.success('群组创建成功')

    // 跳转到群聊页面
    uni.redirectTo({
      url: `/pages/chat/room/index?id=new_group_id&type=group&name=${encodeURIComponent(groupInfo.value.name)}`,
    })
  } catch (error) {
    console.error('创建群组失败:', error)
    toast.error('创建失败，请重试')
  } finally {
    creating.value = false
  }
}

// 加载好友列表
const loadFriendList = async () => {
  try {
    // 模拟API调用
    friendList.value = [
      {
        id: 1,
        username: 'friend1',
        nickname: '张三',
        avatar: '',
        signature: '这是张三的个性签名',
      },
      {
        id: 2,
        username: 'friend2',
        nickname: '李四',
        avatar: '',
        signature: '这是李四的个性签名',
      },
      {
        id: 3,
        username: 'friend3',
        nickname: '王五',
        avatar: '',
        signature: '这是王五的个性签名',
      },
      {
        id: 4,
        username: 'friend4',
        nickname: '赵六',
        avatar: '',
        signature: '这是赵六的个性签名',
      },
    ]
  } catch (error) {
    console.error('加载好友列表失败:', error)
    toast.error('加载好友列表失败')
  }
}

// 页面加载
onMounted(() => {
  loadFriendList()
})
</script>

<style lang="scss" scoped>
.create-group-container {
  background-color: #f5f7fa;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

.group-info-section,
.member-section,
.group-settings {
  background-color: #fff;
  margin-bottom: 20rpx;

  .section-title {
    padding: 30rpx;
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
    border-bottom: 1px solid #f0f0f0;
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1px solid #f0f0f0;

    .member-count {
      font-size: 28rpx;
      color: #999;
    }
  }
}

.group-avatar-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1px solid #f0f0f0;

  .item-label {
    width: 160rpx;
    font-size: 30rpx;
    color: #333;
  }

  .avatar-upload {
    flex: 1;
    display: flex;
    justify-content: flex-end;

    .group-avatar {
      width: 120rpx;
      height: 120rpx;
      border-radius: 20rpx;
      background-color: #eee;
    }

    .avatar-placeholder {
      width: 120rpx;
      height: 120rpx;
      border-radius: 20rpx;
      background-color: #f9f9f9;
      border: 2rpx dashed #ddd;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      .placeholder-text {
        font-size: 24rpx;
        color: #999;
        margin-top: 8rpx;
      }
    }
  }
}

.form-item {
  padding: 30rpx;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }

  .item-label {
    display: block;
    font-size: 30rpx;
    color: #333;
    margin-bottom: 20rpx;
  }
}

.selected-members {
  display: flex;
  flex-wrap: wrap;
  padding: 30rpx;
  gap: 20rpx;

  .selected-member {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 120rpx;

    .member-avatar {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      background-color: #eee;
      margin-bottom: 8rpx;
    }

    .member-name {
      font-size: 24rpx;
      color: #333;
      text-align: center;
      max-width: 100%;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .remove-icon {
      position: absolute;
      top: -8rpx;
      right: 8rpx;
      width: 32rpx;
      height: 32rpx;
      border-radius: 50%;
      background-color: #ff4757;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .add-more-member {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 120rpx;

    .add-icon {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      background-color: #f0f8ff;
      border: 2rpx dashed #4095e5;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 8rpx;
    }

    .add-text {
      font-size: 24rpx;
      color: #4095e5;
    }
  }
}

.select-member-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60rpx 30rpx;
  border: 2rpx dashed #ddd;
  margin: 30rpx;
  border-radius: 16rpx;
  background-color: #f9f9f9;

  .select-text {
    margin-left: 16rpx;
    font-size: 30rpx;
    color: #4095e5;
  }
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }

  .setting-label {
    font-size: 30rpx;
    color: #333;
  }
}

.create-button-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 30rpx;
  background-color: #fff;
  border-top: 1px solid #f0f0f0;
}

.member-selector {
  height: 80vh;
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  display: flex;
  flex-direction: column;

  .selector-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1px solid #f0f0f0;

    .selector-title {
      font-size: 36rpx;
      font-weight: 500;
      color: #333;
    }
  }

  .search-section {
    padding: 20rpx 30rpx;
    border-bottom: 1px solid #f0f0f0;
  }

  .friend-list {
    flex: 1;
    padding: 0 30rpx;

    .friend-item {
      display: flex;
      align-items: center;
      padding: 30rpx 0;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      &.selected {
        background-color: #f0f8ff;
      }

      .friend-checkbox {
        margin-right: 20rpx;
      }

      .friend-avatar {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        margin-right: 24rpx;
        background-color: #eee;
      }

      .friend-info {
        flex: 1;

        .friend-name {
          font-size: 32rpx;
          color: #333;
          margin-bottom: 8rpx;
        }

        .friend-signature {
          font-size: 28rpx;
          color: #999;
        }
      }
    }
  }
}
</style>
