/** * 群组列表页面 * * 此页面用于展示用户参与的群组列表，并提供创建群组、管理群组等功能 */
<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '群组',
    navigationStyle: 'default',
  },
  layout: 'tabbar', // 使用tabbar布局
}
</route>

<template>
  <view class="groups-container">
    <!-- 搜索栏 -->
    <view class="search-bar-wrapper">
      <wd-search
        v-model="searchKeyword"
        placeholder="搜索群组"
        @search="handleSearch"
        @clear="handleSearch"
      />
    </view>

    <!-- 群组列表 -->
    <z-paging
      ref="pagingRef"
      @query="queryGroupList"
      v-model="groupList"
      :refresher-threshold="80"
      :show-scrollbar="false"
    >
      <template #default="{ list }">
        <!-- 创建群组 -->
        <view class="create-group-section">
          <view class="create-group-button" @tap="navigateToCreateGroup">
            <text class="create-icon iconfont icon-add"></text>
            <text class="create-text">创建群聊</text>
          </view>
        </view>

        <!-- 群组列表 -->
        <block v-if="list.length">
          <view class="group-list">
            <view
              v-for="group in list"
              :key="group.id"
              class="group-item"
              hover-class="group-item-hover"
              @tap="navigateToChat(group)"
              @longpress="handleLongPress(group)"
            >
              <image
                class="group-avatar"
                :src="group.avatar || defaultGroupAvatar"
                mode="aspectFill"
              ></image>
              <view class="group-info">
                <view class="group-name">{{ group.name }}</view>
                <view class="group-meta">
                  <text class="group-member-count">{{ group.memberCount }}人</text>
                  <text v-if="group.isOwner" class="group-role owner">群主</text>
                  <text v-else-if="group.isAdmin" class="group-role admin">管理员</text>
                </view>
              </view>
              <text class="group-arrow iconfont icon-arrow-right"></text>
            </view>
          </view>
        </block>

        <!-- 无群组展示 -->
        <view v-else class="empty-container">
          <image class="empty-image" src="/static/images/empty-group.png" mode="aspectFit"></image>
          <text class="empty-text">暂无群组</text>
          <wd-button size="small" type="primary" @tap="navigateToCreateGroup">创建群组</wd-button>
        </view>
      </template>
    </z-paging>

    <!-- 群组操作弹窗 -->
    <wd-action-sheet
      v-model="showActionSheet"
      :actions="groupActions"
      title="群组操作"
      cancel-text="取消"
      @select="handleActionSelect"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { toast } from '@/utils/toast'

// 群组数据接口
interface GroupItem {
  id: string
  name: string
  avatar?: string
  memberCount: number
  isOwner: boolean
  isAdmin: boolean
  notice?: string
  createTime: number
}

// 数据定义
const searchKeyword = ref('')
const groupList = ref<GroupItem[]>([])
const pagingRef = ref(null)
const defaultGroupAvatar = '/static/images/default-group.png'
const selectedGroup = ref<GroupItem | null>(null)
const showActionSheet = ref(false)

/**
 * 获取群组列表
 * @param pageNo 页码
 * @param pageSize 每页大小
 */
async function queryGroupList(pageNo, pageSize) {
  try {
    // 模拟API调用，实际项目中应替换为真实接口调用
    const mockData = getMockGroupData()

    setTimeout(() => {
      groupList.value = mockData
      if (pagingRef.value) {
        pagingRef.value.complete(mockData)
      }
    }, 500)
  } catch (error) {
    console.error('获取群组列表失败', error)
    toast('获取群组列表失败，请重试')
    if (pagingRef.value) {
      pagingRef.value.complete(false)
    }
  }
}

/**
 * 处理搜索
 */
function handleSearch() {
  if (!searchKeyword.value) return
  // 实现搜索逻辑
  toast(`搜索: ${searchKeyword.value}`)
}

/**
 * 跳转到聊天页面
 */
function navigateToChat(group: GroupItem) {
  uni.navigateTo({
    url: `/pages/chat/room/index?id=${group.id}&type=group&name=${encodeURIComponent(group.name)}`,
  })
}

/**
 * 长按群组项
 */
function handleLongPress(group: GroupItem) {
  selectedGroup.value = group
  showActionSheet.value = true
}

/**
 * 跳转到创建群组页面
 */
function navigateToCreateGroup() {
  uni.navigateTo({
    url: '/pages/groups/create/index',
  })
}

// 群组操作列表
const groupActions = computed(() => {
  if (!selectedGroup.value) return []

  const actions = [
    { value: 'chat', name: '发送消息' },
    { value: 'detail', name: '群聊详情' },
  ]

  // 群主有解散群的权限
  if (selectedGroup.value.isOwner) {
    actions.push({ value: 'dismiss', name: '解散群聊', color: '#E64340' })
  }
  // 非群主可以退出群
  else {
    actions.push({ value: 'quit', name: '退出群聊', color: '#E64340' })
  }

  return actions
})

/**
 * 处理操作选择
 */
function handleActionSelect(item: { value: string; name: string }) {
  if (!selectedGroup.value) return

  switch (item.value) {
    case 'chat':
      navigateToChat(selectedGroup.value)
      break
    case 'detail':
      uni.navigateTo({
        url: `/pages/groups/detail/index?id=${selectedGroup.value.id}`,
      })
      break
    case 'dismiss':
      uni.showModal({
        title: '解散群聊',
        content: `确定要解散"${selectedGroup.value.name}"群聊吗？`,
        confirmColor: '#E64340',
        success: (res) => {
          if (res.confirm) {
            dismissGroup(selectedGroup.value!.id)
          }
        },
      })
      break
    case 'quit':
      uni.showModal({
        title: '退出群聊',
        content: `确定要退出"${selectedGroup.value.name}"群聊吗？`,
        confirmColor: '#E64340',
        success: (res) => {
          if (res.confirm) {
            quitGroup(selectedGroup.value!.id)
          }
        },
      })
      break
  }

  showActionSheet.value = false
}

/**
 * 解散群组
 */
async function dismissGroup(groupId: string) {
  try {
    // 模拟API调用，实际项目中应替换为真实接口
    toast('群聊解散成功')

    // 从列表中移除
    const index = groupList.value.findIndex((g) => g.id === groupId)
    if (index !== -1) {
      groupList.value.splice(index, 1)
    }
  } catch (error) {
    console.error('解散群聊失败', error)
    toast('解散群聊失败，请重试')
  }
}

/**
 * 退出群组
 */
async function quitGroup(groupId: string) {
  try {
    // 模拟API调用，实际项目中应替换为真实接口
    toast('退出群聊成功')

    // 从列表中移除
    const index = groupList.value.findIndex((g) => g.id === groupId)
    if (index !== -1) {
      groupList.value.splice(index, 1)
    }
  } catch (error) {
    console.error('退出群聊失败', error)
    toast('退出群聊失败，请重试')
  }
}

/**
 * 获取模拟群组数据
 */
function getMockGroupData(): GroupItem[] {
  return [
    {
      id: '1',
      name: '产品设计小组',
      avatar:
        'https://img1.baidu.com/it/u=3580597430,1650396106&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500',
      memberCount: 15,
      isOwner: true,
      isAdmin: false,
      notice: '本群用于讨论产品设计相关话题',
      createTime: Date.now() - 30 * 24 * 60 * 60 * 1000,
    },
    {
      id: '2',
      name: '技术交流群',
      avatar:
        'https://img0.baidu.com/it/u=2799437447,3897745936&fm=253&fmt=auto&app=138&f=JPEG?w=512&h=500',
      memberCount: 32,
      isOwner: false,
      isAdmin: true,
      notice: '技术交流，互帮互助',
      createTime: Date.now() - 90 * 24 * 60 * 60 * 1000,
    },
    {
      id: '3',
      name: '项目A组',
      memberCount: 8,
      isOwner: false,
      isAdmin: false,
      createTime: Date.now() - 15 * 24 * 60 * 60 * 1000,
    },
    {
      id: '4',
      name: '市场推广讨论',
      memberCount: 20,
      isOwner: false,
      isAdmin: false,
      createTime: Date.now() - 120 * 24 * 60 * 60 * 1000,
    },
  ]
}
</script>

<style lang="scss" scoped>
.groups-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
}

.search-bar-wrapper {
  padding: 20rpx 30rpx;
  background-color: #fff;
}

.create-group-section {
  padding: 30rpx;
}

.create-group-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  height: 90rpx;
  border-radius: 45rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

  .create-icon {
    font-size: 36rpx;
    color: #4095e5;
    margin-right: 10rpx;
  }

  .create-text {
    font-size: 32rpx;
    color: #4095e5;
    font-weight: 500;
  }
}

.group-list {
  background-color: #fff;
}

.group-item {
  display: flex;
  align-items: center;
  padding: 24rpx 30rpx;
  background-color: #fff;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    left: 130rpx;
    right: 0;
    bottom: 0;
    height: 1px;
    background-color: #f0f0f0;
    transform: scaleY(0.5);
  }

  &:last-child::after {
    display: none;
  }

  &-hover {
    background-color: #f9f9f9;
  }

  .group-avatar {
    width: 100rpx;
    height: 100rpx;
    border-radius: 16rpx;
    margin-right: 24rpx;
    background-color: #eee;
  }

  .group-info {
    flex: 1;
    overflow: hidden;

    .group-name {
      font-size: 32rpx;
      color: #333;
      margin-bottom: 10rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .group-meta {
      display: flex;
      align-items: center;

      .group-member-count {
        font-size: 26rpx;
        color: #999;
        margin-right: 16rpx;
      }

      .group-role {
        font-size: 24rpx;
        padding: 2rpx 12rpx;
        border-radius: 10rpx;

        &.owner {
          background-color: rgba(64, 149, 229, 0.1);
          color: #4095e5;
        }

        &.admin {
          background-color: rgba(102, 102, 102, 0.1);
          color: #666;
        }
      }
    }
  }

  .group-arrow {
    font-size: 32rpx;
    color: #ccc;
  }
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 200rpx;

  .empty-image {
    width: 240rpx;
    height: 240rpx;
    margin-bottom: 30rpx;
  }

  .empty-text {
    font-size: 28rpx;
    color: #999;
    margin-bottom: 30rpx;
  }
}
</style>
