<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '登录',
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="login-container">
    <!-- 背景装饰元素 -->
    <view class="bg-decoration bg-circle-1"></view>
    <view class="bg-decoration bg-circle-2"></view>
    <view class="bg-decoration bg-circle-3"></view>

    <view class="login-header">
      <image class="login-logo" :src="appLogo" mode="aspectFit"></image>
      <view class="login-title">{{ appTitle }}</view>
    </view>
    <view class="login-form">
      <view class="welcome-text">欢迎登录</view>
      <view class="login-desc">请输入您的账号和密码</view>
      <view class="login-input-group">
        <view class="input-wrapper">
          <wd-input
            v-model="loginForm.username"
            prefix-icon="user"
            placeholder="请输入用户名"
            clearable
            class="login-input"
            :border="false"
            required
          ></wd-input>
          <view class="input-bottom-line"></view>
        </view>
        <view class="input-wrapper">
          <wd-input
            v-model="loginForm.password"
            prefix-icon="lock-on"
            placeholder="请输入密码"
            clearable
            show-password
            class="login-input"
            :border="false"
            required
          ></wd-input>
          <view class="input-bottom-line"></view>
        </view>
      </view>
      <!-- 登录按钮组 -->
      <view class="login-buttons">
        <!-- 账号密码登录按钮 -->
        <wd-button
          type="primary"
          size="large"
          block
          @click="handleAccountLogin"
          class="account-login-btn"
        >
          <wd-icon name="right" size="18px" class="login-icon"></wd-icon>
          登录
        </wd-button>
        <!-- 微信小程序一键登录按钮 -->
        <!-- #ifdef MP-WEIXIN -->
        <view class="divider">
          <view class="divider-line"></view>
          <view class="divider-text">或</view>
          <view class="divider-line"></view>
        </view>
        <wd-button
          type="info"
          size="large"
          block
          plain
          @click="handleWechatLogin"
          class="wechat-login-btn"
        >
          微信一键登录
        </wd-button>
        <!-- #endif -->
      </view>
    </view>
    <!-- 隐私协议勾选和记住登录 -->
    <view class="login-options">
      <view class="privacy-agreement">
        <wd-checkbox
          v-model="agreePrivacy"
          shape="square"
          class="privacy-checkbox"
          active-color="var(--wot-color-theme, #1989fa)"
        >
          <view class="agreement-text">
            我已阅读并同意
            <text class="agreement-link" @click.stop="handleAgreement('user')">《用户协议》</text>
            和
            <text class="agreement-link" @click.stop="handleAgreement('privacy')">
              《隐私政策》
            </text>
          </view>
        </wd-checkbox>
      </view>
      <!-- 记住登录选项 -->
      <view class="remember-login">
        <wd-checkbox
          v-model="rememberLogin"
          shape="square"
          class="remember-checkbox"
          active-color="var(--wot-color-theme, #1989fa)"
        >
          <text class="remember-text">记住登录</text>
        </wd-checkbox>
      </view>
    </view>
    <view class="login-footer"></view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useUserStore } from '@/store/user'
import { useSystemStore } from '@/store/system'
import { isMpWeixin } from '@/utils/platform'
import type { ILoginForm } from '@/api/login'
import { toast } from '@/utils/toast'
import { isTableBar } from '@/utils/index'
const redirectRoute = ref('')

// 获取环境变量
const appTitle = ref(import.meta.env.VITE_APP_TITLE || 'Unibest Login')
// 使用systemStore中的siteLogo，如果没有则使用默认值
const appLogo = computed(() => {
  return systemStore.siteLogo || import.meta.env.VITE_APP_LOGO || '/static/logo.svg'
})

// 初始化store
const userStore = useUserStore()
const systemStore = useSystemStore()
// 路由位置
// 用户是否同意隐私政策
const agreePrivacy = ref(true)
// 是否记住登录
const rememberLogin = ref(true)
// 登录表单
const loginForm = ref<ILoginForm>({
  username: 'testuser',
  password: 'admin123',
})

// 页面加载完毕时触发
onLoad(async (option) => {
  // 获取跳转路由
  if (option.redirect) {
    redirectRoute.value = option.redirect
  }

  // 检查并初始化系统配置
  try {
    if (!systemStore.systemInfo || !systemStore.contactInfo) {
      console.log('登录页：初始化系统配置数据')
      await systemStore.initSystemData()
    }
  } catch (error) {
    console.error('登录页：系统配置初始化失败', error)
  }

  // 仅在页面加载时检查一次登录状态
  setTimeout(() => {
    checkLoginAndRedirect()
  }, 100) // 延迟一下确保与其他导航不冲突
})

// 检查登录状态并跳转 - 使用了防抖机制确保不会多次跳转
let isRedirecting = false
const checkLoginAndRedirect = () => {
  // 防止重复跳转
  if (isRedirecting) return

  console.log('登录页：检查用户登录状态')
  if (
    userStore.isLoggedIn &&
    userStore.userInfo.token &&
    (userStore.userInfo.tokenExpiration || 0) > Date.now()
  ) {
    isRedirecting = true
    console.log('登录页：用户已登录，自动跳转')

    // 如果有指定的重定向路径，则跳转到该路径
    if (redirectRoute.value) {
      console.log('登录页：重定向到', redirectRoute.value)
      uni.redirectTo({
        url: decodeURIComponent(redirectRoute.value),
        fail: (err) => {
          console.error('登录页跳转失败:', err)
          isRedirecting = false
        },
      })
    } else {
      // 否则跳转到首页
      console.log('登录页：重定向到首页')
      uni.switchTab({
        url: '/pages/chat/sessions/index',
        fail: (err) => {
          console.error('登录页跳转失败:', err)
          isRedirecting = false
        },
      })
    }
  } else {
    console.log('登录页：用户未登录或token已过期，停留在登录页')
  }
}

// 账号密码登录
async function handleAccountLogin() {
  if (!loginForm.value.username) {
    toast.error('请输入用户名')
    return
  }
  if (!loginForm.value.password) {
    toast.error('请输入密码')
    return
  }
  if (!agreePrivacy.value) {
    toast.error('请先阅读并同意用户协议和隐私政策')
    return
  }

  try {
    // 显示加载中
    uni.showLoading({ title: '登录中...' })

    // 登录请求，传递记住登录的状态
    const loginSuccess = await userStore.login({
      ...loginForm.value,
      rememberLogin: rememberLogin.value,
    })

    // 隐藏加载提示
    uni.hideLoading()

    // 检查登录是否成功
    if (loginSuccess) {
      // 登录成功后跳转
      setTimeout(() => {
        // 如果有重定向地址，跳转到重定向地址
        if (redirectRoute.value) {
          uni.redirectTo({
            url: decodeURIComponent(redirectRoute.value),
          })
        } else {
          // 否则跳转到个人中心页面
          uni.switchTab({
            url: '/pages/user/index',
          })
        }
      }, 1000)
    } else {
      // 登录失败，不跳转
      console.log('登录失败，保持在登录页面')
    }
  } catch (error) {
    console.error('登录异常', error)
    uni.hideLoading()
    // 登录异常时不跳转，保持在登录页面
    toast.error('登录异常，请重试')
    return
  }
}

// 微信登录
const handleWechatLogin = async () => {
  if (!isMpWeixin) {
    toast.info('请在微信小程序中使用此功能')
    return
  }

  // 验证是否同意隐私协议
  if (!agreePrivacy.value) {
    toast.error('请先阅读并同意用户协议和隐私政策')
    return
  }

  try {
    // 显示加载中
    uni.showLoading({ title: '微信登录中...' })

    // 微信登录
    const loginSuccess = await userStore.wxLogin()

    // 隐藏加载提示
    uni.hideLoading()

    // 检查登录是否成功
    if (loginSuccess) {
      // 登录成功后跳转到首页或重定向页面
      const targetUrl = redirectRoute.value || '/pages/index/index'
      if (isTableBar(targetUrl)) {
        uni.switchTab({ url: targetUrl })
      } else {
        uni.redirectTo({ url: targetUrl })
      }
    } else {
      // 登录失败，不跳转
      console.log('微信登录失败，保持在登录页面')
    }
  } catch (error) {
    console.error('微信登录异常', error)
    uni.hideLoading()
    // 登录异常时不跳转，保持在登录页面
    toast.error('微信登录异常，请重试')
    return
  }
}

// 处理协议点击
const handleAgreement = (type: 'user' | 'privacy') => {
  const title = type === 'user' ? '用户协议' : '隐私政策'
  // showToast(`查看${title}`)
  // 实际项目中可以跳转到对应的协议页面
  // uni.navigateTo({
  //   url: `/pages/agreement/${type}`
  // })
}
</script>

<style lang="scss" scoped>
/* 验证码输入框样式 */
.captcha-wrapper {
  .captcha-input {
    :deep(.wd-input__suffix) {
      margin-right: 0;
      padding-right: 0;
    }
  }

  .captcha-image {
    width: 100px;
    height: 36px;
    margin-left: 10px;
    border-radius: 8px;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(to bottom, rgba(255, 255, 255, 0.1), transparent);
      pointer-events: none;
    }

    &:active {
      opacity: 0.8;
      transform: scale(0.96);
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
    }
  }
}

.login-container {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding: 0 70rpx;
  background-color: #ffffff;
  background-image: linear-gradient(
    135deg,
    rgba(25, 137, 250, 0.05) 0%,
    rgba(255, 255, 255, 0) 100%
  );
  position: relative;
  overflow: hidden;
}

/* 背景装饰元素 */
.bg-decoration {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(25, 137, 250, 0.05), rgba(25, 137, 250, 0.1));
  z-index: 0;
  pointer-events: none;
}

.bg-circle-1 {
  width: 500rpx;
  height: 500rpx;
  top: -200rpx;
  right: -200rpx;
  opacity: 0.6;
}

.bg-circle-2 {
  width: 400rpx;
  height: 400rpx;
  bottom: 10%;
  left: -200rpx;
  opacity: 0.4;
}

.bg-circle-3 {
  width: 300rpx;
  height: 300rpx;
  bottom: -100rpx;
  right: 10%;
  opacity: 0.3;
  background: linear-gradient(135deg, rgba(7, 193, 96, 0.05), rgba(7, 193, 96, 0.1));
}

.login-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 120rpx;
  animation: fadeInDown 0.8s ease-out;

  .login-logo {
    width: 200rpx;
    height: 200rpx;
    border-radius: 36rpx;
    box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.12);
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.95);
      box-shadow: 0 6rpx 15rpx rgba(0, 0, 0, 0.1);
    }
  }

  .login-title {
    margin-top: 30rpx;
    font-size: 46rpx;
    font-weight: bold;
    color: #333333;
    letter-spacing: 3rpx;
    text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
  }
}

.login-form {
  flex: 1;
  margin-top: 70rpx;
  animation: fadeIn 0.8s ease-out 0.2s both;

  .welcome-text {
    margin-bottom: 16rpx;
    font-size: 48rpx;
    font-weight: bold;
    color: #333333;
    text-align: center;
    letter-spacing: 1rpx;
  }

  .login-desc {
    margin-bottom: 70rpx;
    font-size: 28rpx;
    color: #888888;
    text-align: center;
  }

  .login-input-group {
    margin-bottom: 60rpx;
    position: relative;
    z-index: 1;

    .input-wrapper {
      position: relative;
      margin-bottom: 50rpx;
      transition: all 0.3s ease;
      border-radius: 16rpx;
      overflow: hidden;

      &:last-child {
        margin-bottom: 0;
      }

      .login-input {
        padding: 12rpx 20rpx;
        background-color: rgba(245, 247, 250, 0.7);
        border-radius: 16rpx;
        transition: all 0.3s ease;

        :deep(.wd-input__inner) {
          font-size: 30rpx;
          color: #333333;
        }

        :deep(.wd-input__placeholder) {
          font-size: 28rpx;
          color: #aaaaaa;
        }

        &:focus-within {
          background-color: rgba(245, 247, 250, 0.95);
          box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.06);
          transform: translateY(-3rpx);
        }
      }

      .input-bottom-line {
        position: absolute;
        bottom: -2rpx;
        left: 5%;
        width: 90%;
        height: 2rpx;
        background: linear-gradient(
          to right,
          transparent,
          var(--wot-color-theme, #1989fa),
          transparent
        );
        transition: transform 0.4s ease;
        transform: scaleX(0);
        opacity: 0.8;
      }

      &:focus-within .input-bottom-line {
        transform: scaleX(1);
      }

      .input-icon {
        margin-right: 16rpx;
        color: #666666;
        transition: color 0.3s ease;
      }

      &:focus-within .input-icon {
        color: var(--wot-color-theme, #1989fa);
      }
    }
  }

  .login-buttons {
    display: flex;
    flex-direction: column;
    gap: 36rpx;

    .account-login-btn {
      height: 96rpx;
      margin-top: 20rpx;
      font-size: 32rpx;
      font-weight: 500;
      letter-spacing: 2rpx;
      border-radius: 48rpx;
      box-shadow: 0 10rpx 20rpx rgba(25, 137, 250, 0.25);
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;

      .login-icon {
        margin-right: 8rpx;
        opacity: 0.8;
        transition: all 0.3s ease;
      }

      &:active {
        box-shadow: 0 5rpx 10rpx rgba(25, 137, 250, 0.2);
        transform: scale(0.98);

        .login-icon {
          transform: translateX(3rpx);
        }
      }
    }

    .divider {
      display: flex;
      align-items: center;
      margin: 24rpx 0;

      .divider-line {
        flex: 1;
        height: 1px;
        background-color: #eeeeee;
      }

      .divider-text {
        padding: 0 24rpx;
        font-size: 24rpx;
        color: #999999;
      }
    }

    .wechat-login-btn {
      height: 96rpx;
      font-size: 32rpx;
      color: #07c160;
      border-color: #07c160;
      border-radius: 48rpx;
      transition: all 0.3s ease;

      .wechat-icon {
        margin-right: 12rpx;
      }

      &:active {
        background-color: rgba(7, 193, 96, 0.08);
        transform: scale(0.98);
      }
    }
  }
}

.login-options {
  margin: 30rpx 0 40rpx;
  animation: fadeIn 0.8s ease-out 0.4s both;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.privacy-agreement {
  display: flex;
  justify-content: center;

  .privacy-checkbox {
    display: flex;
    align-items: center;
  }

  .agreement-text {
    font-size: 26rpx;
    line-height: 1.6;
    color: #666666;

    .agreement-link {
      padding: 0 4rpx;
      font-weight: 500;
      color: var(--wot-color-theme, #1989fa);
      transition: all 0.3s ease;

      &:active {
        opacity: 0.8;
        transform: scale(0.98);
      }
    }
  }
}

.remember-login {
  display: flex;
  justify-content: center;
  margin-top: 10rpx;

  .remember-checkbox {
    display: flex;
    align-items: center;
  }

  .remember-text {
    font-size: 26rpx;
    color: #666666;
  }
}

.login-footer {
  padding: 50rpx 0;
  margin-top: auto;
}

/* 添加动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
