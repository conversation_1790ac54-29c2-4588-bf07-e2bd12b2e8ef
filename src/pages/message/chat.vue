<!--
 * @file 聊天会话列表页面
 * @description 显示聊天会话列表
 * <AUTHOR> Assistant
 * @date 2025-01-27
-->

<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '聊天消息',
  },
}
</route>

<template>
  <view class="chat-sessions-container">
    <!-- 会话列表 -->
    <view class="session-list">
      <!-- 加载状态 -->
      <wd-loading v-if="loading" :size="24" color="#4D8EFF">加载中...</wd-loading>

      <!-- 空状态 -->
      <view v-else-if="sessionList.length === 0" class="empty-state">
        <view class="empty-icon">
          <wd-icon name="chat" :size="60" color="#ddd" />
        </view>
        <text class="empty-text">暂无聊天记录</text>
        <wd-button type="primary" size="small" @click="startNewChat" class="start-chat-btn">
          开始聊天
        </wd-button>
      </view>

      <!-- 会话项 -->
      <view v-else class="list-content">
        <view
          v-for="session in sessionList"
          :key="session.id"
          class="session-item"
          @click="enterChat(session)"
        >
          <view class="session-avatar">
            <image v-if="session.target_avatar" :src="session.target_avatar" mode="aspectFill" />
            <view v-else class="default-avatar">
              <wd-icon name="user" :size="20" color="#fff" />
            </view>
            <wd-badge
              v-if="session.unread_count && session.unread_count > 0"
              :value="session.unread_count"
            />
          </view>

          <view class="session-content">
            <view class="session-header">
              <text class="session-title">
                {{ session.target_name || '未知用户' }}
              </text>
              <text class="session-time">
                {{ formatTime(session.last_message?.created_at || session.updated_at) }}
              </text>
            </view>

            <view class="session-preview">
              <text class="preview-text">
                {{ getPreviewText(session.last_message?.content || '') }}
              </text>
              <view v-if="session.status === 0" class="online-status">
                <view class="online-dot"></view>
                <text class="online-text">活跃</text>
              </view>
            </view>
          </view>

          <view v-if="session.last_message?.sender_type !== 'user'" class="message-status">
            <wd-icon name="check" :size="16" color="#52c41a" />
          </view>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <wd-loadmore v-if="sessionList.length > 0" :state="loadMoreState as any" @loadmore="loadMore" />
  </view>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { useMessageStore } from '@/store/message'
import { formatRelativeTime } from '@/utils/date'
import type { ISessionItem } from '@/api/message.typings'

// 使用消息store
const messageStore = useMessageStore()

// 计算属性
const sessionList = computed(() => messageStore.chatSessions)
const loading = computed(() => messageStore.loading.chatSessions)
const hasMore = computed(() => messageStore.pagination.chatSessions.hasMore)

const loadMoreState = computed(() => {
  if (loading.value) return 'loading'
  if (!hasMore.value) return 'finished'
  return 'more'
})

// 格式化时间
const formatTime = (time: string) => {
  return formatRelativeTime(time)
}

// 获取预览文本
const getPreviewText = (content: string) => {
  if (!content) return '暂无消息'
  return content.length > 30 ? content.substring(0, 30) + '...' : content
}

// 进入聊天
const enterChat = (session: ISessionItem) => {
  console.log('点击会话项，准备进入聊天:', session)
  const url = `/pages/chat/room/index?sessionId=${session.id}&targetName=${encodeURIComponent(session.target_name || '')}`
  console.log('跳转URL:', url)

  uni.navigateTo({
    url: url,
    success: () => {
      console.log('成功跳转到聊天室')
    },
    fail: (err) => {
      console.error('跳转到聊天室失败:', err)
    },
  })
}

// 开始新聊天
const startNewChat = () => {
  uni.navigateTo({
    url: '/pages/chat/contacts/index',
  })
}

// 加载更多
const loadMore = () => {
  messageStore.loadMoreChatSessions()
}

// 方法
const loadSessions = async (refresh = false) => {
  try {
    console.log('开始调用 messageStore.fetchChatSessions，refresh:', refresh)
    await messageStore.fetchChatSessions(undefined, refresh)
    console.log('fetchChatSessions 调用完成')
  } catch (error) {
    console.error('加载聊天会话失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'error',
    })
  }
}

// 页面加载
onLoad((options) => {
  console.log('聊天会话页面加载:', options)
})

onMounted(() => {
  console.log('聊天会话页面 onMounted，开始加载会话列表')
  loadSessions(true)
})
</script>

<style lang="scss" scoped>
.chat-sessions-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.session-list {
  padding: 0;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;

  .empty-icon {
    margin-bottom: 24rpx;
  }

  .empty-text {
    font-size: 28rpx;
    color: #999;
    margin-bottom: 40rpx;
  }

  .start-chat-btn {
    width: 200rpx;
  }
}

.list-content {
  background-color: #fff;
}

.session-item {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;

  &:last-child {
    border-bottom: none;
  }

  &:active {
    background-color: #f8f8f8;
  }
}

.session-avatar {
  position: relative;
  margin-right: 24rpx;

  image {
    width: 88rpx;
    height: 88rpx;
    border-radius: 50%;
  }

  .default-avatar {
    width: 88rpx;
    height: 88rpx;
    border-radius: 50%;
    background-color: #ddd;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.session-content {
  flex: 1;
  min-width: 0;
}

.session-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.session-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.session-time {
  font-size: 24rpx;
  color: #999;
  margin-left: 16rpx;
}

.session-preview {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.preview-text {
  font-size: 28rpx;
  color: #666;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.online-status {
  display: flex;
  align-items: center;
  margin-left: 16rpx;

  .online-dot {
    width: 12rpx;
    height: 12rpx;
    border-radius: 50%;
    background-color: #52c41a;
    margin-right: 8rpx;
  }

  .online-text {
    font-size: 24rpx;
    color: #52c41a;
  }
}

.message-status {
  margin-left: 16rpx;
}
</style>
