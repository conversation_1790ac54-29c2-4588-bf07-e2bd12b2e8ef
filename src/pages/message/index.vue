<route lang="json5" type="tabbar">
{
  style: {
    navigationBarTitleText: '消息',
  },
  layout: 'tabbar',
}
</route>

<template>
  <view class="message-container">
    <!-- 搜索栏 -->
    <view class="search-bar">
      <wd-search
        v-model="searchKeyword"
        placeholder="搜索消息"
        @search="handleSearch"
        @clear="handleClearSearch"
      />
    </view>

    <!-- 消息分类 -->
    <view class="message-categories">
      <view
        v-for="category in messageCategories"
        :key="category.type"
        class="category-item"
        @click="goToCategory(category)"
      >
        <view class="category-icon">
          <wd-icon :name="category.icon" :size="24" :color="category.color" />
          <wd-badge v-if="category.unreadCount > 0" :value="category.unreadCount" />
        </view>
        <text class="category-title">{{ category.title }}</text>
      </view>
    </view>

    <!-- 消息列表 -->
    <view class="message-list">
      <!-- 加载状态 -->
      <wd-loading v-if="loading" :size="24" color="#4D8EFF">加载中...</wd-loading>

      <!-- 空状态 -->
      <view v-else-if="filteredMessages.length === 0" class="empty-state">
        <view class="empty-icon">
          <wd-icon name="message" :size="60" color="#ddd" />
        </view>
        <text class="empty-text">{{ searchKeyword ? '暂无搜索结果' : '暂无消息' }}</text>
      </view>

      <!-- 消息项 -->
      <view v-else class="list-content">
        <view
          v-for="message in filteredMessages"
          :key="message.id"
          class="message-item"
          @click="goToChat(message)"
        >
          <view class="message-avatar">
            <image v-if="message.avatar" :src="message.avatar" mode="aspectFill" />
            <view v-else class="default-avatar">
              <text>{{ message.title.charAt(0) }}</text>
            </view>
            <wd-badge v-if="message.unreadCount > 0" :value="message.unreadCount" />
          </view>

          <view class="message-content">
            <view class="message-header">
              <text class="message-title">{{ message.title }}</text>
              <text class="message-time">{{ formatTime(message.time) }}</text>
            </view>

            <view class="message-preview">
              <text class="preview-text">{{ getPreviewText(message.content) }}</text>
            </view>
          </view>

          <view v-if="message.isTop" class="pin-icon">
            <wd-icon name="pin" :size="16" color="#ff6b35" />
          </view>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <wd-loadmore v-if="filteredMessages.length > 0" :state="loadMoreState" @loadmore="loadMore" />
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import { useMessageStore } from '@/store/message'
import type { IMessageItem, IMessageCategory } from '@/api/message.typings'

// 使用消息store
const messageStore = useMessageStore()

// 响应式数据
const searchKeyword = ref('')

// 计算属性
const messageCategories = computed(() => messageStore.messageCategories)
const recentMessages = computed(() => messageStore.recentMessages)
const loading = computed(
  () => messageStore.loading.recentMessages || messageStore.loading.categories,
)

const filteredMessages = computed(() => {
  // 使用搜索结果或空数组
  const messages = recentMessages.value || []

  if (!searchKeyword.value) {
    return messages
  }

  return messages.filter(
    (message) =>
      message.title?.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      message.content?.toLowerCase().includes(searchKeyword.value.toLowerCase()),
  )
})

const loadMoreState = computed(() => {
  if (loading.value) return 'loading'
  return 'finished' // 搜索结果不支持分页加载
})

// 方法
const handleSearch = async () => {
  if (!searchKeyword.value.trim()) {
    // 清空搜索结果
    messageStore.recentMessages = []
    return
  }

  try {
    await messageStore.searchAllMessages(searchKeyword.value)
  } catch (error) {
    console.error('搜索消息失败:', error)
    uni.showToast({
      title: '搜索失败',
      icon: 'error',
    })
  }
}

const handleClearSearch = () => {
  searchKeyword.value = ''
  messageStore.recentMessages = []
}

const goToCategory = async (category: IMessageCategory) => {
  console.log('点击消息分类:', category.type, '跳转路径:', category.path)

  // 订单消息特殊处理：直接进入第一个会话
  if (category.type === 'order') {
    try {
      // 获取订单会话列表
      await messageStore.fetchOrderSessions(undefined, true)
      const orderSessions = messageStore.orderSessions

      if (orderSessions.length > 0) {
        // 直接进入第一个订单会话
        const firstSession = orderSessions[0]
        uni.navigateTo({
          url: `/pages/chat/room/index?sessionId=${firstSession.id}&targetName=${firstSession.target_name}`,
          success: () => {
            console.log('直接进入订单会话:', firstSession.id)
          },
          fail: (err) => {
            console.error('进入订单会话失败:', err)
          },
        })
        return
      } else {
        // 没有订单会话，显示空状态或跳转到会话列表页面
        uni.navigateTo({
          url: category.path,
        })
        return
      }
    } catch (error) {
      console.error('获取订单会话失败:', error)
      // 失败时仍然跳转到会话列表页面
    }
  }

  // 其他消息类型正常跳转
  uni.navigateTo({
    url: category.path,
    success: () => {
      console.log('页面跳转成功:', category.path)
    },
    fail: (err) => {
      console.error('页面跳转失败:', err)
    },
  })
}

const goToChat = (message: IMessageItem) => {
  // 根据消息类型跳转到不同页面
  switch (message.type) {
    case 'chat':
      uni.navigateTo({
        url: `/pages/chat/room/index?id=${message.id}&type=private`,
      })
      break
    case 'system':
      uni.navigateTo({
        url: `/pages/message/system?id=${message.id}`,
      })
      break
    case 'order':
      uni.navigateTo({
        url: `/pages/message/order?id=${message.id}`,
      })
      break
    case 'service':
      uni.navigateTo({
        url: `/pages/message/service?id=${message.id}`,
      })
      break
  }

  // 标记为已读
  if (message.unreadCount && message.unreadCount > 0) {
    message.unreadCount = 0
  }
}

const formatTime = (timeStr: string) => {
  const timestamp = new Date(timeStr).getTime()
  const now = Date.now()
  const diff = now - timestamp
  const minute = 60 * 1000
  const hour = 60 * minute
  const day = 24 * hour

  if (diff < minute) {
    return '刚刚'
  } else if (diff < hour) {
    return `${Math.floor(diff / minute)}分钟前`
  } else if (diff < day) {
    return `${Math.floor(diff / hour)}小时前`
  } else if (diff < 7 * day) {
    return `${Math.floor(diff / day)}天前`
  } else {
    const date = new Date(timestamp)
    return `${date.getMonth() + 1}/${date.getDate()}`
  }
}

const getPreviewText = (content: string) => {
  return content || ''
}

const loadMore = () => {
  // 搜索结果不支持分页加载
  return
}

// 生命周期
onMounted(async () => {
  await messageStore.initMessageData()
})

onShow(() => {
  // 页面显示时更新未读消息统计
  messageStore.fetchUnreadCount()
})
</script>

<style lang="scss" scoped>
.message-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.search-bar {
  background-color: #fff;
  padding: 20rpx 32rpx;
  border-bottom: 1px solid #eee;
}

.message-categories {
  background-color: #fff;
  padding: 32rpx;
  margin-bottom: 20rpx;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 32rpx;

  .category-item {
    display: flex;
    flex-direction: column;
    align-items: center;

    .category-icon {
      position: relative;
      margin-bottom: 16rpx;
    }

    .category-title {
      font-size: 24rpx;
      color: #666;
      text-align: center;
    }
  }
}

.message-list {
  padding: 0 32rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;

  .empty-icon {
    margin-bottom: 32rpx;
  }

  .empty-text {
    font-size: 28rpx;
    color: #999;
  }
}

.list-content {
  .message-item {
    position: relative;
    display: flex;
    align-items: center;
    background-color: #fff;
    border-radius: 16rpx;
    padding: 32rpx 24rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

    .message-avatar {
      position: relative;
      width: 96rpx;
      height: 96rpx;
      margin-right: 24rpx;

      image {
        width: 100%;
        height: 100%;
        border-radius: 50%;
      }

      .default-avatar {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background-color: #4d8eff;
        display: flex;
        align-items: center;
        justify-content: center;

        text {
          font-size: 32rpx;
          color: #fff;
          font-weight: 500;
        }
      }
    }

    .message-content {
      flex: 1;

      .message-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 12rpx;

        .message-title {
          font-size: 32rpx;
          font-weight: 500;
          color: #333;
        }

        .message-time {
          font-size: 24rpx;
          color: #999;
        }
      }

      .message-preview {
        display: flex;
        align-items: center;

        .preview-text {
          flex: 1;
          font-size: 28rpx;
          color: #666;
          line-height: 1.4;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 1;
          overflow: hidden;
          margin-right: 12rpx;
        }
      }
    }

    .pin-icon {
      position: absolute;
      top: 16rpx;
      right: 16rpx;
    }
  }
}
</style>
