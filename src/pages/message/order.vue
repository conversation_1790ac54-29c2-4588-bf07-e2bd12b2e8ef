<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '订单消息',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view class="order-message-page">
    <!-- 自定义导航栏 -->
    <wd-navbar title="订单消息" left-text="返回" left-arrow @click-left="handleBack" />

    <!-- 消息列表 -->
    <view class="message-list">
      <view v-if="orderMessages.length > 0" class="list-content">
        <view
          v-for="message in orderMessages"
          :key="message.id"
          class="message-item"
          @click="handleMessageClick(message)"
        >
          <!-- 消息图标 -->
          <view class="message-icon">
            <wd-icon
              :name="getMessageIcon(message.type)"
              :color="getMessageIconColor(message.type)"
              size="48"
            />
          </view>

          <!-- 消息内容 -->
          <view class="message-content">
            <view class="message-header">
              <text class="message-title">{{ message.title }}</text>
              <text class="message-time">{{ formatTime(message.createdAt) }}</text>
            </view>

            <view class="message-body">
              <text class="message-text">{{ message.content }}</text>
            </view>

            <view class="message-footer">
              <text class="order-no">订单号：{{ message.orderNo }}</text>
              <view v-if="message.amount" class="amount">
                <text class="amount-label">金额：</text>
                <text class="amount-value">¥{{ message.amount }}</text>
              </view>
            </view>
          </view>

          <!-- 操作按钮 -->
          <view v-if="message.actionUrl" class="message-action">
            <wd-button type="primary" size="small" @click.stop="handleAction(message)">
              查看详情
            </wd-button>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-else class="empty-state">
        <Empty image="/static/images/empty-message.png" description="暂无订单消息">
          <wd-button type="primary" @click="handleRefresh">刷新</wd-button>
        </Empty>
      </view>
    </view>

    <!-- 加载更多 -->
    <view v-if="hasMore && orderMessages.length > 0" class="load-more">
      <wd-loading v-if="loading" type="spinner" />
      <text v-else @click="loadMore">加载更多</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useMessageStore } from '@/store/message'
import { formatRelativeTime } from '@/utils/date'
import Empty from '@/components/Empty/index.vue'
import type { IOrderNotification, OrderNotificationType } from '@/api/message.typings'

// 使用消息store
const messageStore = useMessageStore()

// 计算属性
const orderMessages = computed(() => messageStore.orderNotifications)
const loading = computed(() => messageStore.loading.orderNotifications)
const hasMore = computed(() => messageStore.pagination.orderNotifications.hasMore)

// 获取消息图标
const getMessageIcon = (type: OrderNotificationType) => {
  switch (type) {
    case OrderNotificationType.PAYMENT_SUCCESS:
      return 'check-circle'
    case OrderNotificationType.PAYMENT_FAILED:
      return 'close-circle'
    case OrderNotificationType.REFUND_SUCCESS:
      return 'refund'
    case OrderNotificationType.ORDER_SHIPPED:
      return 'truck'
    case OrderNotificationType.ORDER_DELIVERED:
      return 'location'
    case OrderNotificationType.ORDER_CANCELLED:
      return 'close'
    default:
      return 'notification'
  }
}

// 获取消息图标颜色
const getMessageIconColor = (type: OrderNotificationType) => {
  switch (type) {
    case OrderNotificationType.PAYMENT_SUCCESS:
    case OrderNotificationType.REFUND_SUCCESS:
      return '#52c41a'
    case OrderNotificationType.PAYMENT_FAILED:
    case OrderNotificationType.ORDER_CANCELLED:
      return '#ff4d4f'
    case OrderNotificationType.ORDER_SHIPPED:
      return '#1890ff'
    case OrderNotificationType.ORDER_DELIVERED:
      return '#722ed1'
    default:
      return '#666'
  }
}

// 格式化时间
const formatTime = (time: string) => {
  return formatRelativeTime(time)
}

// 点击消息
const handleMessageClick = async (message: IOrderNotification) => {
  console.log('点击消息:', message)
  // 标记消息为已读
  if (!message.isRead) {
    await messageStore.markOrderNotificationRead(message.id)
  }
}

// 处理操作按钮点击
const handleAction = (message: IOrderNotification) => {
  if (message.actionUrl) {
    uni.navigateTo({
      url: message.actionUrl,
    })
  }
}

// 返回
const handleBack = () => {
  uni.navigateBack()
}

// 刷新
const handleRefresh = async () => {
  await loadMessages(true)
}

// 加载更多
const loadMore = async () => {
  await messageStore.loadMoreOrderNotifications()
}

// 方法
const loadMessages = async (refresh = false) => {
  try {
    await messageStore.fetchOrderNotifications(undefined, refresh)
  } catch (error) {
    console.error('加载订单消息失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'error',
    })
  }
}

onMounted(() => {
  loadMessages(true)
})
</script>

<style lang="scss" scoped>
.order-message-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.message-list {
  .list-content {
    background: white;
  }

  .message-item {
    display: flex;
    align-items: flex-start;
    padding: 32rpx;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    &:active {
      background-color: #f0f0f0;
    }
  }

  .message-icon {
    margin-right: 24rpx;
    margin-top: 8rpx;
  }

  .message-content {
    flex: 1;
    min-width: 0;

    .message-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16rpx;

      .message-title {
        font-size: 32rpx;
        font-weight: 500;
        color: #333;
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .message-time {
        font-size: 24rpx;
        color: #999;
        margin-left: 16rpx;
      }
    }

    .message-body {
      margin-bottom: 16rpx;

      .message-text {
        font-size: 28rpx;
        color: #666;
        line-height: 1.5;
      }
    }

    .message-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .order-no {
        font-size: 24rpx;
        color: #999;
      }

      .amount {
        display: flex;
        align-items: center;

        .amount-label {
          font-size: 24rpx;
          color: #999;
        }

        .amount-value {
          font-size: 28rpx;
          color: #ff4757;
          font-weight: 500;
        }
      }
    }
  }

  .message-action {
    margin-left: 16rpx;
    margin-top: 8rpx;
  }
}

.empty-state {
  padding: 120rpx 40rpx;
}

.load-more {
  padding: 32rpx;
  text-align: center;
  color: #666;
  font-size: 28rpx;
}
</style>
