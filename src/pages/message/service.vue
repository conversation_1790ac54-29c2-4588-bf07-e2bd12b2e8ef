<!--
 * @file 客服消息页面
 * @description 显示客服消息列表和聊天功能
 * <AUTHOR> Assistant
 * @date 2025-01-27
-->

<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '客服消息',
  },
}
</route>

<template>
  <view class="service-message-container">
    <!-- 消息列表 -->
    <view class="message-list">
      <!-- 加载状态 -->
      <wd-loading v-if="loading" :size="24" color="#4D8EFF">加载中...</wd-loading>

      <!-- 空状态 -->
      <view v-else-if="messageList.length === 0" class="empty-state">
        <view class="empty-icon">
          <wd-icon name="service" :size="60" color="#ddd" />
        </view>
        <text class="empty-text">暂无客服消息</text>
        <wd-button type="primary" size="small" @click="startNewChat" class="start-chat-btn">
          联系客服
        </wd-button>
      </view>

      <!-- 消息项 -->
      <view v-else class="list-content">
        <view
          v-for="message in messageList"
          :key="message.id"
          class="message-item"
          @click="enterChat(message)"
        >
          <view class="message-avatar">
            <image v-if="message.target_avatar" :src="message.target_avatar" mode="aspectFill" />
            <view v-else class="default-avatar">
              <wd-icon name="service" :size="20" color="#fff" />
            </view>
            <wd-badge
              v-if="message.unread_count && message.unread_count > 0"
              :value="message.unread_count"
            />
          </view>

          <view class="message-content">
            <view class="message-header">
              <text class="message-title">
                {{ message.target_name || '在线客服' }}
              </text>
              <text class="message-time">
                {{ formatTime(message.last_message?.created_at || message.updated_at) }}
              </text>
            </view>

            <view class="message-preview">
              <text class="preview-text">
                {{ getPreviewText(message.last_message?.content || '') }}
              </text>
              <view v-if="message.status === 0" class="online-status">
                <view class="online-dot"></view>
                <text class="online-text">在线</text>
              </view>
            </view>
          </view>

          <view v-if="message.last_message?.sender_type !== 'user'" class="message-status">
            <wd-icon name="check" :size="16" color="#52c41a" />
          </view>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <wd-loadmore v-if="messageList.length > 0" :state="loadMoreState" @loadmore="loadMore" />

    <!-- 快速联系客服按钮 -->
    <view class="quick-contact" v-if="messageList.length > 0">
      <wd-button type="primary" round @click="startNewChat">
        <wd-icon name="add" :size="16" color="#fff" style="margin-right: 8rpx" />
        新建会话
      </wd-button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { useMessageStore } from '@/store/message'
import type { IServiceMessage } from '@/api/message.typings'

// 使用消息store
const messageStore = useMessageStore()

// 响应式数据
const messageId = ref('')

// 计算属性
const messageList = computed(() => messageStore.serviceSessions)
const loading = computed(() => messageStore.loading.serviceSessions)
const hasMore = computed(() => messageStore.pagination.serviceSessions.hasMore)

const loadMoreState = computed((): 'loading' | 'finished' | 'more' => {
  if (loading.value) return 'loading'
  if (!hasMore.value) return 'finished'
  return 'more'
})

/**
 * 加载客服消息列表
 * @param refresh 是否刷新列表
 */
const loadMessages = async (refresh = false) => {
  try {
    await messageStore.fetchServiceSessions(undefined, refresh)
  } catch (error) {
    console.error('加载客服会话失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'error',
    })
  }
}

/**
 * 进入聊天页面
 * @param message 消息对象
 */
const enterChat = (session: any) => {
  uni.navigateTo({
    url: `/pages/chat/room/index?sessionId=${session.id}&type=service&serviceName=${session.target_name}`,
  })
}

/**
 * 开始新的客服会话
 */
const startNewChat = () => {
  uni.navigateTo({
    url: '/pages/chat/room/index?type=service&new=true',
  })
}

/**
 * 获取预览文本
 * @param content 消息内容
 * @returns 预览文本
 */
const getPreviewText = (content: string) => {
  if (!content) return '暂无消息'

  // 处理不同类型的消息内容
  try {
    const parsed = JSON.parse(content)
    if (parsed.type === 'text') {
      return parsed.content
    } else if (parsed.type === 'image') {
      return '[图片]'
    } else if (parsed.type === 'file') {
      return '[文件]'
    } else {
      return content
    }
  } catch {
    return content
  }
}

/**
 * 格式化时间
 * @param timeStr 时间字符串
 * @returns 格式化后的时间
 */
const formatTime = (timeStr: string) => {
  if (!timeStr) return ''

  const timestamp = new Date(timeStr).getTime()
  const now = Date.now()
  const diff = now - timestamp
  const minute = 60 * 1000
  const hour = 60 * minute
  const day = 24 * hour

  if (diff < minute) {
    return '刚刚'
  } else if (diff < hour) {
    return `${Math.floor(diff / minute)}分钟前`
  } else if (diff < day) {
    return `${Math.floor(diff / hour)}小时前`
  } else if (diff < 7 * day) {
    return `${Math.floor(diff / day)}天前`
  } else {
    const date = new Date(timestamp)
    return `${date.getMonth() + 1}/${date.getDate()}`
  }
}

/**
 * 加载更多
 */
const loadMore = () => {
  messageStore.loadMoreServiceSessions()
}

// 页面加载时获取参数
onLoad((options) => {
  if (options?.id) {
    messageId.value = options.id
  }
})

// 生命周期
onMounted(() => {
  loadMessages(true)
})
</script>

<style lang="scss" scoped>
.service-message-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  position: relative;
  padding-bottom: 120rpx;
}

.message-list {
  padding: 32rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;

  .empty-icon {
    margin-bottom: 32rpx;
  }

  .empty-text {
    font-size: 28rpx;
    color: #999;
    margin-bottom: 40rpx;
  }

  .start-chat-btn {
    width: 200rpx;
  }
}

.list-content {
  .message-item {
    position: relative;
    display: flex;
    align-items: center;
    background-color: #fff;
    border-radius: 16rpx;
    padding: 32rpx 24rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

    .message-avatar {
      position: relative;
      width: 96rpx;
      height: 96rpx;
      margin-right: 24rpx;

      image {
        width: 100%;
        height: 100%;
        border-radius: 50%;
      }

      .default-avatar {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background-color: #ff3b30;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .message-content {
      flex: 1;

      .message-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 12rpx;

        .message-title {
          font-size: 32rpx;
          font-weight: 500;
          color: #333;
          flex: 1;
          margin-right: 16rpx;
        }

        .message-time {
          font-size: 24rpx;
          color: #999;
          white-space: nowrap;
        }
      }

      .message-preview {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .preview-text {
          flex: 1;
          font-size: 28rpx;
          color: #666;
          line-height: 1.4;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 1;
          overflow: hidden;
          margin-right: 12rpx;
        }

        .online-status {
          display: flex;
          align-items: center;
          white-space: nowrap;

          .online-dot {
            width: 12rpx;
            height: 12rpx;
            background-color: #34c759;
            border-radius: 50%;
            margin-right: 8rpx;
          }

          .online-text {
            font-size: 24rpx;
            color: #34c759;
          }
        }
      }
    }

    .pin-icon {
      position: absolute;
      top: 16rpx;
      right: 16rpx;
    }
  }
}

.quick-contact {
  position: fixed;
  bottom: 40rpx;
  right: 40rpx;
  z-index: 100;
}
</style>
