<!--
 * @file 系统通知页面
 * @description 显示系统通知消息列表和详情
 * <AUTHOR> Assistant
 * @date 2025-01-27
-->

<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '系统通知',
  },
}
</route>

<template>
  <view class="system-message-container">
    <!-- 消息列表 -->
    <view class="message-list">
      <!-- 加载状态 -->
      <wd-loading v-if="loading" :size="24" color="#4D8EFF">加载中...</wd-loading>

      <!-- 空状态 -->
      <view v-else-if="messageList.length === 0" class="empty-state">
        <view class="empty-icon">
          <wd-icon name="notification" :size="60" color="#ddd" />
        </view>
        <text class="empty-text">暂无系统通知</text>
      </view>

      <!-- 消息项 -->
      <view v-else class="list-content">
        <view
          v-for="message in messageList"
          :key="message.id"
          class="message-item"
          @click="viewMessage(message)"
        >
          <view class="message-icon">
            <wd-icon name="notification" :size="24" color="#FF9500" />
          </view>

          <view class="message-content">
            <view class="message-header">
              <text class="message-title">{{ message.title }}</text>
              <text class="message-time">{{ formatTime(message.publishTime) }}</text>
            </view>

            <view class="message-preview">
              <text class="preview-text">{{ message.content }}</text>
            </view>
          </view>

          <view v-if="!message.isRead" class="unread-dot"></view>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <wd-loadmore v-if="messageList.length > 0" :state="loadMoreState" @loadmore="loadMore" />
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { useMessageStore } from '@/store/message'
import type { ISystemNotification } from '@/api/message.typings'

// 使用消息store
const messageStore = useMessageStore()

// 响应式数据
const messageId = ref('')

// 计算属性
const messageList = computed(() => messageStore.systemNotifications)
const loading = computed(() => messageStore.loading.systemNotifications)
const hasMore = computed(() => messageStore.pagination.systemNotifications.hasMore)

const loadMoreState = computed((): 'loading' | 'finished' | 'more' => {
  if (loading.value) return 'loading'
  if (!hasMore.value) return 'finished'
  return 'more'
})

/**
 * 加载系统消息列表
 * @param refresh 是否刷新列表
 */
const loadMessages = async (refresh = false) => {
  try {
    await messageStore.fetchSystemNotifications(undefined, refresh)
  } catch (error) {
    console.error('加载系统消息失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'error',
    })
  }
}

/**
 * 查看消息详情
 * @param message 消息对象
 */
const viewMessage = async (message: ISystemNotification) => {
  try {
    // 标记为已读
    if (!message.isRead) {
      await messageStore.markSystemNotificationRead(message.id)
    }

    // 显示消息详情
    uni.showModal({
      title: message.title,
      content: message.content,
      showCancel: false,
      confirmText: '确定',
    })
  } catch (error) {
    console.error('标记消息已读失败:', error)
  }
}

/**
 * 格式化时间
 * @param timeStr 时间字符串
 * @returns 格式化后的时间
 */
const formatTime = (timeStr: string) => {
  const timestamp = new Date(timeStr).getTime()
  const now = Date.now()
  const diff = now - timestamp
  const minute = 60 * 1000
  const hour = 60 * minute
  const day = 24 * hour

  if (diff < minute) {
    return '刚刚'
  } else if (diff < hour) {
    return `${Math.floor(diff / minute)}分钟前`
  } else if (diff < day) {
    return `${Math.floor(diff / hour)}小时前`
  } else if (diff < 7 * day) {
    return `${Math.floor(diff / day)}天前`
  } else {
    const date = new Date(timestamp)
    return `${date.getMonth() + 1}/${date.getDate()}`
  }
}

/**
 * 加载更多
 */
const loadMore = () => {
  messageStore.loadMoreSystemNotifications()
}

// 页面加载时获取参数
onLoad((options) => {
  if (options?.id) {
    messageId.value = options.id
  }
})

// 生命周期
onMounted(() => {
  loadMessages(true)
})
</script>

<style lang="scss" scoped>
.system-message-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.message-list {
  padding: 32rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;

  .empty-icon {
    margin-bottom: 32rpx;
  }

  .empty-text {
    font-size: 28rpx;
    color: #999;
  }
}

.list-content {
  .message-item {
    position: relative;
    display: flex;
    align-items: flex-start;
    background-color: #fff;
    border-radius: 16rpx;
    padding: 32rpx 24rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

    .message-icon {
      width: 48rpx;
      height: 48rpx;
      margin-right: 24rpx;
      margin-top: 8rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #fff2e8;
      border-radius: 50%;
    }

    .message-content {
      flex: 1;

      .message-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 12rpx;

        .message-title {
          font-size: 32rpx;
          font-weight: 500;
          color: #333;
          flex: 1;
          margin-right: 16rpx;
        }

        .message-time {
          font-size: 24rpx;
          color: #999;
          white-space: nowrap;
        }
      }

      .message-preview {
        .preview-text {
          font-size: 28rpx;
          color: #666;
          line-height: 1.5;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
        }
      }
    }

    .unread-dot {
      position: absolute;
      top: 32rpx;
      right: 24rpx;
      width: 12rpx;
      height: 12rpx;
      background-color: #ff3b30;
      border-radius: 50%;
    }
  }
}
</style>
