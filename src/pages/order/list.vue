<route lang="json5">
{
  style: {
    navigationBarTitleText: '我的订单',
  },
}
</route>

<template>
  <view class="order-list-container">
    <!-- 订单状态筛选 -->
    <view class="order-tabs">
      <scroll-view scroll-x class="tabs-scroll">
        <view class="tabs-content">
          <view
            v-for="tab in orderTabs"
            :key="tab.value"
            class="tab-item"
            :class="{ active: currentStatus === tab.value }"
            @click="switchTab(tab.value)"
          >
            <text class="tab-text">{{ tab.label }}</text>
            <view v-if="tab.count > 0" class="tab-badge">{{ tab.count }}</view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 订单列表 -->
    <scroll-view
      scroll-y
      class="order-scroll"
      @scrolltolower="loadMore"
      :refresher-enabled="true"
      :refresher-triggered="isRefreshing"
      @refresherrefresh="onRefresh"
    >
      <view class="order-list">
        <!-- 订单项 -->
        <view
          v-for="order in orderList"
          :key="order.id"
          class="order-item"
          @click="goToOrderDetail(order.id)"
        >
          <!-- 订单头部 -->
          <view class="order-header">
            <view class="order-info">
              <text class="order-number">订单号：{{ (order as any).order_no || order.id }}</text>
              <text class="order-time">
                {{
                  formatTime(
                    (order as any).create_time || (order as any).created_at || order.createTime,
                  )
                }}
              </text>
            </view>
            <view class="order-status" :class="getStatusClass((order as any).status)">
              {{ (order as any).status_text || getStatusText((order as any).status) }}
            </view>
          </view>

          <!-- 商品信息 -->
          <view class="order-products">
            <view class="product-item">
              <image
                :src="(order as any).first_product_image || '/static/images/default-product.png'"
                class="product-image"
                mode="aspectFill"
                @click.stop="goToOrderDetail(order.id)"
              />
              <view class="product-info">
                <text class="product-name">
                  {{ (order as any).first_product_name || '商品名称' }}
                </text>
                <text class="product-summary">{{ (order as any).item_summary || '商品详情' }}</text>
                <view class="product-count">
                  <text class="count-text">共{{ (order as any).item_count || 1 }}件商品</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 订单金额 -->
          <view class="order-amount">
            <view class="amount-info">
              <view class="amount-row">
                <text class="amount-label">实付款：</text>
                <text class="amount-value">
                  ¥{{
                    (order as any).pay_amount ||
                    (order as any).total_amount ||
                    order.totalAmount ||
                    (order as any).amount
                  }}
                </text>
              </view>
              <!-- 显示支付方式（仅已支付订单显示） -->
              <view
                v-if="(order as any).pay_status === 2 && (order as any).pay_method_text"
                class="payment-method"
              >
                <text class="payment-label">支付方式：</text>
                <text class="payment-value">{{ (order as any).pay_method_text }}</text>
              </view>
            </view>
          </view>

          <!-- 订单操作 -->
          <view class="order-actions">
            <view class="action-buttons">
              <!-- 待付款 (10) -->
              <template v-if="(order as any).status === 10">
                <wd-button type="default" size="small" @click.stop="cancelOrder(order.id)">
                  取消订单
                </wd-button>
                <wd-button type="primary" size="small" @click.stop="payOrder(order.id)">
                  立即付款
                </wd-button>
              </template>

              <!-- 已付款/待发货 (20) -->
              <template v-else-if="(order as any).status === 20">
                <wd-button type="default" size="small" @click.stop="contactService(order.id)">
                  联系客服
                </wd-button>
                <wd-button type="primary" size="small" @click.stop="applyRefund(order.id)">
                  申请退款
                </wd-button>
              </template>

              <!-- 已发货/待收货 (30) -->
              <template v-else-if="(order as any).status === 30">
                <wd-button type="default" size="small" @click.stop="viewLogistics(order.id)">
                  查看物流
                </wd-button>
                <wd-button type="primary" size="small" @click.stop="confirmReceive(order.id)">
                  确认收货
                </wd-button>
              </template>

              <!-- 已收货/待评价 (40) -->
              <template v-else-if="(order as any).status === 40">
                <wd-button type="default" size="small" @click.stop="buyAgain(order.id)">
                  再次购买
                </wd-button>
                <wd-button type="primary" size="small" @click.stop="goToReview(order.id)">
                  去评价
                </wd-button>
              </template>

              <!-- 已完成 (50) -->
              <template v-else-if="(order as any).status === 50">
                <wd-button type="default" size="small" @click.stop="buyAgain(order.id)">
                  再次购买
                </wd-button>
                <wd-button type="default" size="small" @click.stop="deleteOrder(order.id)">
                  删除订单
                </wd-button>
              </template>

              <!-- 已取消 (60) -->
              <template v-else-if="(order as any).status === 60">
                <wd-button type="default" size="small" @click.stop="buyAgain(order.id)">
                  再次购买
                </wd-button>
                <wd-button type="default" size="small" @click.stop="deleteOrder(order.id)">
                  删除订单
                </wd-button>
              </template>

              <!-- 退款中 (70) -->
              <template v-else-if="(order as any).status === 70">
                <wd-button type="default" size="small" @click.stop="contactService(order.id)">
                  联系客服
                </wd-button>
              </template>

              <!-- 已退款 (80) -->
              <template v-else-if="(order as any).status === 80">
                <wd-button type="default" size="small" @click.stop="buyAgain(order.id)">
                  再次购买
                </wd-button>
              </template>
            </view>
          </view>
        </view>
      </view>

      <!-- 加载状态 -->
      <view v-if="isLoading && orderList.length > 0" class="loading-more">
        <wd-loading size="16" />
        <text>加载中...</text>
      </view>

      <!-- 没有更多数据 -->
      <view v-if="!hasMore && orderList.length > 0" class="no-more">
        <text>没有更多订单了</text>
      </view>

      <!-- 空状态 -->
      <view v-if="!isLoading && orderList.length === 0" class="empty-state">
        <image src="/static/images/empty-order.png" class="empty-image" mode="aspectFit" />
        <text class="empty-text">暂无订单</text>
        <wd-button type="primary" @click="goToShopping">去逛逛</wd-button>
      </view>
    </scroll-view>

    <!-- 取消订单弹窗 -->
    <wd-popup v-model="showCancelModal" position="bottom" :safe-area-inset-bottom="true">
      <view class="cancel-modal">
        <view class="cancel-header">
          <text class="cancel-title">选择取消理由</text>
          <wd-icon name="close" size="20" @click="closeCancelModal" />
        </view>

        <view class="cancel-content">
          <view class="reason-list">
            <view
              v-for="reason in cancelReasons"
              :key="reason"
              class="reason-item"
              :class="{ active: selectedReason === reason }"
              @click="selectedReason = reason"
            >
              <text class="reason-text">{{ reason }}</text>
              <wd-icon v-if="selectedReason === reason" name="check" size="16" color="#ff6b35" />
            </view>
          </view>

          <!-- 自定义理由输入 -->
          <view v-if="selectedReason === '其他原因'" class="custom-reason">
            <wd-textarea
              v-model="customReason"
              placeholder="请输入取消理由"
              :maxlength="100"
              :show-word-limit="true"
              :auto-height="true"
              :min-height="80"
            />
          </view>
        </view>

        <view class="cancel-actions">
          <wd-button type="default" size="large" @click="closeCancelModal">取消</wd-button>
          <wd-button type="primary" size="large" @click="confirmCancelOrder">确认取消</wd-button>
        </view>
      </view>
    </wd-popup>

    <!-- 申请退款弹窗 -->
    <wd-popup v-model="showRefundModal" position="bottom" :safe-area-inset-bottom="true">
      <view class="refund-modal">
        <view class="refund-header">
          <text class="refund-title">选择退款理由</text>
          <wd-icon name="close" size="20" @click="closeRefundModal" />
        </view>

        <view class="refund-content">
          <view class="reason-list">
            <view
              v-for="reason in refundReasons"
              :key="reason"
              class="reason-item"
              :class="{ active: selectedRefundReason === reason }"
              @click="selectedRefundReason = reason"
            >
              <text class="reason-text">{{ reason }}</text>
              <wd-icon
                v-if="selectedRefundReason === reason"
                name="check"
                size="16"
                color="#ff6b35"
              />
            </view>
          </view>

          <!-- 自定义理由输入 -->
          <view v-if="selectedRefundReason === '其他原因'" class="custom-reason">
            <wd-input
              v-model="customRefundReason"
              placeholder="请输入退款理由"
              :maxlength="100"
              show-word-limit
              clearable
            />
          </view>
        </view>

        <view class="refund-actions">
          <wd-button type="default" size="large" @click="closeRefundModal">取消</wd-button>
          <wd-button type="primary" size="large" @click="confirmRefund">确认退款</wd-button>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { onLoad, onShow } from '@dcloudio/uni-app'
import { useOrderStore } from '@/store/order'

const orderStore = useOrderStore()

// 页面参数
const pageParams = ref<{ status?: string }>({})

// 订单状态选项 (对应后端状态值: 10:待付款, 20:已付款, 30:已发货, 40:已收货, 50:已完成, 60:已取消, 70:退款中, 80:已退款)
const orderTabs = ref<Array<{ label: string; value: number | ''; count: number }>>([
  { label: '全部', value: '' as const, count: 0 },
  { label: '待付款', value: 10 as const, count: 0 },
  { label: '待发货', value: 20 as const, count: 0 },
  { label: '待收货', value: 30 as const, count: 0 },
  { label: '待评价', value: 40 as const, count: 0 },
])

// 当前状态
const currentStatus = ref<number | ''>('')

// 状态
const isLoading = ref(false)
const isRefreshing = ref(false)

// 计算属性
const orderList = computed(() => orderStore.orderList)
const hasMore = computed(() => orderStore.hasMore)

/**
 * 切换订单状态
 */
const switchTab = (status: number | '') => {
  if (currentStatus.value === status) return

  currentStatus.value = status
  orderStore.resetOrderList()

  // 重置后需要重新设置搜索参数中的状态
  if (status) {
    orderStore.searchParams.status = status
  } else {
    orderStore.searchParams.status = undefined
  }

  isPageInitialized.value = false
  fetchOrderList()
}

/**
 * 获取订单列表
 */
const fetchOrderList = async () => {
  try {
    isLoading.value = true

    const params: any = {
      page: orderStore.currentPage,
      page_size: 10,
      // 明确设置status参数，如果currentStatus为空字符串或0，则设置为undefined
      status:
        currentStatus.value && typeof currentStatus.value === 'number'
          ? currentStatus.value
          : undefined,
    }

    console.log('订单调试：order params', params)
    await orderStore.fetchOrderList(params)
  } catch (error) {
    console.error('获取订单列表失败:', error)
    uni.showToast({
      title: '获取订单失败',
      icon: 'error',
    })
  } finally {
    isLoading.value = false
  }
}

/**
 * 加载更多
 */
const loadMore = async () => {
  if (isLoading.value || !hasMore.value) return

  try {
    isLoading.value = true

    // 更新搜索参数中的状态筛选
    if (currentStatus.value) {
      orderStore.searchParams.status = currentStatus.value
    } else {
      orderStore.searchParams.status = undefined
    }

    // 直接调用orderStore的loadMoreOrders方法
    await orderStore.loadMoreOrders()
  } catch (error) {
    console.error('加载更多订单失败:', error)
  } finally {
    isLoading.value = false
  }
}

/**
 * 下拉刷新
 */
const onRefresh = async () => {
  try {
    isRefreshing.value = true
    orderStore.resetOrderList()
    isPageInitialized.value = false
    await fetchOrderList()
    await fetchOrderStats()
  } catch (error) {
    console.error('刷新订单列表失败:', error)
  } finally {
    isRefreshing.value = false
  }
}

/**
 * 获取订单统计
 */
const fetchOrderStats = async () => {
  try {
    await orderStore.fetchOrderStatusStats()
    // 更新标签页数量
    const stats = orderStore.orderStatusStats
    orderTabs.value[1].count = stats?.pending || 0
    orderTabs.value[2].count = stats?.paid || 0
    orderTabs.value[3].count = stats?.shipped || 0
    orderTabs.value[4].count = stats?.completed || 0
  } catch (error) {
    console.error('获取订单统计失败:', error)
  }
}

/**
 * 格式化时间
 */
const formatTime = (time: string) => {
  const date = new Date(time)
  const now = new Date()
  const diff = now.getTime() - date.getTime()

  if (diff < 60000) {
    return '刚刚'
  } else if (diff < 3600000) {
    return `${Math.floor(diff / 60000)}分钟前`
  } else if (diff < 86400000) {
    return `${Math.floor(diff / 3600000)}小时前`
  } else {
    return `${date.getMonth() + 1}-${date.getDate()} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
  }
}

/**
 * 获取状态文本
 */
const getStatusText = (status: number) => {
  const statusMap: Record<number, string> = {
    10: '待付款',
    20: '待发货',
    30: '待收货',
    40: '待评价',
    50: '已完成',
    60: '已取消',
    70: '退款中',
    80: '已退款',
  }
  return statusMap[status] || '未知状态'
}

/**
 * 获取状态样式类
 */
const getStatusClass = (status: number) => {
  const classMap: Record<number, string> = {
    10: 'status-pending',
    20: 'status-paid',
    30: 'status-shipped',
    40: 'status-completed',
    50: 'status-finished',
    60: 'status-cancelled',
    70: 'status-refunding',
    80: 'status-refunded',
  }
  return classMap[status] || ''
}

/**
 * 跳转到订单详情
 */
const goToOrderDetail = (orderId: number) => {
  uni.navigateTo({
    url: `/pages/order/detail?id=${orderId}`,
  })
}

/**
 * 跳转到商品详情
 */
const goToProductDetail = (productId: number) => {
  uni.navigateTo({
    url: `/pages/product/detail?id=${productId}`,
  })
}

// 取消理由选项
const cancelReasons = ref([
  '我不想要了',
  '商品信息有误',
  '价格变动',
  '重复下单',
  '配送时间太长',
  '其他原因',
])

// 取消订单弹窗状态
const showCancelModal = ref(false)
const selectedReason = ref('')
const customReason = ref('')
const currentCancelOrderId = ref<number | null>(null)

// 退款理由选项
const refundReasons = ref([
  '不想要了/拍错了',
  '商品质量问题',
  '商品与描述不符',
  '收到商品破损',
  '商家发错货',
  '其他原因',
])

// 申请退款弹窗状态
const showRefundModal = ref(false)
const selectedRefundReason = ref('')
const customRefundReason = ref('')
const currentRefundOrderId = ref<number | null>(null)

/**
 * 取消订单
 */
const cancelOrder = (orderId: number) => {
  currentCancelOrderId.value = orderId
  selectedReason.value = ''
  customReason.value = ''
  showCancelModal.value = true
}

/**
 * 确认取消订单
 */
const confirmCancelOrder = async () => {
  if (!currentCancelOrderId.value) return

  let reason = selectedReason.value
  if (reason === '其他原因') {
    reason = customReason.value.trim()
    if (!reason) {
      uni.showToast({
        title: '请输入取消理由',
        icon: 'none',
      })
      return
    }
  } else if (!reason) {
    uni.showToast({
      title: '请选择取消理由',
      icon: 'none',
    })
    return
  }

  try {
    await orderStore.cancelOrderById(currentCancelOrderId.value, reason)
    uni.showToast({
      title: '订单已取消',
      icon: 'success',
    })
    showCancelModal.value = false

    // 取消成功后刷新订单列表
    orderStore.resetOrderList()
    await fetchOrderList()
    await fetchOrderStats()
  } catch (error: any) {
    uni.showToast({
      title: error.message || '取消失败',
      icon: 'error',
    })
  }
}

/**
 * 关闭取消订单弹窗
 */
const closeCancelModal = () => {
  showCancelModal.value = false
  currentCancelOrderId.value = null
  selectedReason.value = ''
  customReason.value = ''
}

/**
 * 支付订单
 */
const payOrder = (orderId: number) => {
  uni.navigateTo({
    url: `/pages/payment/index?orderId=${orderId}`,
  })
}

/**
 * 确认收货
 */
const confirmReceive = (orderId: number) => {
  uni.showModal({
    title: '确认收货',
    content: '确认已收到商品吗？',
    success: async (res) => {
      if (res.confirm) {
        try {
          await orderStore.confirmReceiveOrder(orderId)
          uni.showToast({
            title: '确认收货成功',
            icon: 'success',
          })
        } catch (error: any) {
          uni.showToast({
            title: error.message || '确认收货失败',
            icon: 'error',
          })
        }
      }
    },
  })
}

/**
 * 查看物流
 */
const viewLogistics = (orderId: number) => {
  uni.navigateTo({
    url: `/pages/order/logistics?orderId=${orderId}`,
  })
}

/**
 * 联系客服
 */
const contactService = (orderId: number) => {
  uni.navigateTo({
    url: `/pages/service/customer-service?orderId=${orderId}`,
  })
}

/**
 * 去评价
 */
const goToReview = (orderId: number) => {
  uni.navigateTo({
    url: `/pages/order/review?orderId=${orderId}`,
  })
}

/**
 * 再次购买
 */
const buyAgain = async (orderId: number) => {
  try {
    await orderStore.buyAgain(orderId)
    uni.showToast({
      title: '已加入购物车',
      icon: 'success',
    })

    setTimeout(() => {
      uni.switchTab({
        url: '/pages/cart/index',
      })
    }, 1500)
  } catch (error: any) {
    uni.showToast({
      title: error.message || '操作失败',
      icon: 'error',
    })
  }
}

/**
 * 删除订单
 */
const deleteOrder = (orderId: number) => {
  uni.showModal({
    title: '确认删除',
    content: '确定要删除这个订单吗？删除后无法恢复。',
    success: async (res) => {
      if (res.confirm) {
        try {
          await orderStore.deleteOrder(orderId)
          uni.showToast({
            title: '订单已删除',
            icon: 'success',
          })
        } catch (error: any) {
          uni.showToast({
            title: error.message || '删除失败',
            icon: 'error',
          })
        }
      }
    },
  })
}

/**
 * 申请退款
 */
const applyRefund = (orderId: number) => {
  currentRefundOrderId.value = orderId
  selectedRefundReason.value = ''
  customRefundReason.value = ''
  showRefundModal.value = true
}

/**
 * 确认申请退款
 */
const confirmRefund = async () => {
  if (!currentRefundOrderId.value) return

  let reason = selectedRefundReason.value
  if (reason === '其他原因') {
    reason = customRefundReason.value.trim()
    if (!reason) {
      uni.showToast({
        title: '请输入退款理由',
        icon: 'none',
      })
      return
    }
  } else if (!reason) {
    uni.showToast({
      title: '请选择退款理由',
      icon: 'none',
    })
    return
  }

  try {
    // 直接调用外卖订单退款API
    const { refundTakeoutOrder } = await import('@/api/takeout')

    await refundTakeoutOrder(currentRefundOrderId.value, reason)

    uni.showToast({
      title: '退款申请已提交',
      icon: 'success',
    })

    showRefundModal.value = false

    // 刷新订单列表
    orderStore.resetOrderList()
    await fetchOrderList()
    await fetchOrderStats()
  } catch (error: any) {
    uni.showToast({
      title: error.message || '申请退款失败',
      icon: 'error',
    })
  }
}

/**
 * 关闭申请退款弹窗
 */
const closeRefundModal = () => {
  showRefundModal.value = false
  currentRefundOrderId.value = null
  selectedRefundReason.value = ''
  customRefundReason.value = ''
}

/**
 * 去购物
 */
const goToShopping = () => {
  uni.switchTab({
    url: '/pages/index/index',
  })
}

// 页面初始化标志
const isPageInitialized = ref(false)

// 页面加载
onLoad((options) => {
  pageParams.value = options || {}
  // 处理URL参数中的status，确保类型正确
  const statusParam = options?.status
  if (statusParam && statusParam !== '') {
    const statusNum = Number(statusParam)
    currentStatus.value = isNaN(statusNum) ? '' : (statusNum as number)
  } else {
    currentStatus.value = ''
  }
})

// 页面显示
onShow(() => {
  // 只有在页面未初始化时才加载数据
  if (!isPageInitialized.value) {
    fetchOrderList()
    fetchOrderStats()
    isPageInitialized.value = true
  } else {
    // 页面已初始化，检查状态同步
    // 如果当前UI状态与store中的搜索参数不一致，需要同步
    const storeStatus = orderStore.searchParams.status
    if (currentStatus.value !== storeStatus) {
      // 同步UI状态到store的状态
      currentStatus.value = storeStatus || ''
    }
  }
})

// 组件挂载
onMounted(() => {
  // 如果页面已经通过onShow初始化过，则不重复加载
  if (!isPageInitialized.value) {
    fetchOrderList()
    fetchOrderStats()
    isPageInitialized.value = true
  }
})
</script>

<style lang="scss" scoped>
.order-list-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

// 订单状态筛选
.order-tabs {
  background-color: #fff;
  border-bottom: 1px solid #eee;
}

.tabs-scroll {
  white-space: nowrap;
}

.tabs-content {
  display: flex;
  padding: 0 10rpx;
}

.tab-item {
  position: relative;
  flex: 1;
  padding: 30rpx 15rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6rpx;

  &.active {
    .tab-text {
      color: #ff6b35;
      font-weight: bold;
    }

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 40rpx;
      height: 4rpx;
      background-color: #ff6b35;
      border-radius: 2rpx;
    }
  }
}

.tab-text {
  font-size: 26rpx;
  color: #666;
  transition: color 0.3s ease;
  white-space: nowrap;
}

.tab-badge {
  background-color: #ff6b35;
  color: #fff;
  font-size: 18rpx;
  min-width: 28rpx;
  height: 28rpx;
  border-radius: 14rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 6rpx;
}

// 订单列表
.order-scroll {
  flex: 1;
}

.order-list {
  padding: 20rpx;
}

.order-item {
  background-color: #fff;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

// 订单头部
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1px solid #f0f0f0;
}

.order-info {
  flex: 1;
}

.order-number {
  font-size: 28rpx;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.order-time {
  font-size: 24rpx;
  color: #999;
}

.order-status {
  font-size: 28rpx;
  font-weight: bold;

  &.status-pending {
    color: #ff6b35;
  }

  &.status-paid {
    color: #4ecdc4;
  }

  &.status-shipped {
    color: #45b7d1;
  }

  &.status-completed {
    color: #ffd700;
  }

  &.status-finished {
    color: #96ceb4;
  }

  &.status-cancelled {
    color: #999;
  }

  &.status-refunding,
  &.status-refunded {
    color: #ff6b9d;
  }
}

// 商品列表
.order-products {
  padding: 30rpx;
}

.product-item {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.product-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  flex-shrink: 0;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-name {
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.product-sku {
  font-size: 24rpx;
  color: #999;
  margin: 8rpx 0;
}

.product-summary {
  font-size: 24rpx;
  color: #999;
  margin: 8rpx 0;
  line-height: 1.4;
}

.product-count {
  margin-top: auto;
}

.count-text {
  font-size: 24rpx;
  color: #666;
}

.product-price-qty {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.product-price {
  font-size: 28rpx;
  color: #ff6b35;
  font-weight: bold;
}

.product-qty {
  font-size: 24rpx;
  color: #999;
}

.more-products {
  text-align: center;
  padding: 20rpx 0;
  font-size: 24rpx;
  color: #999;
  border-top: 1px solid #f0f0f0;
  margin-top: 20rpx;
}

// 订单金额
.order-amount {
  padding: 20rpx 30rpx;
  border-top: 1px solid #f0f0f0;
}

.amount-info {
  text-align: right;
}

.amount-row {
  margin-bottom: 8rpx;
}

.amount-label {
  font-size: 28rpx;
  color: #666;
}

.amount-value {
  font-size: 32rpx;
  color: #ff6b35;
  font-weight: bold;
}

.payment-method {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 8rpx;
}

.payment-label {
  font-size: 24rpx;
  color: #999;
}

.payment-value {
  font-size: 24rpx;
  color: #666;
  background-color: #f5f5f5;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
}

// 订单操作
.order-actions {
  padding: 20rpx 30rpx 30rpx;
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 20rpx;
}

// 加载状态
.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  padding: 40rpx;
  font-size: 24rpx;
  color: #999;
}

.no-more {
  text-align: center;
  padding: 40rpx;
  font-size: 24rpx;
  color: #999;
}

// 空状态
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
}

// 取消订单弹窗
.cancel-modal {
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  overflow: hidden;
}

.cancel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1px solid #f0f0f0;
}

.cancel-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.cancel-content {
  padding: 30rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.reason-list {
  margin-bottom: 20rpx;
}

.reason-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 20rpx;
  margin-bottom: 16rpx;
  background-color: #f8f8f8;
  border-radius: 12rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;

  &:last-child {
    margin-bottom: 0;
  }

  &.active {
    background-color: #fff2ee;
    border-color: #ff6b35;
  }

  &:active {
    transform: scale(0.98);
  }
}

.reason-text {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.custom-reason {
  margin-top: 20rpx;
  padding: 20rpx;
  background-color: #f8f8f8;
  border-radius: 12rpx;
}

.cancel-actions {
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
  border-top: 1px solid #f0f0f0;
  background-color: #fff;
}

.cancel-actions .wd-button {
  flex: 1;
}

// 申请退款弹窗
.refund-modal {
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  overflow: hidden;
}

.refund-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1px solid #f0f0f0;
}

.refund-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.refund-content {
  padding: 30rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.refund-actions {
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
  border-top: 1px solid #f0f0f0;
  background-color: #fff;
}

.refund-actions .wd-button {
  flex: 1;
}
</style>
