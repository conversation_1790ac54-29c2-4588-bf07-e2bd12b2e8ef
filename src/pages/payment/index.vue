<template>
  <view class="payment-page">
    <!-- 自定义导航栏 -->
    <wd-navbar title="收银台" left-text="返回" left-arrow @click-left="handleBack" />

    <!-- 开发模式提示 -->
    <view v-if="isDevelopment" class="dev-tip">
      <view class="dev-content">
        <wd-icon name="info-circle" size="32rpx" color="#1890ff" />
        <text class="dev-text">开发模式：当前使用模拟支付数据</text>
      </view>
    </view>

    <!-- 支付状态提示 -->
    <view v-if="paymentResult && paymentResult.status === 'pending'" class="payment-status-tip">
      <view class="status-content">
        <wd-icon name="time" size="32rpx" color="#ff9500" />
        <text class="status-text">支付订单已创建，请完成支付</text>
      </view>
      <text class="status-desc">订单将在15分钟后自动关闭</text>
    </view>

    <!-- 订单信息 -->
    <view class="order-info">
      <view class="order-header">
        <text class="order-title">订单信息</text>
        <text class="order-no">订单号：{{ orderInfo.orderNo }}</text>
      </view>

      <view class="order-amount">
        <text class="amount-label">支付金额</text>
        <text class="amount-value">¥{{ orderInfo.totalAmount }}</text>
      </view>
    </view>

    <!-- 支付方式选择 -->
    <view class="payment-methods">
      <view class="section-title">选择支付方式</view>

      <wd-radio-group v-model="selectedPaymentMethod" @change="handlePaymentMethodChange">
        <wd-cell-group>
          <wd-cell
            v-for="method in availablePaymentMethods"
            :key="method.method"
            :title="method.name"
            :label="method.description"
            clickable
            @click="selectPaymentMethod(method)"
          >
            <template #icon>
              <image class="payment-icon" :src="method.icon" />
            </template>

            <template #right-icon>
              <wd-radio :value="method.method" />
            </template>
          </wd-cell>
        </wd-cell-group>
      </wd-radio-group>
    </view>

    <!-- 余额支付详情 -->
    <view v-if="selectedMethod?.method === 'balance'" class="balance-info">
      <wd-cell-group>
        <wd-cell title="当前余额" :value="`¥${balanceInfo?.availableBalance || 0}`" />
        <wd-cell
          v-if="balanceInfo && balanceInfo.availableBalance < orderInfo.totalAmount"
          title="余额不足"
          value="请选择其他支付方式"
          label-class="error-text"
        />
      </wd-cell-group>
    </view>

    <!-- 银行卡支付详情 -->
    <view v-if="selectedMethod?.method === 'credit_card'" class="bank-card-info">
      <wd-cell-group>
        <wd-cell
          v-if="defaultBankCard"
          :title="defaultBankCard.bankName"
          :value="`**** **** **** ${defaultBankCard.cardNo.slice(-4)}`"
          clickable
          @click="showBankCardSelector = true"
        >
          <template #right-icon>
            <wd-icon name="arrow-right" />
          </template>
        </wd-cell>

        <wd-cell
          v-else
          title="选择银行卡"
          value="请添加银行卡"
          clickable
          @click="handleAddBankCard"
        >
          <template #right-icon>
            <wd-icon name="arrow-right" />
          </template>
        </wd-cell>
      </wd-cell-group>
    </view>

    <!-- 优惠信息 -->
    <view class="discount-info">
      <wd-cell-group>
        <wd-cell
          title="优惠券"
          :value="selectedCoupon ? `已选择 ${selectedCoupon.name}` : '选择优惠券'"
          clickable
          @click="showCouponSelector = true"
        >
          <template #right-icon>
            <wd-icon name="arrow-right" />
          </template>
        </wd-cell>
      </wd-cell-group>
    </view>

    <!-- 支付详情 -->
    <view class="payment-detail">
      <view class="detail-item">
        <text class="detail-label">商品金额</text>
        <text class="detail-value">¥{{ orderInfo.goodsAmount }}</text>
      </view>

      <view class="detail-item">
        <text class="detail-label">运费</text>
        <text class="detail-value">¥{{ orderInfo.shippingFee }}</text>
      </view>

      <view v-if="selectedCoupon" class="detail-item">
        <text class="detail-label">优惠券</text>
        <text class="detail-value discount">-¥{{ selectedCoupon.amount }}</text>
      </view>

      <view class="detail-item total">
        <text class="detail-label">实付金额</text>
        <text class="detail-value">¥{{ finalAmount }}</text>
      </view>
    </view>

    <!-- 支付按钮 -->
    <view class="payment-footer">
      <view class="footer-buttons">
        <!-- 关闭支付按钮（仅在有支付结果且状态为pending时显示） -->
        <wd-button
          v-if="paymentResult && paymentResult.status === 'pending'"
          type="default"
          size="large"
          @click="handleClosePayment"
          class="close-payment-btn"
        >
          关闭支付
        </wd-button>

        <!-- 主支付按钮 -->
        <wd-button
          type="primary"
          size="large"
          :loading="paymentLoading"
          :disabled="!canPay"
          @click="handlePay"
          :class="{ 'main-payment-btn': paymentResult && paymentResult.status === 'pending' }"
        >
          {{ paymentLoading ? '支付中...' : `立即支付 ¥${finalAmount}` }}
        </wd-button>
      </view>
    </view>

    <!-- 银行卡选择弹窗 -->
    <wd-popup v-model="showBankCardSelector" position="bottom" :safe-area-inset-bottom="true">
      <view class="bank-card-selector">
        <view class="selector-header">
          <text class="selector-title">选择银行卡</text>
          <wd-button type="text" @click="showBankCardSelector = false">取消</wd-button>
        </view>

        <wd-radio-group v-model="selectedBankCardId" @change="handleBankCardChange">
          <view class="bank-card-list">
            <view
              v-for="card in bankCards"
              :key="card.id"
              class="bank-card-item"
              :class="{ active: selectedBankCard?.id === card.id }"
              @click="selectBankCard(card)"
            >
              <view class="card-info">
                <text class="bank-name">{{ card.bankName }}</text>
                <text class="card-no">**** **** **** {{ card.cardNo.slice(-4) }}</text>
              </view>

              <wd-radio :value="card.id" />
            </view>
          </view>
        </wd-radio-group>

        <view class="selector-footer">
          <wd-button type="text" @click="handleAddBankCard">添加新银行卡</wd-button>
        </view>
      </view>
    </wd-popup>

    <!-- 优惠券选择弹窗 -->
    <wd-popup v-model="showCouponSelector" position="bottom" :safe-area-inset-bottom="true">
      <view class="coupon-selector">
        <view class="selector-header">
          <text class="selector-title">选择优惠券</text>
          <wd-button type="text" @click="showCouponSelector = false">取消</wd-button>
        </view>

        <wd-radio-group v-model="selectedCouponId" @change="handleCouponChange">
          <view class="coupon-list">
            <view
              class="coupon-item"
              :class="{ active: !selectedCoupon }"
              @click="selectCoupon(null)"
            >
              <text class="coupon-text">不使用优惠券</text>
              <wd-radio :value="null" />
            </view>

            <view
              v-for="coupon in availableCoupons"
              :key="coupon.id"
              class="coupon-item"
              :class="{ active: selectedCoupon?.id === coupon.id }"
              @click="selectCoupon(coupon)"
            >
              <view class="coupon-info">
                <text class="coupon-name">{{ coupon.name }}</text>
                <text class="coupon-desc">{{ coupon.description }}</text>
              </view>

              <view class="coupon-amount">
                <text class="amount">¥{{ coupon.amount }}</text>
                <wd-radio :value="coupon.id" />
              </view>
            </view>
          </view>
        </wd-radio-group>
      </view>
    </wd-popup>

    <!-- 支付密码弹窗 -->
    <wd-popup v-model="showPasswordInput" position="center">
      <view class="password-input">
        <view class="password-header">
          <text class="password-title">请输入支付密码</text>
          <text class="password-amount">¥{{ finalAmount }}</text>
        </view>

        <wd-password-input
          v-model="paymentPassword"
          :length="6"
          @complete="handlePasswordComplete"
        />

        <view class="password-footer">
          <wd-button type="text" @click="showPasswordInput = false">取消</wd-button>
          <wd-button type="text" @click="handleForgotPassword">忘记密码？</wd-button>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { usePaymentStore } from '@/store/payment'
import { createPayment, queryPayment, closePayment } from '@/api/payment'
import type { IPaymentMethodConfig, IBankCard, ICreatePaymentParams } from '@/api/payment.typings'

const router = useRouter()
const route = useRoute()
const paymentStore = usePaymentStore()

// 订单信息
const orderInfo = ref({
  orderNo: (route.query.orderNo as string) || '',
  totalAmount: Number(route.query.amount) || 0,
  goodsAmount: Number(route.query.goodsAmount) || 0,
  shippingFee: Number(route.query.shippingFee) || 0,
  title: (route.query.title as string) || '商品支付',
  description: (route.query.description as string) || '',
})

// 支付结果状态
const paymentResult = ref<any>(null)
// 支付超时时间（15分钟）
const PAYMENT_TIMEOUT = 15 * 60 * 1000
// 支付开始时间
const paymentStartTime = ref<number>(0)
// 开发模式标识
const isDevelopment = ref<boolean>(import.meta.env.DEV)

// 支付相关状态
const selectedMethod = ref<IPaymentMethodConfig | null>(null)
const selectedBankCard = ref<IBankCard | null>(null)
const selectedCoupon = ref<any>(null)
const availableCoupons = ref<any[]>([])
const paymentLoading = ref(false)
const paymentPassword = ref('')

// wd-radio-group 绑定的值
const selectedPaymentMethod = ref<string>('')
const selectedBankCardId = ref<string | number>('')
const selectedCouponId = ref<string | number | null>(null)

// 弹窗状态
const showBankCardSelector = ref(false)
const showCouponSelector = ref(false)
const showPasswordInput = ref(false)

// 计算属性
const availablePaymentMethods = computed(() => paymentStore.availablePaymentMethods)
const balanceInfo = computed(() => paymentStore.balanceInfo)
const bankCards = computed(() => paymentStore.bankCards)
const defaultBankCard = computed(() => paymentStore.defaultBankCard)

// 最终支付金额
const finalAmount = computed(() => {
  let amount = orderInfo.value.totalAmount
  if (selectedCoupon.value) {
    amount -= selectedCoupon.value.amount
  }
  return Math.max(0, amount)
})

// 是否可以支付
const canPay = computed(() => {
  if (!selectedMethod.value) return false

  if (selectedMethod.value.method === 'balance') {
    return balanceInfo.value && balanceInfo.value.availableBalance >= finalAmount.value
  }

  if (selectedMethod.value.method === 'credit_card') {
    return selectedBankCard.value !== null
  }

  return true
})

// 选择支付方式
const selectPaymentMethod = (method: IPaymentMethodConfig) => {
  selectedMethod.value = method
  selectedPaymentMethod.value = method.method

  // 如果选择银行卡支付，自动选择默认银行卡
  if (method.method === 'credit_card' && defaultBankCard.value) {
    selectedBankCard.value = defaultBankCard.value
    selectedBankCardId.value = defaultBankCard.value.id
  }
}

// 处理支付方式变化
const handlePaymentMethodChange = (value: string) => {
  const method = availablePaymentMethods.value.find(m => m.method === value)
  if (method) {
    selectPaymentMethod(method)
  }
}

// 选择银行卡
const selectBankCard = (card: IBankCard) => {
  selectedBankCard.value = card
  selectedBankCardId.value = card.id
  showBankCardSelector.value = false
}

// 处理银行卡变化
const handleBankCardChange = (value: string | number) => {
  const card = bankCards.value.find(c => c.id === value)
  if (card) {
    selectedBankCard.value = card
  }
}

// 选择优惠券
const selectCoupon = (coupon: any) => {
  selectedCoupon.value = coupon
  selectedCouponId.value = coupon?.id || null
  showCouponSelector.value = false
}

// 处理优惠券变化
const handleCouponChange = (value: string | number | null) => {
  if (value === null) {
    selectedCoupon.value = null
  } else {
    const coupon = availableCoupons.value.find(c => c.id === value)
    if (coupon) {
      selectedCoupon.value = coupon
    }
  }
}

// 处理支付
const handlePay = async () => {
  if (!selectedMethod.value) {
    uni.showToast({
      title: '请选择支付方式',
      icon: 'none',
    })
    return
  }

  // 余额支付需要输入密码
  if (selectedMethod.value.method === 'balance') {
    showPasswordInput.value = true
    return
  }

  // 其他支付方式直接调用支付
  await processPayment()
}

// 处理密码输入完成
const handlePasswordComplete = async () => {
  showPasswordInput.value = false
  await processPayment()
}

// 获取客户端IP（模拟）
const getClientIP = async (): Promise<string> => {
  try {
    // 在实际应用中，可以通过API获取客户端IP
    return '127.0.0.1'
  } catch {
    return '127.0.0.1'
  }
}

// 获取设备信息
const getDeviceInfo = (): string => {
  try {
    const systemInfo = uni.getSystemInfoSync()
    return `${systemInfo.platform}_${systemInfo.system}_${systemInfo.model}`
  } catch {
    return 'unknown_device'
  }
}

// 执行支付
const processPayment = async () => {
  try {
    paymentLoading.value = true

    const paymentParams: ICreatePaymentParams = {
      orderNo: orderInfo.value.orderNo,
      amount: finalAmount.value,
      paymentMethod: selectedMethod.value!.method,
      title: orderInfo.value.title,
      description: orderInfo.value.description,
      couponId: selectedCoupon.value?.id,
      bankCardId: selectedBankCard.value?.id,
      password: paymentPassword.value,
      clientIp: await getClientIP(),
      deviceInfo: getDeviceInfo(),
    }

    const result = await createPayment(paymentParams)
    paymentResult.value = result

    // 处理支付结果
    await handlePaymentResult(result)
  } catch (error: any) {
    console.error('支付失败:', error)

    // 显示具体错误信息
    const errorMessage = error?.message || error?.data?.message || '支付失败，请重试'
    uni.showToast({
      title: errorMessage,
      icon: 'none',
      duration: 3000,
    })
  } finally {
    paymentLoading.value = false
    paymentPassword.value = ''
  }
}

// 处理支付结果
const handlePaymentResult = async (paymentData: any) => {
  const { paymentId, transactionNo, paymentUrl, status, paymentParams } = paymentData

  // 如果支付已经成功（如余额支付）
  if (status === 'success') {
    router.replace({
      path: '/payment/result',
      query: {
        paymentId,
        transactionNo,
        status: 'success',
      },
    })
    return
  }

  // 根据支付方式处理
  if (selectedMethod.value?.method === 'wechat') {
    // 微信支付
    await handleWechatPay(paymentParams, paymentId, transactionNo)
  } else if (selectedMethod.value?.method === 'alipay') {
    // 支付宝支付
    await handleAlipay(paymentParams, paymentId, transactionNo)
  } else if (selectedMethod.value?.method === 'credit_card') {
    // 银行卡支付，跳转到支付页面并开始状态监控
    if (paymentUrl) {
      startPaymentStatusMonitoring(transactionNo)
      uni.navigateTo({
        url: `/pages/webview/index?url=${encodeURIComponent(paymentUrl)}&paymentId=${paymentId}&transactionNo=${transactionNo}`,
      })
    }
  } else {
    // 其他支付方式，开始状态监控
    if (paymentUrl) {
      startPaymentStatusMonitoring(transactionNo)
      uni.navigateTo({
        url: `/pages/webview/index?url=${encodeURIComponent(paymentUrl)}&paymentId=${paymentId}&transactionNo=${transactionNo}`,
      })
    }
  }
}

// 处理微信支付
const handleWechatPay = async (paymentParams: any, paymentId: string, transactionNo: string) => {
  try {
    // 调用微信支付API
    await uni.requestPayment({
      provider: 'wxpay',
      ...paymentParams,
    })

    // 支付成功，跳转到结果页面
    router.replace({
      path: '/payment/result',
      query: {
        paymentId,
        transactionNo,
        status: 'success',
      },
    })
  } catch (error: any) {
    console.error('微信支付失败:', error)

    // 处理用户取消支付
    if (error.errMsg && error.errMsg.includes('cancel')) {
      uni.showToast({
        title: '支付已取消',
        icon: 'none',
      })
    } else {
      uni.showToast({
        title: '支付失败，请重试',
        icon: 'none',
      })
    }
  }
}

// 处理支付宝支付
const handleAlipay = async (paymentParams: any, paymentId: string, transactionNo: string) => {
  try {
    // 调用支付宝支付API
    await uni.requestPayment({
      provider: 'alipay',
      ...paymentParams,
    })

    // 支付成功，跳转到结果页面
    router.replace({
      path: '/payment/result',
      query: {
        paymentId,
        transactionNo,
        status: 'success',
      },
    })
  } catch (error: any) {
    console.error('支付宝支付失败:', error)

    // 处理用户取消支付
    if (error.errMsg && error.errMsg.includes('cancel')) {
      uni.showToast({
        title: '支付已取消',
        icon: 'none',
      })
    } else {
      uni.showToast({
        title: '支付失败，请重试',
        icon: 'none',
      })
    }
  }
}

// 添加银行卡
const handleAddBankCard = () => {
  router.push('/payment/bank-card/add')
}

// 忘记密码
const handleForgotPassword = () => {
  router.push('/payment/password/reset')
}

// 支付状态轮询定时器
let paymentStatusTimer: number | null = null

// 查询支付状态
const checkPaymentStatus = async (transactionNo: string) => {
  try {
    const result = await queryPayment(transactionNo)
    return result
  } catch (error) {
    console.error('查询支付状态失败:', error)
    return null
  }
}

// 开始监控支付状态
const startPaymentStatusMonitoring = (transactionNo: string) => {
  if (paymentStatusTimer) {
    clearInterval(paymentStatusTimer)
  }

  // 记录支付开始时间
  paymentStartTime.value = Date.now()

  paymentStatusTimer = setInterval(async () => {
    // 检查是否超时
    const currentTime = Date.now()
    if (currentTime - paymentStartTime.value > PAYMENT_TIMEOUT) {
      stopPaymentStatusMonitoring()
      uni.showModal({
        title: '支付超时',
        content: '支付时间已超过15分钟，请重新发起支付',
        showCancel: false,
        success: () => {
          uni.navigateBack()
        },
      })
      return
    }

    const result = await checkPaymentStatus(transactionNo)
    if (result && result.status === 'success') {
      // 支付成功，停止轮询并跳转
      stopPaymentStatusMonitoring()
      router.replace({
        path: '/payment/result',
        query: {
          paymentId: result.paymentId,
          transactionNo: transactionNo,
          status: 'success',
        },
      })
    } else if (result && (result.status === 'failed' || result.status === 'cancelled')) {
      // 支付失败或取消，停止轮询
      stopPaymentStatusMonitoring()
      uni.showToast({
        title: result.status === 'failed' ? '支付失败' : '支付已取消',
        icon: 'none',
      })
    }
  }, 3000) // 每3秒查询一次
}

// 停止监控支付状态
const stopPaymentStatusMonitoring = () => {
  if (paymentStatusTimer) {
    clearInterval(paymentStatusTimer)
    paymentStatusTimer = null
  }
}

// 关闭支付订单
const handleClosePayment = async () => {
  if (!paymentResult.value?.transactionNo) {
    return
  }

  try {
    uni.showModal({
      title: '确认关闭',
      content: '确定要关闭当前支付订单吗？',
      success: async (res) => {
        if (res.confirm) {
          try {
            await closePayment(paymentResult.value.transactionNo)
            uni.showToast({
              title: '支付订单已关闭',
              icon: 'success',
            })

            // 返回上一页
            setTimeout(() => {
              uni.navigateBack()
            }, 1500)
          } catch (error) {
            console.error('关闭支付失败:', error)
            uni.showToast({
              title: '关闭失败，请重试',
              icon: 'none',
            })
          }
        }
      },
    })
  } catch (error) {
    console.error('关闭支付失败:', error)
  }
}

// 返回
const handleBack = () => {
  // 如果有未完成的支付，提示用户
  if (paymentResult.value && paymentResult.value.status === 'pending') {
    uni.showModal({
      title: '提示',
      content: '当前有未完成的支付订单，确定要离开吗？',
      success: (res) => {
        if (res.confirm) {
          uni.navigateBack()
        }
      },
    })
  } else {
    uni.navigateBack()
  }
}

// 初始化数据
const initData = async () => {
  try {
    // 显示加载提示
    uni.showLoading({
      title: '加载中...',
      mask: true,
    })

    // 获取支付方式
    await paymentStore.fetchPaymentMethods()

    // 获取用户余额
    await paymentStore.fetchUserBalance()

    // 获取银行卡列表
    await paymentStore.fetchBankCards()

    // 默认选择第一个可用的支付方式
    if (availablePaymentMethods.value.length > 0) {
      selectPaymentMethod(availablePaymentMethods.value[0])
    } else {
      // 如果没有可用的支付方式，显示提示
      uni.showToast({
        title: '暂无可用支付方式',
        icon: 'none',
        duration: 3000,
      })
    }
  } catch (error) {
    console.error('初始化支付页面失败:', error)
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'none',
      duration: 3000,
    })
  } finally {
    // 隐藏加载提示
    uni.hideLoading()
  }
}

onMounted(() => {
  initData()
})

// 页面卸载时清理定时器
onUnmounted(() => {
  stopPaymentStatusMonitoring()
})
</script>

<style lang="scss" scoped>
.payment-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
}

.dev-tip {
  background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 24rpx 32rpx;
  border-left: 8rpx solid #1890ff;

  .dev-content {
    display: flex;
    align-items: center;

    .dev-text {
      font-size: 26rpx;
      color: #0050b3;
      font-weight: 500;
      margin-left: 16rpx;
    }
  }
}

.payment-status-tip {
  background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 32rpx;
  border-left: 8rpx solid #ff9500;

  .status-content {
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;

    .status-text {
      font-size: 28rpx;
      color: #e65100;
      font-weight: 500;
      margin-left: 16rpx;
    }
  }

  .status-desc {
    font-size: 24rpx;
    color: #bf360c;
    opacity: 0.8;
  }
}

.order-info {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 32rpx;

  .order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24rpx;

    .order-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }

    .order-no {
      font-size: 24rpx;
      color: #999;
    }
  }

  .order-amount {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .amount-label {
      font-size: 28rpx;
      color: #666;
    }

    .amount-value {
      font-size: 36rpx;
      font-weight: 600;
      color: #ff4757;
    }
  }
}

.payment-methods {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  overflow: hidden;

  .section-title {
    padding: 32rpx;
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    border-bottom: 1px solid #f0f0f0;
  }

  .payment-icon {
    width: 48rpx;
    height: 48rpx;
    margin-right: 24rpx;
  }
}

.balance-info,
.bank-card-info {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

.discount-info {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

.payment-detail {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 32rpx;

  .detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16rpx;

    &:last-child {
      margin-bottom: 0;
    }

    &.total {
      padding-top: 16rpx;
      border-top: 1px solid #f0f0f0;
      font-weight: 600;

      .detail-value {
        font-size: 32rpx;
        color: #ff4757;
      }
    }

    .detail-label {
      font-size: 28rpx;
      color: #666;
    }

    .detail-value {
      font-size: 28rpx;
      color: #333;

      &.discount {
        color: #ff4757;
      }
    }
  }
}

.payment-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 32rpx;
  border-top: 1px solid #f0f0f0;
  z-index: 100;

  .footer-buttons {
    display: flex;
    gap: 24rpx;

    .close-payment-btn {
      flex: 1;
      max-width: 200rpx;
    }

    .main-payment-btn {
      flex: 2;
    }
  }
}

.bank-card-selector,
.coupon-selector {
  background: white;
  border-radius: 32rpx 32rpx 0 0;
  max-height: 80vh;

  .selector-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 32rpx;
    border-bottom: 1px solid #f0f0f0;

    .selector-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }
  }

  .bank-card-list,
  .coupon-list {
    max-height: 60vh;
    overflow-y: auto;
  }

  .bank-card-item,
  .coupon-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 32rpx;
    border-bottom: 1px solid #f0f0f0;

    &.active {
      background-color: #f8f9fa;
    }

    &:last-child {
      border-bottom: none;
    }
  }

  .card-info {
    .bank-name {
      display: block;
      font-size: 28rpx;
      color: #333;
      margin-bottom: 8rpx;
    }

    .card-no {
      font-size: 24rpx;
      color: #999;
    }
  }

  .coupon-info {
    flex: 1;

    .coupon-name {
      display: block;
      font-size: 28rpx;
      color: #333;
      margin-bottom: 8rpx;
    }

    .coupon-desc {
      font-size: 24rpx;
      color: #999;
    }
  }

  .coupon-amount {
    display: flex;
    align-items: center;

    .amount {
      font-size: 28rpx;
      color: #ff4757;
      margin-right: 16rpx;
    }
  }

  .selector-footer {
    padding: 32rpx;
    border-top: 1px solid #f0f0f0;
    text-align: center;
  }
}

.password-input {
  background: white;
  border-radius: 16rpx;
  padding: 48rpx 32rpx;
  width: 600rpx;

  .password-header {
    text-align: center;
    margin-bottom: 48rpx;

    .password-title {
      display: block;
      font-size: 32rpx;
      color: #333;
      margin-bottom: 16rpx;
    }

    .password-amount {
      font-size: 36rpx;
      font-weight: 600;
      color: #ff4757;
    }
  }

  .password-footer {
    display: flex;
    justify-content: space-between;
    margin-top: 32rpx;
  }
}

.error-text {
  color: #ff4757 !important;
}
</style>
