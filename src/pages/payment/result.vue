<template>
  <view class="payment-result-page">
    <!-- 自定义导航栏 -->
    <wd-navbar
      :title="isSuccess ? '支付成功' : '支付失败'"
      left-text="返回"
      left-arrow
      @click-left="handleBack"
    />

    <!-- 支付结果 -->
    <view class="result-container">
      <!-- 成功状态 -->
      <view v-if="isSuccess" class="result-success">
        <view class="success-icon">
          <wd-icon name="check-circle" size="120rpx" color="#52c41a" />
        </view>

        <view class="success-title">支付成功</view>
        <view class="success-amount">¥{{ paymentAmount }}</view>

        <view class="payment-info">
          <view class="info-item">
            <text class="info-label">支付方式</text>
            <text class="info-value">{{ paymentMethodName }}</text>
          </view>

          <view class="info-item">
            <text class="info-label">支付时间</text>
            <text class="info-value">{{ paymentTime }}</text>
          </view>

          <view class="info-item">
            <text class="info-label">订单号</text>
            <text class="info-value">{{ orderNo }}</text>
          </view>

          <view class="info-item">
            <text class="info-label">交易号</text>
            <text class="info-value">{{ transactionNo }}</text>
          </view>
        </view>
      </view>

      <!-- 失败状态 -->
      <view v-else class="result-failure">
        <view class="failure-icon">
          <wd-icon name="close-circle" size="120rpx" color="#ff4d4f" />
        </view>

        <view class="failure-title">支付失败</view>
        <view class="failure-reason">{{ failureReason }}</view>

        <view class="payment-info">
          <view class="info-item">
            <text class="info-label">订单号</text>
            <text class="info-value">{{ orderNo }}</text>
          </view>

          <view class="info-item">
            <text class="info-label">失败时间</text>
            <text class="info-value">{{ failureTime }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <view v-if="isSuccess" class="success-actions">
        <wd-button type="default" size="large" @click="handleViewOrder">查看订单</wd-button>

        <wd-button type="primary" size="large" @click="handleContinueShopping">继续购物</wd-button>
      </view>

      <view v-else class="failure-actions">
        <wd-button type="default" size="large" @click="handleRetryPayment">重新支付</wd-button>

        <wd-button type="primary" size="large" @click="handleContactService">联系客服</wd-button>
      </view>
    </view>

    <!-- 温馨提示 -->
    <view class="tips">
      <view class="tips-title">
        <wd-icon name="info-circle" size="32rpx" color="#1890ff" />
        <text>温馨提示</text>
      </view>

      <view v-if="isSuccess" class="tips-content">
        <text>• 支付成功后，我们将尽快为您安排发货</text>
        <text>• 您可以在"我的订单"中查看订单详情和物流信息</text>
        <text>• 如有疑问，请联系客服：************</text>
      </view>

      <view v-else class="tips-content">
        <text>• 支付失败可能是网络问题或余额不足导致</text>
        <text>• 请检查网络连接和支付方式后重试</text>
        <text>• 如需帮助，请联系客服：************</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { queryPayment } from '@/api/payment'
import { formatDate } from '@/utils/date'

const router = useRouter()
const route = useRoute()

// 支付结果数据
const paymentResult = ref<any>(null)
const loading = ref(true)

// 路由参数
const paymentId = route.query.paymentId as string
const transactionNoParam = route.query.transactionNo as string
const status = route.query.status as string

// 计算属性
const isSuccess = computed(() => {
  return status === 'success' || paymentResult.value?.status === 'success'
})

const paymentAmount = computed(() => {
  return paymentResult.value?.amount || route.query.amount || 0
})

const paymentMethodName = computed(() => {
  const method = paymentResult.value?.paymentMethod
  const methodMap: Record<string, string> = {
    wechat: '微信支付',
    alipay: '支付宝',
    balance: '余额支付',
    credit_card: '银行卡支付',
  }
  return methodMap[method] || '未知支付方式'
})

const paymentTime = computed(() => {
  if (paymentResult.value?.paidAt) {
    return formatDate(paymentResult.value.paidAt, 'YYYY-MM-DD HH:mm:ss')
  }
  return formatDate(new Date(), 'YYYY-MM-DD HH:mm:ss')
})

const failureTime = computed(() => {
  if (paymentResult.value?.updatedAt) {
    return formatDate(paymentResult.value.updatedAt, 'YYYY-MM-DD HH:mm:ss')
  }
  return formatDate(new Date(), 'YYYY-MM-DD HH:mm:ss')
})

const orderNo = computed(() => {
  return paymentResult.value?.orderNo || route.query.orderNo || ''
})

const transactionNo = computed(() => {
  return paymentResult.value?.transactionNo || transactionNoParam || paymentId || ''
})

const failureReason = computed(() => {
  return paymentResult.value?.failureReason || '支付过程中发生错误，请重试'
})

// 查看订单
const handleViewOrder = () => {
  router.replace({
    path: '/order/detail',
    query: {
      orderNo: orderNo.value,
    },
  })
}

// 继续购物
const handleContinueShopping = () => {
  router.replace('/home')
}

// 重新支付
const handleRetryPayment = () => {
  router.replace({
    path: '/payment',
    query: {
      orderNo: orderNo.value,
      amount: paymentAmount.value,
    },
  })
}

// 联系客服
const handleContactService = () => {
  uni.makePhoneCall({
    phoneNumber: '************',
  })
}

// 返回
const handleBack = () => {
  // 支付成功返回首页，失败返回订单页面
  if (isSuccess.value) {
    router.replace('/home')
  } else {
    router.replace('/order/list')
  }
}

// 获取支付结果详情
const fetchPaymentResult = async () => {
  // 优先使用transactionNo查询，其次使用paymentId
  const queryParam = transactionNoParam || paymentId
  if (!queryParam) {
    loading.value = false
    return
  }

  try {
    const result = await queryPayment(queryParam)
    if (result) {
      paymentResult.value = result
    }
  } catch (error) {
    console.error('获取支付结果失败:', error)
  } finally {
    loading.value = false
  }
}

// 页面加载时获取支付结果
onMounted(() => {
  fetchPaymentResult()
})
</script>

<style lang="scss" scoped>
.payment-result-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 200rpx;
}

.result-container {
  background: white;
  margin: 40rpx 20rpx;
  border-radius: 16rpx;
  padding: 80rpx 40rpx;
  text-align: center;
}

.result-success {
  .success-icon {
    margin-bottom: 32rpx;
  }

  .success-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #52c41a;
    margin-bottom: 16rpx;
  }

  .success-amount {
    font-size: 48rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 48rpx;
  }
}

.result-failure {
  .failure-icon {
    margin-bottom: 32rpx;
  }

  .failure-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #ff4d4f;
    margin-bottom: 16rpx;
  }

  .failure-reason {
    font-size: 28rpx;
    color: #666;
    margin-bottom: 48rpx;
    line-height: 1.5;
  }
}

.payment-info {
  text-align: left;

  .info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24rpx 0;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    .info-label {
      font-size: 28rpx;
      color: #666;
    }

    .info-value {
      font-size: 28rpx;
      color: #333;
      max-width: 400rpx;
      text-align: right;
      word-break: break-all;
    }
  }
}

.action-buttons {
  padding: 0 20rpx;

  .success-actions,
  .failure-actions {
    display: flex;
    gap: 20rpx;

    .wd-button {
      flex: 1;
    }
  }
}

.tips {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 32rpx;

  .tips-title {
    display: flex;
    align-items: center;
    margin-bottom: 24rpx;

    text {
      font-size: 28rpx;
      font-weight: 600;
      color: #333;
      margin-left: 12rpx;
    }
  }

  .tips-content {
    text {
      display: block;
      font-size: 26rpx;
      color: #666;
      line-height: 1.6;
      margin-bottom: 12rpx;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
</style>
