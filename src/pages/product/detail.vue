<route lang="json5">
{
  style: {
    navigationBarTitleText: '商品详情',
  },
}
</route>

<template>
  <view class="product-detail-container">
    <!-- 商品图片轮播 -->
    <view class="product-images">
      <swiper class="image-swiper" indicator-dots circular>
        <swiper-item v-for="(image, index) in productImages" :key="index">
          <image
            :src="image"
            class="product-image"
            mode="aspectFill"
            @click="previewImage(index)"
          />
        </swiper-item>
      </swiper>
    </view>

    <!-- 商品基本信息 -->
    <view class="product-info">
      <view class="product-title">{{ productDetail?.title }}</view>
      <view class="product-subtitle">{{ productDetail?.subtitle }}</view>

      <view class="price-section">
        <view class="current-price">¥{{ productDetail?.price }}</view>
        <view v-if="productDetail?.originalPrice" class="original-price">
          ¥{{ productDetail?.originalPrice }}
        </view>
        <view class="discount-tag" v-if="discountPercent">{{ discountPercent }}折</view>
      </view>

      <view class="product-stats">
        <view class="stat-item">
          <text class="stat-label">销量</text>
          <text class="stat-value">{{ productDetail?.salesCount }}</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">评分</text>
          <view class="rating-section">
            <wot-rate :value="productDetail?.rating || 0" size="14" readonly />
            <text class="rating-text">{{ productDetail?.rating }}</text>
          </view>
        </view>
        <view class="stat-item">
          <text class="stat-label">库存</text>
          <text class="stat-value">{{ productDetail?.stock }}</text>
        </view>
      </view>
    </view>

    <!-- 规格选择 -->
    <view class="spec-section" @click="showSpecModal = true">
      <view class="section-title">选择规格</view>
      <view class="selected-spec">
        <text v-if="selectedSpecs.length > 0">{{ selectedSpecText }}</text>
        <text v-else class="placeholder">请选择商品规格</text>
      </view>
      <wot-icon name="arrow-right" size="16" color="#999" />
    </view>

    <!-- 商品详情 -->
    <view class="detail-section">
      <view class="section-title">商品详情</view>
      <view class="detail-content">
        <rich-text :nodes="productDetail?.description" />
      </view>
    </view>

    <!-- 商品评价 -->
    <view class="review-section">
      <view class="section-header" @click="goToReviews">
        <view class="section-title">商品评价 ({{ productDetail?.reviewCount || 0 }})</view>
        <wot-icon name="arrow-right" size="16" color="#999" />
      </view>

      <view v-if="latestReviews.length > 0" class="review-list">
        <view v-for="review in latestReviews" :key="review.id" class="review-item">
          <view class="review-header">
            <image :src="review.userAvatar" class="user-avatar" />
            <view class="user-info">
              <text class="username">{{ review.username }}</text>
              <wot-rate :value="review.rating" size="12" readonly />
            </view>
            <text class="review-date">{{ formatDate(review.createdAt) }}</text>
          </view>
          <view class="review-content">{{ review.content }}</view>
          <view v-if="review.images?.length" class="review-images">
            <image
              v-for="(img, idx) in review.images"
              :key="idx"
              :src="img"
              class="review-image"
              @click="previewReviewImage(review.images, idx)"
            />
          </view>
        </view>
      </view>

      <view v-else class="no-reviews">
        <text>暂无评价</text>
      </view>
    </view>

    <!-- 推荐商品 -->
    <view class="recommend-section">
      <view class="section-title">推荐商品</view>
      <scroll-view scroll-x class="recommend-scroll">
        <view class="recommend-list">
          <view
            v-for="product in recommendProducts"
            :key="product.id"
            class="recommend-item"
            @click="goToProduct(product.id)"
          >
            <image :src="product.mainImage" class="recommend-image" />
            <view class="recommend-info">
              <text class="recommend-title">{{ product.title }}</text>
              <text class="recommend-price">¥{{ product.price }}</text>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 底部操作栏 -->
    <view class="bottom-actions">
      <view class="action-left">
        <view class="action-btn">
          <FavoriteButton
            v-if="productDetail"
            :type="'mall_product'"
            :target-id="productDetail.id"
            :target-name="productDetail.title"
            :target-image="productDetail.images?.[0]"
            :extra-data="{
              price: productDetail.price,
              original_price: productDetail.originalPrice,
              merchant_id: productDetail.merchantId,
            }"
            :size="20"
            show-text
          />
        </view>
        <view class="action-btn" @click="goToCart">
          <wot-icon name="cart" color="#666" size="20" />
          <text>购物车</text>
          <view v-if="cartCount > 0" class="cart-badge">{{ cartCount }}</view>
        </view>
      </view>

      <view class="action-right">
        <wot-button type="warning" @click="addToCart" :loading="addingToCart">
          加入购物车
        </wot-button>
        <wot-button type="primary" @click="buyNow" :loading="buying">立即购买</wot-button>
      </view>
    </view>

    <!-- 规格选择弹窗 -->
    <wot-popup v-model="showSpecModal" position="bottom">
      <view class="spec-modal">
        <view class="modal-header">
          <view class="product-summary">
            <image :src="currentSpecImage" class="spec-image" />
            <view class="spec-info">
              <text class="spec-price">¥{{ currentSpecPrice }}</text>
              <text class="spec-stock">库存: {{ currentSpecStock }}</text>
            </view>
          </view>
          <wot-icon name="close" @click="showSpecModal = false" />
        </view>

        <view class="spec-content">
          <view v-for="spec in productDetail?.specifications" :key="spec.name" class="spec-group">
            <text class="spec-name">{{ spec.name }}</text>
            <view class="spec-options">
              <view
                v-for="option in spec.options"
                :key="option.value"
                class="spec-option"
                :class="{
                  active: isSpecSelected(spec.name, option.value),
                  disabled: !option.available,
                }"
                @click="selectSpec(spec.name, option.value)"
              >
                <text>{{ option.value }}</text>
              </view>
            </view>
          </view>

          <view class="quantity-section">
            <text class="quantity-label">数量</text>
            <wot-stepper v-model="quantity" :min="1" :max="currentSpecStock" />
          </view>
        </view>

        <view class="spec-actions">
          <wot-button type="warning" @click="confirmAddToCart" :disabled="!canAddToCart">
            加入购物车
          </wot-button>
          <wot-button type="primary" @click="confirmBuyNow" :disabled="!canBuyNow">
            立即购买
          </wot-button>
        </view>
      </view>
    </wot-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { useProductStore } from '@/store/product'
import { useCartStore } from '@/store/cart'
import { formatDate } from '@/utils/date'
import FavoriteButton from '@/components/FavoriteButton.vue'
import { useProductHistory } from '@/composables/useHistory'
import type { IProductDetail, IProductReview } from '@/api/product.typings'

const productStore = useProductStore()
const cartStore = useCartStore()

// 页面参数
const productId = ref<number>(0)

// 商品详情数据
const productDetail = computed(() => productStore.currentProduct)
const loading = computed(() => productStore.loading)

// 商品图片
const productImages = computed(() => {
  if (!productDetail.value) return []
  return [productDetail.value.mainImage, ...(productDetail.value.images || [])]
})

// 价格计算
const discountPercent = computed(() => {
  if (!productDetail.value?.originalPrice || !productDetail.value?.price) return null
  const discount = (productDetail.value.price / productDetail.value.originalPrice) * 10
  return Math.floor(discount)
})

// 规格相关
const showSpecModal = ref(false)
const selectedSpecs = ref<Record<string, string>>({})
const quantity = ref(1)

// 规格选择文本
const selectedSpecText = computed(() => {
  const specs = Object.entries(selectedSpecs.value)
  if (specs.length === 0) return ''
  return specs.map(([key, value]) => `${key}: ${value}`).join(', ')
})

// 当前规格信息
const currentSpecImage = computed(() => {
  // 根据选中规格获取对应图片
  return productDetail.value?.mainImage || ''
})

const currentSpecPrice = computed(() => {
  // 根据选中规格计算价格
  return productDetail.value?.price || 0
})

const currentSpecStock = computed(() => {
  // 根据选中规格获取库存
  return productDetail.value?.stock || 0
})

// 是否可以添加到购物车/购买
const canAddToCart = computed(() => {
  if (!productDetail.value?.specifications) return true
  return productDetail.value.specifications.every((spec) => selectedSpecs.value[spec.name])
})

const canBuyNow = computed(() => canAddToCart.value)

// 收藏状态已由 FavoriteButton 组件管理

// 购物车数量
const cartCount = computed(() => cartStore.totalCount)

// 操作状态
const addingToCart = ref(false)
const buying = ref(false)

// 评价数据
const latestReviews = ref<IProductReview[]>([])
const recommendProducts = ref([])

/**
 * 预览商品图片
 */
const previewImage = (index: number) => {
  uni.previewImage({
    urls: productImages.value,
    current: index,
  })
}

/**
 * 预览评价图片
 */
const previewReviewImage = (images: string[], index: number) => {
  uni.previewImage({
    urls: images,
    current: index,
  })
}

/**
 * 选择规格
 */
const selectSpec = (specName: string, value: string) => {
  selectedSpecs.value[specName] = value
}

/**
 * 判断规格是否选中
 */
const isSpecSelected = (specName: string, value: string) => {
  return selectedSpecs.value[specName] === value
}

// 收藏功能已由 FavoriteButton 组件处理

/**
 * 跳转到购物车
 */
const goToCart = () => {
  uni.switchTab({
    url: '/pages/cart/index',
  })
}

/**
 * 跳转到评价页面
 */
const goToReviews = () => {
  uni.navigateTo({
    url: `/pages/product/reviews?id=${productId.value}`,
  })
}

/**
 * 跳转到其他商品
 */
const goToProduct = (id: number) => {
  uni.navigateTo({
    url: `/pages/product/detail?id=${id}`,
  })
}

/**
 * 添加到购物车
 */
const addToCart = () => {
  if (!productDetail.value?.specifications || productDetail.value.specifications.length === 0) {
    // 无规格商品直接添加
    confirmAddToCart()
  } else {
    // 有规格商品显示规格选择弹窗
    showSpecModal.value = true
  }
}

/**
 * 确认添加到购物车
 */
const confirmAddToCart = async () => {
  if (!productDetail.value) return

  addingToCart.value = true
  try {
    await cartStore.addToCart({
      productId: productDetail.value.id,
      quantity: quantity.value,
      specifications: selectedSpecs.value,
    })

    uni.showToast({
      title: '已添加到购物车',
      icon: 'success',
    })

    showSpecModal.value = false
  } catch (error) {
    uni.showToast({
      title: '添加失败',
      icon: 'error',
    })
  } finally {
    addingToCart.value = false
  }
}

/**
 * 立即购买
 */
const buyNow = () => {
  if (!productDetail.value?.specifications || productDetail.value.specifications.length === 0) {
    // 无规格商品直接购买
    confirmBuyNow()
  } else {
    // 有规格商品显示规格选择弹窗
    showSpecModal.value = true
  }
}

/**
 * 确认立即购买
 */
const confirmBuyNow = () => {
  if (!productDetail.value) return

  buying.value = true

  // 构建订单数据
  const orderData = {
    items: [
      {
        productId: productDetail.value.id,
        quantity: quantity.value,
        specifications: selectedSpecs.value,
      },
    ],
  }

  // 跳转到订单确认页面
  uni.navigateTo({
    url: `/pages/order/confirm?data=${encodeURIComponent(JSON.stringify(orderData))}`,
    success: () => {
      buying.value = false
      showSpecModal.value = false
    },
    fail: () => {
      buying.value = false
    },
  })
}

/**
 * 获取商品详情
 */
const fetchProductDetail = async () => {
  if (!productId.value) return

  try {
    await productStore.fetchProductDetail(productId.value)

    // 获取推荐商品
    await productStore.fetchRecommendProducts()
    recommendProducts.value = productStore.recommendProducts

    // 模拟获取最新评价
    latestReviews.value = [
      {
        id: 1,
        username: '用户***1',
        userAvatar: 'https://via.placeholder.com/40x40',
        rating: 5,
        content: '商品质量很好，物流很快，推荐购买！',
        images: [],
        createdAt: '2024-01-15',
      },
      {
        id: 2,
        username: '用户***2',
        userAvatar: 'https://via.placeholder.com/40x40',
        rating: 4,
        content: '性价比不错，包装精美。',
        images: ['https://via.placeholder.com/100x100'],
        createdAt: '2024-01-14',
      },
    ]
  } catch (error) {
    uni.showToast({
      title: '获取商品详情失败',
      icon: 'error',
    })
  }
}

// 页面加载
onLoad((options) => {
  if (options?.id) {
    productId.value = Number(options.id)
  }
})

// 历史记录追踪
let historyTracker: any = null

// 组件挂载
onMounted(() => {
  fetchProductDetail()
})

// 当商品详情加载完成后记录历史
watch(
  productDetail,
  (newDetail) => {
    if (newDetail && productId.value) {
      // 初始化历史记录追踪
      historyTracker = useProductHistory(
        productId.value,
        newDetail.name,
        newDetail.images?.[0] || '',
        {
          price: newDetail.price,
          original_price: newDetail.originalPrice,
          merchant_id: newDetail.merchantId,
          category_id: newDetail.categoryId,
        },
        {
          source: 'product_detail',
          autoTrack: true, // 自动追踪
        },
      )
    }
  },
  { immediate: true },
)
</script>

<style lang="scss" scoped>
.product-detail-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
}

// 商品图片
.product-images {
  background-color: #fff;
}

.image-swiper {
  height: 750rpx;
}

.product-image {
  width: 100%;
  height: 100%;
}

// 商品信息
.product-info {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.product-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
  margin-bottom: 12rpx;
}

.product-subtitle {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.price-section {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.current-price {
  font-size: 48rpx;
  color: #ff6b35;
  font-weight: bold;
}

.original-price {
  font-size: 28rpx;
  color: #999;
  text-decoration: line-through;
}

.discount-tag {
  background-color: #ff6b35;
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
}

.product-stats {
  display: flex;
  justify-content: space-between;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #999;
}

.stat-value {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.rating-section {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.rating-text {
  font-size: 24rpx;
  color: #666;
}

// 规格选择
.spec-section {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.selected-spec {
  flex: 1;
  margin-left: 20rpx;
}

.placeholder {
  color: #999;
}

// 详情内容
.detail-section {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.detail-content {
  line-height: 1.6;
}

// 评价区域
.review-section {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.review-list {
  gap: 30rpx;
}

.review-item {
  border-bottom: 1px solid #eee;
  padding-bottom: 30rpx;
  margin-bottom: 30rpx;

  &:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
  }
}

.review-header {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 16rpx;
}

.user-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
}

.user-info {
  flex: 1;
}

.username {
  font-size: 28rpx;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.review-date {
  font-size: 24rpx;
  color: #999;
}

.review-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 16rpx;
}

.review-images {
  display: flex;
  gap: 16rpx;
}

.review-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
}

.no-reviews {
  text-align: center;
  color: #999;
  padding: 60rpx 0;
}

// 推荐商品
.recommend-section {
  background-color: #fff;
  padding: 30rpx;
}

.recommend-scroll {
  white-space: nowrap;
}

.recommend-list {
  display: flex;
  gap: 20rpx;
  padding-bottom: 20rpx;
}

.recommend-item {
  min-width: 200rpx;
  text-align: center;
}

.recommend-image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 12rpx;
  margin-bottom: 12rpx;
}

.recommend-info {
  padding: 0 12rpx;
}

.recommend-title {
  font-size: 24rpx;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.recommend-price {
  font-size: 28rpx;
  color: #ff6b35;
  font-weight: bold;
}

// 底部操作栏
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 20rpx 30rpx;
  border-top: 1px solid #eee;
  display: flex;
  align-items: center;
  gap: 20rpx;
  z-index: 100;
}

.action-left {
  display: flex;
  gap: 40rpx;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  position: relative;
  font-size: 24rpx;
  color: #666;
}

.cart-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background-color: #ff6b35;
  color: #fff;
  font-size: 20rpx;
  min-width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 8rpx;
}

.action-right {
  flex: 1;
  display: flex;
  gap: 20rpx;
}

// 规格弹窗
.spec-modal {
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 30rpx;
  border-bottom: 1px solid #eee;
}

.product-summary {
  display: flex;
  gap: 20rpx;
  flex: 1;
}

.spec-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
}

.spec-info {
  flex: 1;
}

.spec-price {
  font-size: 36rpx;
  color: #ff6b35;
  font-weight: bold;
  display: block;
  margin-bottom: 12rpx;
}

.spec-stock {
  font-size: 24rpx;
  color: #666;
}

.spec-content {
  padding: 30rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.spec-group {
  margin-bottom: 40rpx;
}

.spec-name {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  display: block;
  margin-bottom: 20rpx;
}

.spec-options {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.spec-option {
  padding: 16rpx 24rpx;
  border: 2rpx solid #ddd;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #666;

  &.active {
    border-color: #ff6b35;
    color: #ff6b35;
    background-color: #fff5f0;
  }

  &.disabled {
    background-color: #f5f5f5;
    color: #ccc;
    border-color: #eee;
  }
}

.quantity-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 40rpx;
}

.quantity-label {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.spec-actions {
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
  border-top: 1px solid #eee;
}
</style>
