<!--
 * 外卖商品详情页面
 *
 * 功能特性：
 * - 使用TakeoutFoodDetail组件展示商品详情
 * - 与商家详情页面中的弹窗保持一致的样式和功能
 * - 支持商品收藏、规格选择、购物车操作等
 * - 自动记录浏览历史
 * - 响应式设计：适配移动端和桌面端
-->
<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '商品详情',
    navigationStyle: 'custom',
  },
  layout: 'tabbar',
}
</route>

<template>
  <view class="food-detail-page">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <!-- 返回按钮 -->
        <view class="back-button" @click="handleGoBack">
          <text class="back-icon">‹</text>
        </view>

        <!-- 标题 -->
        <view class="navbar-title">
          <text>商品详情</text>
        </view>

        <!-- 占位元素，保持标题居中 -->
        <view class="navbar-placeholder"></view>
      </view>
    </view>

    <!-- 页面内容 -->
    <view class="page-content">
      <!-- 加载状态 -->
      <view v-if="loading" class="loading-container">
        <text>加载中...</text>
      </view>

      <!-- 商品详情组件 -->
      <TakeoutFoodDetail
        v-else-if="foodDetail"
        :food="foodDetail"
        :is-popup="false"
        @add-to-cart="handleAddToCart"
      />

      <!-- 数据加载失败 -->
      <view v-else class="error-container">
        <text>商品信息加载失败</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
/**
 * 外卖商品详情页面逻辑
 *
 * 主要功能：
 * - 获取商品详情数据
 * - 使用TakeoutFoodDetail组件展示
 * - 处理购物车操作
 * - 记录浏览历史
 */

import { ref, watch } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { useTakeoutStore } from '@/store/takeout'
import TakeoutFoodDetail from '@/components/TakeoutFoodDetail.vue'
import { getTakeoutFoodDetail } from '@/api/takeout'
import { useFoodHistory } from '@/composables/useHistory'
import type { ITakeoutFood } from '@/api/takeout.typings'

// 响应式数据
const foodId = ref<number>(0)
const foodDetail = ref<ITakeoutFood | null>(null)
const loading = ref(false)

// Store
const takeoutStore = useTakeoutStore()

// 历史记录追踪
let historyTracker: any = null

/**
 * 页面加载
 */
onLoad((options) => {
  console.log('页面加载参数:', options)

  if (options.id) {
    const parsedId = parseInt(options.id)

    // 验证ID是否有效
    if (isNaN(parsedId) || parsedId <= 0) {
      console.error('无效的商品ID:', options.id)
      uni.showToast({
        title: '商品ID无效',
        icon: 'error',
      })
      // 返回上一页
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
      return
    }

    foodId.value = parsedId
    console.log('设置商品ID:', foodId.value)
    fetchFoodDetail()
  } else {
    console.error('缺少商品ID参数')
    uni.showToast({
      title: '缺少商品参数',
      icon: 'error',
    })
    // 返回上一页
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }
})

/**
 * 获取商品详情
 */
const fetchFoodDetail = async () => {
  try {
    loading.value = true
    console.log('开始获取商品详情，商品ID:', foodId.value)

    // 调用真实的API获取商品详情
    const response = await getTakeoutFoodDetail(foodId.value)
    console.log('获取到的商品详情:', response)

    // 提取data字段
    // 检查响应结构：如果有data字段则提取，否则直接使用响应
    const data =
      response && typeof response === 'object' && 'data' in response ? response.data : response
    console.log('提取的商品数据:', data)

    // 类型断言，确保data是商品对象
    const foodData = data as any

    // 验证返回的数据
    if (!foodData || !foodData.id) {
      throw new Error('商品数据无效')
    }

    // 设置商品详情数据
    foodDetail.value = foodData

    console.log('商品详情设置完成:', {
      food: foodDetail.value,
    })
  } catch (error) {
    console.error('获取商品详情失败:', error)
    uni.showToast({
      title: '获取商品详情失败',
      icon: 'error',
    })

    // 设置为null表示加载失败
    foodDetail.value = null
  } finally {
    loading.value = false
  }
}

/**
 * 处理添加到购物车
 */
const handleAddToCart = async (data: {
  food: ITakeoutFood
  quantity: number
  variantId?: number
  comboSelections?: Record<number, number[]>
}) => {
  try {
    console.log('添加到购物车:', data)

    await takeoutStore.addToCart({
      food_id: data.food.id,
      variant_id: data.variantId || 0,
      quantity: data.quantity,
      remark: '',
      combo_selections: [], // 简化处理，暂时不处理套餐选择
    })

    uni.showToast({
      title: '已添加到购物车',
      icon: 'success',
    })
  } catch (error) {
    console.error('添加到购物车失败:', error)
    uni.showToast({
      title: '添加失败，请稍后重试',
      icon: 'none',
    })
  }
}

/**
 * 处理返回按钮点击
 */
const handleGoBack = () => {
  uni.navigateBack({
    fail: () => {
      // 如果没有上一页，则跳转到首页
      uni.switchTab({
        url: '/pages/index/index',
      })
    },
  })
}

// 当商品详情加载完成后记录历史
watch(foodDetail, (newDetail) => {
  if (newDetail && foodId.value) {
    // 直接初始化历史记录追踪，不使用nextTick
    historyTracker = useFoodHistory(
      foodId.value,
      newDetail.name,
      newDetail.image || '',
      {
        price: newDetail.price,
        original_price: newDetail.original_price,
        merchant_id: newDetail.merchant_id,
        category_id: newDetail.category_id,
      },
      {
        source: 'food_detail',
        autoTrack: true, // 自动追踪
      },
    )
  }
})
</script>

<style lang="scss" scoped>
.food-detail-page {
  min-height: 100vh;
  background-color: #f5f7fa;
  display: flex;
  flex-direction: column;
}

// 自定义导航栏样式
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
  padding-top: var(--status-bar-height, 44rpx);
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 32rpx;
}

.back-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.05);
  transition: background-color 0.2s;

  &:active {
    background-color: rgba(0, 0, 0, 0.1);
  }
}

.back-icon {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  line-height: 1;
}

.navbar-title {
  flex: 1;
  text-align: center;

  text {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
  }
}

.navbar-placeholder {
  width: 64rpx;
  height: 64rpx;
}

// 页面内容样式
.page-content {
  flex: 1;
  margin-top: calc(var(--status-bar-height, 44rpx) + 88rpx);
  min-height: calc(100vh - var(--status-bar-height, 44rpx) - 88rpx);
}

// 加载和错误状态样式
.loading-container,
.error-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  color: #999;
  font-size: 28rpx;
}

.error-container {
  color: #ff4757;
}
</style>
