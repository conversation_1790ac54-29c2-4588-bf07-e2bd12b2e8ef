<!--
 * 外卖商家列表页面
 * 
 * 功能特性：
 * - 地理位置服务：自动获取用户位置，计算商家距离
 * - 商家列表展示：支持分页的商家信息展示
 * - 智能搜索：商家名称关键词搜索
 * - 分类筛选：全局分类导航和子分类筛选
 * - 多维排序：默认、评分、距离、配送费排序
 * - 促销展示：商家促销活动信息展示
 * - 响应式设计：适配移动端和桌面端
-->
<route lang="json5">
{
  style: {
    navigationBarTitleText: '商家列表',
    navigationStyle: 'default',
  },
  layout: 'tabbar',
}
</route>
<template>
  <view class="merchant-list-container">
    <!-- 自定义导航栏 -->
    <wd-navbar title="外卖商家" :back="false">
      <template #right>
        <view class="location-info" @click="handleGetLocation">
          <wd-icon
            name="location"
            size="16"
            color="#999"
          />
          <text class="location-text">
            点击定位
          </text>
        </view>
      </template>
    </wd-navbar>

    <!-- 搜索栏 -->
    <view class="search-bar">
      <view class="search-input">
        <wd-icon name="search" size="18" color="#999" />
        <input
          type="text"
          v-model="searchKeyword"
          placeholder="搜索商家名称"
          confirm-type="search"
          @confirm="handleSearch"
          @input="onSearchInput"
        />
        <wd-icon v-if="searchKeyword" name="clear" size="18" color="#999" @click="clearSearch" />
      </view>
    </view>

    <!-- 外卖全局分类导航 -->
    <view class="category-section">
      <view class="section-title">
        <text>美食分类</text>
      </view>

      <scroll-view
        scroll-x
        class="category-scroll"
        :show-scrollbar="false"
      >
        <view class="category-list">
          <view
            class="category-item"
            :class="{ active: currentGlobalCategory === null }"
            @click="selectGlobalCategory(null)"
          >
            <view class="category-icon-wrapper">
              <wd-icon name="list" size="24" color="#666" />
            </view>
            <text class="category-name">全部</text>
          </view>
          <view
            v-for="category in globalCategories"
            :key="category.id"
            class="category-item"
            :class="{ active: currentGlobalCategory === category.id }"
            @click="onCategoryClick(category)"
          >
            <image
              :src="category.icon || '/static/images/default-category.png'"
              mode="aspectFit"
              class="category-icon"
            />
            <text class="category-name">{{ category.name }}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 子分类筛选 -->
    <scroll-view
      v-if="subCategories.length > 0"
      scroll-x
      class="sub-category-filter"
      :show-scrollbar="false"
    >
      <view
        class="sub-category-item"
        :class="{ active: currentCategory === null }"
        @click="selectCategory(null)"
      >
        全部
      </view>
      <view
        v-for="category in subCategories"
        :key="category.id"
        class="sub-category-item"
        :class="{ active: currentCategory === category.id }"
        @click="selectCategory(category.id)"
      >
        {{ category.name }}
      </view>
    </scroll-view>

    <!-- 排序和筛选选项 -->
    <view class="filter-bar">
      <view class="sort-options">
        <view
          class="sort-item"
          :class="{ active: sortOption === 'default' }"
          @click="setSortOption('default')"
        >
          <text>综合排序</text>
          <wd-icon name="arrow-down" size="12" color="#999" />
        </view>
        <view
          class="sort-item"
          :class="{ active: sortOption === 'distance' }"
          @click="setSortOption('distance')"
        >
          <text>距离最近</text>
          <wd-icon name="arrow-down" size="12" color="#999" />
        </view>
        <view
          class="sort-item"
          :class="{ active: sortOption === 'rating' }"
          @click="setSortOption('rating')"
        >
          <text>评分最高</text>
          <wd-icon name="arrow-down" size="12" color="#999" />
        </view>
        <view
          class="sort-item"
          :class="{ active: sortOption === 'sales' }"
          @click="setSortOption('sales')"
        >
          <text>销量最高</text>
          <wd-icon name="arrow-down" size="12" color="#999" />
        </view>
      </view>
      <view class="filter-toggle" :class="{ active: onlyShowOpen }" @click="toggleOnlyShowOpen">
        <text>营业中</text>
      </view>
    </view>

    <!-- 商家列表 -->
    <scroll-view
      scroll-y
      class="merchant-scroll"
      refresher-enabled
      :refresher-triggered="refreshing"
      @refresherrefresh="onRefresh"
      @scrolltolower="onLoadMore"
    >
      <view class="merchant-list">
        <MerchantCard
          v-for="merchant in merchantStore.merchants"
          :key="merchant.id"
          :merchant="merchant"
          :distance="getMerchantDistance(merchant)"
          @click="goToMerchantDetail"
        />

        <!-- 加载状态 -->
        <view v-if="false" class="loading-state">
          <wd-loading />
          <text>加载中...</text>
        </view>

        <!-- 无数据状态 -->
        <view v-if="merchantStore.merchants.length === 0" class="empty-state">
          <image src="/static/images/empty-merchant.png" mode="aspectFit" class="empty-image" />
          <text class="empty-text">暂无商家</text>
          <text class="empty-tip">试试调整筛选条件</text>
        </view>

        <!-- 加载完成状态 -->
        <view v-if="merchantStore.merchants.length > 0 && !hasMore" class="load-complete">
          <text>已加载全部商家</text>
        </view>
      </view>
    </scroll-view>

    <!-- 购物车悬浮按钮 -->
    <view v-if="cartItemCount > 0" class="cart-fab" @click="goToCart">
      <wd-icon name="cart" size="24" color="#fff" />
      <view class="cart-badge">{{ cartItemCount }}</view>
    </view>
  </view>
</template>

<script setup lang="ts">
/**
 * 外卖商家列表页面逻辑
 *
 * 主要功能：
 * - 商家列表加载和展示
 * - 地理位置获取和距离计算
 * - 分类筛选和搜索
 * - 排序和状态筛选
 * - 分页加载
 */

import { ref, computed, watch, onMounted } from 'vue'
import { onLoad, onShow } from '@dcloudio/uni-app'
import { useTakeoutStore } from '@/store/takeout'
import { useUserStore } from '@/store/user'
import { useSystemStore } from '@/store/system'
import type { Merchant, TakeoutCategory } from '@/api/takeout.typings'
import type { IGlobalCategory } from '@/api/system.typings'
import MerchantCard from '@/components/MerchantCard.vue'

// Store实例
const takeoutStore = useTakeoutStore()
const userStore = useUserStore()
const systemStore = useSystemStore()

// 响应式状态
const subCategories = ref<TakeoutCategory[]>([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const searchKeyword = ref('')
const sortOption = ref('default')
const currentGlobalCategory = ref<number | null>(null)
const currentCategory = ref<number | null>(null)
const onlyShowOpen = ref(true)
const refreshing = ref(false)
const hasMore = ref(true)

// 计算属性
const merchantStore = computed(() => takeoutStore)
const cartItemCount = computed(() => takeoutStore.cartStats.total_quantity)
// 外卖全局分类数据 - 使用系统配置的外卖全局分类
const globalCategories = computed(() => {
  return systemStore.primaryGlobalCategories
})

/**
 * 获取用户位置
 */
async function handleGetLocation() {
  try {
    uni.getLocation({
      type: 'gcj02',
      success: (res) => {
        console.log('获取位置成功:', res)
        uni.showToast({
          title: '位置获取成功',
          icon: 'success',
        })
        // 重新加载商家列表以显示距离信息
        loadMerchants()
      },
      fail: (err) => {
        console.error('获取位置失败:', err)
        uni.showToast({
          title: '位置获取失败，请检查权限设置',
          icon: 'none',
        })
      }
    })
  } catch (error) {
    console.error('获取位置失败:', error)
    uni.showToast({
      title: '获取位置失败',
      icon: 'none',
    })
  }
}

/**
 * 加载全局分类
 */
async function loadGlobalCategories() {
  try {
    await systemStore.getGlobalCategoryList()
    
    // 默认选择第一个全局分类
    if (globalCategories.value.length > 0 && !currentGlobalCategory.value) {
      selectGlobalCategory(globalCategories.value[0].id)
    }
  } catch (error) {
    console.error('获取全局分类失败:', error)
    uni.showToast({
      title: '获取分类失败，请稍后重试',
      icon: 'none',
    })
  }
}

/**
 * 选择全局分类
 */
function selectGlobalCategory(categoryId: number | null) {
  currentGlobalCategory.value = categoryId
  currentCategory.value = null
  loadSubCategories()
  currentPage.value = 1
  loadMerchants()
}

/**
 * 分类点击处理
 */
function onCategoryClick(category: IGlobalCategory) {
  console.log('分类点击:', category)
  selectGlobalCategory(category.id)
}

/**
 * 加载子分类
 */
async function loadSubCategories() {
  if (!currentGlobalCategory.value) {
    subCategories.value = []
    return
  }

  try {
    // 这里应该调用获取子分类的API
    // const response = await takeoutService.getSubCategories(currentGlobalCategory.value)
    // subCategories.value = response.data || []
    subCategories.value = [] // 临时处理
  } catch (error) {
    console.error('获取子分类失败:', error)
    subCategories.value = []
  }
}

/**
 * 选择子分类
 */
function selectCategory(categoryId: number | null) {
  currentCategory.value = categoryId
  currentPage.value = 1
  loadMerchants()
}

/**
 * 加载商家列表
 */
async function loadMerchants() {
  try {
    const params: any = {
      page: currentPage.value,
      page_size: pageSize.value,
    }

    // 添加分类过滤
    if (currentGlobalCategory.value) {
      params.global_category_id = currentGlobalCategory.value
    }

    if (currentCategory.value) {
      params.category_id = currentCategory.value
    }

    // 添加搜索关键词
    if (searchKeyword.value) {
      params.keyword = searchKeyword.value
    }

    // 添加排序
    if (sortOption.value !== 'default') {
      params.sort = sortOption.value
    }

    // 添加营业状态筛选
    if (onlyShowOpen.value) {
      params.operation_status = 1
    }

    // 添加用户位置信息用于距离计算
    // 实际项目中需要获取用户位置并传递给API

    const result = await takeoutStore.getMerchants(params)
    total.value = result.total

    // 检查是否还有更多数据
    hasMore.value = result.list.length === pageSize.value
  } catch (error) {
    console.error('获取商家列表失败:', error)
    uni.showToast({
      title: '获取商家列表失败，请稍后重试',
      icon: 'none',
    })
  }
}

/**
 * 处理搜索输入
 */
function onSearchInput() {
  // 可以添加防抖逻辑
}

/**
 * 处理搜索
 */
function handleSearch() {
  currentPage.value = 1
  loadMerchants()
}

/**
 * 清空搜索
 */
function clearSearch() {
  searchKeyword.value = ''
  currentPage.value = 1
  loadMerchants()
}

/**
 * 设置排序选项
 */
function setSortOption(sort: string) {
  sortOption.value = sort
  currentPage.value = 1
  loadMerchants()
}

/**
 * 切换只显示营业中
 */
function toggleOnlyShowOpen() {
  onlyShowOpen.value = !onlyShowOpen.value
  currentPage.value = 1
  loadMerchants()
}

/**
 * 下拉刷新
 */
async function onRefresh() {
  refreshing.value = true
  currentPage.value = 1
  hasMore.value = true
  await loadMerchants()
  refreshing.value = false
}

/**
 * 加载更多
 */
function onLoadMore() {
  if (hasMore.value) {
    currentPage.value++
    loadMerchants()
  }
}

/**
 * 获取商家距离
 */
function getMerchantDistance(merchant: Merchant): string | null {
  if (!merchant.latitude || !merchant.longitude) {
    return null
  }

  // 暂时返回固定距离，实际项目中需要根据用户位置计算
  return '1.2km'
}

/**
 * 跳转到商家详情
 */
function goToMerchantDetail(merchantId: number) {
  uni.navigateTo({
    url: `/pages/takeout/merchant-detail?id=${merchantId}`,
  })
}

/**
 * 跳转到购物车
 */
function goToCart() {
  uni.switchTab({
    url: '/pages/cart/index',
  })
}

// 监听器
watch([sortOption, onlyShowOpen], () => {
  currentPage.value = 1
  loadMerchants()
})

// 生命周期
onLoad(() => {
  // 获取全局分类
  loadGlobalCategories()
})

onShow(() => {
  // 获取购物车统计
  takeoutStore.getCartStats()
})

onMounted(() => {
  // 初始化加载
  loadMerchants()
})
</script>

<style lang="scss">
.merchant-list-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
}

// 导航栏右侧位置信息
.location-info {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 12px;
  background-color: rgba(255, 255, 255, 0.1);

  .location-text {
    margin-left: 4px;
    font-size: 12px;
    color: #666;
  }
}

// 搜索栏
.search-bar {
  padding: 12px 16px;
  background-color: #fff;
  border-bottom: 1px solid #f0f0f0;

  .search-input {
    display: flex;
    align-items: center;
    height: 36px;
    padding: 0 12px;
    background-color: #f5f7fa;
    border-radius: 18px;

    input {
      flex: 1;
      height: 36px;
      margin: 0 8px;
      font-size: 14px;
      color: #333;

      &::placeholder {
        color: #999;
      }
    }
  }
}

// 外卖全局分类导航
.category-section {
  padding: 15px 0;
  background-color: #fff;
  margin-bottom: 10px;

  .section-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 0 15px;

    text {
      font-size: 16px;
      font-weight: 500;
      color: #333;
    }
  }

  .category-scroll {
    white-space: nowrap;
  }

  .category-list {
    display: flex;
    padding: 0 15px;
  }

  .category-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 70px;
    margin-right: 20px;
    padding: 10px 8px;
    border-radius: 8px;
    transition: background-color 0.2s;
    flex-shrink: 0;

    &:last-child {
      margin-right: 15px;
    }

    &:active {
      background-color: #f5f5f5;
    }

    &.active {
      background-color: #fff0e6;
      
      .category-name {
        color: #ff5500;
        font-weight: 500;
      }
      
      .category-icon-wrapper {
        background-color: #ff5500;
        
        wd-icon {
          color: #fff !important;
        }
      }
    }

    .category-icon {
      width: 40px;
      height: 40px;
      margin-bottom: 8px;
      border-radius: 50%;
      background-color: #f8f8f8;
    }
    
    .category-icon-wrapper {
      width: 40px;
      height: 40px;
      margin-bottom: 8px;
      border-radius: 50%;
      background-color: #f8f8f8;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .category-name {
      font-size: 12px;
      color: #333;
      text-align: center;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 60px;
    }
  }
}

// 子分类筛选
.sub-category-filter {
  display: flex;
  padding: 8px 0;
  background-color: #fff;
  white-space: nowrap;
  border-bottom: 1px solid #f0f0f0;

  .sub-category-item {
    display: inline-flex;
    align-items: center;
    margin: 0 6px;
    padding: 4px 10px;
    font-size: 13px;
    color: #666;
    border-radius: 12px;
    transition: all 0.3s;

    &:first-child {
      margin-left: 16px;
    }

    &:last-child {
      margin-right: 16px;
    }

    &.active {
      color: #ff5500;
      background-color: #fff0e6;
    }
  }
}

// 筛选栏
.filter-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background-color: #fff;
  border-bottom: 1px solid #f0f0f0;

  .sort-options {
    display: flex;
    align-items: center;

    .sort-item {
      display: flex;
      align-items: center;
      margin-right: 20px;
      font-size: 13px;
      color: #666;
      transition: all 0.3s;

      &.active {
        color: #ff5500;
        font-weight: 500;
      }

      text {
        margin-right: 4px;
      }
    }
  }

  .filter-toggle {
    display: flex;
    align-items: center;
    padding: 4px 12px;
    font-size: 13px;
    color: #666;
    background-color: #f5f7fa;
    border-radius: 12px;
    transition: all 0.3s;

    &.active {
      color: #fff;
      background-color: #ff5500;
    }
  }
}

// 商家列表滚动区域
.merchant-scroll {
  flex: 1;
  height: 0;
}

.merchant-list {
  padding: 12px 16px;
}

// 商家卡片样式已移至 MerchantCard 组件

// 状态样式
.loading-state,
.empty-state,
.load-complete {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px;
  text-align: center;
}

.loading-state {
  text {
    margin-top: 12px;
    font-size: 14px;
    color: #999;
  }
}

.empty-state {
  .empty-image {
    width: 120px;
    height: 120px;
    margin-bottom: 16px;
  }

  .empty-text {
    font-size: 16px;
    color: #666;
    margin-bottom: 8px;
  }

  .empty-tip {
    font-size: 14px;
    color: #999;
  }
}

.load-complete {
  text {
    font-size: 14px;
    color: #999;
  }
}

// 购物车悬浮按钮
.cart-fab {
  position: fixed;
  right: 20px;
  bottom: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 56px;
  height: 56px;
  background-color: #ff5500;
  border-radius: 50%;
  box-shadow: 0 4px 12px rgba(255, 85, 0, 0.3);
  z-index: 100;
  transition: all 0.3s;

  &:active {
    transform: scale(0.95);
  }

  .cart-badge {
    position: absolute;
    top: -2px;
    right: -2px;
    min-width: 18px;
    height: 18px;
    padding: 0 4px;
    font-size: 11px;
    color: #fff;
    text-align: center;
    line-height: 18px;
    background-color: #ff3b30;
    border-radius: 9px;
    font-weight: 500;
  }
}
</style>
