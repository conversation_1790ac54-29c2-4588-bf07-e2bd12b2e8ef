<route lang="json5">
{
  style: {
    navigationBarTitleText: '订单确认',
  },
  layout: 'tabbar',
}
</route>
<template>
  <view class="order-confirm-container">
    <!-- 自定义导航栏 -->
    <wd-navbar title="确认订单" />

    <scroll-view scroll-y class="order-content">
      <!-- 收货地址显示 -->
      <view class="address-section">
        <view v-if="selectedAddress" class="address-info">
          <view class="address-header">
            <wd-icon name="location" size="20" color="#ff5500" />
            <view class="address-details">
              <view class="address-name-phone">
                <text class="name">{{ selectedAddress.receiver_name }}</text>
                <text class="phone">{{ selectedAddress.receiver_mobile }}</text>
              </view>
              <view class="address-text">{{ selectedAddress.full_address }}</view>
            </view>
          </view>
        </view>

        <view v-else class="no-address">
          <wd-icon name="location" size="20" color="#ff5500" />
          <text>请先在购物车页面选择收货地址</text>
        </view>
      </view>

      <!-- 商家信息 -->
      <view v-if="merchant" class="merchant-section">
        <view class="merchant-header">
          <image :src="merchant.logo" mode="aspectFill" class="merchant-logo" />
          <view class="merchant-info">
            <view class="merchant-name">{{ merchant.name }}</view>
            <view class="merchant-delivery">
              <text>{{ merchant.delivery_time || '30-45分钟' }}</text>
              <text class="divider">|</text>
              <text>配送费¥{{ merchant.delivery_fee.toFixed(2) }}</text>
            </view>
          </view>
          <view class="merchant-status">
            <text :class="{ open: merchant.is_open, closed: !merchant.is_open }">
              {{ merchant.is_open ? '营业中' : '休息中' }}
            </text>
          </view>
        </view>
      </view>

      <!-- 订单商品 -->
      <view class="order-items-section">
        <view class="section-title">
          <text>订单商品</text>
          <text class="item-count">({{ orderItems.length }}件)</text>
        </view>

        <view class="order-items">
          <view v-for="item in orderItems" :key="item.id" class="order-item">
            <image :src="item.food.image" mode="aspectFill" class="item-image" />
            <view class="item-info">
              <view class="item-name">{{ item.food.name }}</view>

              <!-- 规格信息 -->
              <view v-if="item.variant" class="item-variant">规格：{{ item.variant.name }}</view>

              <!-- 套餐信息 -->
              <view
                v-if="item.combo_selections && item.combo_selections.length > 0"
                class="item-combo"
              >
                <view
                  v-for="selection in item.combo_selections"
                  :key="selection.combo_item_id"
                  class="combo-selection"
                >
                  {{ getComboItemName(selection.combo_item_id) }}：{{
                    getComboOptionNames(selection.option_ids).join('、')
                  }}
                </view>
              </view>

              <view class="item-price-quantity">
                <text class="price">¥{{ item.total_price.toFixed(2) }}</text>
                <text class="quantity">x{{ item.quantity }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 配送信息 -->
      <view class="delivery-section">
        <view class="section-title">配送信息</view>
        <view class="delivery-info">
          <view class="delivery-item">
            <text class="label">配送方式：</text>
            <text class="value">{{ deliveryOption.name }}</text>
          </view>
          <view class="delivery-item">
            <text class="label">预计送达：</text>
            <text class="value">{{ deliveryOption.time }}</text>
          </view>
          <view class="delivery-item">
            <text class="label">配送费：</text>
            <view class="delivery-fee-info">
              <text class="value">¥{{ deliveryFee.toFixed(2) }}</text>
              <text v-if="deliveryFeeResult?.isFree" class="free-tag">免费</text>
              <text v-else-if="deliveryFeeResult?.discountAmount > 0" class="discount-tag">
                已优惠¥{{ deliveryFeeResult.discountAmount.toFixed(2) }}
              </text>
            </view>
          </view>
          <view v-if="deliveryFeeResult?.distance" class="delivery-item">
            <text class="label">配送距离：</text>
            <text class="value">{{ deliveryFeeResult.distance.toFixed(1) }}km</text>
          </view>
        </view>
      </view>

      <!-- 优惠信息 -->
      <view v-if="selectedCoupon" class="coupon-section">
        <view class="section-title">优惠信息</view>
        <view class="coupon-info">
          <wd-icon name="gift" size="20" color="#ff5500" />
          <view class="coupon-details">
            <text class="coupon-name">{{ selectedCoupon.couponName }}</text>
            <text class="coupon-amount">-¥{{ discountAmount.toFixed(2) }}</text>
          </view>
        </view>
      </view>

      <!-- 备注信息 -->
      <view class="remark-section">
        <view class="section-title">备注信息</view>
        <textarea
          v-model="remark"
          placeholder="请输入备注信息（选填）"
          class="remark-input"
          maxlength="100"
        />
      </view>

      <!-- 支付方式 -->
      <view class="payment-section">
        <view class="section-title">支付方式</view>
        <view class="payment-methods">
          <view
            v-for="method in paymentMethods"
            :key="method.id"
            class="payment-method"
            :class="{ selected: selectedPaymentMethod.id === method.id }"
            @click="selectPaymentMethod(method)"
          >
            <image :src="method.icon" mode="aspectFit" class="payment-icon" />
            <text class="payment-name">{{ method.name }}</text>
            <wd-icon
              :name="selectedPaymentMethod.id === method.id ? 'check-circle' : 'circle'"
              :color="selectedPaymentMethod.id === method.id ? '#ff5500' : '#ddd'"
              size="20"
            />
          </view>
        </view>
      </view>

      <!-- 费用明细 -->
      <view class="cost-detail-section">
        <view class="section-title">费用明细</view>
        <view class="cost-items">
          <view class="cost-item">
            <text class="label">商品金额</text>
            <text class="value">¥{{ itemsTotalPrice.toFixed(2) }}</text>
          </view>
          <view class="cost-item">
            <text class="label">配送费</text>
            <text class="value">¥{{ deliveryFee.toFixed(2) }}</text>
          </view>
          <view v-if="discountAmount > 0" class="cost-item discount">
            <text class="label">优惠金额</text>
            <text class="value">-¥{{ discountAmount.toFixed(2) }}</text>
          </view>
          <view class="cost-item total">
            <text class="label">实付金额</text>
            <text class="value">¥{{ finalPrice.toFixed(2) }}</text>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 底部提交栏 -->
    <view class="submit-bar">
      <view class="price-info">
        <text class="total-label">实付：</text>
        <text class="total-price">¥{{ finalPrice.toFixed(2) }}</text>
      </view>
      <view class="submit-btn" :class="{ disabled: !canSubmit }" @click="submitOrder">
        {{ submitting ? '提交中...' : '提交订单' }}
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { useTakeoutStore } from '@/store/takeout'
import { useUserStore } from '@/store/user'
import { useAddressStore } from '@/store/address'
import {
  getDeliveryConfig,
  calculateDeliveryFee,
  formatDeliveryFeeText,
  getDeliveryFeeTip,
} from '@/api/delivery'
import type { IDeliveryConfig, IDeliveryFeeCalculateResponse } from '@/api/delivery'
import type {
  ITakeoutCartItem,
  ITakeoutMerchant,
  CouponInfo,
  TakeoutDelivery,
} from '@/api/takeout.typings'

// 定义本地接口类型
interface IDeliveryOption {
  id: number
  name: string
  desc: string
  time: string
  fee: number
}

interface IPaymentMethod {
  id: number
  name: string
  icon: string
  type: string
}

const takeoutStore = useTakeoutStore()
const userStore = useUserStore()
const addressStore = useAddressStore()

// 页面参数
const couponId = ref<number | undefined>()
const deliveryOptionId = ref(1)
const remarkParam = ref('')

// 订单数据
const orderItems = computed(() => takeoutStore.selectedCartItems)
const merchant = computed(() => takeoutStore.currentMerchant)
const selectedCoupon = ref<CouponInfo | null>(null)
const deliveryOption = ref<IDeliveryOption>({
  id: 1,
  name: '标准配送',
  desc: '由商家配送',
  time: '30-45分钟',
  fee: 0,
})
const remark = ref('')

// 配送费相关
const deliveryConfig = ref<IDeliveryConfig | null>(null)
const deliveryFeeResult = ref<IDeliveryFeeCalculateResponse | null>(null)
const isCalculatingDeliveryFee = ref(false)

// 地址相关
const selectedAddress = computed(
  () =>
    addressStore.defaultAddress ||
    (addressStore.addressList.length > 0 ? addressStore.addressList[0] : null),
)

// 支付方式
const selectedPaymentMethod = ref<IPaymentMethod>({
  id: 1,
  name: '微信支付',
  icon: '/static/images/wechat-pay.png',
  type: 'wechat',
})

const paymentMethods = ref<IPaymentMethod[]>([
  {
    id: 1,
    name: '微信支付',
    icon: '/static/images/wechat-pay.png',
    type: 'wechat',
  },
  {
    id: 2,
    name: '支付宝',
    icon: '/static/images/alipay.png',
    type: 'alipay',
  },
])

// 提交状态
const submitting = ref(false)

// 价格计算
const itemsTotalPrice = computed(() => {
  return orderItems.value.reduce((total, item) => total + item.price * item.quantity, 0)
})

const deliveryFee = computed(() => {
  return deliveryFeeResult.value?.deliveryFee || deliveryOption.value.fee || 0
})

const discountAmount = computed(() => {
  if (!selectedCoupon.value) return 0

  // 根据优惠券类型计算折扣
  // couponType: 1-满减券, 2-折扣券
  if (selectedCoupon.value.couponType === 1) {
    // 满减券：直接使用couponValue作为减免金额
    return Math.min(selectedCoupon.value.couponValue, itemsTotalPrice.value)
  } else if (selectedCoupon.value.couponType === 2) {
    // 折扣券：couponValue为折扣比例（如85表示8.5折）
    const discountRate = selectedCoupon.value.couponValue / 100
    return itemsTotalPrice.value * (1 - discountRate)
  }
  return 0
})

const finalPrice = computed(() => {
  return Math.max(0, itemsTotalPrice.value + deliveryFee.value - discountAmount.value)
})

// 是否可以提交
const canSubmit = computed(() => {
  return (
    !submitting.value &&
    selectedAddress.value &&
    orderItems.value.length > 0 &&
    merchant.value?.operation_status === 1 // 1表示营业中
  )
})

// 加载页面数据
const loadPageData = async () => {
  try {
    // 加载配送费配置
    await loadDeliveryConfig()

    // 加载用户地址
    await loadUserAddresses()

    // 加载优惠券信息
    if (couponId.value) {
      await loadCouponInfo()
    }

    // 加载配送选项
    await loadDeliveryOption()

    // 计算初始配送费
    await calculateOrderDeliveryFee()
  } catch (error) {
    console.error('加载页面数据失败:', error)
  }
}

// 加载用户地址
const loadUserAddresses = async () => {
  try {
    await addressStore.fetchAddressList()
  } catch (error) {
    console.error('加载用户地址失败:', error)
    uni.showToast({
      title: '加载地址失败',
      icon: 'error',
    })
  }
}

// 加载配送费配置
const loadDeliveryConfig = async () => {
  try {
    deliveryConfig.value = await getDeliveryConfig()
    console.log('配送费配置加载成功:', deliveryConfig.value)
  } catch (error) {
    console.error('加载配送费配置失败:', error)
  }
}

// 计算配送费
const calculateOrderDeliveryFee = async () => {
  if (!deliveryConfig.value || !selectedAddress.value || !merchant.value) {
    console.log('配送费计算条件不满足:', {
      hasConfig: !!deliveryConfig.value,
      hasAddress: !!selectedAddress.value,
      hasMerchant: !!merchant.value,
    })
    return
  }

  isCalculatingDeliveryFee.value = true

  try {
    const result = calculateDeliveryFee(
      deliveryConfig.value,
      itemsTotalPrice.value,
      merchant.value.id,
      undefined, // distance - 将通过坐标计算
      selectedAddress.value.location_latitude || selectedAddress.value.locationLatitude,
      selectedAddress.value.location_longitude || selectedAddress.value.locationLongitude,
      merchant.value.latitude,
      merchant.value.longitude,
    )

    deliveryFeeResult.value = result
    console.log('配送费计算结果:', result)

    // 更新配送选项的费用
    deliveryOption.value.fee = result.deliveryFee
  } catch (error) {
    console.error('计算配送费失败:', error)
  } finally {
    isCalculatingDeliveryFee.value = false
  }
}

// 加载优惠券信息
const loadCouponInfo = async () => {
  try {
    // 这里应该调用API获取优惠券详情
    // const response = await takeoutStore.getCouponDetail(couponId.value!)
    // selectedCoupon.value = response.data

    // 模拟数据 - 使用正确的CouponInfo接口
    if (couponId.value === 1) {
      selectedCoupon.value = {
        couponID: 1,
        couponName: '满30减5',
        couponType: 1, // 1表示满减券
        couponValue: 5, // 减免5元
      }
    } else if (couponId.value === 2) {
      selectedCoupon.value = {
        couponID: 2,
        couponName: '满50减10',
        couponType: 1, // 1表示满减券
        couponValue: 10, // 减免10元
      }
    }
  } catch (error) {
    console.error('加载优惠券信息失败:', error)
  }
}

// 加载配送选项
const loadDeliveryOption = async () => {
  try {
    // 根据配送选项ID设置配送信息
    if (deliveryOptionId.value === 1) {
      deliveryOption.value = {
        id: 1,
        name: '标准配送',
        desc: '由商家配送',
        time: '30-45分钟',
        fee: 0,
      }
    } else if (deliveryOptionId.value === 2) {
      deliveryOption.value = {
        id: 2,
        name: '快速配送',
        desc: '专人配送，更快送达',
        time: '20-30分钟',
        fee: 3,
      }
    }
  } catch (error) {
    console.error('加载配送选项失败:', error)
  }
}

// 获取套餐项目名称
const getComboItemName = (comboItemId: number) => {
  return '套餐选项'
}

// 获取套餐选项名称
const getComboOptionNames = (optionIds: number[]) => {
  return optionIds.map((id) => `选项${id}`)
}

// 选择支付方式
const selectPaymentMethod = (method: IPaymentMethod) => {
  selectedPaymentMethod.value = method
}

// 提交订单
const submitOrder = async () => {
  if (!canSubmit.value) return

  if (!selectedAddress.value) {
    uni.showToast({
      title: '请选择收货地址',
      icon: 'none',
    })
    return
  }

  submitting.value = true

  try {
    // 构建订单数据 - 使用正确的CreateTakeoutOrderRequest接口
    const orderData = {
      takeoutAddressID: selectedAddress.value!.id,
      deliveryTime: undefined, // 可选字段
      remark: remark.value || undefined,
      paymentMethod: selectedPaymentMethod.value.type,
      couponID: selectedCoupon.value?.couponID,
      cartItemIDs: orderItems.value.map((item) => item.cart_item_id),
    }

    // 提交订单
    const response = await takeoutStore.createOrder(orderData)

    uni.showToast({
      title: '订单提交成功',
      icon: 'success',
    })

    // 跳转到支付页面或订单详情页面
    uni.redirectTo({
      url: `/pages/takeout/order-detail?id=${response.orderID}`,
    })
  } catch (error) {
    console.error('提交订单失败:', error)
    uni.showToast({
      title: '提交订单失败',
      icon: 'none',
    })
  } finally {
    submitting.value = false
  }
}

// 页面加载
onLoad((options) => {
  if (options.couponId) {
    couponId.value = parseInt(options.couponId)
  }
  if (options.deliveryOptionId) {
    deliveryOptionId.value = parseInt(options.deliveryOptionId)
  }
  if (options.remark) {
    remarkParam.value = decodeURIComponent(options.remark)
    remark.value = remarkParam.value
  }

  loadPageData()
})
</script>

<style lang="scss">
.order-confirm-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

.order-content {
  flex: 1;
  height: 0;
  padding-bottom: 80px;
}

.address-section {
  margin: 10px;
  padding: 15px;
  background-color: #fff;
  border-radius: 8px;

  .address-info {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .address-header {
      display: flex;
      align-items: flex-start;
      flex: 1;

      .address-details {
        margin-left: 10px;
        flex: 1;

        .address-name-phone {
          display: flex;
          align-items: center;
          margin-bottom: 5px;

          .name {
            margin-right: 10px;
            font-size: 16px;
            font-weight: 500;
            color: #333;
          }

          .phone {
            font-size: 14px;
            color: #666;
          }
        }

        .address-text {
          font-size: 14px;
          color: #666;
          line-height: 1.4;
        }
      }
    }
  }

  .no-address {
    display: flex;
    align-items: center;
    justify-content: space-between;

    text {
      margin-left: 10px;
      flex: 1;
      font-size: 14px;
      color: #999;
    }
  }
}

.merchant-section {
  margin: 0 10px 10px;
  padding: 15px;
  background-color: #fff;
  border-radius: 8px;

  .merchant-header {
    display: flex;
    align-items: center;

    .merchant-logo {
      width: 50px;
      height: 50px;
      margin-right: 12px;
      border-radius: 4px;
    }

    .merchant-info {
      flex: 1;

      .merchant-name {
        margin-bottom: 5px;
        font-size: 16px;
        font-weight: 500;
        color: #333;
      }

      .merchant-delivery {
        font-size: 12px;
        color: #666;

        .divider {
          margin: 0 5px;
          color: #ddd;
        }
      }
    }

    .merchant-status {
      padding: 4px 8px;
      font-size: 12px;
      border-radius: 4px;

      .open {
        color: #52c41a;
        background-color: #f6ffed;
      }

      .closed {
        color: #ff4d4f;
        background-color: #fff2f0;
      }
    }
  }
}

.order-items-section,
.delivery-section,
.coupon-section,
.remark-section,
.payment-section,
.cost-detail-section {
  margin: 0 10px 10px;
  padding: 15px;
  background-color: #fff;
  border-radius: 8px;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  font-size: 16px;
  font-weight: 500;
  color: #333;

  .item-count {
    margin-left: 5px;
    font-size: 14px;
    font-weight: normal;
    color: #999;
  }
}

.order-items {
  .order-item {
    display: flex;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f5f5f5;

    &:last-child {
      border-bottom: none;
    }

    .item-image {
      width: 60px;
      height: 60px;
      margin-right: 12px;
      border-radius: 4px;
    }

    .item-info {
      flex: 1;

      .item-name {
        margin-bottom: 5px;
        font-size: 14px;
        font-weight: 500;
        color: #333;
      }

      .item-variant,
      .item-combo {
        margin-bottom: 3px;
        font-size: 12px;
        color: #999;

        .combo-selection {
          margin-bottom: 2px;
        }
      }

      .item-price-quantity {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .price {
          font-size: 14px;
          font-weight: 500;
          color: #ff5500;
        }

        .quantity {
          font-size: 12px;
          color: #999;
        }
      }
    }
  }
}

.delivery-info {
  .delivery-item {
    display: flex;
    align-items: center;
    margin-bottom: 5px;

    &:last-child {
      margin-bottom: 0;
    }

    .label {
      width: 80px;
      font-size: 14px;
      color: #666;
    }

    .value {
      flex: 1;
      font-size: 14px;
      color: #333;
    }

    .delivery-fee-info {
      display: flex;
      align-items: center;
      gap: 8px;
      flex: 1;

      .free-tag {
        font-size: 12px;
        color: #ff5500;
        background-color: #fff0e6;
        padding: 2px 6px;
        border-radius: 4px;
      }

      .discount-tag {
        font-size: 12px;
        color: #ff5500;
        background-color: #fff0e6;
        padding: 2px 6px;
        border-radius: 4px;
      }
    }
  }
}

.coupon-info {
  display: flex;
  align-items: center;

  .coupon-details {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-left: 10px;
    flex: 1;

    .coupon-name {
      font-size: 14px;
      color: #333;
    }

    .coupon-amount {
      font-size: 14px;
      font-weight: 500;
      color: #52c41a;
    }
  }
}

.remark-input {
  width: 100%;
  min-height: 60px;
  padding: 8px;
  font-size: 14px;
  color: #333;
  background-color: #f8f8f8;
  border-radius: 4px;
  border: none;
  resize: none;
}

.payment-methods {
  .payment-method {
    display: flex;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f5f5f5;

    &:last-child {
      border-bottom: none;
    }

    &.selected {
      background-color: #fff0e6;
    }

    .payment-icon {
      width: 24px;
      height: 24px;
      margin-right: 12px;
    }

    .payment-name {
      flex: 1;
      font-size: 14px;
      color: #333;
    }
  }
}

.cost-items {
  .cost-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }

    .label {
      font-size: 14px;
      color: #666;
    }

    .value {
      font-size: 14px;
      color: #333;
    }

    &.discount {
      .value {
        color: #52c41a;
      }
    }

    &.total {
      padding-top: 8px;
      border-top: 1px solid #f5f5f5;

      .label {
        font-size: 16px;
        font-weight: 500;
        color: #333;
      }

      .value {
        font-size: 16px;
        font-weight: 500;
        color: #ff5500;
      }
    }
  }
}

.submit-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px;
  background-color: #fff;
  border-top: 1px solid #eee;

  .price-info {
    .total-label {
      font-size: 14px;
      color: #333;
    }

    .total-price {
      font-size: 18px;
      font-weight: 500;
      color: #ff5500;
    }
  }

  .submit-btn {
    padding: 12px 30px;
    font-size: 16px;
    color: #fff;
    background-color: #ff5500;
    border-radius: 25px;

    &.disabled {
      background-color: #ccc;
    }
  }
}
</style>
