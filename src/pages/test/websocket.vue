<template>
  <view class="websocket-test-page">
    <view class="header">
      <text class="title">WebSocket服务测试 v2.0</text>
    </view>

    <view class="test-section">
      <view class="section-title">服务状态</view>
      <view class="status-grid">
        <view class="status-item">
          <text class="label">连接状态:</text>
          <text :class="['status', statusClass]">{{ statusText }}</text>
        </view>
        <view class="status-item">
          <text class="label">服务初始化:</text>
          <text :class="['status', isInitialized ? 'connected' : 'disconnected']">
            {{ isInitialized ? '已初始化' : '未初始化' }}
          </text>
        </view>
        <view class="status-item">
          <text class="label">用户登录:</text>
          <text :class="['status', userStore.isLoggedIn ? 'connected' : 'disconnected']">
            {{ userStore.isLoggedIn ? '已登录' : '未登录' }}
          </text>
        </view>
        <view class="status-item">
          <text class="label">设备ID:</text>
          <text class="status">{{ deviceId || '未设置' }}</text>
        </view>
      </view>
    </view>

    <view class="test-section">
      <view class="section-title">服务控制</view>
      <view class="test-buttons">
        <button @click="initService" :disabled="isInitialized" class="test-btn init">
          初始化服务
        </button>
        <button @click="connect" :disabled="!isInitialized" class="test-btn connect">
          手动连接
        </button>
        <button @click="disconnect" :disabled="!isConnected" class="test-btn disconnect">
          手动断开
        </button>
        <button @click="reconnect" :disabled="!isInitialized" class="test-btn reconnect">
          重新连接
        </button>
        <button @click="runDiagnostic" class="test-btn diagnostic">运行诊断</button>
        <button @click="debugStatus" class="test-btn debug">调试状态</button>
      </view>
    </view>

    <view class="test-section">
      <view class="section-title">消息测试</view>
      <view class="test-buttons">
        <button @click="simulateNotification" class="test-btn test">模拟通知消息</button>
        <button @click="simulateChatMessage" class="test-btn test">模拟聊天消息</button>
        <button @click="simulateOrderMessage" class="test-btn test">模拟订单消息</button>
        <button @click="simulateSystemMessage" class="test-btn test">模拟系统消息</button>
      </view>
    </view>

    <view class="test-section">
      <view class="section-title">
        测试日志
        <button @click="clearLogs" class="clear-btn">清空</button>
      </view>
      <scroll-view class="log-container" scroll-y>
        <view v-for="(log, index) in logs" :key="index" :class="['log-item', log.type]">
          <text class="log-time">{{ log.time }}</text>
          <text class="log-content">{{ log.message }}</text>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useWebSocketStore } from '@/store/websocket'
import { useUserStore } from '@/store/user'
import { webSocketService } from '@/services/websocket'
import { generateDeviceInfo } from '@/utils/device'

// Store
const wsStore = useWebSocketStore()
const userStore = useUserStore()

// 响应式状态
const isConnected = computed(() => wsStore.isConnected)
const isInitialized = computed(() => wsStore.isInitialized)
const currentStatus = computed(() => wsStore.currentStatus)
const deviceId = ref('')

// 状态显示
const statusText = computed(() => {
  switch (currentStatus.value) {
    case 'CONNECTED':
      return '已连接'
    case 'CONNECTING':
      return '连接中'
    case 'DISCONNECTED':
      return '已断开'
    case 'RECONNECTING':
      return '重连中'
    case 'ERROR':
      return '连接错误'
    default:
      return '未知状态'
  }
})

const statusClass = computed(() => {
  switch (currentStatus.value) {
    case 'CONNECTED':
      return 'connected'
    case 'CONNECTING':
    case 'RECONNECTING':
      return 'connecting'
    case 'ERROR':
      return 'error'
    default:
      return 'disconnected'
  }
})

// 日志记录
const logs = ref<Array<{ time: string; message: string; type: string }>>([])

const addLog = (message: string, type: 'info' | 'success' | 'warning' | 'error' = 'info') => {
  const now = new Date()
  const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`

  logs.value.unshift({
    time,
    message,
    type,
  })

  // 限制日志数量
  if (logs.value.length > 100) {
    logs.value = logs.value.slice(0, 100)
  }
}

// 服务控制方法
const initService = async () => {
  try {
    await wsStore.initService()
    addLog('WebSocket服务初始化成功', 'success')
  } catch (error) {
    addLog(`WebSocket服务初始化失败: ${error}`, 'error')
  }
}

const connect = () => {
  try {
    wsStore.connect()
    addLog('手动连接WebSocket', 'info')
  } catch (error) {
    addLog(`手动连接失败: ${error}`, 'error')
  }
}

const disconnect = () => {
  try {
    wsStore.disconnect()
    addLog('手动断开WebSocket', 'info')
  } catch (error) {
    addLog(`手动断开失败: ${error}`, 'error')
  }
}

const reconnect = () => {
  try {
    wsStore.reconnect()
    addLog('手动重新连接WebSocket', 'info')
  } catch (error) {
    addLog(`手动重连失败: ${error}`, 'error')
  }
}

// 模拟消息测试
const simulateNotification = () => {
  const mockMessage = {
    type: 'notification',
    event: 'user_order_payment_success',
    data: {
      message: '订单支付成功',
      priority: 2,
      order_id: 'test_order_123',
      amount: 99.99,
      title: '支付成功通知',
    },
  }

  try {
    // 直接调用消息处理器
    if (webSocketService && webSocketService['messageHandler']) {
      webSocketService['messageHandler'].handleMessage(mockMessage)
      addLog('模拟通知消息发送成功', 'success')
    } else {
      addLog('WebSocket服务未初始化', 'error')
    }
  } catch (error) {
    addLog(`模拟通知消息失败: ${error}`, 'error')
  }
}

const simulateChatMessage = () => {
  const mockMessage = {
    type: 'message',
    event: 'user_new_message',
    data: {
      session_id: 'test_session_123',
      sender_name: '测试用户',
      content: '这是一条测试聊天消息',
      timestamp: Date.now(),
      message: '您有新的聊天消息',
    },
  }

  try {
    if (webSocketService && webSocketService['messageHandler']) {
      webSocketService['messageHandler'].handleMessage(mockMessage)
      addLog('模拟聊天消息发送成功', 'success')
    } else {
      addLog('WebSocket服务未初始化', 'error')
    }
  } catch (error) {
    addLog(`模拟聊天消息失败: ${error}`, 'error')
  }
}

const simulateOrderMessage = () => {
  const mockMessage = {
    type: 'notification',
    event: 'user_order_status_update',
    data: {
      message: '您的订单状态已更新为已发货',
      priority: 1,
      order_id: 'test_order_456',
      status: 'shipped',
      title: '订单状态更新',
    },
  }

  try {
    if (webSocketService && webSocketService['messageHandler']) {
      webSocketService['messageHandler'].handleMessage(mockMessage)
      addLog('模拟订单消息发送成功', 'success')
    } else {
      addLog('WebSocket服务未初始化', 'error')
    }
  } catch (error) {
    addLog(`模拟订单消息失败: ${error}`, 'error')
  }
}

const simulateSystemMessage = () => {
  const mockMessage = {
    type: 'notification',
    event: 'user_system_maintenance',
    data: {
      message: '系统将于今晚23:00-01:00进行维护',
      priority: 3,
      title: '系统维护通知',
      maintenance_time: '23:00-01:00',
    },
  }

  try {
    if (webSocketService && webSocketService['messageHandler']) {
      webSocketService['messageHandler'].handleMessage(mockMessage)
      addLog('模拟系统消息发送成功', 'success')
    } else {
      addLog('WebSocket服务未初始化', 'error')
    }
  } catch (error) {
    addLog(`模拟系统消息失败: ${error}`, 'error')
  }
}

const clearLogs = () => {
  logs.value = []
  addLog('日志已清空', 'info')
}

// 诊断和调试方法
const runDiagnostic = async () => {
  try {
    addLog('开始运行WebSocket服务诊断...', 'info')

    // 动态导入调试工具
    const { runFullDiagnostic } = await import('@/utils/websocket-debug')
    const result = await runFullDiagnostic()

    addLog(
      `诊断完成 - 整体状态: ${result.overall ? '正常' : '异常'}`,
      result.overall ? 'success' : 'error',
    )
    addLog(
      `服务: ${result.service ? '✅' : '❌'}, URL: ${result.url ? '✅' : '❌'}, 设备: ${result.device ? '✅' : '❌'}, 用户: ${result.user ? '✅' : '❌'}`,
      'info',
    )
  } catch (error) {
    addLog(`诊断失败: ${error}`, 'error')
  }
}

const debugStatus = () => {
  try {
    const stats = wsStore.getConnectionStats()
    addLog(`调试状态: ${JSON.stringify(stats)}`, 'info')

    // 输出到控制台以便详细查看
    console.log('🔍 WebSocket调试状态:', {
      isInitialized: isInitialized.value,
      isConnected: isConnected.value,
      currentStatus: currentStatus.value,
      deviceId: deviceId.value,
      stats,
    })
  } catch (error) {
    addLog(`调试状态获取失败: ${error}`, 'error')
  }
}

// 生命周期
onMounted(() => {
  // 获取设备信息
  const deviceInfo = generateDeviceInfo()
  deviceId.value = deviceInfo.device_id

  addLog('WebSocket测试页面已加载', 'info')
  addLog(`当前连接状态: ${statusText.value}`, 'info')
  addLog(`服务初始化状态: ${isInitialized.value ? '已初始化' : '未初始化'}`, 'info')
  addLog(`用户登录状态: ${userStore.isLoggedIn ? '已登录' : '未登录'}`, 'info')
  addLog(`设备ID: ${deviceId.value}`, 'info')
})

onUnmounted(() => {
  // 页面卸载时不需要特殊处理，服务会继续运行
  addLog('测试页面已卸载，WebSocket服务继续运行', 'info')
})
</script>

<style lang="scss" scoped>
.websocket-test-page {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30rpx;

  .title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }
}

.test-section {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);

  .section-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
    border-bottom: 2rpx solid #eee;
    padding-bottom: 10rpx;

    .clear-btn {
      padding: 10rpx 20rpx;
      background-color: #ff4d4f;
      color: white;
      border-radius: 6rpx;
      font-size: 24rpx;
      border: none;
    }
  }
}

.status-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;

  .status-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20rpx;
    background-color: #f9f9f9;
    border-radius: 8rpx;

    .label {
      font-size: 24rpx;
      color: #999;
      margin-bottom: 10rpx;
    }

    .status {
      font-size: 28rpx;
      font-weight: bold;

      &.connected {
        color: #52c41a;
      }

      &.connecting {
        color: #1890ff;
      }

      &.disconnected {
        color: #999;
      }

      &.error {
        color: #ff4d4f;
      }
    }
  }
}

.test-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15rpx;

  .test-btn {
    height: 80rpx;
    border-radius: 8rpx;
    font-size: 28rpx;
    border: none;
    color: white;

    &.init {
      background-color: #1890ff;
    }

    &.connect {
      background-color: #52c41a;
    }

    &.disconnect {
      background-color: #ff4d4f;
    }

    &.reconnect {
      background-color: #fa8c16;
    }

    &.test {
      background-color: #13c2c2;
    }

    &.diagnostic {
      background-color: #722ed1;
    }

    &.debug {
      background-color: #eb2f96;
    }

    &:disabled {
      background-color: #f5f5f5;
      color: #ccc;
    }
  }
}

.log-container {
  height: 500rpx;
  border: 2rpx solid #eee;
  border-radius: 8rpx;
  padding: 15rpx;

  .log-item {
    display: flex;
    margin-bottom: 10rpx;
    padding: 15rpx;
    border-radius: 6rpx;
    font-size: 24rpx;

    &.info {
      background-color: #f0f9ff;
      border-left: 4rpx solid #1890ff;
    }

    &.success {
      background-color: #f6ffed;
      border-left: 4rpx solid #52c41a;
    }

    &.warning {
      background-color: #fffbe6;
      border-left: 4rpx solid #faad14;
    }

    &.error {
      background-color: #fff2f0;
      border-left: 4rpx solid #ff4d4f;
    }

    .log-time {
      color: #999;
      margin-right: 20rpx;
      min-width: 120rpx;
      font-weight: bold;
    }

    .log-content {
      color: #333;
      flex: 1;
    }
  }
}
</style>
