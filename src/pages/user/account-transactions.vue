<route lang="json5">
{
  style: {
    navigationBarTitleText: '账户明细',
  },
}
</route>

<template>
  <view class="account-transactions-container">
    <!-- 账户信息卡片 -->
    <view class="account-info-card">
      <view class="balance-section">
        <text class="balance-label">可用余额</text>
        <text class="balance-amount">¥{{ accountInfo?.balance || '0.00' }}</text>
      </view>
      <view class="account-details">
        <view class="detail-item">
          <text class="detail-label">冻结金额</text>
          <text class="detail-value">¥{{ accountInfo?.frozen_balance || '0.00' }}</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">累计充值</text>
          <text class="detail-value">¥{{ accountInfo?.total_recharge || '0.00' }}</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">累计消费</text>
          <text class="detail-value">¥{{ accountInfo?.total_consume || '0.00' }}</text>
        </view>
      </view>
    </view>

    <!-- 筛选条件 -->
    <view class="filter-section">
      <wd-picker
        v-model="selectedTransactionType"
        :columns="transactionTypeOptions"
        @confirm="handleFilterChange"
      >
        <wd-cell
          title="交易类型"
          :value="getTransactionTypeName(selectedTransactionType)"
          is-link
        />
      </wd-picker>
    </view>

    <!-- 交易记录列表 -->
    <view class="transactions-list">
      <view v-if="loading && transactions.length === 0" class="loading-container">
        <wd-loading type="spinner" />
        <text class="loading-text">加载中...</text>
      </view>

      <view v-else-if="transactions.length === 0" class="empty-container">
        <wd-icon name="order" size="80" color="#ddd" />
        <text class="empty-text">暂无交易记录</text>
      </view>

      <view v-else>
        <view
          v-for="item in transactions"
          :key="item.id"
          class="transaction-item"
          @click="handleTransactionClick(item)"
        >
          <view class="transaction-info">
            <view class="transaction-desc">{{ item.description }}</view>
            <view class="transaction-details">
              <text class="transaction-time">{{ formatTime(item.created_at) }}</text>
              <text class="transaction-no">流水号：{{ item.transaction_no }}</text>
            </view>
          </view>
          <view class="transaction-amount" :class="getAmountClass(item.operation)">
            <text class="amount-symbol">{{ item.operation === 1 ? '+' : '-' }}</text>
            <text class="amount-value">¥{{ item.amount }}</text>
          </view>
          <view class="transaction-status">
            <wd-tag :type="getStatusType(item.status)" size="small">
              {{ getStatusText(item.status) }}
            </wd-tag>
          </view>
        </view>

        <!-- 加载更多 -->
        <view v-if="hasMore" class="load-more" @click="loadMore">
          <wd-loading v-if="loadingMore" type="spinner" size="16" />
          <text class="load-more-text">{{ loadingMore ? '加载中...' : '加载更多' }}</text>
        </view>

        <view v-else-if="transactions.length > 0" class="no-more">
          <text class="no-more-text">没有更多数据了</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { onLoad, onShow, onReachBottom } from '@dcloudio/uni-app'
import { useUserStore } from '@/store/user'
import type { IAccountTransaction } from '@/api/account.typings'
import { TransactionType, OperationType, TransactionStatus } from '@/api/account.typings'

const userStore = useUserStore()

// 响应式数据
const loading = ref(false)
const loadingMore = ref(false)
const currentPage = ref(1)
const pageSize = ref(20)
const hasMore = ref(true)
const selectedTransactionType = ref(0)

// 计算属性
const accountInfo = computed(() => userStore.accountInfo)
const transactions = computed(() => userStore.accountTransactions)

// 交易类型选项
const transactionTypeOptions = ref([
  { label: '全部类型', value: 0 },
  { label: '充值', value: 1 },
  { label: '消费', value: 2 },
  { label: '退款', value: 3 },
  { label: '提现', value: 4 },
  { label: '转账', value: 5 },
])

/**
 * 获取交易类型名称
 */
const getTransactionTypeName = (type: number): string => {
  const option = transactionTypeOptions.value.find((item) => item.value === type)
  return option?.label || '全部类型'
}

/**
 * 获取金额样式类
 */
const getAmountClass = (operation: number): string => {
  return operation === OperationType.INCREASE ? 'amount-increase' : 'amount-decrease'
}

/**
 * 获取状态类型
 */
const getStatusType = (status: number): string => {
  switch (status) {
    case TransactionStatus.SUCCESS:
      return 'success'
    case TransactionStatus.FAILED:
      return 'danger'
    case TransactionStatus.PROCESSING:
      return 'warning'
    default:
      return 'info'
  }
}

/**
 * 获取状态文本
 */
const getStatusText = (status: number): string => {
  switch (status) {
    case TransactionStatus.SUCCESS:
      return '成功'
    case TransactionStatus.FAILED:
      return '失败'
    case TransactionStatus.PROCESSING:
      return '处理中'
    default:
      return '未知'
  }
}

/**
 * 格式化时间
 */
const formatTime = (timeStr: string): string => {
  if (!timeStr) return ''
  const date = new Date(timeStr)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (days === 0) {
    return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
  } else if (days === 1) {
    return '昨天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
  } else if (days < 7) {
    return `${days}天前`
  } else {
    return date.toLocaleDateString('zh-CN')
  }
}

/**
 * 处理筛选条件变化
 */
const handleFilterChange = () => {
  currentPage.value = 1
  hasMore.value = true
  loadTransactions(true)
}

/**
 * 加载交易记录
 */
const loadTransactions = async (refresh = false) => {
  if (refresh) {
    loading.value = true
  } else {
    loadingMore.value = true
  }

  try {
    const params = {
      page: currentPage.value,
      page_size: pageSize.value,
      transaction_type: selectedTransactionType.value,
    }

    const result = await userStore.fetchAccountTransactions(params)

    if (result) {
      // 检查是否还有更多数据
      hasMore.value = result.list.length === pageSize.value

      if (!refresh && result.list.length > 0) {
        currentPage.value++
      }
    }
  } catch (error) {
    console.error('加载交易记录失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'error',
    })
  } finally {
    loading.value = false
    loadingMore.value = false
  }
}

/**
 * 加载更多
 */
const loadMore = () => {
  if (!loadingMore.value && hasMore.value) {
    currentPage.value++
    loadTransactions()
  }
}

/**
 * 处理交易记录点击
 */
const handleTransactionClick = (transaction: IAccountTransaction) => {
  // 可以跳转到交易详情页面或显示详情弹窗
  uni.showModal({
    title: '交易详情',
    content: `交易类型：${getTransactionTypeName(transaction.type)}\n交易金额：${transaction.operation === 1 ? '+' : '-'}¥${transaction.amount}\n交易前余额：¥${transaction.before_balance}\n交易后余额：¥${transaction.after_balance}\n交易时间：${transaction.created_at}\n备注：${transaction.remark || '无'}`,
    showCancel: false,
  })
}

/**
 * 初始化数据
 */
const initData = async () => {
  try {
    // 获取账户信息
    await userStore.fetchAccountInfo()
    // 加载交易记录
    await loadTransactions(true)
  } catch (error) {
    console.error('初始化数据失败:', error)
  }
}

// 页面加载
onLoad(() => {
  console.log('账户明细页面加载')
})

// 页面显示
onShow(() => {
  initData()
})

// 触底加载更多
onReachBottom(() => {
  loadMore()
})

// 组件挂载
onMounted(() => {
  initData()
})
</script>

<style lang="scss" scoped>
.account-transactions-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 40rpx;
}

// 账户信息卡片
.account-info-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  margin: 20rpx;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  color: #fff;
}

.balance-section {
  text-align: center;
  margin-bottom: 30rpx;
}

.balance-label {
  font-size: 28rpx;
  opacity: 0.9;
  display: block;
  margin-bottom: 10rpx;
}

.balance-amount {
  font-size: 48rpx;
  font-weight: bold;
  display: block;
}

.account-details {
  display: flex;
  justify-content: space-between;
}

.detail-item {
  text-align: center;
  flex: 1;
}

.detail-label {
  font-size: 24rpx;
  opacity: 0.8;
  display: block;
  margin-bottom: 8rpx;
}

.detail-value {
  font-size: 28rpx;
  font-weight: bold;
  display: block;
}

// 筛选条件
.filter-section {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 20rpx;
  overflow: hidden;
}

// 交易记录列表
.transactions-list {
  margin: 0 20rpx;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 0;
  background-color: #fff;
  border-radius: 20rpx;
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #999;
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 120rpx 0;
  background-color: #fff;
  border-radius: 20rpx;
}

.empty-text {
  margin-top: 30rpx;
  font-size: 28rpx;
  color: #999;
}

.transaction-item {
  background-color: #fff;
  margin-bottom: 20rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
  position: relative;
}

.transaction-info {
  flex: 1;
  margin-right: 20rpx;
}

.transaction-desc {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.transaction-details {
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}

.transaction-time {
  font-size: 24rpx;
  color: #999;
}

.transaction-no {
  font-size: 24rpx;
  color: #666;
}

.transaction-amount {
  text-align: right;
  margin-right: 20rpx;
}

.amount-symbol {
  font-size: 24rpx;
  font-weight: bold;
}

.amount-value {
  font-size: 32rpx;
  font-weight: bold;
  margin-left: 4rpx;
}

.amount-increase {
  color: #52c41a;
}

.amount-decrease {
  color: #f5222d;
}

.transaction-status {
  position: absolute;
  top: 30rpx;
  right: 30rpx;
}

// 加载更多
.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
  background-color: #fff;
  border-radius: 20rpx;
  margin-top: 20rpx;
}

.load-more-text {
  margin-left: 16rpx;
  font-size: 28rpx;
  color: #666;
}

.no-more {
  text-align: center;
  padding: 40rpx 0;
  background-color: #fff;
  border-radius: 20rpx;
  margin-top: 20rpx;
}

.no-more-text {
  font-size: 28rpx;
  color: #999;
}
</style>
