<route lang="json5">
{
  style: {
    navigationBarTitleText: '收藏夹管理',
  },
}
</route>

<template>
  <view class="folder-manager">
    <!-- 顶部操作栏 -->
    <view class="header-actions">
      <wd-button type="primary" size="small" @click="showCreateDialog">
        <wd-icon name="add" size="16" />
        新建收藏夹
      </wd-button>
    </view>

    <!-- 收藏夹列表 -->
    <view class="folder-list">
      <view
        v-for="folder in folderList"
        :key="folder.id"
        class="folder-item"
        @click="goToFolderDetail(folder)"
      >
        <view class="folder-icon">
          <wd-icon :name="folder.icon || 'folder'" size="32" :color="folder.color || '#4D8EFF'" />
        </view>

        <view class="folder-info">
          <view class="folder-header">
            <text class="folder-name">{{ folder.name }}</text>
            <text class="folder-count">{{ folder.item_count }}项</text>
          </view>
          <text v-if="folder.description" class="folder-desc">{{ folder.description }}</text>
          <view class="folder-meta">
            <text class="folder-time">{{ formatTime(folder.updated_at) }}</text>
            <view class="folder-badges">
              <text v-if="folder.is_default" class="badge default">默认</text>
              <text v-if="folder.is_public" class="badge public">公开</text>
            </view>
          </view>
        </view>

        <view class="folder-actions" @click.stop>
          <wd-button type="text" size="small" @click="editFolder(folder)">
            <wd-icon name="edit" size="16" />
          </wd-button>
          <wd-button
            v-if="!folder.is_default"
            type="text"
            size="small"
            @click="deleteFolder(folder)"
          >
            <wd-icon name="delete" size="16" color="#ff4757" />
          </wd-button>
        </view>
      </view>
    </view>

    <!-- 创建/编辑收藏夹弹窗 -->
    <wd-popup v-model="showDialog" position="center" :close-on-click-modal="false">
      <view class="dialog-content">
        <view class="dialog-header">
          <text class="dialog-title">{{ isEdit ? '编辑收藏夹' : '新建收藏夹' }}</text>
        </view>

        <view class="dialog-body">
          <wd-cell-group>
            <wd-input
              v-model="formData.name"
              label="收藏夹名称"
              placeholder="请输入收藏夹名称"
              required
            />
            <wd-input
              v-model="formData.description"
              label="描述"
              placeholder="请输入描述（可选）"
              type="textarea"
              :rows="3"
            />

            <!-- 图标选择 -->
            <wd-cell title="图标">
              <view class="icon-selector">
                <view
                  v-for="icon in iconOptions"
                  :key="icon.name"
                  class="icon-option"
                  :class="{ active: formData.icon === icon.name }"
                  @click="formData.icon = icon.name"
                >
                  <wd-icon :name="icon.name" size="24" />
                </view>
              </view>
            </wd-cell>

            <!-- 颜色选择 -->
            <wd-cell title="颜色">
              <view class="color-selector">
                <view
                  v-for="color in colorOptions"
                  :key="color"
                  class="color-option"
                  :class="{ active: formData.color === color }"
                  :style="{ backgroundColor: color }"
                  @click="formData.color = color"
                />
              </view>
            </wd-cell>

            <wd-cell>
              <wd-checkbox v-model="formData.is_public">设为公开</wd-checkbox>
            </wd-cell>
          </wd-cell-group>
        </view>

        <view class="dialog-footer">
          <wd-button type="default" @click="closeDialog">取消</wd-button>
          <wd-button type="primary" @click="saveFolder" :loading="saving">
            {{ isEdit ? '保存' : '创建' }}
          </wd-button>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import {
  getFavoriteFolderList,
  createFavoriteFolder,
  updateFavoriteFolder,
  deleteFavoriteFolder,
} from '@/api/user'
import type {
  IFavoriteFolder,
  ICreateFolderRequest,
  IUpdateFolderRequest,
} from '@/api/user.typings'

// 响应式数据
const folderList = ref<IFavoriteFolder[]>([])
const showDialog = ref(false)
const isEdit = ref(false)
const saving = ref(false)
const currentFolder = ref<IFavoriteFolder | null>(null)

// 表单数据
const formData = ref<ICreateFolderRequest>({
  name: '',
  description: '',
  icon: 'folder',
  color: '#4D8EFF',
  is_public: false,
})

// 图标选项
const iconOptions = [
  { name: 'folder', label: '文件夹' },
  { name: 'heart', label: '爱心' },
  { name: 'star', label: '星星' },
  { name: 'bookmark', label: '书签' },
  { name: 'tag', label: '标签' },
  { name: 'gift', label: '礼物' },
  { name: 'shopping-cart', label: '购物车' },
  { name: 'food', label: '美食' },
]

// 颜色选项
const colorOptions = [
  '#4D8EFF',
  '#FF6B35',
  '#FF4757',
  '#2ED573',
  '#FFA502',
  '#A55EEA',
  '#26D0CE',
  '#778CA3',
]

// 方法
const loadFolders = async () => {
  try {
    const result = await getFavoriteFolderList()
    folderList.value = result.data.list || []
  } catch (error) {
    console.error('加载收藏夹失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'error',
    })
  }
}

const showCreateDialog = () => {
  isEdit.value = false
  currentFolder.value = null
  formData.value = {
    name: '',
    description: '',
    icon: 'folder',
    color: '#4D8EFF',
    is_public: false,
  }
  showDialog.value = true
}

const editFolder = (folder: IFavoriteFolder) => {
  isEdit.value = true
  currentFolder.value = folder
  formData.value = {
    name: folder.name,
    description: folder.description || '',
    icon: folder.icon || 'folder',
    color: folder.color || '#4D8EFF',
    is_public: folder.is_public,
  }
  showDialog.value = true
}

const closeDialog = () => {
  showDialog.value = false
}

const saveFolder = async () => {
  if (!formData.value.name.trim()) {
    uni.showToast({
      title: '请输入收藏夹名称',
      icon: 'error',
    })
    return
  }

  try {
    saving.value = true

    if (isEdit.value && currentFolder.value) {
      const updateData: IUpdateFolderRequest = {
        name: formData.value.name,
        description: formData.value.description,
        icon: formData.value.icon,
        color: formData.value.color,
        is_public: formData.value.is_public,
      }
      await updateFavoriteFolder(currentFolder.value.id, updateData)
      uni.showToast({
        title: '更新成功',
        icon: 'success',
      })
    } else {
      await createFavoriteFolder(formData.value)
      uni.showToast({
        title: '创建成功',
        icon: 'success',
      })
    }

    closeDialog()
    loadFolders()
  } catch (error) {
    console.error('保存收藏夹失败:', error)
    uni.showToast({
      title: '保存失败',
      icon: 'error',
    })
  } finally {
    saving.value = false
  }
}

const deleteFolder = async (folder: IFavoriteFolder) => {
  try {
    await uni.showModal({
      title: '确认删除',
      content: `确定要删除收藏夹"${folder.name}"吗？其中的收藏将移动到默认收藏夹。`,
    })

    await deleteFavoriteFolder(folder.id)
    uni.showToast({
      title: '删除成功',
      icon: 'success',
    })
    loadFolders()
  } catch (error) {
    if (error === 'cancel') return

    console.error('删除收藏夹失败:', error)
    uni.showToast({
      title: '删除失败',
      icon: 'error',
    })
  }
}

const goToFolderDetail = (folder: IFavoriteFolder) => {
  uni.navigateTo({
    url: `/pages/user/favorites?folder_id=${folder.id}`,
  })
}

const formatTime = (timeStr: string) => {
  const time = new Date(timeStr)
  return time.toLocaleDateString()
}

// 生命周期
onMounted(() => {
  loadFolders()
})
</script>

<style lang="scss" scoped>
.folder-manager {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header-actions {
  background-color: #fff;
  padding: 20rpx 32rpx;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: flex-end;
}

.folder-list {
  padding: 20rpx 32rpx;
}

.folder-item {
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

  .folder-icon {
    margin-right: 24rpx;
  }

  .folder-info {
    flex: 1;

    .folder-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 8rpx;

      .folder-name {
        font-size: 32rpx;
        font-weight: 500;
        color: #333;
      }

      .folder-count {
        font-size: 24rpx;
        color: #999;
      }
    }

    .folder-desc {
      font-size: 24rpx;
      color: #666;
      line-height: 1.4;
      margin-bottom: 12rpx;
    }

    .folder-meta {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .folder-time {
        font-size: 22rpx;
        color: #999;
      }

      .folder-badges {
        display: flex;
        gap: 8rpx;

        .badge {
          font-size: 20rpx;
          padding: 4rpx 8rpx;
          border-radius: 6rpx;

          &.default {
            background-color: #4d8eff;
            color: #fff;
          }

          &.public {
            background-color: #2ed573;
            color: #fff;
          }
        }
      }
    }
  }

  .folder-actions {
    display: flex;
    gap: 8rpx;
  }
}

.dialog-content {
  width: 600rpx;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
}

.dialog-header {
  padding: 32rpx;
  border-bottom: 1px solid #eee;
  text-align: center;

  .dialog-title {
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
  }
}

.dialog-body {
  padding: 32rpx;
}

.icon-selector {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;

  .icon-option {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2rpx solid #eee;
    border-radius: 12rpx;
    transition: all 0.3s;

    &.active {
      border-color: #4d8eff;
      background-color: rgba(77, 142, 255, 0.1);
    }
  }
}

.color-selector {
  display: flex;
  gap: 16rpx;

  .color-option {
    width: 40rpx;
    height: 40rpx;
    border-radius: 50%;
    border: 3rpx solid transparent;
    transition: all 0.3s;

    &.active {
      border-color: #333;
      transform: scale(1.1);
    }
  }
}

.dialog-footer {
  padding: 32rpx;
  border-top: 1px solid #eee;
  display: flex;
  gap: 24rpx;
  justify-content: flex-end;
}
</style>
