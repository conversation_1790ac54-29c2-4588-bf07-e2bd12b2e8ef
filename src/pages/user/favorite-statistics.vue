<route lang="json5">
{
  style: {
    navigationBarTitleText: '收藏统计',
  },
}
</route>

<template>
  <view class="statistics-container">
    <!-- 总体统计 -->
    <view class="overview-section">
      <view class="section-title">总体统计</view>
      <view class="overview-grid">
        <view class="overview-item">
          <text class="overview-value">{{ statistics?.total_count || 0 }}</text>
          <text class="overview-label">总收藏</text>
        </view>
        <view class="overview-item">
          <text class="overview-value">{{ statistics?.today_count || 0 }}</text>
          <text class="overview-label">今日新增</text>
        </view>
        <view class="overview-item">
          <text class="overview-value">{{ statistics?.week_count || 0 }}</text>
          <text class="overview-label">本周新增</text>
        </view>
        <view class="overview-item">
          <text class="overview-value">{{ statistics?.month_count || 0 }}</text>
          <text class="overview-label">本月新增</text>
        </view>
      </view>
    </view>

    <!-- 类型分布 -->
    <view class="type-section">
      <view class="section-title">类型分布</view>
      <view class="type-list">
        <view
          v-for="typeItem in statistics?.type_statistics || []"
          :key="typeItem.type"
          class="type-item"
          @click="goToTypeList(typeItem.type)"
        >
          <view class="type-info">
            <view class="type-icon">
              <wd-icon
                :name="getTypeIcon(typeItem.type)"
                size="24"
                :color="getTypeColor(typeItem.type)"
              />
            </view>
            <view class="type-details">
              <text class="type-name">{{ typeItem.type_name }}</text>
              <text class="type-count">{{ typeItem.count }}项</text>
            </view>
          </view>
          <view class="type-percentage">
            <text class="percentage-text">{{ typeItem.percentage.toFixed(1) }}%</text>
            <view class="percentage-bar">
              <view
                class="percentage-fill"
                :style="{
                  width: typeItem.percentage + '%',
                  backgroundColor: getTypeColor(typeItem.type),
                }"
              />
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 收藏夹分布 -->
    <view v-if="statistics?.folder_statistics?.length > 0" class="folder-section">
      <view class="section-title">收藏夹分布</view>
      <view class="folder-list">
        <view
          v-for="folderItem in statistics.folder_statistics"
          :key="folderItem.folder_id"
          class="folder-item"
          @click="goToFolderDetail(folderItem.folder_id)"
        >
          <view class="folder-info">
            <wd-icon name="folder" size="20" color="#4D8EFF" />
            <text class="folder-name">{{ folderItem.folder_name }}</text>
          </view>
          <view class="folder-stats">
            <text class="folder-count">{{ folderItem.count }}项</text>
            <text class="folder-percentage">{{ folderItem.percentage.toFixed(1) }}%</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="actions-section">
      <wd-button type="primary" @click="goToFavorites">查看所有收藏</wd-button>
      <wd-button type="default" @click="goToFolderManager">管理收藏夹</wd-button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { getFavoriteStatistics } from '@/api/user'
import { FavoriteType } from '@/api/user.typings'
import type { IFavoriteStatistics } from '@/api/user.typings'

// 响应式数据
const statistics = ref<IFavoriteStatistics | null>(null)
const loading = ref(false)

// 类型图标映射
const typeIconMap = {
  [FavoriteType.TAKEOUT_FOOD]: 'food',
  [FavoriteType.MALL_PRODUCT]: 'shopping-cart',
  [FavoriteType.MERCHANT]: 'shop',
  [FavoriteType.CATEGORY]: 'tag',
  [FavoriteType.COMBO]: 'gift',
  [FavoriteType.COUPON]: 'coupon',
}

// 类型颜色映射
const typeColorMap = {
  [FavoriteType.TAKEOUT_FOOD]: '#FF6B35',
  [FavoriteType.MALL_PRODUCT]: '#4D8EFF',
  [FavoriteType.MERCHANT]: '#2ED573',
  [FavoriteType.CATEGORY]: '#FFA502',
  [FavoriteType.COMBO]: '#A55EEA',
  [FavoriteType.COUPON]: '#FF4757',
}

// 方法
const loadStatistics = async () => {
  try {
    loading.value = true
    const result = await getFavoriteStatistics()
    statistics.value = result.data
  } catch (error) {
    console.error('加载收藏统计失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'error',
    })
  } finally {
    loading.value = false
  }
}

const getTypeIcon = (type: FavoriteType) => {
  return typeIconMap[type] || 'heart'
}

const getTypeColor = (type: FavoriteType) => {
  return typeColorMap[type] || '#666'
}

const goToTypeList = (type: FavoriteType) => {
  uni.navigateTo({
    url: `/pages/user/favorites?type=${type}`,
  })
}

const goToFolderDetail = (folderId: number) => {
  uni.navigateTo({
    url: `/pages/user/favorites?folder_id=${folderId}`,
  })
}

const goToFavorites = () => {
  uni.navigateTo({
    url: '/pages/user/favorites',
  })
}

const goToFolderManager = () => {
  uni.navigateTo({
    url: '/pages/user/favorite-folders',
  })
}

// 生命周期
onMounted(() => {
  loadStatistics()
})
</script>

<style lang="scss" scoped>
.statistics-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
}

// 总体统计
.overview-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 32rpx;
}

.overview-item {
  text-align: center;

  .overview-value {
    display: block;
    font-size: 48rpx;
    font-weight: 600;
    color: #4d8eff;
    margin-bottom: 8rpx;
  }

  .overview-label {
    font-size: 24rpx;
    color: #666;
  }
}

// 类型分布
.type-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
}

.type-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.type-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  transition: all 0.3s;

  &:active {
    background-color: #e9ecef;
  }
}

.type-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.type-icon {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(77, 142, 255, 0.1);
  border-radius: 50%;
}

.type-details {
  display: flex;
  flex-direction: column;

  .type-name {
    font-size: 28rpx;
    color: #333;
    font-weight: 500;
  }

  .type-count {
    font-size: 24rpx;
    color: #666;
    margin-top: 4rpx;
  }
}

.type-percentage {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8rpx;

  .percentage-text {
    font-size: 24rpx;
    color: #666;
    font-weight: 500;
  }

  .percentage-bar {
    width: 100rpx;
    height: 8rpx;
    background-color: #e9ecef;
    border-radius: 4rpx;
    overflow: hidden;

    .percentage-fill {
      height: 100%;
      border-radius: 4rpx;
      transition: width 0.3s;
    }
  }
}

// 收藏夹分布
.folder-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
}

.folder-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.folder-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  transition: all 0.3s;

  &:active {
    background-color: #e9ecef;
  }
}

.folder-info {
  display: flex;
  align-items: center;
  gap: 12rpx;

  .folder-name {
    font-size: 28rpx;
    color: #333;
  }
}

.folder-stats {
  display: flex;
  align-items: center;
  gap: 16rpx;

  .folder-count {
    font-size: 24rpx;
    color: #666;
  }

  .folder-percentage {
    font-size: 24rpx;
    color: #4d8eff;
    font-weight: 500;
  }
}

// 操作按钮
.actions-section {
  display: flex;
  gap: 20rpx;
  padding: 32rpx;
}
</style>
