<route lang="json5">
{
  style: {
    navigationBarTitleText: '我的收藏',
    navigationStyle: 'custom',
  },
  layout: 'tabbar',
}
</route>

<template>
  <view class="favorites-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="navbar-left" @click="goBack">
          <wd-icon name="arrow-left" size="20" color="#333" />
        </view>
        <view class="navbar-title">
          <text class="title-text">我的收藏</text>
        </view>
        <view class="navbar-right">
          <!-- 占位，保持布局平衡 -->
        </view>
      </view>
    </view>

    <!-- 顶部工具栏 -->
    <view class="toolbar">
      <view class="search-bar">
        <wd-search
          v-model="searchKeyword"
          placeholder="搜索收藏内容"
          @search="handleSearch"
          @clear="handleSearchClear"
        />
      </view>
      <view class="toolbar-actions">
        <wd-button type="text" size="small" @click="showFolderManager">
          <wd-icon name="folder" size="16" />
          收藏夹
        </wd-button>
        <wd-button type="text" size="small" @click="showFilterPanel">
          <wd-icon name="filter" size="16" />
          筛选
        </wd-button>
      </view>
    </view>

    <!-- 收藏夹选择 -->
    <view class="folder-selector" v-if="folderList.length > 0">
      <scroll-view class="folder-scroll" scroll-x>
        <view class="folder-list">
          <view
            v-for="folder in folderList"
            :key="folder.id"
            class="folder-item"
            :class="{ active: selectedFolderId === folder.id }"
            @click="selectFolder(folder.id)"
          >
            <wd-icon :name="folder.icon || 'folder'" size="16" :color="folder.color" />
            <text class="folder-name">{{ folder.name }}</text>
            <text class="folder-count">({{ folder.item_count }})</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 类型筛选 -->
    <view class="type-filter">
      <wd-tabs v-model="activeType" @change="handleTypeChange">
        <wd-tab title="全部" name="all"></wd-tab>
        <wd-tab title="外卖" name="takeout_food"></wd-tab>
        <wd-tab title="商城" name="mall_product"></wd-tab>
        <wd-tab title="商家" name="merchant"></wd-tab>
        <wd-tab title="套餐" name="combo"></wd-tab>
        <wd-tab title="优惠券" name="coupon"></wd-tab>
      </wd-tabs>
    </view>

    <!-- 收藏列表 -->
    <view class="favorites-list">
      <!-- 加载状态 -->
      <wd-loading v-if="loading" size="24" color="#4D8EFF">加载中...</wd-loading>

      <!-- 空状态 -->
      <view v-else-if="favoriteList.length === 0" class="empty-state">
        <view class="empty-icon">
          <wd-icon name="heart" size="60" color="#ddd" />
        </view>
        <text class="empty-text">暂无收藏内容</text>
        <wd-button type="primary" size="small" @click="goShopping">去逛逛</wd-button>
      </view>

      <!-- 收藏列表 -->
      <view v-else class="list-content">
        <view
          v-for="item in favoriteList"
          :key="item.id"
          class="favorite-item"
          @click="goToDetail(item)"
        >
          <view class="item-checkbox">
            <wd-checkbox v-model="item.checked" @change="handleItemCheck(item)" @click.stop />
          </view>

          <view class="item-image">
            <image
              :src="item.target_image || '/static/images/placeholder.png'"
              mode="aspectFill"
              @error="handleImageError"
            />
            <view class="item-type-badge">{{ item.type_name || '未知类型' }}</view>
          </view>

          <view class="item-info">
            <text class="item-title">{{ item.target_name }}</text>
            <text v-if="item.notes" class="item-notes">{{ item.notes }}</text>

            <!-- 额外信息显示 -->
            <view v-if="item.extra_data && typeof item.extra_data === 'object'" class="item-extra">
              <text v-if="item.extra_data.price" class="item-price">
                <text class="price-symbol">¥</text>
                <text class="price-value">{{ item.extra_data.price }}</text>
                <text v-if="item.extra_data.original_price" class="original-price">
                  ¥{{ item.extra_data.original_price }}
                </text>
              </text>
              <text v-if="item.extra_data.merchant_name" class="merchant-name">
                {{ item.extra_data.merchant_name }}
              </text>
            </view>

            <!-- 标签显示 -->
            <view
              v-if="item.tags && Array.isArray(item.tags) && item.tags.length > 0"
              class="item-tags"
            >
              <text v-for="(tag, index) in item.tags" :key="`tag-${index}-${tag}`" class="tag">
                {{ tag }}
              </text>
            </view>

            <!-- 收藏时间 -->
            <text class="item-time">
              {{ item.created_at ? formatTime(item.created_at) : '未知时间' }}
            </text>
          </view>

          <view class="item-actions">
            <wd-button
              v-if="item.type === 'takeout_food' || item.type === 'mall_product'"
              type="primary"
              size="small"
              @click.stop="addToCart(item)"
            >
              加购物车
            </wd-button>
            <wd-button v-else type="default" size="small" @click.stop="goToDetail(item)">
              查看详情
            </wd-button>
            <wd-button
              type="error"
              size="small"
              plain
              @click.stop="handleSingleDelete(item)"
              class="delete-btn"
            >
              删除
            </wd-button>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部操作栏 -->
    <view v-if="favoriteList.length > 0" class="bottom-actions">
      <view class="select-all">
        <wd-checkbox v-model="selectAll" @change="handleSelectAll">全选</wd-checkbox>
      </view>

      <view class="action-buttons">
        <wd-button
          type="default"
          size="small"
          :disabled="selectedCount === 0"
          @click="handleBatchRemove"
        >
          删除({{ selectedCount }})
        </wd-button>
        <wd-button
          type="primary"
          size="small"
          :disabled="selectedCount === 0 || !canBatchAddToCart"
          @click="handleBatchAddToCart"
        >
          加入购物车({{ selectedCount }})
        </wd-button>
      </view>
    </view>

    <!-- 加载更多 - 使用优化的组件 -->
    <OptimizedLoadmore
      v-if="favoriteList.length > 0"
      :state="loadMoreState"
      :enable-touch-optimization="true"
      @loadmore="loadMore"
      @retry="loadMore"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import {
  getFavoriteList,
  getFavoriteFolderList,
  deleteFavorite,
  batchDeleteFavorites,
} from '@/api/user'
import OptimizedLoadmore from '@/components/common/OptimizedLoadmore.vue'
import type {
  IFavoriteItem,
  IFavoriteFolder,
  IFavoriteQueryParams,
  FavoriteType,
  IBatchDeleteRequest,
} from '@/api/user.typings'

// 通用API响应类型
interface ApiResponse<T = any> {
  code: number
  message?: string
  data?: T
}

// 响应式数据
const activeType = ref<FavoriteType | 'all'>('all')
const loading = ref(false)
const favoriteList = ref<(IFavoriteItem & { checked: boolean })[]>([])
const folderList = ref<IFavoriteFolder[]>([])
const selectedFolderId = ref<number>(0)
const searchKeyword = ref('')
const pageNo = ref(1)
const pageSize = ref(20)
const hasMore = ref(true)
// 移除未使用的响应式变量
// const showEditMode = ref(false)
// const showFilterDialog = ref(false)
// const showFolderDialog = ref(false)

// 搜索定时器
let searchTimer: number | null = null

// 计算属性
const selectedCount = computed(() => {
  return favoriteList.value.filter((item) => item.checked).length
})

const selectAll = computed({
  get: () => {
    return favoriteList.value.length > 0 && favoriteList.value.every((item) => item.checked)
  },
  set: (value: boolean) => {
    favoriteList.value.forEach((item) => {
      item.checked = value
    })
  },
})

const loadMoreState = computed(() => {
  if (loading.value) return 'loading'
  if (!hasMore.value) return 'finished'
  return 'loading'
})

// 判断选中的收藏是否可以批量加入购物车（排除商家类型）
const canBatchAddToCart = computed(() => {
  const selectedItems = favoriteList.value.filter((item) => item.checked)
  return (
    selectedItems.length > 0 &&
    selectedItems.every((item) => item.type === 'takeout_food' || item.type === 'mall_product')
  )
})

// 工具方法
/**
 * 格式化时间显示
 * @param timeStr 时间字符串
 * @returns 格式化后的时间字符串
 */
const formatTime = (timeStr: string) => {
  if (!timeStr) return '未知时间'

  try {
    const time = new Date(timeStr)
    if (isNaN(time.getTime())) return '时间格式错误'

    const now = new Date()
    const diff = now.getTime() - time.getTime()

    if (diff < 60000) return '刚刚'
    if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
    if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
    if (diff < 2592000000) return `${Math.floor(diff / 86400000)}天前`

    return time.toLocaleDateString()
  } catch (error) {
    console.warn('时间格式化失败:', error)
    return '时间格式错误'
  }
}

/**
 * 处理图片加载错误
 * @param event 错误事件
 */
const handleImageError = (event: any) => {
  console.warn('图片加载失败:', event)
  // 可以在这里设置默认图片
}

// 方法
/**
 * 加载收藏夹列表
 */
const loadFolders = async () => {
  try {
    const result = await getFavoriteFolderList()
    const response = result as unknown as ApiResponse<{ list: IFavoriteFolder[] }>
    if (response?.code === 200 && response?.data) {
      folderList.value = Array.isArray(response.data.list) ? response.data.list : []
    } else {
      console.warn('获取收藏夹列表失败:', response)
      folderList.value = []
    }
  } catch (error) {
    console.error('加载收藏夹失败:', error)
    folderList.value = []
    // 可以选择显示错误提示
    // uni.showToast({ title: '加载收藏夹失败', icon: 'none' })
  }
}

/**
 * 加载收藏列表
 * @param refresh 是否刷新列表
 */
const loadFavorites = async (refresh = false) => {
  if (loading.value) return

  try {
    loading.value = true

    if (refresh) {
      pageNo.value = 1
      favoriteList.value = []
    }

    const params: IFavoriteQueryParams = {
      page: pageNo.value,
      page_size: pageSize.value,
      type: activeType.value === 'all' ? undefined : (activeType.value as FavoriteType),
      folder_id: selectedFolderId.value || undefined,
      keyword: searchKeyword.value || undefined,
    }

    const result = await getFavoriteList(params)

    const response = result as unknown as ApiResponse<{ list: IFavoriteItem[] }>
    if (response?.code === 200 && response?.data) {
      // 确保数据结构正确
      const dataList = Array.isArray(response.data.list) ? response.data.list : []
      const newItems = dataList.map((item) => ({
        ...item,
        checked: false,
      }))

      if (refresh) {
        favoriteList.value = newItems
      } else {
        favoriteList.value.push(...newItems)
      }

      hasMore.value = dataList.length === pageSize.value
    } else {
      console.warn('获取收藏列表失败:', response)
      if (refresh) {
        favoriteList.value = []
      }
      hasMore.value = false
    }
  } catch (error) {
    console.error('加载收藏列表失败:', error)
    if (refresh) {
      favoriteList.value = []
    }
    hasMore.value = false
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'error',
    })
  } finally {
    loading.value = false
  }
}

const handleTypeChange = () => {
  loadFavorites(true)
}

const selectFolder = (folderId: number) => {
  selectedFolderId.value = folderId
  loadFavorites(true)
}

const handleSearch = () => {
  loadFavorites(true)
}

const handleSearchClear = () => {
  searchKeyword.value = ''
  loadFavorites(true)
}

const showFolderManager = () => {
  // TODO: 实现收藏夹管理功能
  uni.showToast({
    title: '收藏夹管理功能开发中',
    icon: 'none',
  })
}

const showFilterPanel = () => {
  // TODO: 实现筛选功能
  uni.showToast({
    title: '筛选功能开发中',
    icon: 'none',
  })
}

const handleItemCheck = (item: IFavoriteItem & { checked: boolean }) => {
  // 单项选择逻辑已在模板中处理
  console.log('选择状态变更:', item.target_name, item.checked)
}

const handleSelectAll = () => {
  // 全选逻辑已在计算属性中处理
}

const handleBatchRemove = async () => {
  const selectedItems = favoriteList.value.filter((item) => item.checked)
  const selectedIds = selectedItems.map((item) => item.id).filter((id) => id)

  if (selectedIds.length === 0) {
    uni.showToast({
      title: '请选择要删除的收藏',
      icon: 'none',
    })
    return
  }

  try {
    await uni.showModal({
      title: '确认删除',
      content: `确定要删除选中的${selectedIds.length}个收藏吗？`,
    })

    const params: IBatchDeleteRequest = { ids: selectedIds }
    const result = await batchDeleteFavorites(params)
    const response = result as ApiResponse

    if (response?.code !== 200) {
      throw new Error(response?.message || '删除失败')
    }

    // 从列表中移除已删除的项目
    favoriteList.value = favoriteList.value.filter((item) => !selectedIds.includes(item.id))

    // 更新收藏夹数量
    await loadFolders()

    uni.showToast({
      title: '删除成功',
      icon: 'success',
    })
  } catch (error) {
    if (error === 'cancel') return

    console.error('批量删除失败:', error)
    uni.showToast({
      title: error instanceof Error ? error.message : '删除失败，请重试',
      icon: 'error',
    })
  }
}

const handleBatchAddToCart = () => {
  const selectedItems = favoriteList.value.filter((item) => item.checked)

  if (selectedItems.length === 0) return

  // 过滤出可以加入购物车的商品（排除商家类型）
  const cartItems = selectedItems.filter(
    (item) => item.type === 'takeout_food' || item.type === 'mall_product',
  )

  if (cartItems.length === 0) {
    uni.showToast({
      title: '选中的收藏无法加入购物车',
      icon: 'none',
    })
    return
  }

  // TODO: 实现批量加入购物车逻辑
  uni.showToast({
    title: `已加入购物车(${cartItems.length}件)`,
    icon: 'success',
  })

  // 取消选中状态
  selectedItems.forEach((item) => {
    item.checked = false
  })
}

// 单个删除收藏
const handleSingleDelete = async (item: IFavoriteItem) => {
  if (!item?.id) {
    uni.showToast({
      title: '收藏项ID无效',
      icon: 'none',
    })
    return
  }

  try {
    await uni.showModal({
      title: '确认删除',
      content: `确定要删除收藏"${item.target_name}"吗？`,
    })

    const result = await deleteFavorite(item.id)
    const response = result as ApiResponse

    if (response?.code !== 200) {
      throw new Error(response?.message || '删除失败')
    }

    // 从列表中移除已删除的项目
    const index = favoriteList.value.findIndex((favItem) => favItem.id === item.id)
    if (index > -1) {
      favoriteList.value.splice(index, 1)
    }

    // 更新收藏夹数量
    await loadFolders()

    uni.showToast({
      title: '删除成功',
      icon: 'success',
    })
  } catch (error) {
    if (error === 'cancel') return

    console.error('删除收藏失败:', error)
    uni.showToast({
      title: error instanceof Error ? error.message : '删除失败，请重试',
      icon: 'error',
    })
  }
}

const addToCart = async (item: IFavoriteItem) => {
  if (!item?.target_id) {
    uni.showToast({
      title: '商品信息无效',
      icon: 'none',
    })
    return
  }

  try {
    // TODO: 实现单个商品加入购物车逻辑
    console.log('加入购物车:', item)
    uni.showToast({
      title: `已将"${item.target_name}"加入购物车`,
      icon: 'success',
    })
  } catch (error) {
    console.error('加入购物车失败:', error)
    uni.showToast({
      title: error instanceof Error ? error.message : '加入购物车失败',
      icon: 'none',
    })
  }
}

const goToDetail = (item: IFavoriteItem) => {
  if (!item?.type || !item?.target_id) {
    uni.showToast({
      title: '收藏信息不完整',
      icon: 'none',
    })
    return
  }

  try {
    // 根据收藏类型跳转到不同页面
    let url = ''
    switch (item.type) {
      case 'takeout_food':
        url = `/pages/takeout/food-detail?id=${item.target_id}`
        break
      case 'mall_product':
        url = `/pages/product/detail?id=${item.target_id}`
        break
      case 'merchant':
        url = `/pages/takeout/merchant-detail?id=${item.target_id}`
        break
      case 'category':
        // 分类页面，可以跳转到分类商品列表
        url = `/pages/takeout/category?id=${item.target_id}`
        break
      case 'combo':
        // 套餐详情页面
        url = `/pages/takeout/combo-detail?id=${item.target_id}`
        break
      case 'coupon':
        // 优惠券详情页面
        url = `/pages/user/coupon-detail?id=${item.target_id}`
        break
      default:
        // 默认跳转到商品详情页
        url = `/pages/product/detail?id=${item.target_id}`
    }

    uni.navigateTo({ url })
  } catch (error) {
    console.error('页面跳转失败:', error)
    uni.showToast({
      title: '页面跳转失败',
      icon: 'none',
    })
  }
}

const goShopping = () => {
  try {
    uni.switchTab({
      url: '/pages/index/index',
    })
  } catch (error) {
    console.error('跳转首页失败:', error)
    // 如果switchTab失败，尝试使用redirectTo
    uni.redirectTo({
      url: '/pages/index/index',
    })
  }
}

/**
 * 返回上一页
 */
const goBack = () => {
  try {
    // 由于收藏页面是通过 switchTab 进入的，直接跳转回个人中心
    uni.switchTab({
      url: '/pages/user/index',
    })
  } catch (error) {
    console.error('返回失败:', error)
    // 如果返回失败，跳转到首页
    uni.switchTab({
      url: '/pages/index/index',
    })
  }
}

const loadMore = async () => {
  if (!hasMore.value || loading.value) {
    console.log('无法加载更多:', { hasMore: hasMore.value, loading: loading.value })
    return
  }

  try {
    pageNo.value++
    await loadFavorites()
  } catch (error) {
    console.error('加载更多失败:', error)
    uni.showToast({
      title: '加载更多失败',
      icon: 'none',
    })
  }
}

// 监听搜索关键词变化
watch(searchKeyword, () => {
  if (searchTimer) {
    clearTimeout(searchTimer)
  }
  searchTimer = setTimeout(() => {
    if (searchKeyword.value.length === 0) {
      loadFavorites(true)
    }
  }, 500) as unknown as number
})

// 组件卸载时清理定时器
onUnmounted(() => {
  if (searchTimer) {
    clearTimeout(searchTimer as unknown as number)
    searchTimer = null
  }
})

// 生命周期
onMounted(() => {
  loadFolders()
  loadFavorites(true)
})
</script>

<style lang="scss" scoped>
.favorites-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 220rpx; /* 增加底部内边距，为上移的操作栏和 tabbar 留出空间 */
}

// 自定义导航栏样式
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: #fff;
  border-bottom: 1px solid #eee;
  padding-top: var(--status-bar-height, 44rpx);

  .navbar-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 88rpx;
    padding: 0 32rpx;

    .navbar-left {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 60rpx;
      height: 60rpx;
      border-radius: 50%;
      transition: background-color 0.3s;

      &:active {
        background-color: #f5f5f5;
      }
    }

    .navbar-title {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;

      .title-text {
        font-size: 36rpx;
        font-weight: 600;
        color: #333;
      }
    }

    .navbar-right {
      width: 60rpx;
      height: 60rpx;
      /* 占位，保持布局平衡 */
    }
  }
}

// 顶部工具栏样式
.toolbar {
  background-color: #fff;
  padding: 20rpx 32rpx;
  border-bottom: 1px solid #eee;
  margin-top: calc(var(--status-bar-height, 44rpx) + 88rpx); /* 为自定义导航栏留出空间 */

  .search-bar {
    margin-bottom: 20rpx;
  }

  .toolbar-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

// 收藏夹选择器样式
.folder-selector {
  background-color: #fff;
  padding: 20rpx 0;
  border-bottom: 1px solid #eee;

  .folder-scroll {
    white-space: nowrap;
  }

  .folder-list {
    display: flex;
    padding: 0 32rpx;
    gap: 24rpx;
  }

  .folder-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 16rpx 24rpx;
    border-radius: 12rpx;
    background-color: #f8f9fa;
    min-width: 120rpx;
    transition: all 0.3s;

    &.active {
      background-color: #4d8eff;
      color: #fff;

      .folder-name,
      .folder-count {
        color: #fff;
      }
    }

    .folder-name {
      font-size: 24rpx;
      margin-top: 8rpx;
      color: #333;
    }

    .folder-count {
      font-size: 20rpx;
      color: #999;
      margin-top: 4rpx;
    }
  }
}

// 类型筛选样式
.type-filter {
  background-color: #fff;
  padding: 0 32rpx;
  border-bottom: 1px solid #eee;
}

.favorites-list {
  padding: 20rpx 32rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;

  .empty-icon {
    margin-bottom: 32rpx;
  }

  .empty-text {
    font-size: 28rpx;
    color: #999;
    margin-bottom: 40rpx;
  }
}

.list-content {
  .favorite-item {
    display: flex;
    align-items: center;
    background-color: #fff;
    border-radius: 16rpx;
    padding: 24rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

    .item-checkbox {
      margin-right: 24rpx;
    }

    .item-image {
      width: 160rpx;
      height: 160rpx;
      border-radius: 12rpx;
      overflow: hidden;
      margin-right: 24rpx;
      position: relative;

      image {
        width: 100%;
        height: 100%;
      }

      .item-type-badge {
        position: absolute;
        top: 8rpx;
        left: 8rpx;
        background-color: rgba(0, 0, 0, 0.6);
        color: #fff;
        font-size: 20rpx;
        padding: 4rpx 8rpx;
        border-radius: 6rpx;
      }
    }

    .item-info {
      flex: 1;

      .item-title {
        font-size: 32rpx;
        font-weight: 500;
        color: #333;
        line-height: 1.4;
        margin-bottom: 12rpx;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        overflow: hidden;
      }

      .item-notes {
        font-size: 24rpx;
        color: #666;
        line-height: 1.3;
        margin-bottom: 12rpx;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
        line-clamp: 1;
        overflow: hidden;
      }

      .item-extra {
        margin-bottom: 12rpx;

        .item-price {
          display: flex;
          align-items: baseline;
          margin-bottom: 8rpx;

          .price-symbol {
            font-size: 24rpx;
            color: #ff6b35;
            font-weight: 500;
          }

          .price-value {
            font-size: 32rpx;
            color: #ff6b35;
            font-weight: 600;
            margin-right: 16rpx;
          }

          .original-price {
            font-size: 22rpx;
            color: #999;
            text-decoration: line-through;
          }
        }

        .merchant-name {
          font-size: 22rpx;
          color: #666;
          background-color: #f8f9fa;
          padding: 4rpx 8rpx;
          border-radius: 6rpx;
        }
      }

      .item-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 8rpx;
        margin-bottom: 12rpx;

        .tag {
          font-size: 20rpx;
          color: #4d8eff;
          background-color: rgba(77, 142, 255, 0.1);
          padding: 4rpx 8rpx;
          border-radius: 6rpx;
          border: 1px solid rgba(77, 142, 255, 0.2);
        }
      }

      .item-time {
        font-size: 20rpx;
        color: #999;
      }
    }

    .item-actions {
      margin-left: 24rpx;
      display: flex;
      flex-direction: column;
      gap: 12rpx;

      .delete-btn {
        min-width: 120rpx;
      }
    }
  }
}

.bottom-actions {
  position: fixed;
  bottom: 100rpx; /* 上移避免遮挡 tabbar */
  left: 0;
  right: 0;
  background-color: #fff;
  border-top: 1px solid #eee;
  border-radius: 16rpx 16rpx 0 0;
  padding: 24rpx 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: 100;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);

  .select-all {
    display: flex;
    align-items: center;
  }

  .action-buttons {
    display: flex;
    gap: 24rpx;
  }
}
</style>
