<route lang="json5">
{
  style: {
    navigationBarTitleText: '浏览统计',
  },
}
</route>

<template>
  <view class="statistics-container">
    <!-- 总体统计 -->
    <view class="overview-section">
      <view class="section-title">总体统计</view>
      <view class="overview-grid">
        <view class="overview-item">
          <text class="overview-value">{{ statistics?.total_count || 0 }}</text>
          <text class="overview-label">总浏览</text>
        </view>
        <view class="overview-item">
          <text class="overview-value">{{ statistics?.today_count || 0 }}</text>
          <text class="overview-label">今日浏览</text>
        </view>
        <view class="overview-item">
          <text class="overview-value">{{ statistics?.week_count || 0 }}</text>
          <text class="overview-label">本周浏览</text>
        </view>
        <view class="overview-item">
          <text class="overview-value">{{ statistics?.month_count || 0 }}</text>
          <text class="overview-label">本月浏览</text>
        </view>
      </view>
    </view>

    <!-- 类型分布 -->
    <view class="type-section">
      <view class="section-title">浏览类型分布</view>
      <view class="type-list">
        <view
          v-for="typeItem in statistics?.type_statistics || []"
          :key="typeItem.type"
          class="type-item"
          @click="goToTypeHistory(typeItem.type)"
        >
          <view class="type-info">
            <view class="type-icon">
              <wd-icon
                :name="getTypeIcon(typeItem.type)"
                size="24"
                :color="getTypeColor(typeItem.type)"
              />
            </view>
            <view class="type-details">
              <text class="type-name">{{ typeItem.type_name }}</text>
              <text class="type-count">{{ typeItem.count }}次浏览</text>
            </view>
          </view>
          <view class="type-percentage">
            <text class="percentage-text">{{ typeItem.percentage.toFixed(1) }}%</text>
            <view class="percentage-bar">
              <view
                class="percentage-fill"
                :style="{
                  width: typeItem.percentage + '%',
                  backgroundColor: getTypeColor(typeItem.type),
                }"
              />
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 最近浏览 -->
    <view v-if="statistics?.recent_history?.length > 0" class="recent-section">
      <view class="section-title">最近浏览</view>
      <view class="recent-list">
        <view
          v-for="item in statistics.recent_history"
          :key="item.id"
          class="recent-item"
          @click="goToDetail(item)"
        >
          <view class="recent-image">
            <image :src="item.target_image" mode="aspectFill" />
            <view class="recent-type-badge">{{ getTypeName(item.type) }}</view>
          </view>
          <view class="recent-info">
            <text class="recent-title">{{ item.target_name }}</text>
            <view class="recent-meta">
              <text class="visit-count">访问{{ item.visit_count }}次</text>
              <text class="visit-time">{{ formatTime(item.last_visit_at) }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 热门浏览 -->
    <view v-if="statistics?.popular_items?.length > 0" class="popular-section">
      <view class="section-title">热门浏览</view>
      <view class="popular-list">
        <view
          v-for="(item, index) in statistics.popular_items"
          :key="item.target_id"
          class="popular-item"
          @click="goToPopularDetail(item)"
        >
          <view class="popular-rank">{{ index + 1 }}</view>
          <view class="popular-image">
            <image :src="item.target_image" mode="aspectFill" />
          </view>
          <view class="popular-info">
            <text class="popular-title">{{ item.target_name }}</text>
            <view class="popular-meta">
              <text class="popular-type">{{ item.type_name }}</text>
              <text class="popular-count">{{ item.visit_count }}次</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="actions-section">
      <wd-button type="primary" @click="goToHistory">查看全部历史</wd-button>
      <wd-button type="default" @click="showAnalytics">详细分析</wd-button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { getHistoryStatistics } from '@/api/user'
import { HistoryType } from '@/api/user.typings'
import type { IHistoryStatistics, IHistoryItem, IPopularHistoryItem } from '@/api/user.typings'

// 响应式数据
const statistics = ref<IHistoryStatistics | null>(null)
const loading = ref(false)

// 类型图标映射
const typeIconMap = {
  [HistoryType.TAKEOUT_FOOD]: 'food',
  [HistoryType.MALL_PRODUCT]: 'shopping-cart',
  [HistoryType.MERCHANT]: 'shop',
  [HistoryType.CATEGORY]: 'tag',
  [HistoryType.SEARCH]: 'search',
  [HistoryType.PAGE]: 'file',
}

// 类型颜色映射
const typeColorMap = {
  [HistoryType.TAKEOUT_FOOD]: '#FF6B35',
  [HistoryType.MALL_PRODUCT]: '#4D8EFF',
  [HistoryType.MERCHANT]: '#2ED573',
  [HistoryType.CATEGORY]: '#FFA502',
  [HistoryType.SEARCH]: '#A55EEA',
  [HistoryType.PAGE]: '#778CA3',
}

// 类型名称映射
const typeNameMap = {
  [HistoryType.TAKEOUT_FOOD]: '外卖',
  [HistoryType.MALL_PRODUCT]: '商城',
  [HistoryType.MERCHANT]: '商家',
  [HistoryType.CATEGORY]: '分类',
  [HistoryType.SEARCH]: '搜索',
  [HistoryType.PAGE]: '页面',
}

// 方法
const loadStatistics = async () => {
  try {
    loading.value = true
    const result = await getHistoryStatistics()
    statistics.value = result.data
  } catch (error) {
    console.error('加载浏览统计失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'error',
    })
  } finally {
    loading.value = false
  }
}

const getTypeIcon = (type: HistoryType) => {
  return typeIconMap[type] || 'history'
}

const getTypeColor = (type: HistoryType) => {
  return typeColorMap[type] || '#666'
}

const getTypeName = (type: HistoryType) => {
  return typeNameMap[type] || type
}

const goToTypeHistory = (type: HistoryType) => {
  uni.navigateTo({
    url: `/pages/user/history?type=${type}`,
  })
}

const goToDetail = (item: IHistoryItem) => {
  // 根据历史记录类型跳转到不同页面
  let url = ''
  switch (item.type) {
    case HistoryType.TAKEOUT_FOOD:
      url = `/pages/takeout/food-detail?id=${item.target_id}`
      break
    case HistoryType.MALL_PRODUCT:
      url = `/pages/product/detail?id=${item.target_id}`
      break
    case HistoryType.MERCHANT:
      url = `/pages/takeout/merchant-detail?id=${item.target_id}`
      break
    default:
      return
  }

  uni.navigateTo({ url })
}

const goToPopularDetail = (item: IPopularHistoryItem) => {
  // 根据热门项目类型跳转到不同页面
  let url = ''
  switch (item.type) {
    case HistoryType.TAKEOUT_FOOD:
      url = `/pages/takeout/food-detail?id=${item.target_id}`
      break
    case HistoryType.MALL_PRODUCT:
      url = `/pages/product/detail?id=${item.target_id}`
      break
    case HistoryType.MERCHANT:
      url = `/pages/takeout/merchant-detail?id=${item.target_id}`
      break
    default:
      return
  }

  uni.navigateTo({ url })
}

const goToHistory = () => {
  uni.navigateTo({
    url: '/pages/user/history',
  })
}

const showAnalytics = () => {
  uni.showToast({
    title: '详细分析功能开发中',
    icon: 'none',
  })
}

const formatTime = (timeStr: string) => {
  const time = new Date(timeStr)
  const now = new Date()
  const diff = now.getTime() - time.getTime()

  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  if (diff < 2592000000) return `${Math.floor(diff / 86400000)}天前`

  return time.toLocaleDateString()
}

// 生命周期
onMounted(() => {
  loadStatistics()
})
</script>

<style lang="scss" scoped>
.statistics-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
}

// 总体统计
.overview-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 32rpx;
}

.overview-item {
  text-align: center;

  .overview-value {
    display: block;
    font-size: 48rpx;
    font-weight: 600;
    color: #4d8eff;
    margin-bottom: 8rpx;
  }

  .overview-label {
    font-size: 24rpx;
    color: #666;
  }
}

// 类型分布
.type-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
}

.type-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.type-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  transition: all 0.3s;

  &:active {
    background-color: #e9ecef;
  }
}

.type-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.type-icon {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(77, 142, 255, 0.1);
  border-radius: 50%;
}

.type-details {
  display: flex;
  flex-direction: column;

  .type-name {
    font-size: 28rpx;
    color: #333;
    font-weight: 500;
  }

  .type-count {
    font-size: 24rpx;
    color: #666;
    margin-top: 4rpx;
  }
}

.type-percentage {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8rpx;

  .percentage-text {
    font-size: 24rpx;
    color: #666;
    font-weight: 500;
  }

  .percentage-bar {
    width: 100rpx;
    height: 8rpx;
    background-color: #e9ecef;
    border-radius: 4rpx;
    overflow: hidden;

    .percentage-fill {
      height: 100%;
      border-radius: 4rpx;
      transition: width 0.3s;
    }
  }
}

// 最近浏览
.recent-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
}

.recent-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.recent-item {
  display: flex;
  align-items: center;
  padding: 16rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  transition: all 0.3s;

  &:active {
    background-color: #e9ecef;
  }
}

.recent-image {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
  overflow: hidden;
  margin-right: 16rpx;
  position: relative;

  image {
    width: 100%;
    height: 100%;
  }

  .recent-type-badge {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0.6);
    color: #fff;
    font-size: 18rpx;
    text-align: center;
    padding: 2rpx;
  }
}

.recent-info {
  flex: 1;

  .recent-title {
    font-size: 26rpx;
    color: #333;
    font-weight: 500;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    overflow: hidden;
  }

  .recent-meta {
    display: flex;
    align-items: center;
    gap: 16rpx;
    margin-top: 8rpx;

    .visit-count {
      font-size: 22rpx;
      color: #4d8eff;
    }

    .visit-time {
      font-size: 22rpx;
      color: #999;
    }
  }
}

// 热门浏览
.popular-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
}

.popular-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.popular-item {
  display: flex;
  align-items: center;
  padding: 16rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  transition: all 0.3s;

  &:active {
    background-color: #e9ecef;
  }
}

.popular-rank {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #4d8eff;
  color: #fff;
  font-size: 20rpx;
  font-weight: 600;
  border-radius: 50%;
  margin-right: 16rpx;
}

.popular-image {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
  overflow: hidden;
  margin-right: 16rpx;

  image {
    width: 100%;
    height: 100%;
  }
}

.popular-info {
  flex: 1;

  .popular-title {
    font-size: 26rpx;
    color: #333;
    font-weight: 500;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    overflow: hidden;
  }

  .popular-meta {
    display: flex;
    align-items: center;
    gap: 16rpx;
    margin-top: 8rpx;

    .popular-type {
      font-size: 22rpx;
      color: #666;
    }

    .popular-count {
      font-size: 22rpx;
      color: #ff6b35;
      font-weight: 500;
    }
  }
}

// 操作按钮
.actions-section {
  display: flex;
  gap: 20rpx;
  padding: 32rpx;
}
</style>
