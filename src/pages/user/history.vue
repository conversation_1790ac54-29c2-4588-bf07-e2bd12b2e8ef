<route lang="json5">
{
  style: {
    navigationBarTitleText: '浏览历史',
  },
}
</route>

<template>
  <view class="history-container">
    <!-- 顶部工具栏 -->
    <view class="toolbar">
      <view class="search-bar">
        <wd-search
          v-model="searchKeyword"
          placeholder="搜索历史记录"
          @search="handleSearch"
          @clear="handleSearchClear"
        />
      </view>
      <view class="toolbar-actions">
        <wd-button type="text" size="small" @click="showFilterPanel">
          <wd-icon name="filter" size="16" />
          筛选
        </wd-button>
        <wd-button type="text" size="small" @click="showStatistics">
          <wd-icon name="chart" size="16" />
          统计
        </wd-button>
        <wd-button type="text" size="small" @click="toggleEditMode">
          <wd-icon name="edit" size="16" />
          {{ editMode ? '完成' : '管理' }}
        </wd-button>
      </view>
    </view>

    <!-- 类型筛选 -->
    <view class="type-filter">
      <wd-tabs v-model="activeType" @change="handleTypeChange">
        <wd-tab title="全部" name="all"></wd-tab>
        <wd-tab title="外卖" name="takeout_food"></wd-tab>
        <wd-tab title="商城" name="mall_product"></wd-tab>
        <wd-tab title="商家" name="merchant"></wd-tab>
        <wd-tab title="搜索" name="search"></wd-tab>
        <wd-tab title="页面" name="page"></wd-tab>
      </wd-tabs>
    </view>

    <!-- 编辑模式工具栏 -->
    <view v-if="editMode" class="edit-toolbar">
      <view class="edit-actions">
        <wd-checkbox v-model="selectAll" @change="handleSelectAll">全选</wd-checkbox>
        <text class="selected-count">已选择 {{ selectedCount }} 项</text>
      </view>
      <view class="edit-buttons">
        <wd-button
          type="danger"
          size="small"
          :disabled="selectedCount === 0"
          @click="handleBatchDelete"
        >
          删除选中
        </wd-button>
      </view>
    </view>

    <!-- 历史记录列表 -->
    <view class="history-content">
      <!-- 空状态 -->
      <view v-if="historyList.length === 0 && !loading" class="empty-state">
        <wd-icon name="history" size="64" color="#ccc" />
        <text class="empty-text">暂无浏览历史</text>
        <text class="empty-desc">快去逛逛吧~</text>
      </view>

      <!-- 历史记录列表 -->
      <view v-else class="list-content">
        <view
          v-for="item in historyList"
          :key="item.id"
          class="history-item"
          @click="goToDetail(item)"
        >
          <view v-if="editMode" class="item-checkbox">
            <wd-checkbox v-model="item.checked" @change="handleItemCheck" @click.stop />
          </view>

          <view class="item-image">
            <image :src="item.target_image" mode="aspectFill" />
            <view class="item-type-badge">{{ getTypeName(item.type) }}</view>
          </view>

          <view class="item-info">
            <text class="item-title">{{ item.target_name }}</text>

            <!-- 访问信息 -->
            <view class="item-meta">
              <text class="visit-count">访问 {{ item.visit_count }} 次</text>
              <text class="visit-time">{{ formatTime(item.last_visit_at) }}</text>
            </view>

            <!-- 创建时间 -->
            <text class="item-time">首次访问：{{ formatTime(item.created_at) }}</text>
          </view>

          <view v-if="!editMode" class="item-actions">
            <wd-button type="text" size="small" @click.stop="deleteItem(item)">
              <wd-icon name="delete" size="16" color="#ff4757" />
            </wd-button>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <wd-loadmore
        :state="loadMoreState"
        @loadmore="loadMore"
        loading-text="加载中..."
        finished-text="没有更多了"
        error-text="加载失败，点击重试"
      />
    </view>

    <!-- 筛选弹窗 -->
    <wd-popup v-model="showFilterDialog" position="bottom">
      <view class="filter-content">
        <view class="filter-header">
          <text class="filter-title">筛选条件</text>
          <wd-button type="text" @click="resetFilter">重置</wd-button>
        </view>

        <view class="filter-body">
          <!-- 时间范围 -->
          <wd-cell-group title="时间范围">
            <wd-cell title="开始日期">
              <wd-datetime-picker
                v-model="filterParams.start_date"
                type="date"
                placeholder="选择开始日期"
              />
            </wd-cell>
            <wd-cell title="结束日期">
              <wd-datetime-picker
                v-model="filterParams.end_date"
                type="date"
                placeholder="选择结束日期"
              />
            </wd-cell>
          </wd-cell-group>
        </view>

        <view class="filter-footer">
          <wd-button type="default" @click="closeFilterPanel">取消</wd-button>
          <wd-button type="primary" @click="applyFilter">确定</wd-button>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import {
  getHistoryList,
  deleteHistory,
  batchDeleteHistory,
  searchHistory,
  getHistoryByType,
} from '@/api/user'
import type {
  IHistoryItem,
  IHistoryQueryParams,
  HistoryType,
  IBatchDeleteHistoryRequest,
} from '@/api/user.typings'

// 响应式数据
const activeType = ref<HistoryType | 'all'>('all')
const loading = ref(false)
const historyList = ref<(IHistoryItem & { checked: boolean })[]>([])
const searchKeyword = ref('')
const pageNo = ref(1)
const pageSize = ref(20)
const hasMore = ref(true)
const editMode = ref(false)
const selectAll = ref(false)
const showFilterDialog = ref(false)

// 筛选参数
const filterParams = ref<IHistoryQueryParams>({
  start_date: '',
  end_date: '',
})

// 计算属性
const selectedCount = computed(() => {
  return historyList.value.filter((item) => item.checked).length
})

const loadMoreState = computed(() => {
  if (loading.value) return 'loading'
  if (!hasMore.value) return 'finished'
  return 'more'
})

// 类型名称映射
const typeNameMap = {
  takeout_food: '外卖',
  mall_product: '商城',
  merchant: '商家',
  category: '分类',
  search: '搜索',
  page: '页面',
}

// 方法
const loadHistory = async (refresh = false) => {
  if (loading.value) return

  try {
    loading.value = true

    if (refresh) {
      pageNo.value = 1
      historyList.value = []
    }

    const params: IHistoryQueryParams = {
      page: pageNo.value,
      page_size: pageSize.value,
      type: activeType.value === 'all' ? undefined : (activeType.value as HistoryType),
      keyword: searchKeyword.value || undefined,
      ...filterParams.value,
    }

    const result = await getHistoryList(params)

    const newItems = result.data.list.map((item) => ({
      ...item,
      checked: false,
    }))

    if (refresh) {
      historyList.value = newItems
    } else {
      historyList.value.push(...newItems)
    }

    hasMore.value = result.data.list.length === pageSize.value
    pageNo.value++
  } catch (error) {
    console.error('加载历史记录失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'error',
    })
  } finally {
    loading.value = false
  }
}

const loadMore = () => {
  if (!hasMore.value || loading.value) return
  loadHistory(false)
}

const handleTypeChange = () => {
  loadHistory(true)
}

const handleSearch = () => {
  loadHistory(true)
}

const handleSearchClear = () => {
  searchKeyword.value = ''
  loadHistory(true)
}

const showFilterPanel = () => {
  showFilterDialog.value = true
}

const closeFilterPanel = () => {
  showFilterDialog.value = false
}

const resetFilter = () => {
  filterParams.value = {
    start_date: '',
    end_date: '',
  }
}

const applyFilter = () => {
  showFilterDialog.value = false
  loadHistory(true)
}

const showStatistics = () => {
  uni.navigateTo({
    url: '/pages/user/history-statistics',
  })
}

const toggleEditMode = () => {
  editMode.value = !editMode.value
  if (!editMode.value) {
    // 退出编辑模式时清除选择状态
    historyList.value.forEach((item) => {
      item.checked = false
    })
    selectAll.value = false
  }
}

const handleSelectAll = () => {
  historyList.value.forEach((item) => {
    item.checked = selectAll.value
  })
}

const handleItemCheck = () => {
  const checkedCount = historyList.value.filter((item) => item.checked).length
  selectAll.value = checkedCount === historyList.value.length && checkedCount > 0
}

const deleteItem = async (item: IHistoryItem) => {
  try {
    await uni.showModal({
      title: '确认删除',
      content: `确定要删除"${item.target_name}"的访问记录吗？`,
    })

    await deleteHistory(item.id)

    // 从列表中移除
    const index = historyList.value.findIndex((h) => h.id === item.id)
    if (index > -1) {
      historyList.value.splice(index, 1)
    }

    uni.showToast({
      title: '删除成功',
      icon: 'success',
    })
  } catch (error) {
    if (error === 'cancel') return

    console.error('删除历史记录失败:', error)
    uni.showToast({
      title: '删除失败',
      icon: 'error',
    })
  }
}

const handleBatchDelete = async () => {
  const selectedIds = historyList.value.filter((item) => item.checked).map((item) => item.id)

  if (selectedIds.length === 0) return

  try {
    await uni.showModal({
      title: '确认删除',
      content: `确定要删除选中的${selectedIds.length}条历史记录吗？`,
    })

    const params: IBatchDeleteHistoryRequest = { ids: selectedIds }
    await batchDeleteHistory(params)

    // 从列表中移除已删除的项目
    historyList.value = historyList.value.filter((item) => !selectedIds.includes(item.id))

    uni.showToast({
      title: '删除成功',
      icon: 'success',
    })

    // 退出编辑模式
    editMode.value = false
    selectAll.value = false
  } catch (error) {
    if (error === 'cancel') return

    console.error('批量删除失败:', error)
    uni.showToast({
      title: '删除失败',
      icon: 'error',
    })
  }
}

const goToDetail = (item: IHistoryItem) => {
  if (editMode.value) return

  // 根据历史记录类型跳转到不同页面
  let url = ''
  switch (item.type) {
    case 'takeout_food':
      url = `/pages/takeout/food-detail?id=${item.target_id}`
      break
    case 'mall_product':
      url = `/pages/product/detail?id=${item.target_id}`
      break
    case 'merchant':
      url = `/pages/takeout/merchant-detail?id=${item.target_id}`
      break
    default:
      return
  }

  uni.navigateTo({ url })
}

const getTypeName = (type: HistoryType) => {
  return typeNameMap[type] || type
}

const formatTime = (timeStr: string) => {
  const time = new Date(timeStr)
  const now = new Date()
  const diff = now.getTime() - time.getTime()

  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  if (diff < 2592000000) return `${Math.floor(diff / 86400000)}天前`

  return time.toLocaleDateString()
}

// 监听搜索关键词变化
let searchTimer: number | null = null
watch(searchKeyword, () => {
  if (searchTimer) {
    clearTimeout(searchTimer)
  }
  searchTimer = setTimeout(() => {
    if (searchKeyword.value.length === 0) {
      loadHistory(true)
    }
  }, 500)
})

// 生命周期
onMounted(() => {
  loadHistory(true)
})
</script>

<style lang="scss" scoped>
.history-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

// 顶部工具栏样式
.toolbar {
  background-color: #fff;
  padding: 20rpx 32rpx;
  border-bottom: 1px solid #eee;

  .search-bar {
    margin-bottom: 20rpx;
  }

  .toolbar-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

// 类型筛选样式
.type-filter {
  background-color: #fff;
  padding: 0 32rpx;
  border-bottom: 1px solid #eee;
}

// 编辑工具栏样式
.edit-toolbar {
  background-color: #fff;
  padding: 20rpx 32rpx;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .edit-actions {
    display: flex;
    align-items: center;
    gap: 24rpx;

    .selected-count {
      font-size: 24rpx;
      color: #666;
    }
  }
}

// 历史记录内容样式
.history-content {
  flex: 1;
}

// 空状态样式
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 32rpx;

  .empty-text {
    font-size: 32rpx;
    color: #666;
    margin: 24rpx 0 12rpx;
  }

  .empty-desc {
    font-size: 24rpx;
    color: #999;
  }
}

// 历史记录列表样式
.list-content {
  padding: 20rpx 32rpx;
}

.history-item {
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s;

  &:active {
    transform: scale(0.98);
  }

  .item-checkbox {
    margin-right: 24rpx;
  }

  .item-image {
    width: 120rpx;
    height: 120rpx;
    border-radius: 12rpx;
    overflow: hidden;
    margin-right: 24rpx;
    position: relative;

    image {
      width: 100%;
      height: 100%;
    }

    .item-type-badge {
      position: absolute;
      top: 8rpx;
      left: 8rpx;
      background-color: rgba(0, 0, 0, 0.6);
      color: #fff;
      font-size: 20rpx;
      padding: 4rpx 8rpx;
      border-radius: 6rpx;
    }
  }

  .item-info {
    flex: 1;

    .item-title {
      font-size: 28rpx;
      font-weight: 500;
      color: #333;
      line-height: 1.4;
      margin-bottom: 12rpx;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      overflow: hidden;
    }

    .item-meta {
      display: flex;
      align-items: center;
      gap: 16rpx;
      margin-bottom: 8rpx;

      .visit-count {
        font-size: 22rpx;
        color: #4d8eff;
        background-color: rgba(77, 142, 255, 0.1);
        padding: 4rpx 8rpx;
        border-radius: 6rpx;
      }

      .visit-time {
        font-size: 22rpx;
        color: #666;
      }
    }

    .item-time {
      font-size: 20rpx;
      color: #999;
    }
  }

  .item-actions {
    display: flex;
    align-items: center;
  }
}

// 筛选弹窗样式
.filter-content {
  background-color: #fff;
  border-radius: 16rpx 16rpx 0 0;
  max-height: 80vh;
  overflow: hidden;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1px solid #eee;

  .filter-title {
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
  }
}

.filter-body {
  padding: 32rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.filter-footer {
  display: flex;
  gap: 24rpx;
  padding: 32rpx;
  border-top: 1px solid #eee;
}
</style>
