<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '我的',
  },
  layout: 'tabbar',
}
</route>

<template>
  <view class="user-container" :style="{ paddingTop: safeAreaInsets?.top + 'px' }">
    <!-- 用户信息头部 -->
    <view class="user-header">
      <view class="header-bg"></view>

      <view class="user-info" @click="handleUserClick">
        <view class="avatar-section">
          <image :src="userInfo?.avatar || defaultAvatar" class="user-avatar" mode="aspectFill" />
          <view v-if="!isLoggedIn" class="login-tip">
            <text>点击登录</text>
          </view>
        </view>

        <view class="user-details">
          <view class="username">{{ userInfo?.nickname || '未登录' }}</view>
          <view v-if="isLoggedIn" class="user-level">
            <wd-icon name="vip" color="#ffd700" size="16" />
            <text>{{ userInfo?.level || 'VIP1' }}</text>
          </view>
          <view v-else class="login-hint">登录享受更多权益</view>
        </view>

        <wd-icon name="arrow-right" color="#fff" size="16" />
      </view>

      <!-- 用户统计 -->
      <view v-if="isLoggedIn" class="user-stats">
        <view class="stat-item" @click="goToAccountTransactions">
          <text class="stat-value">¥{{ accountInfo?.balance || '0.00' }}</text>
          <text class="stat-label">余额</text>
        </view>
        <view class="stat-item" @click="goToCoupons">
          <text class="stat-value">{{ userStats?.couponCount || 0 }}</text>
          <text class="stat-label">优惠券</text>
        </view>
        <view class="stat-item" @click="goToPoints">
          <text class="stat-value">{{ userStats?.points || 0 }}</text>
          <text class="stat-label">积分</text>
        </view>
        <view class="stat-item" @click="goToFavorites">
          <text class="stat-value">{{ userStats?.favoriteCount || 0 }}</text>
          <text class="stat-label">收藏</text>
        </view>
      </view>
    </view>

    <!-- 订单管理 -->
    <view class="order-section">
      <view class="section-header" @click="goToOrders()">
        <view class="section-title">
          <wd-icon name="order" color="#333" size="20" />
          <text>我的订单</text>
        </view>
        <view class="section-action">
          <text>查看全部</text>
          <wd-icon name="arrow-right" color="#999" size="14" />
        </view>
      </view>

      <view class="order-types">
        <view class="order-type" @click="goToOrders('pending')">
          <view class="type-icon">
            <wd-icon name="time" color="#ff6b35" size="24" />
            <view v-if="orderCounts?.pending > 0" class="order-badge">
              {{ orderCounts.pending }}
            </view>
          </view>
          <text class="type-label">待付款</text>
        </view>

        <view class="order-type" @click="goToOrders('paid')">
          <view class="type-icon">
            <wd-icon name="bags" color="#4ecdc4" size="24" />
            <view v-if="orderCounts?.paid > 0" class="order-badge">{{ orderCounts.paid }}</view>
          </view>
          <text class="type-label">待发货</text>
        </view>

        <view class="order-type" @click="goToOrders('shipped')">
          <view class="type-icon">
            <wd-icon name="move" color="#45b7d1" size="24" />
            <view v-if="orderCounts?.shipped > 0" class="order-badge">
              {{ orderCounts.shipped }}
            </view>
          </view>
          <text class="type-label">待收货</text>
        </view>

        <view class="order-type" @click="goToOrders('completed')">
          <view class="type-icon">
            <wd-icon name="star" color="#ffd700" size="24" />
            <view v-if="orderCounts?.completed > 0" class="order-badge">
              {{ orderCounts.completed }}
            </view>
          </view>
          <text class="type-label">待评价</text>
        </view>

        <view class="order-type" @click="goToAfterSales">
          <view class="type-icon">
            <wd-icon name="service" color="#96ceb4" size="24" />
          </view>
          <text class="type-label">售后</text>
        </view>
      </view>
    </view>

    <!-- 功能菜单 -->
    <view class="menu-section">
      <!-- 常用功能 -->
      <view class="menu-group">
        <view class="menu-item" @click="goToAddresses">
          <view class="menu-icon">
            <wd-icon name="location" color="#ff6b35" size="20" />
          </view>
          <text class="menu-label">收货地址</text>
          <wd-icon name="arrow-right" color="#999" size="14" />
        </view>

        <view class="menu-item" @click="goToCoupons">
          <view class="menu-icon">
            <wd-icon name="gift" color="#4ecdc4" size="20" />
          </view>
          <text class="menu-label">我的优惠券</text>
          <view v-if="userStats?.couponCount > 0" class="menu-badge">
            {{ userStats.couponCount }}
          </view>
          <wd-icon name="arrow-right" color="#999" size="14" />
        </view>

        <view class="menu-item" @click="goToCouponCenter">
          <view class="menu-icon">
            <wd-icon name="gift" color="#ff6b9d" size="20" />
          </view>
          <text class="menu-label">优惠券中心</text>
          <view class="menu-extra">
            <text class="extra-text">领券</text>
          </view>
          <wd-icon name="arrow-right" color="#999" size="14" />
        </view>

        <view class="menu-item" @click="goToFavorites">
          <view class="menu-icon">
            <wd-icon name="heart" color="#ff6b9d" size="20" />
          </view>
          <text class="menu-label">我的收藏</text>
          <view class="menu-extra">
            <text class="extra-text" @click.stop="goToFavoriteStatistics">统计</text>
            <wd-icon name="arrow-right" color="#999" size="14" />
          </view>
        </view>

        <view class="menu-item" @click="goToHistory">
          <view class="menu-icon">
            <wd-icon name="history" color="#45b7d1" size="20" />
          </view>
          <text class="menu-label">浏览历史</text>
          <view class="menu-extra">
            <text class="extra-text" @click.stop="goToHistoryStatistics">统计</text>
            <wd-icon name="arrow-right" color="#999" size="14" />
          </view>
        </view>
      </view>

      <!-- 服务功能 -->
      <view class="menu-group">
        <view class="menu-item" @click="goToCustomerService">
          <view class="menu-icon">
            <wd-icon name="service" color="#96ceb4" size="20" />
          </view>
          <text class="menu-label">客服中心</text>
          <wd-icon name="arrow-right" color="#999" size="14" />
        </view>

        <view class="menu-item" @click="goToFeedback">
          <view class="menu-icon">
            <wd-icon name="chart" color="#feca57" size="20" />
          </view>
          <text class="menu-label">意见反馈</text>
          <wd-icon name="arrow-right" color="#999" size="14" />
        </view>

        <view class="menu-item" @click="goToAbout">
          <view class="menu-icon">
            <wd-icon name="link" color="#a55eea" size="20" />
          </view>
          <text class="menu-label">关于我们</text>
          <wd-icon name="arrow-right" color="#999" size="14" />
        </view>
      </view>

      <!-- 设置功能 -->
      <view class="menu-group">
        <view class="menu-item" @click="goToSettings">
          <view class="menu-icon">
            <wd-icon name="setting" color="#778ca3" size="20" />
          </view>
          <text class="menu-label">设置</text>
          <wd-icon name="arrow-right" color="#999" size="14" />
        </view>
      </view>
    </view>

    <!-- 退出登录 -->
    <view v-if="isLoggedIn" class="logout-section">
      <wd-button type="error" @click="handleLogout" block>退出登录</wd-button>
    </view>

    <!-- 版本信息 -->
    <view class="version-info">
      <text>O_Mall v1.0.0</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { onLoad, onShow } from '@dcloudio/uni-app'
import { useUserStore } from '@/store/user'
import { useOrderStore } from '@/store/order'
import { useCouponStore } from '@/store/coupon'
import { getFavoriteStatistics } from '@/api/user'

const { safeAreaInsets } = uni.getSystemInfoSync()
const userStore = useUserStore()
const orderStore = useOrderStore()
const couponStore = useCouponStore()

// 默认头像
const defaultAvatar = 'https://via.placeholder.com/80x80/ff6b35/ffffff?text=User'

// 用户信息
const userInfo = computed(() => userStore.userInfo)
const isLoggedIn = computed(() => userStore.isLoggedIn)
const accountInfo = computed(() => userStore.accountInfo)

// 用户统计数据
const userStats = ref({
  balance: '0.00',
  couponCount: 0,
  points: 0,
  favoriteCount: 0,
})

// 订单数量统计
const orderCounts = ref({
  pending: 0,
  paid: 0,
  shipped: 0,
  completed: 0,
})

/**
 * 处理用户信息点击
 */
const handleUserClick = () => {
  if (isLoggedIn.value) {
    // 已登录，跳转到个人信息页面
    uni.navigateTo({
      url: '/pages/user/profile',
    })
  } else {
    // 未登录，跳转到登录页面
    uni.navigateTo({
      url: '/pages/login/index',
    })
  }
}

/**
 * 跳转到订单页面
 */
const goToOrders = (status?: string) => {
  let statusParam = ''

  // 根据状态名称映射到对应的数字状态值
  if (status) {
    const statusMap: Record<string, number> = {
      pending: 10, // 待付款
      paid: 20, // 待发货
      shipped: 30, // 待收货
      completed: 40, // 待评价
    }
    statusParam = statusMap[status]?.toString() || ''
  }

  const url = statusParam ? `/pages/order/list?status=${statusParam}` : '/pages/order/list'
  uni.navigateTo({ url })
}

/**
 * 跳转到售后页面
 */
const goToAfterSales = () => {
  uni.navigateTo({
    url: '/pages/order/after-sales',
  })
}

/**
 * 跳转到收货地址
 */
const goToAddresses = () => {
  if (!isLoggedIn.value) {
    goToLogin()
    return
  }
  uni.navigateTo({
    url: '/pages/address/list',
  })
}

/**
 * 跳转到优惠券
 */
const goToCoupons = () => {
  if (!isLoggedIn.value) {
    goToLogin()
    return
  }
  uni.navigateTo({
    url: '/pages/coupon/my-coupons',
  })
}

/**
 * 跳转到优惠券中心
 */
const goToCouponCenter = () => {
  uni.navigateTo({
    url: '/pages/coupon/center',
  })
}

/**
 * 跳转到收藏
 */
const goToFavorites = () => {
  if (!isLoggedIn.value) {
    goToLogin()
    return
  }
  uni.switchTab({
    url: '/pages/user/favorites',
  })
}

/**
 * 跳转到收藏统计
 */
const goToFavoriteStatistics = () => {
  if (!isLoggedIn.value) {
    goToLogin()
    return
  }
  uni.navigateTo({
    url: '/pages/user/favorite-statistics',
  })
}

/**
 * 跳转到浏览历史
 */
const goToHistory = () => {
  if (!isLoggedIn.value) {
    goToLogin()
    return
  }
  uni.navigateTo({
    url: '/pages/user/history',
  })
}

/**
 * 跳转到历史统计
 */
const goToHistoryStatistics = () => {
  if (!isLoggedIn.value) {
    goToLogin()
    return
  }
  uni.navigateTo({
    url: '/pages/user/history-statistics',
  })
}

/**
 * 跳转到账户明细
 */
const goToAccountTransactions = () => {
  if (!isLoggedIn.value) {
    goToLogin()
    return
  }
  uni.navigateTo({
    url: '/pages/user/account-transactions',
  })
}

/**
 * 跳转到钱包
 */
const goToWallet = () => {
  if (!isLoggedIn.value) {
    goToLogin()
    return
  }
  uni.navigateTo({
    url: '/pages/user/wallet',
  })
}

/**
 * 跳转到积分
 */
const goToPoints = () => {
  if (!isLoggedIn.value) {
    goToLogin()
    return
  }
  uni.navigateTo({
    url: '/pages/user/points',
  })
}

/**
 * 跳转到客服中心
 */
const goToCustomerService = () => {
  uni.navigateTo({
    url: '/pages/service/customer-service',
  })
}

/**
 * 跳转到意见反馈
 */
const goToFeedback = () => {
  uni.navigateTo({
    url: '/pages/service/feedback',
  })
}

/**
 * 跳转到关于我们
 */
const goToAbout = () => {
  uni.navigateTo({
    url: '/pages/service/about',
  })
}

/**
 * 跳转到设置
 */
const goToSettings = () => {
  uni.navigateTo({
    url: '/pages/user/settings',
  })
}

/**
 * 跳转到登录页面
 */
const goToLogin = () => {
  uni.navigateTo({
    url: '/pages/login/index',
  })
}

/**
 * 退出登录
 */
const handleLogout = () => {
  uni.showModal({
    title: '确认退出',
    content: '确定要退出登录吗？',
    success: async (res) => {
      if (res.confirm) {
        try {
          // 显示加载提示
          uni.showLoading({
            title: '正在退出...',
          })

          // 调用退出登录API
          await userStore.logout()

          // 关闭加载提示
          uni.hideLoading()

          // 显示成功提示
          uni.showToast({
            title: '已退出登录',
            icon: 'success',
          })

          // 重置数据
          resetUserData()
        } catch (error) {
          // 关闭加载提示
          uni.hideLoading()

          // 显示错误提示
          uni.showToast({
            title: '退出失败',
            icon: 'error',
          })
        }
      }
    },
  })
}

/**
 * 重置用户数据
 */
const resetUserData = () => {
  userStats.value = {
    balance: '0.00',
    couponCount: 0,
    points: 0,
    favoriteCount: 0,
  }
  orderCounts.value = {
    pending: 0,
    paid: 0,
    shipped: 0,
    completed: 0,
  }
  // 清空账户信息
  userStore.accountInfo = null
  userStore.accountTransactions = []
}

/**
 * 获取用户统计数据
 */
const fetchUserStats = async () => {
  if (!isLoggedIn.value) return

  try {
    // 获取账户信息
    try {
      await userStore.fetchAccountInfo()
    } catch (error) {
      console.warn('获取账户信息失败:', error)
    }

    // 获取收藏统计
    let favoriteCount = 0
    try {
      const favoriteStats = await getFavoriteStatistics()
      favoriteCount = favoriteStats.total_count || 0
    } catch (error) {
      console.warn('获取收藏统计失败:', error)
    }

    // 获取优惠券数量
    let couponCount = 0
    try {
      await couponStore.fetchMyCoupons({ refresh: true })
      couponCount = couponStore.unusedCoupons?.length || 0
    } catch (error) {
      console.warn('获取优惠券统计失败:', error)
      couponCount = 0
    }

    // 如果用户有余额和积分信息，可以直接使用
    if (userInfo.value) {
      userStats.value = {
        balance: userInfo.value.balance ? userInfo.value.balance.toFixed(2) : '0.00',
        couponCount: couponCount,
        points: userInfo.value.points || 0,
        favoriteCount: favoriteCount,
      }
    } else {
      // 模拟数据
      userStats.value = {
        balance: '0.00',
        couponCount: couponCount,
        points: 0,
        favoriteCount: favoriteCount,
      }
    }
  } catch (error) {
    console.error('获取用户统计数据失败:', error)
  }
}

/**
 * 获取订单数量统计
 */
const fetchOrderCounts = async () => {
  if (!isLoggedIn.value) return

  try {
    // 获取订单状态统计数据
    await orderStore.fetchOrderStatusStats()
    const stats = orderStore.orderStatusStats

    // 根据订单列表页面的状态映射更新角标
    orderCounts.value = {
      pending: stats?.pending || 0, // 待付款 (状态10)
      paid: stats?.paid || 0, // 待发货 (状态20)
      shipped: stats?.shipped || 0, // 待收货 (状态30)
      completed: stats?.completed || 0, // 待评价 (状态40)
    }
  } catch (error) {
    console.error('获取订单统计失败:', error)
    // 出错时重置为0
    orderCounts.value = {
      pending: 0,
      paid: 0,
      shipped: 0,
      completed: 0,
    }
  }
}
const initPageData = async () => {
  if (isLoggedIn.value) {
    await Promise.all([fetchUserStats(), fetchOrderCounts()])
  }
}

// 页面加载
onLoad(() => {
  console.log('用户中心页面加载')
})

// 页面显示时刷新数据
onShow(() => {
  initPageData()
})

// 组件挂载
onMounted(() => {
  initPageData()
})
</script>

<style lang="scss" scoped>
.user-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

// 用户头部
.user-header {
  position: relative;
  padding: 40rpx 30rpx 30rpx;
  margin-bottom: 20rpx;
}

.header-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  border-radius: 0 0 40rpx 40rpx;
}

.user-info {
  position: relative;
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.avatar-section {
  position: relative;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.login-tip {
  position: absolute;
  bottom: -10rpx;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  white-space: nowrap;
}

.user-details {
  flex: 1;
}

.username {
  font-size: 36rpx;
  color: #fff;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.user-level {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 24rpx;
  color: #fff;
  opacity: 0.9;
}

.login-hint {
  font-size: 24rpx;
  color: #fff;
  opacity: 0.8;
}

.user-stats {
  position: relative;
  display: flex;
  background-color: rgba(255, 255, 255, 0.15);
  border-radius: 20rpx;
  padding: 30rpx 0;
  backdrop-filter: blur(10px);
}

.stat-item {
  flex: 1;
  text-align: center;
  color: #fff;
}

.stat-value {
  font-size: 32rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  opacity: 0.9;
}

// 订单管理
.order-section {
  background-color: #fff;
  margin-bottom: 20rpx;
  border-radius: 20rpx;
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1px solid #eee;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.section-action {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 28rpx;
  color: #999;
}

.order-types {
  display: flex;
  padding: 30rpx;
}

.order-type {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.type-icon {
  position: relative;
}

.order-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background-color: #ff6b35;
  color: #fff;
  font-size: 20rpx;
  min-width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 8rpx;
}

.type-label {
  font-size: 24rpx;
  color: #666;
}

// 功能菜单
.menu-section {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.menu-group {
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1px solid #eee;
  position: relative;

  &:last-child {
    border-bottom: none;
  }
}

.menu-icon {
  margin-right: 20rpx;
}

.menu-label {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.menu-extra {
  display: flex;
  align-items: center;
  gap: 16rpx;

  .extra-text {
    font-size: 24rpx;
    color: #4d8eff;
    padding: 8rpx 16rpx;
    background-color: rgba(77, 142, 255, 0.1);
    border-radius: 8rpx;
    transition: all 0.3s;

    &:active {
      background-color: rgba(77, 142, 255, 0.2);
    }
  }
}

.menu-badge {
  background-color: #ff6b35;
  color: #fff;
  font-size: 20rpx;
  min-width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 8rpx;
  margin-right: 20rpx;
}

// 退出登录
.logout-section {
  padding: 40rpx 30rpx;
}

// 版本信息
.version-info {
  text-align: center;
  padding: 40rpx;
  font-size: 24rpx;
  color: #999;
}
</style>
