<!--
 * @file 个人信息页面
 * @description 用户个人信息展示和编辑页面，使用 Wot Design Uni 表单组件
 * <AUTHOR>
 * @date 2025-01-22
-->

<route lang="json5">
{
  style: {
    navigationBarTitleText: '个人信息',
    navigationBarBackgroundColor: '#ff6b35',
    navigationBarTextStyle: 'white',
  },
}
</route>

<template>
  <view class="profile-container">
    <!-- 头像区域 -->
    <view class="avatar-section">
      <view class="avatar-title">头像</view>
      <view class="avatar-upload-container">
        <!-- 头像主体 -->
        <view class="avatar-main" @click.stop="handleAvatarUpload">
          <image :src="currentAvatar" class="avatar" mode="aspectFill" />
          <view class="avatar-mask">
            <wd-icon name="camera" color="#fff" size="24" />
            <text class="avatar-tip">更换头像</text>
          </view>
        </view>

        <!-- 删除按钮 - 独立定位 -->
        <view v-if="userInfo?.avatar" class="avatar-delete-btn" @click.stop="handleDeleteAvatar">
          <wd-icon name="close" color="#fff" size="16" />
        </view>
      </view>

      <!-- 隐藏的文件上传组件 -->
      <FileUpload
        ref="avatarUploadRef"
        v-model="avatarFileList"
        file-type="avatar"
        :max-count="1"
        :max-size="5"
        upload-url="/api/v1/user/secured/upload"
        :form-data="{ file_usage: 'avatar' }"
        :show-upload-button="false"
        :show-file-list="false"
        :show-tips="false"
        @success="handleAvatarSuccess"
        @error="handleAvatarError"
      />
    </view>

    <!-- 用户信息表单 -->
    <wd-form ref="profileForm" :model="editForm" :rules="formRules">
      <wd-cell-group border custom-class="form-section">
        <!-- 昵称输入框 -->
        <wd-input
          v-model="editForm.nickname"
          label="昵称"
          label-width="100px"
          prop="nickname"
          placeholder="请输入昵称"
          :maxlength="20"
          show-word-limit
          clearable
          suffix-icon="edit"
          @clicksuffixicon="editNickname"
        />

        <!-- 性别选择器 -->
        <wd-picker
          v-model="editForm.gender"
          label="性别"
          label-width="100px"
          :columns="genderOptions"
          placeholder="请选择性别"
        />

        <!-- 生日选择器 -->
        <wd-datetime-picker
          v-model="birthdayValue"
          type="date"
          label="生日"
          placeholder="请选择生日"
          :min-date="minBirthdayDate"
          :max-date="maxBirthdayDate"
          @confirm="handleBirthdayConfirm"
          :show="showBirthdayPopup"
          @close="showBirthdayPopup = false"
        />

        <!-- 邮箱输入框 -->
        <wd-input
          v-model="editForm.email"
          label="邮箱"
          label-width="100px"
          prop="email"
          placeholder="请输入邮箱地址"
          clearable
          suffix-icon="edit"
          @clicksuffixicon="editEmail"
        />
      </wd-cell-group>
    </wd-form>

    <!-- 账户信息 -->
    <wd-cell-group border custom-class="account-section" title="账户信息">
      <wd-cell title="用户ID" title-width="100px">
        <text class="value-text">{{ userInfo?.id || '-' }}</text>
      </wd-cell>

      <!-- 手机号绑定 -->
      <wd-cell title="手机号" title-width="100px" @click="editPhone">
        <view class="form-value">
          <text class="value-text">{{ phoneDisplay }}</text>
          <wd-icon name="arrow-right" color="#999" size="14" />
        </view>
      </wd-cell>

      <wd-cell title="注册时间" title-width="100px">
        <text class="value-text">{{ formatDate(userInfo?.created_at) }}</text>
      </wd-cell>

      <wd-cell title="会员等级" title-width="100px">
        <view class="level-badge">
          <wd-icon name="vip" color="#ffd700" size="16" />
          <text class="level-text">{{ userInfo?.level || 'VIP1' }}</text>
        </view>
      </wd-cell>
    </wd-cell-group>

    <!-- 操作按钮 -->
    <view class="action-section" v-if="hasUnsavedChanges">
      <wd-button type="primary" @click="saveProfile" :loading="saving" block>保存修改</wd-button>
    </view>

    <!-- 无更改提示 -->
    <view class="no-changes-tip" v-else>
      <text class="tip-text">暂无修改内容</text>
    </view>

    <!-- 昵称编辑弹窗 -->
    <wd-popup v-model="showNicknamePopup" position="center" custom-style="border-radius: 20rpx;">
      <view class="popup-content">
        <view class="popup-title">修改昵称</view>
        <wd-input
          v-model="editForm.nickname"
          placeholder="请输入昵称"
          :maxlength="20"
          show-word-limit
          clearable
          no-border
        />
        <view class="popup-actions">
          <wd-button @click="showNicknamePopup = false" plain>取消</wd-button>
          <wd-button type="primary" @click="confirmNickname">确定</wd-button>
        </view>
      </view>
    </wd-popup>

    <!-- 邮箱编辑弹窗 -->
    <wd-popup v-model="showEmailPopup" position="center" custom-style="border-radius: 20rpx;">
      <view class="popup-content">
        <view class="popup-title">修改邮箱</view>
        <wd-input v-model="editForm.email" placeholder="请输入邮箱地址" clearable no-border />
        <view class="popup-actions">
          <wd-button @click="showEmailPopup = false" plain>取消</wd-button>
          <wd-button type="primary" @click="confirmEmail">确定</wd-button>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { useUserStore } from '@/store/user'
import { updateUserInfo, uploadAvatar } from '@/api/user'
import FileUpload from '@/components/FileUpload.vue'
import type { FileItem } from '@/components/FileUpload.vue'
const userStore = useUserStore()

// 默认头像
const defaultAvatar = 'https://via.placeholder.com/120x120/ff6b35/ffffff?text=User'

// 用户信息
const userInfo = computed(() => userStore.userInfo)

// 编辑表单
const editForm = ref({
  nickname: '',
  gender: 0, // 0: 保密, 1: 男, 2: 女
  birthday: '',
  phone: '',
  email: '',
  avatar: '',
})

// 表单验证规则
const formRules = {
  nickname: [
    {
      required: false,
      message: '昵称长度必须在1-20个字符之间',
      validator: (value: string) => {
        if (value && (value.length < 1 || value.length > 20)) {
          return Promise.reject('昵称长度必须在1-20个字符之间')
        }
        return Promise.resolve()
      },
    },
  ],
  email: [
    {
      required: false,
      message: '请输入有效的邮箱地址',
      validator: (value: string) => {
        if (value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
          return Promise.reject('请输入有效的邮箱地址')
        }
        return Promise.resolve()
      },
    },
  ],
}

// 弹窗状态
const showNicknamePopup = ref(false)
const showBirthdayPopup = ref(false)
const showEmailPopup = ref(false)
const saving = ref(false)

// 表单引用
const profileForm = ref()

// 性别选项
const genderOptions = [
  { value: 1, label: '男' },
  { value: 2, label: '女' },
  { value: 0, label: '保密' },
]

// 头像上传相关
const avatarFileList = ref<FileItem[]>([])
const avatarUploadRef = ref()

// 当前头像显示
const currentAvatar = computed(() => {
  if (avatarFileList.value.length > 0 && avatarFileList.value[0].url) {
    return avatarFileList.value[0].url
  }
  return userInfo.value?.avatar || defaultAvatar
})

// 手机号显示（脱敏）
const phoneDisplay = computed(() => {
  const phone = editForm.value.phone || userInfo.value?.mobile
  if (!phone) return '未设置'
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
})

// 生日选择器相关
const birthdayValue = ref<number>(Date.now())

// 生日日期范围设置
const currentDate = new Date()
// 最大日期：3年前（用户至少3岁）
const maxBirthdayDate = new Date(
  currentDate.getFullYear() - 3,
  currentDate.getMonth(),
  currentDate.getDate(),
).getTime()
// 最小日期：70年前（用户最多70岁）
const minBirthdayDate = new Date(
  currentDate.getFullYear() - 70,
  currentDate.getMonth(),
  currentDate.getDate(),
).getTime()

/**
 * 将 Date 对象格式化为 YYYY-MM-DD 字符串
 */
const formatDateToString = (date: Date): string => {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  return `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`
}

/**
 * 初始化编辑表单
 */
const initEditForm = () => {
  if (userInfo.value) {
    editForm.value = {
      nickname: userInfo.value.nickname || '',
      gender: userInfo.value.gender !== undefined ? userInfo.value.gender : 0,
      birthday: userInfo.value.birthday || '',
      phone: userInfo.value.mobile || '',
      email: userInfo.value.email || '',
      avatar: userInfo.value.avatar || '',
    }

    // 初始化头像文件列表
    if (userInfo.value.avatar) {
      avatarFileList.value = [
        {
          id: 'current-avatar',
          name: 'avatar.jpg',
          size: 0,
          type: 'image',
          url: userInfo.value.avatar,
          status: 'success',
        },
      ]
    } else {
      avatarFileList.value = []
    }

    // 初始化生日选择器的值和表单值
    if (userInfo.value.birthday) {
      try {
        let date: Date
        if (userInfo.value.birthday.includes('T')) {
          date = new Date(userInfo.value.birthday)
        } else {
          date = new Date(userInfo.value.birthday + 'T00:00:00.000Z')
        }

        if (!isNaN(date.getTime())) {
          const timestamp = date.getTime()
          // 确保生日在合理范围内
          if (timestamp >= minBirthdayDate && timestamp <= maxBirthdayDate) {
            birthdayValue.value = timestamp
            // 同时设置表单中的生日值为 YYYY-MM-DD 格式
            editForm.value.birthday = formatDateToString(date)
          } else {
            // 如果超出范围，设置为默认值（20年前）
            const defaultDate = new Date(
              currentDate.getFullYear() - 20,
              currentDate.getMonth(),
              currentDate.getDate(),
            )
            birthdayValue.value = defaultDate.getTime()
            editForm.value.birthday = formatDateToString(defaultDate)
          }
        } else {
          // 如果日期无效，设置为默认值（20年前）
          const defaultDate = new Date(
            currentDate.getFullYear() - 20,
            currentDate.getMonth(),
            currentDate.getDate(),
          )
          birthdayValue.value = defaultDate.getTime()
          editForm.value.birthday = formatDateToString(defaultDate)
        }
      } catch (error) {
        console.error('生日初始化错误:', error)
        // 设置为默认值（20年前）
        const defaultDate = new Date(
          currentDate.getFullYear() - 20,
          currentDate.getMonth(),
          currentDate.getDate(),
        )
        birthdayValue.value = defaultDate.getTime()
        editForm.value.birthday = formatDateToString(defaultDate)
      }
    } else {
      // 如果没有生日，设置为默认值（20年前）
      const defaultDate = new Date(
        currentDate.getFullYear() - 20,
        currentDate.getMonth(),
        currentDate.getDate(),
      )
      birthdayValue.value = defaultDate.getTime()
      editForm.value.birthday = formatDateToString(defaultDate)
    }
  }
}

/**
 * 检查是否有未保存的更改
 */
const hasUnsavedChanges = computed(() => {
  if (!userInfo.value) return false

  const current = editForm.value
  const original = userInfo.value

  return (
    current.nickname !== (original.nickname || '') ||
    current.gender !== (original.gender || 0) ||
    current.birthday !== (original.birthday || '') ||
    current.email !== (original.email || '') ||
    current.avatar !== (original.avatar || '')
  )
})

/**
 * 触发头像上传
 */
const handleAvatarUpload = () => {
  console.log('点击头像上传按钮')

  if (!avatarUploadRef.value) {
    console.error('avatarUploadRef 未找到')
    uni.showToast({
      title: '上传组件未初始化',
      icon: 'error',
    })
    return
  }

  console.log('调用 FileUpload 的 upload 方法')
  try {
    avatarUploadRef.value.upload()
  } catch (error) {
    console.error('调用上传方法失败:', error)
    uni.showToast({
      title: '启动上传失败',
      icon: 'error',
    })
  }
}

/**
 * 头像上传成功处理
 */
const handleAvatarSuccess = (file: FileItem, response: any) => {
  console.log('头像上传成功:', response)

  try {
    let avatarUrl = ''

    // 处理 FileUpload 组件的响应格式
    if (response && response.data) {
      avatarUrl = response.data.file_url || response.data.url || response.data.file_path
    }

    if (avatarUrl) {
      // 更新表单中的头像字段
      editForm.value.avatar = avatarUrl

      uni.showToast({
        title: '头像上传成功',
        icon: 'success',
      })
    } else {
      throw new Error('响应中未找到头像URL')
    }
  } catch (error) {
    console.error('处理头像上传响应失败:', error)
    uni.showToast({
      title: '头像上传失败',
      icon: 'error',
    })
  }
}

/**
 * 头像上传失败处理
 */
const handleAvatarError = (file: FileItem, error: any) => {
  console.error('头像上传失败:', error)
  uni.showToast({
    title: '头像上传失败',
    icon: 'error',
  })
}

/**
 * 删除头像
 */
const handleDeleteAvatar = () => {
  uni.showModal({
    title: '确认删除',
    content: '确定要删除当前头像吗？',
    success: (res) => {
      if (res.confirm) {
        // 清空头像
        editForm.value.avatar = ''
        avatarFileList.value = []

        uni.showToast({
          title: '头像已删除',
          icon: 'success',
        })
      }
    },
  })
}

/**
 * 编辑昵称
 */
const editNickname = () => {
  showNicknamePopup.value = true
}

/**
 * 确认昵称修改
 */
const confirmNickname = () => {
  const nickname = editForm.value.nickname.trim()

  // 验证昵称长度（1-50字符）
  if (!nickname) {
    uni.showToast({
      title: '请输入昵称',
      icon: 'error',
    })
    return
  }

  if (nickname.length > 50) {
    uni.showToast({
      title: '昵称长度不能超过50个字符',
      icon: 'error',
    })
    return
  }

  editForm.value.nickname = nickname
  showNicknamePopup.value = false
  uni.showToast({
    title: '昵称已更新',
    icon: 'success',
  })
}

/**
 * 处理生日选择确认
 */
const handleBirthdayConfirm = ({ value }: { value: number }) => {
  const selectedDate = new Date(value)

  // 生成标准的日期字符串（YYYY-MM-DD格式）
  const birthdayString = formatDateToString(selectedDate)

  editForm.value.birthday = birthdayString
  showBirthdayPopup.value = false

  console.log('生日选择确认:', birthdayString)

  uni.showToast({
    title: '生日已更新',
    icon: 'success',
  })
}

/**
 * 编辑手机号
 */
const editPhone = () => {
  uni.navigateTo({
    url: '/pages/user/phone',
  })
}

/**
 * 编辑邮箱
 */
const editEmail = () => {
  showEmailPopup.value = true
}

/**
 * 确认邮箱修改
 */
const confirmEmail = () => {
  const email = editForm.value.email?.trim()

  // 验证邮箱格式
  if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
    uni.showToast({
      title: '请输入有效的邮箱地址',
      icon: 'error',
    })
    return
  }

  showEmailPopup.value = false
  uni.showToast({
    title: '邮箱已更新',
    icon: 'success',
  })
}

/**
 * 保存个人信息
 */
const saveProfile = async () => {
  try {
    // 使用表单验证
    const { valid } = await profileForm.value.validate()
    if (!valid) {
      return
    }

    saving.value = true

    // 准备更新数据，只发送有值的字段
    const updateData: any = {}

    if (editForm.value.nickname?.trim()) {
      updateData.nickname = editForm.value.nickname.trim()
    }

    if (editForm.value.avatar) {
      updateData.avatar = editForm.value.avatar
    }

    if (editForm.value.email?.trim()) {
      updateData.email = editForm.value.email.trim()
    }

    if (editForm.value.gender !== undefined) {
      updateData.gender = editForm.value.gender
    }

    if (editForm.value.birthday) {
      try {
        // 验证日期格式是否为 YYYY-MM-DD
        const dateRegex = /^\d{4}-\d{2}-\d{2}$/
        if (!dateRegex.test(editForm.value.birthday)) {
          throw new Error('日期格式不正确')
        }

        // 将日期格式转换为后端接受的 RFC3339 格式
        const birthdayDate = new Date(editForm.value.birthday + 'T00:00:00.000Z')

        // 验证日期是否有效
        if (isNaN(birthdayDate.getTime())) {
          throw new Error('无效的日期值')
        }

        updateData.birthday = birthdayDate.toISOString()
        console.log('生日转换成功:', editForm.value.birthday, '->', updateData.birthday)
      } catch (error) {
        console.error('生日格式转换失败:', error, '原始值:', editForm.value.birthday)
        uni.showToast({
          title: '生日格式错误，请重新选择',
          icon: 'error',
        })
        return
      }
    }

    // 如果没有任何更新数据，提示用户
    if (Object.keys(updateData).length === 0) {
      uni.showToast({
        title: '没有需要保存的更改',
        icon: 'none',
      })
      return
    }

    // 调用API更新用户信息
    await updateUserInfo(updateData)

    // 更新本地store
    await userStore.getUserInfo()

    uni.showToast({
      title: '保存成功',
      icon: 'success',
    })

    // 延迟返回上一页
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  } catch (error: any) {
    console.error('保存个人信息失败:', error)

    // 根据错误类型显示不同的提示信息
    let errorMessage = '保存失败'

    if (error?.data?.message) {
      errorMessage = error.data.message
    } else if (error?.message) {
      errorMessage = error.message
    }

    // 特殊处理常见错误
    if (
      errorMessage.includes('邮箱已被其他用户使用') ||
      errorMessage.includes('email already exists')
    ) {
      errorMessage = '该邮箱已被其他用户使用'
    } else if (
      errorMessage.includes('参数验证失败') ||
      errorMessage.includes('validation failed')
    ) {
      errorMessage = '输入信息格式不正确，请检查后重试'
    } else if (errorMessage.includes('未授权') || errorMessage.includes('unauthorized')) {
      errorMessage = '登录已过期，请重新登录'
      // 可以在这里跳转到登录页面
      setTimeout(() => {
        uni.navigateTo({
          url: '/pages/login/index',
        })
      }, 2000)
    }

    uni.showToast({
      title: errorMessage,
      icon: 'error',
      duration: 3000,
    })
  } finally {
    saving.value = false
  }
}

/**
 * 格式化日期
 */
const formatDate = (dateStr: string) => {
  if (!dateStr) return '未设置'
  const date = new Date(dateStr)
  return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`
}

// 页面加载
onLoad(async () => {
  console.log('个人信息页面加载')

  // 如果没有用户信息，先获取
  if (!userInfo.value) {
    try {
      await userStore.getUserInfo()
    } catch (error) {
      console.error('获取用户信息失败:', error)
      uni.showToast({
        title: '获取用户信息失败',
        icon: 'error',
      })
    }
  }

  initEditForm()
})

// 组件挂载
onMounted(() => {
  initEditForm()
})

// 监听用户信息变化，自动更新表单
watch(
  () => userInfo.value,
  (newUserInfo) => {
    if (newUserInfo) {
      initEditForm()
    }
  },
  { immediate: true },
)
</script>

<style lang="scss" scoped>
.profile-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 40rpx;
}

// 头像区域
.avatar-section {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  padding: 60rpx 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 20rpx;
}

.avatar-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #fff;
}

// 头像上传容器
.avatar-upload-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  width: 200rpx;
  height: 200rpx;
  margin: 0 auto;
}

.avatar-main {
  position: relative;
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  overflow: hidden;
  border: 6rpx solid rgba(255, 255, 255, 0.3);
}

.avatar {
  width: 100%;
  height: 100%;
}

.avatar-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 8rpx;
  opacity: 0;
  transition: opacity 0.3s;
  z-index: 5;
}

.avatar-main:active .avatar-mask {
  opacity: 1;
}

.avatar-tip {
  font-size: 20rpx;
  color: #fff;
}

// 头像删除按钮
.avatar-delete-btn {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(255, 59, 48, 0.9);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 2rpx solid #fff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
  z-index: 999;
  pointer-events: auto;

  &:active {
    background-color: rgba(255, 59, 48, 1);
    transform: scale(0.95);
  }
}

// 表单区域
:deep(.form-section) {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 20rpx;
  overflow: hidden;
}

.form-value {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 16rpx;
}

.value-text {
  font-size: 28rpx;
  color: #666;
}

.level-badge {
  display: flex;
  align-items: center;
  gap: 8rpx;
  background-color: rgba(255, 215, 0, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.level-text {
  font-size: 24rpx;
  color: #ffd700;
  font-weight: bold;
}

// 账户信息
:deep(.account-section) {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 20rpx;
  overflow: hidden;
}

// 操作按钮
.action-section {
  padding: 40rpx 30rpx;
}

// 无更改提示
.no-changes-tip {
  padding: 40rpx 30rpx;
  text-align: center;
}

.tip-text {
  font-size: 28rpx;
  color: #999;
}

// 弹窗样式
.popup-content {
  padding: 40rpx;
  width: 600rpx;
}

.popup-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 40rpx;
}

.popup-actions {
  display: flex;
  gap: 20rpx;
  margin-top: 40rpx;
}

// 选择器样式
.picker-content {
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 80vh;
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1px solid #eee;
}

.picker-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.birthday-picker {
  height: 400rpx;
}

.picker-item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  font-size: 28rpx;
  color: #333;
}

.picker-actions {
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
  border-top: 1px solid #eee;
}
</style>
