/**
 * 会话列表实现验证脚本
 * @description 验证会话列表功能的完整实现
 * <AUTHOR> Assistant
 * @date 2025-01-27
 */

import { readFileSync, existsSync } from 'fs'
import { join } from 'path'

interface VerificationResult {
  success: boolean
  message: string
  details?: string[]
}

class SessionImplementationVerifier {
  private projectRoot: string

  constructor(projectRoot: string) {
    this.projectRoot = projectRoot
  }

  /**
   * 验证文件是否存在
   */
  private verifyFileExists(filePath: string): VerificationResult {
    const fullPath = join(this.projectRoot, filePath)
    const exists = existsSync(fullPath)
    
    return {
      success: exists,
      message: exists ? `✅ 文件存在: ${filePath}` : `❌ 文件缺失: ${filePath}`
    }
  }

  /**
   * 验证文件内容包含指定字符串
   */
  private verifyFileContains(filePath: string, searchStrings: string[]): VerificationResult {
    const fullPath = join(this.projectRoot, filePath)
    
    if (!existsSync(fullPath)) {
      return {
        success: false,
        message: `❌ 文件不存在: ${filePath}`
      }
    }

    try {
      const content = readFileSync(fullPath, 'utf-8')
      const missingStrings: string[] = []
      const foundStrings: string[] = []

      searchStrings.forEach(searchString => {
        if (content.includes(searchString)) {
          foundStrings.push(searchString)
        } else {
          missingStrings.push(searchString)
        }
      })

      return {
        success: missingStrings.length === 0,
        message: missingStrings.length === 0 
          ? `✅ ${filePath} 包含所有必需内容` 
          : `❌ ${filePath} 缺少内容`,
        details: missingStrings.length > 0 
          ? [`缺少: ${missingStrings.join(', ')}`, `已有: ${foundStrings.join(', ')}`]
          : [`已有: ${foundStrings.join(', ')}`]
      }
    } catch (error) {
      return {
        success: false,
        message: `❌ 读取文件失败: ${filePath}`,
        details: [String(error)]
      }
    }
  }

  /**
   * 验证API类型定义
   */
  verifyApiTypes(): VerificationResult {
    return this.verifyFileContains('src/api/message.typings.ts', [
      'ISessionItem',
      'ISessionListParams',
      'ISessionListResponse',
      'target_name',
      'target_avatar',
      'last_message',
      'unread_count'
    ])
  }

  /**
   * 验证API方法
   */
  verifyApiMethods(): VerificationResult {
    return this.verifyFileContains('src/api/message.ts', [
      'getChatSessions',
      'getOrderSessions', 
      'getServiceSessions',
      'ISessionListParams',
      'ISessionListResponse'
    ])
  }

  /**
   * 验证Store实现
   */
  verifyStore(): VerificationResult {
    return this.verifyFileContains('src/store/message.ts', [
      'chatSessions',
      'orderSessions',
      'serviceSessions',
      'fetchChatSessions',
      'fetchOrderSessions',
      'fetchServiceSessions',
      'loadMoreChatSessions',
      'loadMoreOrderSessions',
      'loadMoreServiceSessions'
    ])
  }

  /**
   * 验证页面文件
   */
  verifyPages(): VerificationResult[] {
    const results: VerificationResult[] = []

    // 验证聊天会话页面
    results.push(this.verifyFileExists('src/pages/message/chat.vue'))
    results.push(this.verifyFileContains('src/pages/message/chat.vue', [
      'chatSessions',
      'fetchChatSessions',
      'target_name',
      'target_avatar',
      'unread_count'
    ]))

    // 验证订单会话页面
    results.push(this.verifyFileExists('src/pages/message/order-sessions.vue'))
    results.push(this.verifyFileContains('src/pages/message/order-sessions.vue', [
      'orderSessions',
      'fetchOrderSessions',
      'target_name'
    ]))

    // 验证客服消息页面更新
    results.push(this.verifyFileContains('src/pages/message/service.vue', [
      'serviceSessions',
      'fetchServiceSessions',
      'target_name',
      'target_avatar'
    ]))

    return results
  }

  /**
   * 验证路由配置
   */
  verifyRoutes(): VerificationResult {
    return this.verifyFileContains('src/pages.json', [
      'pages/message/chat',
      'pages/message/order-sessions'
    ])
  }

  /**
   * 验证Store路径配置
   */
  verifyStorePaths(): VerificationResult {
    return this.verifyFileContains('src/store/message.ts', [
      '/pages/message/chat',
      '/pages/message/order-sessions'
    ])
  }

  /**
   * 运行完整验证
   */
  runFullVerification(): void {
    console.log('🔍 开始验证会话列表实现...\n')

    const verifications = [
      { name: 'API类型定义', result: this.verifyApiTypes() },
      { name: 'API方法', result: this.verifyApiMethods() },
      { name: 'Store实现', result: this.verifyStore() },
      { name: '路由配置', result: this.verifyRoutes() },
      { name: 'Store路径配置', result: this.verifyStorePaths() },
      ...this.verifyPages().map((result, index) => ({
        name: `页面文件 ${index + 1}`,
        result
      }))
    ]

    let successCount = 0
    let totalCount = verifications.length

    verifications.forEach(({ name, result }) => {
      console.log(`📋 ${name}:`)
      console.log(`   ${result.message}`)
      
      if (result.details) {
        result.details.forEach(detail => {
          console.log(`   ${detail}`)
        })
      }
      
      if (result.success) {
        successCount++
      }
      
      console.log('')
    })

    console.log('📊 验证结果汇总:')
    console.log(`   成功: ${successCount}/${totalCount}`)
    console.log(`   失败: ${totalCount - successCount}/${totalCount}`)
    
    if (successCount === totalCount) {
      console.log('🎉 所有验证通过！会话列表功能实现完整。')
    } else {
      console.log('⚠️  部分验证失败，请检查上述问题。')
    }

    console.log('\n🔧 下一步建议:')
    console.log('1. 运行测试: npm run test src/tests/session-list.test.ts')
    console.log('2. 启动开发服务器测试页面功能')
    console.log('3. 检查API端点是否正确返回会话列表数据')
    console.log('4. 验证页面导航和数据显示是否正常')
  }
}

// 运行验证
if (require.main === module) {
  const verifier = new SessionImplementationVerifier(process.cwd())
  verifier.runFullVerification()
}

export { SessionImplementationVerifier }
