import { ref, watch } from 'vue'
import { WebSocketStatus, type WebSocketConfig } from './types'
import { generateDeviceInfo } from '@/utils/device'

/**
 * WebSocket连接管理服务
 * 负责WebSocket连接的建立、断开、重连等核心功能
 */
export class WebSocketConnectionService {
  private static instance: WebSocketConnectionService | null = null
  private socket: UniApp.SocketTask | null = null
  private status = ref<WebSocketStatus>(WebSocketStatus.DISCONNECTED)
  private reconnectTimer: number | null = null
  private reconnectAttempts = 0
  private heartbeatTimer: number | null = null
  private tokenProvider: (() => string) | null = null
  private deviceId: string = ''
  private messageHandlers: Set<(message: any) => void> = new Set()

  // 配置参数
  private config: WebSocketConfig = {
    url: import.meta.env.VITE_WEBSOCKET_URL || 'ws://localhost:8181/api/v1/chat/ws',
    heartbeatInterval: 30000, // 30秒心跳
    reconnectMaxAttempts: 5,
    reconnectBaseDelay: 1000,
    connectionTimeout: 10000, // 10秒连接超时
  }

  private constructor() {
    console.log('🔧 WebSocket连接服务初始化')
  }

  public static getInstance(): WebSocketConnectionService {
    if (!this.instance) {
      this.instance = new WebSocketConnectionService()
    }
    return this.instance
  }

  /**
   * 设置token提供函数
   */
  public setTokenProvider(provider: () => string): void {
    this.tokenProvider = provider
    console.log('🔑 Token提供函数已设置')
  }

  /**
   * 添加消息处理器
   */
  public addMessageHandler(handler: (message: any) => void): void {
    this.messageHandlers.add(handler)
  }

  /**
   * 移除消息处理器
   */
  public removeMessageHandler(handler: (message: any) => void): void {
    this.messageHandlers.delete(handler)
  }

  /**
   * 用户登录后自动连接
   */
  public async connectAfterLogin(): Promise<void> {
    console.log('🔗 用户登录成功，建立WebSocket连接...')

    if (!this.tokenProvider) {
      console.error('❌ Token提供函数未设置')
      return
    }

    const token = this.tokenProvider()
    if (!token) {
      console.error('❌ 无法获取有效token')
      return
    }

    await this.connect(token)
  }

  /**
   * Token刷新后自动重连
   */
  public async reconnectAfterTokenRefresh(): Promise<void> {
    console.log('🔄 Token已刷新，重新建立WebSocket连接...')

    // 1. 断开现有连接
    this.disconnect()

    // 2. 等待断开完成
    await this.waitForDisconnection()

    // 3. 使用新token重新连接
    if (this.tokenProvider) {
      const newToken = this.tokenProvider()
      if (newToken) {
        await this.connect(newToken)
      }
    }
  }

  /**
   * 用户退出后断开连接
   */
  public disconnectAfterLogout(): void {
    console.log('👋 用户退出，断开WebSocket连接...')
    this.disconnect()
    this.clearReconnectTimer()
  }

  /**
   * 建立WebSocket连接
   */
  private async connect(token: string): Promise<void> {
    if (
      this.status.value === WebSocketStatus.CONNECTED ||
      this.status.value === WebSocketStatus.CONNECTING
    ) {
      console.log('⚠️ WebSocket已连接或正在连接中，跳过重复连接')
      return
    }

    this.status.value = WebSocketStatus.CONNECTING

    // 获取设备ID - 优先使用存储的设备ID，否则生成新的
    this.deviceId =
      uni.getStorageSync('device_id') ||
      uni.getStorageSync('current_device_id') ||
      generateDeviceInfo().device_id

    // 构建查询参数
    const params = new URLSearchParams()
    params.append('token', token)
    if (this.deviceId) {
      params.append('device_id', this.deviceId)
    }

    const fullUrl = `${this.config.url}?${params.toString()}`

    try {
      console.log('🔌 正在连接WebSocket:', this.config.url, '设备ID:', this.deviceId)

      this.socket = uni.connectSocket({
        url: fullUrl,
        success: () => {
          console.log('🔌 WebSocket连接创建中...')
        },
        fail: (err) => {
          console.error('❌ WebSocket连接创建失败:', err)
          this.handleConnectionError()
        },
      })

      this.setupSocketEvents()
    } catch (error) {
      console.error('❌ WebSocket连接异常:', error)
      this.handleConnectionError()
    }
  }

  /**
   * 设置Socket事件监听
   */
  private setupSocketEvents(): void {
    if (!this.socket) return

    this.socket.onOpen(() => {
      console.log('✅ WebSocket连接已建立')
      this.status.value = WebSocketStatus.CONNECTED
      this.reconnectAttempts = 0
      this.startHeartbeat()

      // 通知连接成功
      this.notifyConnectionEstablished()
    })

    this.socket.onMessage((event) => {
      try {
        const message = JSON.parse(event.data as string)
        console.log('📨 收到WebSocket消息:', message)

        // 分发消息给所有处理器
        this.messageHandlers.forEach((handler) => {
          try {
            handler(message)
          } catch (error) {
            console.error('❌ 消息处理器执行失败:', error)
          }
        })
      } catch (error) {
        console.error('❌ 解析WebSocket消息失败:', error)
      }
    })

    this.socket.onClose(() => {
      console.log('🔌 WebSocket连接已关闭')
      this.status.value = WebSocketStatus.DISCONNECTED
      this.stopHeartbeat()
      this.scheduleReconnect()
    })

    this.socket.onError((error) => {
      console.error('❌ WebSocket连接错误:', error)
      this.handleConnectionError()
    })
  }

  /**
   * 心跳机制
   */
  private startHeartbeat(): void {
    this.stopHeartbeat()
    console.log('💓 启动心跳机制')

    this.heartbeatTimer = setInterval(() => {
      if (this.socket && this.status.value === WebSocketStatus.CONNECTED) {
        this.socket.send({
          data: JSON.stringify({
            type: 'heartbeat',
            timestamp: Date.now(),
          }),
        })
        console.log('💓 发送心跳')
      }
    }, this.config.heartbeatInterval) as unknown as number
  }

  private stopHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
      console.log('💓 停止心跳机制')
    }
  }

  /**
   * 重连机制
   */
  private scheduleReconnect(): void {
    if (this.reconnectAttempts >= this.config.reconnectMaxAttempts) {
      console.error('❌ WebSocket重连次数已达上限，停止重连')
      this.status.value = WebSocketStatus.ERROR
      return
    }

    const delay = Math.min(
      this.config.reconnectBaseDelay * Math.pow(2, this.reconnectAttempts),
      30000,
    )
    this.reconnectAttempts++

    console.log(`⏰ ${delay}ms后尝试第${this.reconnectAttempts}次重连...`)
    this.status.value = WebSocketStatus.RECONNECTING

    this.reconnectTimer = setTimeout(() => {
      if (this.tokenProvider) {
        const token = this.tokenProvider()
        if (token) {
          this.connect(token)
        } else {
          console.error('❌ 重连时无法获取token')
        }
      }
    }, delay) as unknown as number
  }

  private clearReconnectTimer(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
  }

  /**
   * 断开连接
   */
  private disconnect(): void {
    this.stopHeartbeat()
    this.clearReconnectTimer()

    if (this.socket) {
      this.socket.close({})
      this.socket = null
    }

    this.status.value = WebSocketStatus.DISCONNECTED
  }

  /**
   * 等待断开完成
   */
  private waitForDisconnection(): Promise<void> {
    return new Promise((resolve) => {
      if (this.status.value === WebSocketStatus.DISCONNECTED) {
        resolve()
        return
      }

      const unwatch = watch(this.status, (newStatus) => {
        if (newStatus === WebSocketStatus.DISCONNECTED) {
          unwatch()
          resolve()
        }
      })

      // 超时保护
      setTimeout(() => {
        unwatch()
        resolve()
      }, 2000)
    })
  }

  private handleConnectionError(): void {
    this.status.value = WebSocketStatus.ERROR
    this.stopHeartbeat()
    this.scheduleReconnect()
  }

  private notifyConnectionEstablished(): void {
    console.log('🎉 WebSocket连接已建立，开始接收消息')
  }

  // Getters
  public get connectionStatus() {
    return this.status
  }

  public get isConnected() {
    return this.status.value === WebSocketStatus.CONNECTED
  }

  public get currentStatus() {
    return this.status.value
  }
}
