import { watch } from 'vue'
import { WebSocketConnectionService } from './connection'
import { WebSocketMessageHandler } from './messageHandler'
import { WebSocketStatus } from './types'

/**
 * WebSocket服务主入口
 * 统一管理WebSocket连接和消息处理
 */
export class WebSocketService {
  private static instance: WebSocketService | null = null
  private connectionService: WebSocketConnectionService
  private messageHandler: WebSocketMessageHandler
  private isInitialized = false
  private userStateWatchers: Array<() => void> = []
  private userStoreRef: any = null // 用户Store引用

  private constructor() {
    this.connectionService = WebSocketConnectionService.getInstance()
    this.messageHandler = WebSocketMessageHandler.getInstance()
    console.log('🚀 WebSocket服务实例创建')
  }

  public static getInstance(): WebSocketService {
    if (!this.instance) {
      this.instance = new WebSocketService()
    }
    return this.instance
  }

  /**
   * 初始化WebSocket服务
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      console.log('⚠️ WebSocket服务已初始化，跳过重复初始化')
      return
    }

    console.log('🚀 初始化WebSocket服务...')

    try {
      // 1. 首先设置用户状态监听（这会设置userStoreRef）
      await this.setupUserStateWatcher()
      console.log('✅ 用户状态监听设置完成')

      // 2. 设置token提供函数（现在userStoreRef已经可用）
      this.setupTokenProvider()

      // 3. 设置消息处理器
      this.setupMessageHandler()

      this.isInitialized = true
      console.log('✅ WebSocket服务初始化完成')
    } catch (error) {
      console.error('❌ WebSocket服务初始化失败:', error)
    }
  }

  /**
   * 设置token提供函数
   */
  private setupTokenProvider(): void {
    this.connectionService.setTokenProvider(() => {
      try {
        // 直接导入用户store - 在运行时导入避免循环依赖
        // 注意：这里不能使用动态import，因为tokenProvider是同步函数
        // 我们需要在初始化时就建立对userStore的引用
        if (!this.userStoreRef) {
          console.error('❌ 用户Store引用未设置')
          return ''
        }
        return this.userStoreRef.userInfo?.token || ''
      } catch (error) {
        console.error('❌ 获取token失败:', error)
        return ''
      }
    })
  }

  /**
   * 设置消息处理器
   */
  private setupMessageHandler(): void {
    this.connectionService.addMessageHandler((message: any) => {
      this.messageHandler.handleMessage(message)
    })
  }

  /**
   * 监听用户状态变化
   */
  private async setupUserStateWatcher(): Promise<void> {
    try {
      // 导入用户store
      const { useUserStore } = await import('@/store/user')
      const userStore = useUserStore()

      // 设置用户Store引用供token提供函数使用
      this.userStoreRef = userStore

      // 监听登录状态变化
      const loginWatcher = watch(
        () => userStore.isLoggedIn,
        (isLoggedIn, wasLoggedIn) => {
          console.log('👤 用户登录状态变化:', { isLoggedIn, wasLoggedIn })

          if (isLoggedIn && !wasLoggedIn) {
            // 用户刚登录
            console.log('🔐 用户登录，建立WebSocket连接')
            this.connectionService.connectAfterLogin()
          } else if (!isLoggedIn && wasLoggedIn) {
            // 用户刚退出
            console.log('👋 用户退出，断开WebSocket连接')
            this.connectionService.disconnectAfterLogout()
          }
        },
        { immediate: true },
      )

      // 监听token变化（用于检测token刷新）
      let lastToken = userStore.userInfo?.token
      const tokenWatcher = watch(
        () => userStore.userInfo?.token,
        (newToken) => {
          if (newToken && newToken !== lastToken && userStore.isLoggedIn) {
            // Token已刷新且用户仍在登录状态
            console.log('🔄 检测到token刷新，重新连接WebSocket')
            this.connectionService.reconnectAfterTokenRefresh()
          }
          lastToken = newToken
        },
      )

      // 保存watcher引用以便清理
      this.userStateWatchers.push(loginWatcher, tokenWatcher)
    } catch (error) {
      console.error('❌ 设置用户状态监听失败:', error)
    }
  }

  /**
   * 手动触发连接（用于特殊情况）
   */
  public connect(): void {
    console.log('🔗 手动触发WebSocket连接')
    this.connectionService.connectAfterLogin()
  }

  /**
   * 手动断开连接
   */
  public disconnect(): void {
    console.log('🔌 手动断开WebSocket连接')
    this.connectionService.disconnectAfterLogout()
  }

  /**
   * 重新连接
   */
  public reconnect(): void {
    console.log('🔄 手动重新连接WebSocket')
    this.connectionService.reconnectAfterTokenRefresh()
  }

  /**
   * 获取连接状态
   */
  public get isConnected(): boolean {
    return this.connectionService.isConnected
  }

  /**
   * 获取连接状态（响应式）
   */
  public get connectionStatus() {
    return this.connectionService.connectionStatus
  }

  /**
   * 获取当前状态值
   */
  public get currentStatus(): WebSocketStatus {
    return this.connectionService.currentStatus
  }

  /**
   * 检查服务是否已初始化
   */
  public get initialized(): boolean {
    return this.isInitialized
  }

  /**
   * 销毁服务（清理资源）
   */
  public destroy(): void {
    console.log('🗑️ 销毁WebSocket服务')

    // 断开连接
    this.disconnect()

    // 清理状态监听器
    this.userStateWatchers.forEach((unwatch) => {
      try {
        unwatch()
      } catch (error) {
        console.error('❌ 清理状态监听器失败:', error)
      }
    })
    this.userStateWatchers = []

    // 重置初始化状态
    this.isInitialized = false

    console.log('✅ WebSocket服务已销毁')
  }

  /**
   * 获取连接统计信息
   */
  public getConnectionStats(): {
    isConnected: boolean
    status: WebSocketStatus
    isInitialized: boolean
  } {
    return {
      isConnected: this.isConnected,
      status: this.currentStatus,
      isInitialized: this.isInitialized,
    }
  }

  /**
   * 调试方法：打印当前状态
   */
  public debugStatus(): void {
    const stats = this.getConnectionStats()
    console.log('🔍 WebSocket服务状态:', stats)
  }
}

// 导出单例实例
export const webSocketService = WebSocketService.getInstance()

// 导出其他服务类供外部使用
export { WebSocketConnectionService } from './connection'
export { WebSocketMessageHandler } from './messageHandler'
export { NotificationService } from './notificationService'
export * from './types'

// 默认导出主服务
export default WebSocketService
