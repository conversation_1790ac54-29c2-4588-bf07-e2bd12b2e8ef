import type { NotificationData, ChatMessageData, SystemMessageData } from './types'

/**
 * 通知显示服务
 * 使用WOT UI的wd-notify显示各种通知
 */
export class NotificationService {
  private static instance: NotificationService | null = null

  private constructor() {
    console.log('🔔 通知显示服务初始化')
  }

  public static getInstance(): NotificationService {
    if (!this.instance) {
      this.instance = new NotificationService()
    }
    return this.instance
  }

  /**
   * 显示通知消息
   */
  public showNotification(data: NotificationData): void {
    const priority = data.priority || 1
    const message = data.message || '您有新消息'

    switch (priority) {
      case 3: // 高优先级
        this.showUrgentNotification(message, data)
        break
      case 2: // 中优先级
        this.showImportantNotification(message, data)
        break
      case 1: // 低优先级
      default:
        this.showNormalNotification(message, data)
        break
    }
  }

  /**
   * 显示紧急通知（高优先级）
   */
  private showUrgentNotification(message: string, data: NotificationData): void {
    console.log('🚨 显示紧急通知:', message)

    // 紧急通知直接使用系统弹窗确保用户看到
    uni.showModal({
      title: '重要通知',
      content: message,
      showCancel: false,
      confirmText: '查看',
      success: (res) => {
        if (res.confirm) {
          this.handleNotificationClick(data)
        }
      },
    })

    // 同时尝试显示WOT UI通知
    try {
      this.showWotNotify({
        type: 'danger',
        message,
        duration: 6000, // 较长显示时间
        onClick: () => {
          this.handleNotificationClick(data)
        },
      })
    } catch (error) {
      console.warn('⚠️ WOT UI通知失败:', error)
    }
  }

  /**
   * 显示重要通知（中优先级）
   */
  private showImportantNotification(message: string, data: NotificationData): void {
    console.log('⚠️ 显示重要通知:', message)

    try {
      this.showWotNotify({
        type: 'warning',
        message,
        duration: 5000,
        onClick: () => {
          this.handleNotificationClick(data)
        },
      })
    } catch (error) {
      console.warn('⚠️ WOT UI通知失败，使用降级方案:', error)
      this.showFallbackToast(message)
    }
  }

  /**
   * 显示普通通知（低优先级）
   */
  private showNormalNotification(message: string, data: NotificationData): void {
    console.log('ℹ️ 显示普通通知:', message)

    try {
      this.showWotNotify({
        type: 'primary',
        message,
        duration: 3000,
        onClick: () => {
          this.handleNotificationClick(data)
        },
      })
    } catch (error) {
      console.warn('⚠️ WOT UI通知失败，使用降级方案:', error)
      this.showFallbackToast(message)
    }
  }

  /**
   * 显示聊天消息通知
   */
  public showChatNotification(data: ChatMessageData): void {
    const message = `${data.sender_name || '新消息'}: ${this.truncateMessage(data.content || '', 20)}`

    console.log('💬 显示聊天通知:', message)

    try {
      this.showWotNotify({
        type: 'primary',
        message,
        duration: 4000,
        onClick: () => {
          // 跳转到聊天页面
          this.navigateToChat(data.session_id)
        },
      })
    } catch (error) {
      console.warn('⚠️ WOT UI聊天通知失败，使用降级方案:', error)
      this.showFallbackToast(message, () => {
        this.navigateToChat(data.session_id)
      })
    }
  }

  /**
   * 显示安全警告
   */
  public showSecurityAlert(data: any): void {
    const message = data.message || '检测到账户异常活动'

    // 安全警告使用系统弹窗
    uni.showModal({
      title: '安全提醒',
      content: message,
      showCancel: true,
      cancelText: '忽略',
      confirmText: '查看详情',
      success: (res) => {
        if (res.confirm && data.action_url) {
          this.navigateToUrl(data.action_url, data.action_type)
        }
      },
    })
  }

  /**
   * 显示系统维护通知
   */
  public showMaintenanceNotice(data: SystemMessageData): void {
    const message = data.message || '系统将进行维护，请注意保存数据'

    uni.showModal({
      title: '系统维护通知',
      content: message,
      showCancel: false,
      confirmText: '知道了',
    })
  }

  /**
   * 显示版本更新通知
   */
  public showVersionUpdateNotice(data: SystemMessageData): void {
    const message = data.message || '发现新版本，建议您及时更新'

    uni.showModal({
      title: '版本更新',
      content: message,
      showCancel: true,
      cancelText: '稍后',
      confirmText: '立即更新',
      success: (res) => {
        if (res.confirm) {
          // 处理版本更新逻辑
          this.handleVersionUpdate()
        }
      },
    })
  }

  /**
   * 显示服务公告
   */
  public showServiceAnnouncement(data: SystemMessageData): void {
    const message = data.message || '服务公告'

    // 服务公告使用info级别的notify
    uni.$emit('wd-notify', {
      type: 'info',
      message,
      duration: 6000,
    })
  }

  /**
   * 显示订单相关通知
   */
  public showOrderNotification(type: string, message: string, orderId?: string): void {
    let notifyType: 'success' | 'warning' | 'danger' | 'primary' | 'info' = 'info'

    switch (type) {
      case 'payment_success':
        notifyType = 'success'
        break
      case 'order_cancelled':
        notifyType = 'warning'
        break
      case 'delivery_update':
        notifyType = 'primary'
        break
      default:
        notifyType = 'info'
    }

    console.log('📦 显示订单通知:', type, message)

    try {
      this.showWotNotify({
        type: notifyType,
        message,
        duration: 4000,
        onClick: () => {
          if (orderId) {
            this.navigateToOrder(orderId)
          }
        },
      })
    } catch (error) {
      console.warn('⚠️ WOT UI订单通知失败，使用降级方案:', error)
      this.showFallbackToast(message, () => {
        if (orderId) {
          this.navigateToOrder(orderId)
        }
      })
    }
  }

  /**
   * 显示优惠券通知
   */
  public showCouponNotification(type: string, message: string): void {
    let notifyType: 'success' | 'warning' | 'primary' = 'primary'

    switch (type) {
      case 'received':
        notifyType = 'success'
        break
      case 'expire_reminder':
        notifyType = 'warning'
        break
      default:
        notifyType = 'primary'
    }

    uni.$emit('wd-notify', {
      type: notifyType,
      message,
      duration: 4000,
      onClick: () => {
        this.navigateToCoupons()
      },
    })
  }

  /**
   * 显示余额变动通知
   */
  public showBalanceChangeNotification(
    changeType: 'increase' | 'decrease',
    amount: number,
    reason: string,
  ): void {
    const symbol = changeType === 'increase' ? '+' : '-'
    const message = `余额${changeType === 'increase' ? '增加' : '减少'} ${symbol}¥${Math.abs(amount)} (${reason})`

    uni.$emit('wd-notify', {
      type: changeType === 'increase' ? 'success' : 'warning',
      message,
      duration: 4000,
      onClick: () => {
        this.navigateToWallet()
      },
    })
  }

  /**
   * 显示积分变动通知
   */
  public showPointsChangeNotification(
    changeType: 'earned' | 'redeemed',
    points: number,
    reason: string,
  ): void {
    const symbol = changeType === 'earned' ? '+' : '-'
    const message = `积分${changeType === 'earned' ? '获得' : '消费'} ${symbol}${Math.abs(points)} (${reason})`

    uni.$emit('wd-notify', {
      type: changeType === 'earned' ? 'success' : 'info',
      message,
      duration: 4000,
      onClick: () => {
        this.navigateToPoints()
      },
    })
  }

  /**
   * 处理通知点击事件
   */
  private handleNotificationClick(data: NotificationData): void {
    if (!data.action_url) return

    this.navigateToUrl(data.action_url, data.action_type)
  }

  /**
   * 导航到指定URL
   */
  private navigateToUrl(url: string, actionType?: string): void {
    try {
      const type = actionType || 'navigate'

      switch (type) {
        case 'navigate':
          uni.navigateTo({ url })
          break
        case 'redirect':
          uni.redirectTo({ url })
          break
        case 'reLaunch':
          uni.reLaunch({ url })
          break
        case 'switchTab':
          uni.switchTab({ url })
          break
        default:
          uni.navigateTo({ url })
      }
    } catch (error) {
      console.error('❌ 导航失败:', error)
      // 降级处理
      try {
        uni.navigateTo({ url })
      } catch (fallbackError) {
        console.error('❌ 降级导航也失败:', fallbackError)
      }
    }
  }

  /**
   * 导航到聊天页面
   */
  private navigateToChat(sessionId: string): void {
    try {
      uni.navigateTo({
        url: `/pages/chat/room/index?session_id=${sessionId}`,
      })
    } catch (error) {
      console.error('❌ 导航到聊天页面失败:', error)
    }
  }

  /**
   * 导航到订单详情页面
   */
  private navigateToOrder(orderId: string): void {
    try {
      uni.navigateTo({
        url: `/pages/order/detail/index?id=${orderId}`,
      })
    } catch (error) {
      console.error('❌ 导航到订单页面失败:', error)
    }
  }

  /**
   * 导航到优惠券页面
   */
  private navigateToCoupons(): void {
    try {
      uni.navigateTo({
        url: '/pages/user/coupon/index',
      })
    } catch (error) {
      console.error('❌ 导航到优惠券页面失败:', error)
    }
  }

  /**
   * 导航到钱包页面
   */
  private navigateToWallet(): void {
    try {
      uni.navigateTo({
        url: '/pages/user/wallet/index',
      })
    } catch (error) {
      console.error('❌ 导航到钱包页面失败:', error)
    }
  }

  /**
   * 导航到积分页面
   */
  private navigateToPoints(): void {
    try {
      uni.navigateTo({
        url: '/pages/user/points/index',
      })
    } catch (error) {
      console.error('❌ 导航到积分页面失败:', error)
    }
  }

  /**
   * 处理版本更新
   */
  private handleVersionUpdate(): void {
    // 这里可以实现版本更新逻辑
    // 比如跳转到应用商店或下载页面
    console.log('🆙 处理版本更新')
  }

  /**
   * 截断消息内容
   */
  private truncateMessage(message: string, maxLength: number): string {
    if (message.length <= maxLength) {
      return message
    }
    return message.substring(0, maxLength) + '...'
  }

  /**
   * 显示WOT UI通知
   * 使用正确的WOT UI通知方式
   */
  private showWotNotify(options: {
    type: 'success' | 'warning' | 'danger' | 'primary' | 'info'
    message: string
    duration: number
    onClick?: () => void
  }): void {
    console.log('🔔 尝试显示WOT UI通知:', options)

    // 方法1: 使用uni.$u.toast (如果可用)
    try {
      if (typeof uni !== 'undefined' && (uni as any).$u && (uni as any).$u.toast) {
        ;(uni as any).$u.toast({
          type: options.type === 'danger' ? 'error' : options.type,
          message: options.message,
          duration: options.duration,
        })
        console.log('✅ WOT UI通知已发送 (uni.$u.toast)')
        return
      }
    } catch (error) {
      console.warn('⚠️ uni.$u.toast方式失败:', error)
    }

    // 方法2: 使用uni.showToast作为降级方案
    try {
      let icon: 'success' | 'error' | 'loading' | 'none' = 'none'
      if (options.type === 'success') icon = 'success'
      else if (options.type === 'danger') icon = 'error'

      uni.showToast({
        title: options.message,
        icon: icon,
        duration: options.duration,
        mask: false,
      })
      console.log('✅ 通知已发送 (uni.showToast降级)')

      // 如果有点击回调，延迟执行
      if (options.onClick) {
        setTimeout(() => {
          options.onClick?.()
        }, options.duration)
      }
      return
    } catch (error) {
      console.warn('⚠️ uni.showToast降级方案失败:', error)
    }

    // 方法3: 最后的降级方案 - 控制台输出
    console.log(`📢 通知: [${options.type.toUpperCase()}] ${options.message}`)
  }

  /**
   * 降级Toast通知
   */
  private showFallbackToast(message: string, onClick?: () => void): void {
    console.log('📱 显示降级Toast通知:', message)

    uni.showToast({
      title: message,
      icon: 'none',
      duration: 3000,
      success: () => {
        if (onClick) {
          // 延迟执行点击回调，避免与Toast冲突
          setTimeout(onClick, 500)
        }
      },
    })
  }
}
