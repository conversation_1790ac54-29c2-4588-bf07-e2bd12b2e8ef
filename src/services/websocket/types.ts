/**
 * WebSocket相关类型定义
 */

// WebSocket连接状态枚举
export enum WebSocketStatus {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  ERROR = 'error',
  RECONNECTING = 'reconnecting'
}

// WebSocket消息基础结构
export interface WebSocketMessage {
  type: string
  event: string
  session_id?: string
  timestamp: number
  data: any
}

// 通知消息数据结构
export interface NotificationData {
  message: string
  priority: 1 | 2 | 3 // 1=低优先级, 2=中优先级, 3=高优先级
  action_type?: 'navigate' | 'redirect' | 'reLaunch' | 'switchTab'
  action_url?: string
  [key: string]: any
}

// 聊天消息数据结构
export interface ChatMessageData {
  session_id: string
  sender_id: string
  sender_name: string
  content: string
  message_type: string
  timestamp: number
  [key: string]: any
}

// 系统消息数据结构
export interface SystemMessageData {
  message: string
  level: 'info' | 'warning' | 'error'
  action_required?: boolean
  [key: string]: any
}

// 订单相关消息数据结构
export interface OrderMessageData {
  order_id: string
  status?: string
  message: string
  [key: string]: any
}

// 余额变动消息数据结构
export interface BalanceChangeData {
  balance: number
  change_amount: number
  change_type: 'increase' | 'decrease'
  reason: string
  [key: string]: any
}

// 优惠券消息数据结构
export interface CouponMessageData {
  coupon_id?: string
  coupon?: any
  message: string
  expire_time?: string
  [key: string]: any
}

// 积分消息数据结构
export interface PointsMessageData {
  points: number
  change_amount: number
  change_type: 'earned' | 'redeemed'
  reason: string
  [key: string]: any
}

// WebSocket事件回调函数类型
export type WebSocketEventCallback = (data: any) => void

// WebSocket连接配置
export interface WebSocketConfig {
  url: string
  heartbeatInterval: number
  reconnectMaxAttempts: number
  reconnectBaseDelay: number
  connectionTimeout: number
}
