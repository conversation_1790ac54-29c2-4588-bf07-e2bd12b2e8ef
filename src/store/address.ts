/**
 * 地址管理状态模块
 * 管理用户收货地址的增删改查等操作
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import {
  getAddressList,
  getAddressDetail,
  createAddress,
  updateAddress,
  deleteAddress,
  setDefaultAddress,
  getDefaultAddress,
} from '@/api/address'
import type { IAddress, ICreateAddressParams, IUpdateAddressParams } from '@/api/address.typings'
import { toast } from '@/utils/toast'
import { get } from '@/utils/request'
import { useUserStore } from '@/store/user'

// 地址选项接口定义
interface AddressOption {
  label: string
  value: number | string
  level: number
  children?: AddressOption[]
  longitude?: number | null
  latitude?: number | null
  isLeaf?: boolean
}

// 扩展地址接口，添加selectedKeys字段
interface IAddressWithKeys extends IAddress {
  selectedKeys?: (string | number)[]
}

export const useAddressStore = defineStore(
  'address',
  () => {
    // 用户store实例
    const userStore = useUserStore()

    // 地址列表
    const addressList = ref<IAddressWithKeys[]>([])
    // 地址总数
    const addressTotal = ref(0)
    // 是否正在加载
    const loading = ref(false)
    // 当前地址详情
    const currentAddress = ref<IAddressWithKeys | null>(null)
    // 默认地址
    const defaultAddress = ref<IAddressWithKeys | null>(null)
    // 缓存的地址数据
    const addressData = ref<AddressOption[]>([])

    // 计算属性：是否为空列表
    const isEmpty = computed(() => addressList.value.length === 0)

    // 计算属性：是否有默认地址
    const hasDefaultAddress = computed(() => defaultAddress.value !== null)

    // 计算属性：非默认地址列表
    const nonDefaultAddresses = computed(() =>
      addressList.value.filter((address) => !address.isDefault),
    )

    /**
     * 加载社区地址数据
     */
    const loadAddressData = async () => {
      // 如果已有缓存数据，直接返回
      if (addressData.value.length > 0) {
        console.log('使用缓存的地址数据:', addressData.value)
        return addressData.value
      }

      try {
        console.log('开始加载社区地址数据...')
        // 使用正确的API路径，参考CommunityAddressSelector组件
        const API_BASE_URL = '/api/v1/system'
        const response: any = await get(`${API_BASE_URL}/addresses/options`)

        console.log('社区地址API响应:', response)

        // 处理不同格式的响应数据
        if (
          response &&
          response.data &&
          response.data.options &&
          Array.isArray(response.data.options)
        ) {
          // 处理data.options数据格式，并标记叶子节点
          addressData.value = markLeafNodes(transformAddressData(response.data.options, 1))
          console.log('处理data.options格式，结果:', addressData.value)
        } else if (response && response.options && Array.isArray(response.options)) {
          // 兼容直接在response中包含options的情况
          addressData.value = markLeafNodes(transformAddressData(response.options, 1))
          console.log('处理response.options格式，结果:', addressData.value)
        } else if (response && Array.isArray(response)) {
          // 兼容response直接是数组的情况
          const firstLevelAddresses = response.filter(
            (addr: any) => addr.level === 1 || addr.parentId === 0,
          )
          addressData.value = markLeafNodes(transformAddressData(firstLevelAddresses, 1))
          console.log('处理数组格式，结果:', addressData.value)
        } else {
          console.error('无效的地址数据格式:', response)
          addressData.value = []
        }

        console.log('社区地址数据加载完成，缓存到store中:', addressData.value)
        return addressData.value
      } catch (error) {
        console.error('加载地址数据失败:', error)
        addressData.value = []
        return []
      }
    }

    /**
     * 转换地址数据格式
     */
    const transformAddressData = (data: any[], level: number): AddressOption[] => {
      return data.map((item: any) => ({
        label: item.name || item.label || '',
        value: Number(item.id || item.value) || 0,
        level: level,
        longitude: item.longitude || null,
        latitude: item.latitude || null,
        children: item.children ? transformAddressData(item.children, level + 1) : undefined,
        isLeaf: !item.children || item.children.length === 0,
      }))
    }

    /**
     * 根据层级设置是否为叶子节点
     */
    const markLeafNodes = (data: AddressOption[]): AddressOption[] => {
      if (!Array.isArray(data)) return []

      return data.map((item) => {
        const node = { ...item }

        if (node.level === 2 || node.level === 3 || !node.children || !node.children.length) {
          node.isLeaf = true
        }

        if (node.children && node.children.length > 0) {
          node.children = markLeafNodes(node.children)
        }

        return node
      })
    }

    /**
     * 通过详细地址字符串计算selectedKeys
     * @param detailedAddress 详细地址字符串，格式如："小区名/楼栋名/单元名"
     * @returns 计算出的selectedKeys数组
     */
    const calculateSelectedKeysFromAddress = async (
      detailedAddress: string,
    ): Promise<(string | number)[]> => {
      console.log('store calculateSelectedKeysFromAddress: 开始计算，详细地址 =', detailedAddress)

      if (!detailedAddress) {
        console.log('store calculateSelectedKeysFromAddress: 地址为空，返回空数组')
        return []
      }

      // 确保地址数据已加载
      await loadAddressData()

      if (!addressData.value || !addressData.value.length) {
        console.log('store calculateSelectedKeysFromAddress: 地址数据未加载，返回空数组')
        return []
      }

      // 分割地址字符串
      const addressParts = detailedAddress.split('/').filter((part) => part.trim())
      console.log('store calculateSelectedKeysFromAddress: 地址分割结果 =', addressParts)

      if (addressParts.length === 0) {
        console.log('store calculateSelectedKeysFromAddress: 地址分割后为空，返回空数组')
        return []
      }

      const result: (string | number)[] = []
      let currentOptions = addressData.value

      // 逐级查找匹配的节点
      for (let i = 0; i < addressParts.length; i++) {
        const addressPart = addressParts[i].trim()
        console.log(
          `store calculateSelectedKeysFromAddress: 查找第${i}级，地址部分="${addressPart}"，当前选项:`,
          currentOptions,
        )

        // 在当前级别中查找匹配的节点
        const matchedNode = currentOptions.find(
          (node) =>
            node.label === addressPart ||
            node.label.includes(addressPart) ||
            addressPart.includes(node.label),
        )

        if (matchedNode) {
          console.log(`store calculateSelectedKeysFromAddress: 找到第${i}级匹配节点:`, matchedNode)
          result.push(matchedNode.value)

          // 如果有子节点，继续下一级查找
          if (matchedNode.children && matchedNode.children.length > 0) {
            currentOptions = matchedNode.children
          } else {
            console.log(`store calculateSelectedKeysFromAddress: 第${i}级节点没有子节点，停止查找`)
            break
          }
        } else {
          console.log(
            `store calculateSelectedKeysFromAddress: 第${i}级未找到匹配节点，地址部分="${addressPart}"`,
          )
          // 尝试模糊匹配
          const fuzzyMatch = currentOptions.find((node) => {
            const nodeName = node.label.toLowerCase()
            const searchName = addressPart.toLowerCase()
            return nodeName.includes(searchName) || searchName.includes(nodeName)
          })

          if (fuzzyMatch) {
            console.log(
              `store calculateSelectedKeysFromAddress: 第${i}级模糊匹配到节点:`,
              fuzzyMatch,
            )
            result.push(fuzzyMatch.value)
            if (fuzzyMatch.children && fuzzyMatch.children.length > 0) {
              currentOptions = fuzzyMatch.children
            } else {
              break
            }
          } else {
            console.log(`store calculateSelectedKeysFromAddress: 第${i}级完全未找到匹配，停止查找`)
            break
          }
        }
      }

      console.log('store calculateSelectedKeysFromAddress: 最终计算结果 =', result)
      return result
    }

    /**
     * 为地址对象添加selectedKeys
     * @param address 地址对象
     * @returns 带有selectedKeys的地址对象
     */
    const addSelectedKeysToAddress = async (
      address: IAddress | null,
    ): Promise<IAddressWithKeys | null> => {
      if (!address) {
        return null
      }

      const addressWithKeys = { ...address } as IAddressWithKeys

      // 如果有详细地址，计算selectedKeys
      if (address.detailed_address) {
        try {
          const selectedKeys = await calculateSelectedKeysFromAddress(address.detailed_address)
          addressWithKeys.selectedKeys = selectedKeys
          console.log(`为地址 ${address.id} 计算selectedKeys:`, selectedKeys)
        } catch (error) {
          console.error(`为地址 ${address.id} 计算selectedKeys失败:`, error)
          addressWithKeys.selectedKeys = []
        }
      } else {
        addressWithKeys.selectedKeys = []
      }

      return addressWithKeys
    }

    /**
     * 获取地址列表
     */
    const fetchAddressList = async () => {
      try {
        // 检查用户登录状态
        if (!userStore.isLoggedIn) {
          console.log('📍 [AddressStore] 用户未登录，无法获取地址列表')
          addressList.value = []
          addressTotal.value = 0
          return []
        }

        loading.value = true
        console.log('📍 [AddressStore] 开始获取地址列表')

        const res = await getAddressList()
        console.log('📍 [AddressStore] API返回:', res)
        const data = res.data

        if (!data || !data.list) {
          console.log('📍 [AddressStore] 地址列表为空')
          addressList.value = []
          addressTotal.value = 0
          return []
        }

        // 为每个地址计算selectedKeys
        console.log('📍 [AddressStore] 开始为地址列表计算selectedKeys，地址数量:', data.list.length)
        const addressesWithKeys: IAddressWithKeys[] = []

        for (const address of data.list) {
          const addressWithKeys = await addSelectedKeysToAddress(address)
          if (addressWithKeys) {
            addressesWithKeys.push(addressWithKeys)
          }
        }

        addressList.value = addressesWithKeys
        addressTotal.value = data.total || addressesWithKeys.length

        console.log('📍 [AddressStore] 地址列表获取成功:', {
          total: addressesWithKeys.length,
          addresses: addressesWithKeys.map((addr) => ({
            id: addr.id,
            isDefault: addr.is_default || addr.isDefault,
            address: `${addr.province}${addr.city}${addr.district}${addr.detailed_address}`,
            hasCoordinates: !!(addr.location_latitude && addr.location_longitude),
          })),
        })

        // 查找默认地址 - 支持新旧API字段
        const defaultAddr = addressesWithKeys.find(
          (address) => address.is_default || address.isDefault,
        )
        if (defaultAddr) {
          defaultAddress.value = defaultAddr
          console.log('📍 [AddressStore] 找到默认地址:', {
            id: defaultAddr.id,
            address: `${defaultAddr.province}${defaultAddr.city}${defaultAddr.district}${defaultAddr.detailed_address}`,
          })
        } else {
          console.log('📍 [AddressStore] 没有找到默认地址')
        }

        return addressesWithKeys
      } catch (error) {
        console.error('📍 [AddressStore] 获取地址列表失败:', error)
        addressList.value = []
        addressTotal.value = 0
        // 不显示错误提示，因为可能是权限问题
        return []
      } finally {
        loading.value = false
      }
    }

    /**
     * 获取地址详情
     * @param addressId 地址ID
     */
    const fetchAddressDetail = async (addressId: number) => {
      try {
        loading.value = true
        const { data } = await getAddressDetail(addressId)

        // 为地址计算selectedKeys
        console.log('开始为地址详情计算selectedKeys，地址ID:', addressId)
        const addressWithKeys = await addSelectedKeysToAddress(data)
        currentAddress.value = addressWithKeys
        console.log('地址详情selectedKeys计算完成:', addressWithKeys)

        return addressWithKeys
      } catch (error) {
        console.error('获取地址详情失败:', error)
        toast.error('获取地址详情失败')
        throw error
      } finally {
        loading.value = false
      }
    }

    /**
     * 创建新地址
     * @param params 创建地址参数
     */
    const createNewAddress = async (params: ICreateAddressParams) => {
      try {
        loading.value = true
        const { data } = await createAddress(params)
        toast.success('地址添加成功')

        // 为新创建的地址计算selectedKeys
        console.log('开始为新创建的地址计算selectedKeys')
        const addressWithKeys = await addSelectedKeysToAddress(data)
        console.log('新地址selectedKeys计算完成:', addressWithKeys)

        // 添加到本地列表
        addressList.value.push(addressWithKeys)
        addressTotal.value++

        // 如果是默认地址，更新默认地址 - 支持新旧API字段
        if (addressWithKeys.is_default || addressWithKeys.isDefault) {
          // 清除其他地址的默认状态
          addressList.value.forEach((address) => {
            if (address.id !== addressWithKeys.id) {
              address.is_default = false
              address.isDefault = false
            }
          })
          defaultAddress.value = addressWithKeys
        }

        return addressWithKeys
      } catch (error) {
        console.error('创建地址失败:', error)
        toast.error('创建地址失败')
        throw error
      } finally {
        loading.value = false
      }
    }

    /**
     * 更新地址
     * @param addressId 地址ID
     * @param params 更新地址参数
     */
    const updateAddressById = async (addressId: number, params: IUpdateAddressParams) => {
      try {
        loading.value = true
        await updateAddress(addressId, params)
        toast.success('地址更新成功')

        // 更新地址后重新获取地址列表
        console.log('地址更新成功，重新获取地址列表')
        await fetchAddressList()

        return true
      } catch (error) {
        console.error('更新地址失败:', error)
        toast.error('更新地址失败')
        throw error
      } finally {
        loading.value = false
      }
    }

    /**
     * 删除地址
     * @param addressId 地址ID
     */
    const deleteAddressById = async (addressId: number) => {
      try {
        await deleteAddress(addressId)
        toast.success('地址删除成功')

        // 从本地列表中移除
        const index = addressList.value.findIndex((address) => address.id === addressId)
        if (index > -1) {
          const deletedAddress = addressList.value[index]
          addressList.value.splice(index, 1)
          addressTotal.value--

          // 如果删除的是默认地址，清除默认地址 - 支持新旧API字段
          if (deletedAddress.is_default || deletedAddress.isDefault) {
            defaultAddress.value = null
          }
        }

        // 清除当前地址详情
        if (currentAddress.value && currentAddress.value.id === addressId) {
          currentAddress.value = null
        }
      } catch (error) {
        console.error('删除地址失败:', error)
        toast.error('删除地址失败')
      }
    }

    /**
     * 设置默认地址
     * @param addressId 地址ID
     */
    const setDefaultAddressById = async (addressId: number) => {
      try {
        await setDefaultAddress(addressId)
        toast.success('默认地址设置成功')

        // 重新获取地址列表以更新状态
        console.log('默认地址设置成功，重新获取地址列表')
        await fetchAddressList()
      } catch (error) {
        console.error('设置默认地址失败:', error)
        toast.error('设置默认地址失败')
      }
    }

    /**
     * 获取默认地址
     */
    const fetchDefaultAddress = async () => {
      try {
        // 检查用户登录状态
        if (!userStore.isLoggedIn) {
          console.log('用户未登录，无法获取默认地址')
          defaultAddress.value = null
          return null
        }

        const { data } = await getDefaultAddress()

        // 检查是否有默认地址数据
        if (!data) {
          console.log('用户未设置默认地址')
          defaultAddress.value = null
          return null
        }

        // 为默认地址计算selectedKeys
        console.log('开始为默认地址计算selectedKeys')
        const addressWithKeys = await addSelectedKeysToAddress(data)
        console.log('默认地址selectedKeys计算完成:', addressWithKeys)

        defaultAddress.value = addressWithKeys
        return addressWithKeys
      } catch (error) {
        console.error('获取默认地址失败:', error)
        // 这里不显示错误提示，因为可能没有默认地址或用户未登录
        defaultAddress.value = null
        return null
      }
    }

    /**
     * 根据ID获取地址
     * @param addressId 地址ID
     */
    const getAddressById = (addressId: number): IAddress | undefined => {
      return addressList.value.find((address) => address.id === addressId)
    }

    /**
     * 设置当前编辑的地址
     * @param address 地址对象
     */
    const setCurrentAddress = async (address: IAddress | IAddressWithKeys | null) => {
      if (!address) {
        currentAddress.value = null
        return
      }

      // 如果地址没有selectedKeys，计算它
      if (!('selectedKeys' in address) || !address.selectedKeys) {
        console.log('设置当前地址时计算selectedKeys')
        const addressWithKeys = await addSelectedKeysToAddress(address as IAddress)
        currentAddress.value = addressWithKeys
      } else {
        currentAddress.value = address as IAddressWithKeys
      }
    }

    /**
     * 本地更新地址信息
     * @param addressId 地址ID
     * @param updates 更新的字段
     */
    const updateAddressLocal = async (addressId: number, updates: Partial<IAddress>) => {
      const address = addressList.value.find((addr) => addr.id === addressId)
      if (address) {
        Object.assign(address, updates)

        // 如果更新了详细地址，重新计算selectedKeys
        if (updates.detailed_address !== undefined) {
          console.log('本地更新地址时重新计算selectedKeys，地址ID:', addressId)
          const selectedKeys = await calculateSelectedKeysFromAddress(
            updates.detailed_address || '',
          )
          address.selectedKeys = selectedKeys
        }

        // 如果更新的是默认地址状态 - 支持新旧API字段
        if (updates.is_default !== undefined || updates.isDefault !== undefined) {
          const isDefault = updates.is_default || updates.isDefault
          if (isDefault) {
            // 设置为默认地址，清除其他地址的默认状态
            addressList.value.forEach((addr) => {
              if (addr.id !== addressId) {
                addr.is_default = false
                addr.isDefault = false
              }
            })
            defaultAddress.value = address
          } else if (defaultAddress.value && defaultAddress.value.id === addressId) {
            // 取消默认地址
            defaultAddress.value = null
          }
        }

        // 如果是当前地址，也更新当前地址
        if (currentAddress.value && currentAddress.value.id === addressId) {
          currentAddress.value = address
        }
      }
    }

    /**
     * 重置地址状态
     */
    const resetAddressState = () => {
      addressList.value = []
      addressTotal.value = 0
      currentAddress.value = null
      defaultAddress.value = null
    }

    /**
     * 清除当前地址详情
     */
    const clearCurrentAddress = () => {
      currentAddress.value = null
    }

    return {
      // 状态
      addressList,
      addressTotal,
      loading,
      currentAddress,
      defaultAddress,
      addressData,

      // 计算属性
      isEmpty,
      hasDefaultAddress,
      nonDefaultAddresses,

      // 方法
      fetchAddressList,
      fetchAddressDetail,
      createNewAddress,
      updateAddressById,
      deleteAddressById,
      setDefaultAddressById,
      fetchDefaultAddress,
      getAddressById,
      setCurrentAddress,
      updateAddressLocal,
      resetAddressState,
      clearCurrentAddress,

      // 新增的地址处理方法
      loadAddressData,
      calculateSelectedKeysFromAddress,
      addSelectedKeysToAddress,
    }
  },
  {
    persist: {
      paths: ['defaultAddress'],
    },
  },
)
