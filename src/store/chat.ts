/**
 * 聊天系统状态管理
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import {
  getConversationList,
  getMessageList,
  sendMessage as apiSendMessage,
  markMessageRead,
  getUnreadCount,
  createConversation,
  deleteConversation,
  getCustomerServiceList,
} from '@/api/chat'
import { MessageStatus, MessageType } from '@/api/chat.typings'
import type {
  IConversation,
  IChatMessage,
  IConversationListParams,
  IMessageListParams,
  ISendMessageParams,
  ICustomerService,
  ConversationType,
} from '@/api/chat.typings'

export const useChatStore = defineStore('chat', () => {
  // 会话列表
  const conversations = ref<IConversation[]>([])
  // 当前会话
  const currentConversation = ref<IConversation | null>(null)
  // 当前会话的消息列表
  const currentMessages = ref<IChatMessage[]>([])
  // 客服列表
  const customerServices = ref<ICustomerService[]>([])
  // 未读消息数量
  const unreadCount = ref(0)
  // 各会话未读数量
  const conversationUnreadCounts = ref<Record<string, number>>({})
  // 加载状态
  const loading = ref({
    conversations: false,
    messages: false,
    sending: false,
    services: false,
  })
  // WebSocket连接状态
  const wsConnected = ref(false)
  // 输入状态
  const typingUsers = ref<Record<string, string[]>>({})

  // 计算属性
  const totalUnreadCount = computed(() => {
    return Object.values(conversationUnreadCounts.value).reduce((sum, count) => sum + count, 0)
  })

  const onlineServices = computed(() => {
    return customerServices.value.filter((service) => service.isOnline)
  })

  // 为兼容性添加messages计算属性
  const messages = computed(() => currentMessages.value)

  // 添加hasMoreMessages计算属性
  const hasMoreMessages = ref(false)

  // 添加quickReplies
  const quickReplies = ref<any[]>([])

  /**
   * 获取会话列表
   */
  const fetchConversations = async (params: IConversationListParams = {}) => {
    try {
      loading.value.conversations = true
      const res = await getConversationList(params)
      conversations.value = res.conversations || []

      // 更新未读数量
      const unreadCounts: Record<string, number> = {}
      ;(res.conversations || []).forEach((conv) => {
        unreadCounts[conv.id] = conv.unreadCount
      })
      conversationUnreadCounts.value = unreadCounts

      return res
    } catch (error) {
      console.error('获取会话列表失败:', error)
      return { conversations: [], total: 0, page: 1, pageSize: 10 }
    } finally {
      loading.value.conversations = false
    }
  }

  /**
   * 获取消息列表
   */
  const fetchMessages = async (conversationId: string, params?: Partial<IMessageListParams>) => {
    try {
      loading.value.messages = true
      const messageParams: IMessageListParams = {
        conversationId,
        page: 1,
        pageSize: 20,
        ...params,
      }
      const res = await getMessageList(messageParams)

      // 处理消息时间字段格式
      const processedMessages = (res.messages || []).map((msg: any) => ({
        ...msg,
        createdAt: msg.createdAt || msg.created_at || new Date().toISOString(),
        updatedAt: msg.updatedAt || msg.updated_at || new Date().toISOString(),
        isRead: msg.isRead !== undefined ? msg.isRead : msg.status === 1,
        status: msg.status || MessageStatus.SENT,
      }))

      if (messageParams.page === 1) {
        // 第一页，替换消息列表
        currentMessages.value = processedMessages
      } else {
        // 后续页，追加到消息列表前面（历史消息）
        currentMessages.value = [...processedMessages, ...currentMessages.value]
      }

      hasMoreMessages.value = res.hasMore || false
      return res
    } catch (error) {
      console.error('获取消息列表失败:', error)
      return { messages: [], total: 0, page: 1, pageSize: 20, hasMore: false }
    } finally {
      loading.value.messages = false
    }
  }

  /**
   * 发送消息
   */
  const sendChatMessage = async (params: ISendMessageParams) => {
    console.log('🎯 [Store.sendChatMessage] 开始发送消息:', params)

    try {
      loading.value.sending = true

      // 先添加到本地消息列表（显示发送中状态）
      const tempMessage: IChatMessage = {
        id: `temp_${Date.now()}`,
        conversationId: params.conversationId,
        senderId: 'current_user', // 当前用户ID
        senderInfo: {
          id: 'current_user',
          nickname: '我',
          avatar: '',
          userType: 'user',
          isOnline: true,
        },
        receiverId: params.receiverId,
        type: params.type,
        content: params.content,
        status: MessageStatus.SENDING,
        isRead: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      }

      currentMessages.value.push(tempMessage)

      // 发送消息
      console.log('🎯 [Store.sendChatMessage] 调用API发送消息:', params)
      const res = await apiSendMessage(params)
      console.log('🎯 [Store.sendChatMessage] API响应:', res)

      // 替换临时消息
      const index = currentMessages.value.findIndex((msg) => msg.id === tempMessage.id)
      if (index !== -1) {
        // 确保API返回的消息有正确的时间字段
        const apiResponse = res as any // 临时类型断言处理后端字段名差异
        const apiMessage: IChatMessage = {
          ...res,
          createdAt: res.createdAt || apiResponse.created_at || new Date().toISOString(),
          updatedAt: res.updatedAt || apiResponse.updated_at || new Date().toISOString(),
          // 确保其他必要字段存在
          isRead: res.isRead !== undefined ? res.isRead : false,
          status: res.status || MessageStatus.SENT,
        }
        currentMessages.value[index] = apiMessage
      }

      // 更新会话的最后一条消息
      updateConversationLastMessage(params.conversationId, res)

      return res
    } catch (error) {
      console.error('发送消息失败:', error)

      // 更新消息状态为失败
      const tempMessage = currentMessages.value.find((msg) => msg.id.startsWith('temp_'))
      if (tempMessage) {
        tempMessage.status = MessageStatus.FAILED
      }

      throw error
    } finally {
      loading.value.sending = false
    }
  }

  /**
   * 标记消息已读
   */
  const markMessagesRead = async (conversationId: string, messageIds?: string[]) => {
    try {
      await markMessageRead({ conversationId, messageIds })

      // 更新本地消息状态
      currentMessages.value.forEach((msg) => {
        if (msg.conversationId === conversationId && (!messageIds || messageIds.includes(msg.id))) {
          msg.isRead = true
        }
      })

      // 清除该会话的未读数量
      conversationUnreadCounts.value[conversationId] = 0

      // 更新会话列表中的未读数量
      const conversation = conversations.value.find((conv) => conv.id === conversationId)
      if (conversation) {
        conversation.unreadCount = 0
      }
    } catch (error) {
      console.error('标记消息已读失败:', error)
    }
  }

  /**
   * 创建会话
   */
  const createNewConversation = async (
    type: ConversationType,
    participantId: string,
    extra?: any,
  ) => {
    try {
      const res = await createConversation({
        type,
        participantId,
        extra,
      })

      // 添加到会话列表
      conversations.value.unshift(res)

      return res
    } catch (error) {
      console.error('创建会话失败:', error)
      throw error
    }
  }

  /**
   * 删除会话
   */
  const removeConversation = async (conversationId: string) => {
    try {
      await deleteConversation(conversationId)

      // 从本地列表中移除
      const index = conversations.value.findIndex((conv) => conv.id === conversationId)
      if (index !== -1) {
        conversations.value.splice(index, 1)
      }

      // 清除未读数量
      delete conversationUnreadCounts.value[conversationId]

      // 如果是当前会话，清空消息列表
      if (currentConversation.value?.id === conversationId) {
        currentConversation.value = null
        currentMessages.value = []
      }
    } catch (error) {
      console.error('删除会话失败:', error)
      throw error
    }
  }

  /**
   * 获取客服列表
   */
  const fetchCustomerServices = async () => {
    try {
      loading.value.services = true
      const res = await getCustomerServiceList()
      customerServices.value = res.services || []
      return res
    } catch (error) {
      console.error('获取客服列表失败:', error)
      return { services: [], onlineCount: 0, totalCount: 0 }
    } finally {
      loading.value.services = false
    }
  }

  /**
   * 获取未读消息数量
   */
  const fetchUnreadCount = async () => {
    try {
      const res = await getUnreadCount()
      unreadCount.value = res.total || 0
      conversationUnreadCounts.value = res.conversations || {}
      return res
    } catch (error) {
      console.error('获取未读消息数量失败:', error)
      return { total: 0, conversations: {} }
    }
  }

  // 原setCurrentConversation方法已移动到下方，支持字符串ID

  /**
   * 更新会话的最后一条消息
   */
  const updateConversationLastMessage = (conversationId: string, message: IChatMessage) => {
    const conversation = conversations.value.find((conv) => conv.id === conversationId)
    if (conversation) {
      conversation.lastMessage = message
      conversation.updatedAt = message.createdAt

      // 将会话移到列表顶部
      const index = conversations.value.findIndex((conv) => conv.id === conversationId)
      if (index > 0) {
        conversations.value.splice(index, 1)
        conversations.value.unshift(conversation)
      }
    }
  }

  /**
   * 接收新消息（WebSocket）
   */
  const receiveMessage = (message: IChatMessage) => {
    // 如果是当前会话的消息，添加到消息列表
    if (currentConversation.value?.id === message.conversationId) {
      currentMessages.value.push(message)
    }

    // 更新会话的最后一条消息
    updateConversationLastMessage(message.conversationId, message)

    // 更新未读数量
    if (message.senderId !== 'current_user') {
      const currentCount = conversationUnreadCounts.value[message.conversationId] || 0
      conversationUnreadCounts.value[message.conversationId] = currentCount + 1

      const conversation = conversations.value.find((conv) => conv.id === message.conversationId)
      if (conversation) {
        conversation.unreadCount = currentCount + 1
      }
    }
  }

  /**
   * 设置WebSocket连接状态
   */
  const setWSConnected = (connected: boolean) => {
    wsConnected.value = connected
  }

  /**
   * 设置输入状态
   */
  const setTypingStatus = (conversationId: string, userId: string, isTyping: boolean) => {
    if (!typingUsers.value[conversationId]) {
      typingUsers.value[conversationId] = []
    }

    const users = typingUsers.value[conversationId]
    const index = users.indexOf(userId)

    if (isTyping && index === -1) {
      users.push(userId)
    } else if (!isTyping && index !== -1) {
      users.splice(index, 1)
    }
  }

  /**
   * 获取快捷回复
   */
  const fetchQuickReplies = async () => {
    try {
      // 暂时返回空数组，后续可以实现具体逻辑
      quickReplies.value = []
      return quickReplies.value
    } catch (error) {
      console.error('获取快捷回复失败:', error)
      return []
    }
  }

  /**
   * 标记消息为已读（兼容方法名）
   */
  const markMessagesAsRead = async (conversationId: string) => {
    try {
      // 获取当前会话的所有未读消息ID
      const unreadMessages = currentMessages.value.filter((msg) => !msg.isRead)
      if (unreadMessages.length === 0) return

      const messageIds = unreadMessages.map((msg) => msg.id)
      await markMessagesRead(conversationId, messageIds)

      // 更新本地消息状态
      currentMessages.value.forEach((msg) => {
        if (messageIds.includes(msg.id)) {
          msg.isRead = true
        }
      })

      // 更新未读数量
      if (conversationUnreadCounts.value[conversationId]) {
        conversationUnreadCounts.value[conversationId] = 0
      }

      return true
    } catch (error) {
      console.error('标记消息已读失败:', error)
      return false
    }
  }

  /**
   * 发送输入状态
   */
  const sendTypingStatus = async (conversationId: string, isTyping: boolean) => {
    try {
      // 这里可以通过WebSocket发送输入状态
      // 暂时只在本地更新状态
      if (isTyping) {
        typingUsers.value[conversationId] = ['current_user']
      } else {
        delete typingUsers.value[conversationId]
      }
      return true
    } catch (error) {
      console.error('发送输入状态失败:', error)
      return false
    }
  }

  /**
   * 上传聊天文件
   */
  const uploadChatFile = async (filePath: string, fileType: string) => {
    try {
      // 这里应该调用文件上传API
      // 暂时返回模拟数据
      return {
        url: filePath,
        fileName: `file_${Date.now()}`,
        fileSize: 0,
        fileType: fileType,
      }
    } catch (error) {
      console.error('上传文件失败:', error)
      throw error
    }
  }

  /**
   * 发送消息（兼容方法名）
   */
  const sendMessage = async (
    conversationId: string,
    messageData: {
      type: string
      content: any
    },
  ) => {
    console.log('🎯 [Store.sendMessage] 接收到发送消息请求:', {
      conversationId,
      conversationIdType: typeof conversationId,
      messageData,
    })

    try {
      // 转换字符串类型为MessageType枚举
      let messageType: MessageType
      switch (messageData.type.toLowerCase()) {
        case 'text':
          messageType = MessageType.TEXT
          break
        case 'image':
          messageType = MessageType.IMAGE
          break
        case 'voice':
          messageType = MessageType.VOICE
          break
        case 'video':
          messageType = MessageType.VIDEO
          break
        case 'file':
          messageType = MessageType.FILE
          break
        case 'location':
          messageType = MessageType.LOCATION
          break
        case 'system':
          messageType = MessageType.SYSTEM
          break
        case 'order':
          messageType = MessageType.ORDER
          break
        case 'goods':
          messageType = MessageType.GOODS
          break
        default:
          messageType = MessageType.TEXT
      }

      const params: ISendMessageParams = {
        conversationId,
        receiverId: '', // 这个需要从会话信息中获取
        type: messageType,
        content: messageData.content,
      }

      console.log('🎯 [Store.sendMessage] 准备调用sendChatMessage:', params)
      return await sendChatMessage(params)
    } catch (error) {
      console.error('发送消息失败:', error)
      throw error
    }
  }

  /**
   * 设置当前会话（支持字符串ID）
   */
  const setCurrentConversation = async (conversationId: string | IConversation | null) => {
    if (typeof conversationId === 'string') {
      if (conversationId) {
        // 根据ID查找会话
        const conversation = conversations.value.find((conv) => conv.id === conversationId)
        currentConversation.value = conversation || null
        if (!conversation) {
          // 如果没有找到会话，清空消息列表
          currentMessages.value = []
        }
      } else {
        currentConversation.value = null
        currentMessages.value = []
      }
    } else {
      currentConversation.value = conversationId
      if (!conversationId) {
        currentMessages.value = []
      }
    }
  }

  /**
   * 清空聊天状态
   */
  const clearChatState = () => {
    conversations.value = []
    currentConversation.value = null
    currentMessages.value = []
    customerServices.value = []
    unreadCount.value = 0
    conversationUnreadCounts.value = {}
    typingUsers.value = {}
    wsConnected.value = false
  }

  return {
    // 状态
    conversations,
    currentConversation,
    currentMessages,
    customerServices,
    unreadCount,
    conversationUnreadCounts,
    loading,
    wsConnected,
    typingUsers,
    quickReplies,
    hasMoreMessages,
    // 计算属性
    totalUnreadCount,
    onlineServices,
    messages,
    // 方法
    fetchConversations,
    fetchMessages,
    sendChatMessage,
    markMessagesRead,
    createNewConversation,
    removeConversation,
    fetchCustomerServices,
    fetchUnreadCount,
    fetchQuickReplies,
    markMessagesAsRead,
    sendTypingStatus,
    sendMessage,
    uploadChatFile,
    setCurrentConversation,
    updateConversationLastMessage,
    receiveMessage,
    setWSConnected,
    setTypingStatus,
    clearChatState,
  }
})
