/**
 * 优惠券状态管理
 */

import { defineStore } from 'pinia'
import {
  getMyCoupons,
  getAvailableCouponsForOrder,
  claimCoupon,
  getCouponCenter,
  getExpiringSoonCoupons,
  batchClaimCoupons,
} from '@/api/coupon'
import type { IUserCoupon, ICoupon, ICouponStats } from '@/api/coupon.typings'
import { CouponStatus, CouponType } from '@/api/coupon.typings'

interface CouponState {
  // 我的优惠券
  myCoupons: IUserCoupon[]

  // 优惠券中心
  centerCoupons: ICoupon[]

  // 可用优惠券（按商家分组）
  availableCoupons: Record<number, IUserCoupon[]>
  unavailableCoupons: Record<number, IUserCoupon[]>

  // 选中的优惠券（按商家分组）
  selectedCoupons: Record<number, IUserCoupon | null>

  // 即将过期的优惠券
  expiringSoonCoupons: IUserCoupon[]

  // 加载状态
  loading: {
    myCoupons: boolean
    centerCoupons: boolean
    availableCoupons: boolean
    claiming: boolean
  }

  // 分页信息
  pagination: {
    myCoupons: {
      page: number
      pageSize: number
      total: number
      hasMore: boolean
    }
    centerCoupons: {
      page: number
      pageSize: number
      total: number
      hasMore: boolean
    }
  }
}

export const useCouponStore = defineStore('coupon', {
  state: (): CouponState => ({
    myCoupons: [],
    centerCoupons: [],
    availableCoupons: {},
    unavailableCoupons: {},
    selectedCoupons: {},
    expiringSoonCoupons: [],

    loading: {
      myCoupons: false,
      centerCoupons: false,
      availableCoupons: false,
      claiming: false,
    },
    pagination: {
      myCoupons: {
        page: 1,
        pageSize: 20,
        total: 0,
        hasMore: true,
      },
      centerCoupons: {
        page: 1,
        pageSize: 20,
        total: 0,
        hasMore: true,
      },
    },
  }),

  getters: {
    // 未使用的优惠券（需要检查实际过期时间）
    unusedCoupons: (state) => {
      const now = new Date()
      return (
        state.myCoupons?.filter((coupon) => {
          // 如果后端状态已经是已使用或已过期，直接返回false
          if (coupon.status === CouponStatus.USED || coupon.status === CouponStatus.EXPIRED) {
            return false
          }

          // 检查实际过期时间
          const expireTime = coupon.expire_time || coupon.coupon?.end_time
          if (expireTime) {
            const expireDate = new Date(expireTime)
            if (now > expireDate) {
              // 实际已过期，更新本地状态
              coupon.status = CouponStatus.EXPIRED
              coupon.status_text = '已过期'
              return false
            }
          }

          return coupon.status === CouponStatus.UNUSED
        }) || []
      )
    },

    // 已使用的优惠券
    usedCoupons: (state) => {
      return state.myCoupons?.filter((coupon) => coupon.status === CouponStatus.USED) || []
    },

    // 已过期的优惠券（包含实际过期的）
    expiredCoupons: (state) => {
      const now = new Date()
      return (
        state.myCoupons?.filter((coupon) => {
          // 后端状态已经是已过期
          if (coupon.status === CouponStatus.EXPIRED) {
            return true
          }

          // 检查实际过期时间
          const expireTime = coupon.expire_time || coupon.coupon?.end_time
          if (expireTime) {
            const expireDate = new Date(expireTime)
            if (now > expireDate) {
              // 实际已过期，更新本地状态
              coupon.status = CouponStatus.EXPIRED
              coupon.status_text = '已过期'
              return true
            }
          }

          return false
        }) || []
      )
    },

    // 按类型分组的优惠券
    couponsByType: (state) => {
      const groups: Record<CouponType, IUserCoupon[]> = {
        [CouponType.DISCOUNT]: [],
        [CouponType.PERCENTAGE]: [],
        [CouponType.FREE_DELIVERY]: [],
        [CouponType.GIFT]: [],
        [CouponType.CASHBACK]: [],
      }

      state.myCoupons?.forEach((coupon) => {
        if (groups[coupon.coupon.type]) {
          groups[coupon.coupon.type].push(coupon)
        }
      })

      return groups
    },

    // 按商家分组的优惠券
    couponsByMerchant: (state) => {
      const groups: Record<number, IUserCoupon[]> = {}

      state.myCoupons?.forEach((coupon) => {
        const merchantId = coupon.coupon.merchant_id || 0
        if (!groups[merchantId]) {
          groups[merchantId] = []
        }
        groups[merchantId].push(coupon)
      })

      return groups
    },

    // 获取指定商家的选中优惠券
    getSelectedCouponForMerchant: (state) => {
      return (merchantId: number) => state.selectedCoupons[merchantId] || null
    },

    // 获取指定商家的可用优惠券
    getAvailableCouponsForMerchant: (state) => {
      return (merchantId: number) => state.availableCoupons[merchantId] || []
    },

    // 获取指定商家的不可用优惠券
    getUnavailableCouponsForMerchant: (state) => {
      return (merchantId: number) => state.unavailableCoupons[merchantId] || []
    },

    // 计算总优惠金额
    totalDiscountAmount: (state) => {
      return Object.values(state.selectedCoupons).reduce((total, coupon) => {
        return total + (coupon?.discount_amount || 0)
      }, 0)
    },

    // 优惠券统计信息
    couponStats: (state) => {
      const unused = state.myCoupons?.filter((c) => c.status === CouponStatus.UNUSED).length || 0
      const used = state.myCoupons?.filter((c) => c.status === CouponStatus.USED).length || 0
      const expired = state.myCoupons?.filter((c) => c.status === CouponStatus.EXPIRED).length || 0
      const expiringSoon = state.expiringSoonCoupons?.length || 0

      // 计算已节省金额（已使用的优惠券金额总和）
      const totalSaved =
        state.myCoupons
          ?.filter((c) => c.status === CouponStatus.USED)
          .reduce((total, coupon) => {
            const amount = coupon.coupon?.amount || coupon.discount_amount || 0
            return total + parseFloat(amount.toString())
          }, 0) || 0

      return {
        unused_coupons: unused,
        used_coupons: used,
        expired_coupons: expired,
        expiring_soon: expiringSoon,
        total_saved: Math.round(totalSaved * 100) / 100, // 保留两位小数
      }
    },

    // 新领取的优惠券数量
    newCouponsCount: (state) => {
      return state.myCoupons?.filter((coupon) => coupon.is_new && !coupon.is_read).length || 0
    },
  },

  actions: {
    /**
     * 加载我的优惠券列表
     */
    async fetchMyCoupons(
      params: {
        status?: CouponStatus
        merchant_id?: number
        refresh?: boolean
      } = {},
    ) {
      const { refresh = false } = params

      if (refresh) {
        this.pagination.myCoupons.page = 1
        this.myCoupons = []
      }

      this.loading.myCoupons = true

      try {
        const response = await getMyCoupons({
          ...params,
          page: this.pagination.myCoupons.page,
          page_size: this.pagination.myCoupons.pageSize,
        })

        // 检查响应数据是否有效
        if (!response || !response.data) {
          console.warn('优惠券API返回空数据，可能是token过期或其他错误')
          if (refresh) {
            this.myCoupons = []
          }
          return
        }

        // 适配API返回的数据结构
        const { list = [], total = 0 } = response.data || {}
        const has_more =
          this.pagination.myCoupons.page * this.pagination.myCoupons.pageSize < (total || 0)

        if (refresh) {
          this.myCoupons = list || []
        } else {
          this.myCoupons.push(...(list || []))
        }

        this.pagination.myCoupons.total = total || 0
        this.pagination.myCoupons.hasMore = has_more
        this.pagination.myCoupons.page += 1

        console.log('✅ 优惠券列表加载成功:', (list || []).length)
      } catch (error) {
        console.error('❌ 加载优惠券列表失败:', error)
        // 在API不可用时，设置空数组避免undefined错误
        if (refresh) {
          this.myCoupons = []
        }

        throw error
      } finally {
        this.loading.myCoupons = false
      }
    },

    /**
     * 加载优惠券中心
     */
    async fetchCouponCenter(
      params: {
        category?: string
        refresh?: boolean
      } = {},
    ) {
      const { refresh = false } = params

      if (refresh) {
        this.pagination.centerCoupons.page = 1
        this.centerCoupons = []
      }

      this.loading.centerCoupons = true

      try {
        const response = await getCouponCenter({
          category: params.category || 'all',
          page: this.pagination.centerCoupons.page,
          page_size: this.pagination.centerCoupons.pageSize,
        })

        // 适配API返回的数据结构
        const { list: coupons, total } = response.data
        const has_more =
          this.pagination.centerCoupons.page * this.pagination.centerCoupons.pageSize < (total || 0)

        if (refresh) {
          this.centerCoupons = coupons || []
        } else {
          this.centerCoupons.push(...(coupons || []))
        }

        this.pagination.centerCoupons.total = total || 0
        this.pagination.centerCoupons.hasMore = has_more
        this.pagination.centerCoupons.page += 1

        console.log('✅ 优惠券中心加载成功:', (coupons || []).length)
      } catch (error) {
        console.error('❌ 加载优惠券中心失败:', error)
        // 在API不可用时，设置空数组避免undefined错误
        if (refresh) {
          this.centerCoupons = []
        }
        throw error
      } finally {
        this.loading.centerCoupons = false
      }
    },

    /**
     * 加载订单可用优惠券
     */
    async fetchAvailableCouponsForOrder(params: {
      merchant_id: number
      total_amount: number
      food_ids?: string
    }) {
      this.loading.availableCoupons = true

      try {
        console.log('🎫 开始加载订单可用优惠券:', params)

        // 首先检查是否已有我的优惠券数据，如果没有则先加载
        if (this.myCoupons.length === 0) {
          console.log('🎫 先加载我的优惠券数据')
          await this.fetchMyCoupons({ refresh: false })
        }

        const response = await getAvailableCouponsForOrder(params)
        console.log('🎫 API响应:', response)

        // 根据后端实际返回的数据结构处理
        if (response.data) {
          const { available_coupons = [], unavailable_coupons = [] } = response.data

          // 使用我的优惠券数据来补充完整信息
          const availableData = this.enrichCouponDataFromMyList(available_coupons, true)
          const unavailableData = this.enrichCouponDataFromMyList(unavailable_coupons, false)

          // 按商家ID存储数据
          this.availableCoupons[params.merchant_id] = availableData
          this.unavailableCoupons[params.merchant_id] = unavailableData

          console.log('✅ 可用优惠券加载成功:', {
            merchantId: params.merchant_id,
            available: availableData.length,
            unavailable: unavailableData.length,
            availableData,
            unavailableData,
          })
        } else {
          console.warn('⚠️ API返回数据为空')
          this.availableCoupons[params.merchant_id] = []
          this.unavailableCoupons[params.merchant_id] = []
        }
      } catch (error) {
        console.error('❌ 加载可用优惠券失败:', error)
        this.availableCoupons[params.merchant_id] = []
        this.unavailableCoupons[params.merchant_id] = []
        throw error
      } finally {
        this.loading.availableCoupons = false
      }
    },

    /**
     * 使用我的优惠券数据来补充available-for-order API返回的简化数据
     */
    enrichCouponDataFromMyList(simplifiedCoupons: any[], canUse: boolean): IUserCoupon[] {
      if (!Array.isArray(simplifiedCoupons)) {
        console.warn('⚠️ 优惠券数据不是数组格式:', simplifiedCoupons)
        return []
      }

      return simplifiedCoupons.map((item: any) => {
        // 根据coupon_id或id在我的优惠券列表中查找完整数据
        const fullCouponData = this.myCoupons.find(
          (myCoupon) =>
            myCoupon.coupon_id === (item.coupon_id || item.id) || myCoupon.id === item.id,
        )

        console.log('🔍 查找完整优惠券数据:', {
          searchId: item.coupon_id || item.id,
          found: !!fullCouponData,
          fullData: fullCouponData,
        })

        if (fullCouponData) {
          // 使用完整数据，但更新可用性信息
          return {
            ...fullCouponData,
            can_use: canUse,
            reason: item.reason || '',
            discount_amount: item.discount_amount || item.amount || fullCouponData.coupon.amount,
          }
        }

        // 如果找不到完整数据，使用简化数据创建
        return {
          id: item.id || 0,
          user_id: 0,
          coupon_id: item.coupon_id || item.id,
          coupon: {
            id: item.coupon_id || item.id,
            name: item.name || '优惠券',
            description: item.description || '',
            type: CouponType.DISCOUNT,
            amount: item.amount || 0,
            min_order_amount: item.min_order_amount || 0,
            max_discount_amount: 0,
            merchant_id: 0,
            merchant_name: '',
            start_time: '',
            end_time: '',
            total_quantity: 0,
            claimed_quantity: 0,
            per_user_limit: 1,
            is_active: true,
          },
          status: CouponStatus.UNUSED,
          claimed_at: '',
          used_at: undefined,
          expire_time: '',
          order_id: undefined,
          can_use: canUse,
          reason: item.reason || '',
          discount_amount: item.discount_amount || item.amount || 0,
        } as IUserCoupon
      })
    },

    /**
     * 转换优惠券数据格式
     */
    transformCouponData(coupons: any[]): IUserCoupon[] {
      if (!Array.isArray(coupons)) {
        console.warn('⚠️ 优惠券数据不是数组格式:', coupons)
        return []
      }

      return coupons.map((item: any) => {
        // 如果已经是标准格式，直接返回
        if (item.coupon && typeof item.coupon === 'object') {
          return item as IUserCoupon
        }

        // 如果是后端返回的简化格式，需要转换
        return {
          id: item.id || 0,
          user_id: 0, // 用户ID在这里不重要
          coupon_id: item.coupon_id || item.id,
          coupon: {
            id: item.coupon_id || item.id,
            name: item.name || '优惠券',
            description: item.description || '',
            type: item.type || CouponType.DISCOUNT,
            amount: item.amount || 0,
            min_order_amount: item.min_order_amount || 0,
            max_discount_amount: item.max_discount_amount,
            merchant_id: item.merchant_id,
            merchant_name: item.merchant_name,
            start_time: item.start_time || '',
            end_time: item.end_time || '',
            total_quantity: item.total_quantity || 0,
            claimed_quantity: item.claimed_quantity || 0,
            per_user_limit: item.per_user_limit || 1,
            is_active: item.is_active !== false,
          },
          status: item.status || CouponStatus.UNUSED,
          claimed_at: item.claimed_at || '',
          used_at: item.used_at,
          expire_time: item.expire_time || item.end_time || '',
          order_id: item.order_id,
          can_use: item.can_use !== false,
          reason: item.reason || '',
          discount_amount: item.discount_amount || item.amount || 0,
        } as IUserCoupon
      })
    },

    /**
     * 领取优惠券
     */
    async claimCoupon(couponId: number) {
      this.loading.claiming = true

      try {
        const response = await claimCoupon({ coupon_id: couponId })

        // 检查响应状态码，后端返回的是标准格式 { code, message, data }
        if (response.code === 0 || response.code === 200) {
          uni.showToast({
            title: '领取成功',
            icon: 'success',
          })

          // 刷新我的优惠券列表
          await this.fetchMyCoupons({ refresh: true })

          // 返回领取到的优惠券数据
          return response.data
        } else {
          throw new Error(response.message || '领取失败')
        }
      } catch (error: any) {
        console.error('❌ 领取优惠券失败:', error)
        uni.showToast({
          title: error.message || '领取失败',
          icon: 'error',
        })
        throw error
      } finally {
        this.loading.claiming = false
      }
    },

    /**
     * 批量领取优惠券
     */
    async batchClaimCoupons(couponIds: number[]) {
      this.loading.claiming = true

      try {
        const response = await batchClaimCoupons({ coupon_ids: couponIds })

        // 检查响应状态码，后端返回的是标准格式 { code, message, data }
        if (response.code === 0 || response.code === 200) {
          // 根据实际返回的数据结构调整
          const claimedCount = Array.isArray(response.data)
            ? response.data.length
            : couponIds.length
          uni.showToast({
            title: `成功领取${claimedCount}张优惠券`,
            icon: 'success',
          })

          // 刷新我的优惠券列表
          await this.fetchMyCoupons({ refresh: true })
        } else {
          throw new Error(response.message || '批量领取失败')
        }
      } catch (error: any) {
        console.error('❌ 批量领取优惠券失败:', error)
        uni.showToast({
          title: error.message || '领取失败',
          icon: 'error',
        })
        throw error
      } finally {
        this.loading.claiming = false
      }
    },

    /**
     * 选择优惠券
     */
    selectCouponForMerchant(merchantId: number, coupon: IUserCoupon | null) {
      this.selectedCoupons[merchantId] = coupon
      console.log('✅ 选择优惠券:', { merchantId, coupon: coupon?.coupon.name })
    },

    /**
     * 清除选中的优惠券
     */
    clearSelectedCoupon(merchantId: number) {
      delete this.selectedCoupons[merchantId]
    },

    /**
     * 清除所有选中的优惠券
     */
    clearAllSelectedCoupons() {
      this.selectedCoupons = {}
    },

    /**
     * 加载即将过期的优惠券
     */
    async fetchExpiringSoonCoupons(days: number = 7) {
      try {
        const response = await getExpiringSoonCoupons({ days })
        // 适配API返回的数据结构，使用coupons字段匹配ICouponListResponse接口
        this.expiringSoonCoupons = response.data.coupons || []

        console.log('✅ 即将过期优惠券加载成功:', this.expiringSoonCoupons.length)
      } catch (error) {
        console.error('❌ 加载即将过期优惠券失败:', error)
        this.expiringSoonCoupons = []
      }
    },

    /**
     * 重置状态
     */
    resetState() {
      this.myCoupons = []
      this.centerCoupons = []
      this.availableCoupons = []
      this.unavailableCoupons = []
      this.selectedCoupons = {}
      this.expiringSoonCoupons = []

      this.pagination.myCoupons.page = 1
      this.pagination.centerCoupons.page = 1
    },
  },
})
