/**
 * 消息系统状态管理
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import {
  getMessageCategories,
  getMessageList,
  getSystemNotifications,
  getOrderNotifications,
  getServiceMessages,
  getChatSessions,
  getOrderSessions,
  getServiceSessions,
  getUnreadCount,
  markMessagesAsRead,
  searchMessages,
  markSystemNotificationAsRead,
  markOrderNotificationAsRead,
} from '@/api/message'
import type {
  IMessageCategory,
  IMessageItem,
  ISystemNotification,
  IOrderNotification,
  IServiceMessage,
  ISessionItem,
  IUnreadCount,
  MessageCategoryType,
  IMessageListParams,
  ISystemNotificationParams,
  IOrderNotificationParams,
  IServiceMessageParams,
  ISessionListParams,
} from '@/api/message.typings'

export const useMessageStore = defineStore(
  'message',
  () => {
    // ==================== 状态 ====================

    // 消息分类列表
    const messageCategories = ref<IMessageCategory[]>([])

    // 未读消息统计
    const unreadCount = ref<IUnreadCount>({
      chat: 0,
      system: 0,
      order: 0,
      service: 0,
      total: 0,
    })

    // 各类消息列表
    const systemNotifications = ref<ISystemNotification[]>([])
    const orderNotifications = ref<IOrderNotification[]>([])
    const serviceMessages = ref<IServiceMessage[]>([])
    const recentMessages = ref<IMessageItem[]>([])

    // 会话列表
    const chatSessions = ref<ISessionItem[]>([])
    const orderSessions = ref<ISessionItem[]>([])
    const serviceSessions = ref<ISessionItem[]>([]) // 客服会话列表

    // 加载状态
    const loading = ref({
      categories: false,
      unreadCount: false,
      systemNotifications: false,
      orderNotifications: false,
      serviceMessages: false,
      recentMessages: false,
      chatSessions: false,
      orderSessions: false,
      serviceSessions: false,
    })

    // 分页信息
    const pagination = ref({
      systemNotifications: { page: 1, hasMore: true },
      orderNotifications: { page: 1, hasMore: true },
      serviceMessages: { page: 1, hasMore: true },
      recentMessages: { page: 1, hasMore: true },
      chatSessions: { page: 1, hasMore: true },
      orderSessions: { page: 1, hasMore: true },
      serviceSessions: { page: 1, hasMore: true },
    })

    // ==================== 计算属性 ====================

    // 总未读消息数
    const totalUnreadCount = computed(() => unreadCount.value.total)

    // 各分类未读数
    const chatUnreadCount = computed(() => unreadCount.value.chat)
    const systemUnreadCount = computed(() => unreadCount.value.system)
    const orderUnreadCount = computed(() => unreadCount.value.order)
    const serviceUnreadCount = computed(() => unreadCount.value.service)

    // ==================== 方法 ====================

    /**
     * 获取消息分类列表
     */
    const fetchMessageCategories = async () => {
      try {
        loading.value.categories = true
        const response = await getMessageCategories()

        // 处理字段名差异，将 unread_count 转换为 unreadCount，并修正路径
        const pathMapping = {
          chat: '/pages/message/chat',
          system: '/pages/message/system',
          order: '/pages/message/order-sessions',
          service: '/pages/message/service',
        }

        messageCategories.value = response.data.map((category) => ({
          ...category,
          unreadCount: (category as any).unread_count || category.unreadCount || 0,
          path: pathMapping[category.type] || category.path,
        }))

        return response
      } catch (error) {
        console.error('获取消息分类失败:', error)
        // 使用默认分类
        messageCategories.value = [
          {
            type: 'chat',
            title: '聊天消息',
            icon: 'chat',
            color: '#4D8EFF',
            unreadCount: 0,
            path: '/pages/message/chat',
          },
          {
            type: 'system',
            title: '系统通知',
            icon: 'notification',
            color: '#FF9500',
            unreadCount: 0,
            path: '/pages/message/system',
          },
          {
            type: 'order',
            title: '订单消息',
            icon: 'goods',
            color: '#34C759',
            unreadCount: 0,
            path: '/pages/message/order-sessions',
          },
          {
            type: 'service',
            title: '客服消息',
            icon: 'service',
            color: '#FF3B30',
            unreadCount: 0,
            path: '/pages/message/service',
          },
        ]
        throw error
      } finally {
        loading.value.categories = false
      }
    }

    /**
     * 获取未读消息统计
     */
    const fetchUnreadCount = async () => {
      try {
        loading.value.unreadCount = true
        const response = await getUnreadCount()
        unreadCount.value = response.data

        // 同步更新消息分类的未读数
        messageCategories.value.forEach((category) => {
          category.unreadCount = unreadCount.value[category.type] || 0
        })

        return response
      } catch (error) {
        console.error('获取未读消息统计失败:', error)
        throw error
      } finally {
        loading.value.unreadCount = false
      }
    }

    /**
     * 获取系统通知列表
     */
    const fetchSystemNotifications = async (
      params?: ISystemNotificationParams,
      refresh = false,
    ) => {
      try {
        loading.value.systemNotifications = true

        if (refresh) {
          pagination.value.systemNotifications.page = 1
          systemNotifications.value = []
        }

        const requestParams = {
          page: pagination.value.systemNotifications.page,
          pageSize: 20,
          ...params,
        }

        const response = await getSystemNotifications(requestParams)

        if (refresh) {
          systemNotifications.value = response.data.list
        } else {
          systemNotifications.value.push(...response.data.list)
        }

        pagination.value.systemNotifications.hasMore = response.data.hasMore

        return response
      } catch (error) {
        console.error('获取系统通知失败:', error)
        throw error
      } finally {
        loading.value.systemNotifications = false
      }
    }

    /**
     * 获取订单通知列表
     */
    const fetchOrderNotifications = async (params?: IOrderNotificationParams, refresh = false) => {
      try {
        loading.value.orderNotifications = true

        if (refresh) {
          pagination.value.orderNotifications.page = 1
          orderNotifications.value = []
        }

        const requestParams = {
          page: pagination.value.orderNotifications.page,
          pageSize: 20,
          ...params,
        }

        const response = await getOrderNotifications(requestParams)

        if (refresh) {
          orderNotifications.value = response.data.list
        } else {
          orderNotifications.value.push(...response.data.list)
        }

        pagination.value.orderNotifications.hasMore = response.data.hasMore

        return response
      } catch (error) {
        console.error('获取订单通知失败:', error)
        throw error
      } finally {
        loading.value.orderNotifications = false
      }
    }

    /**
     * 获取客服消息列表
     */
    const fetchServiceMessages = async (params?: IServiceMessageParams, refresh = false) => {
      try {
        loading.value.serviceMessages = true

        if (refresh) {
          pagination.value.serviceMessages.page = 1
          serviceMessages.value = []
        }

        const requestParams = {
          page: pagination.value.serviceMessages.page,
          pageSize: 20,
          ...params,
        }

        const response = await getServiceMessages(requestParams)

        if (refresh) {
          serviceMessages.value = response.data.list
        } else {
          serviceMessages.value.push(...response.data.list)
        }

        pagination.value.serviceMessages.hasMore = response.data.hasMore

        return response
      } catch (error) {
        console.error('获取客服消息失败:', error)
        throw error
      } finally {
        loading.value.serviceMessages = false
      }
    }

    /**
     * 获取聊天会话列表
     */
    const fetchChatSessions = async (params?: ISessionListParams, refresh = false) => {
      try {
        console.log('fetchChatSessions 开始执行，params:', params, 'refresh:', refresh)
        loading.value.chatSessions = true

        if (refresh) {
          pagination.value.chatSessions.page = 1
          chatSessions.value = []
        }

        const requestParams = {
          page: pagination.value.chatSessions.page,
          pageSize: 20,
          ...params,
        }

        console.log('准备调用 getChatSessions API，请求参数:', requestParams)
        const response = await getChatSessions(requestParams)
        console.log('getChatSessions API 调用成功，响应:', response)

        if (refresh) {
          chatSessions.value = response.data.list
        } else {
          chatSessions.value.push(...response.data.list)
        }

        pagination.value.chatSessions.hasMore = response.data.page < response.data.page_count

        return response
      } catch (error) {
        console.error('获取聊天会话失败:', error)
        throw error
      } finally {
        loading.value.chatSessions = false
      }
    }

    /**
     * 获取订单会话列表
     */
    const fetchOrderSessions = async (params?: ISessionListParams, refresh = false) => {
      try {
        loading.value.orderSessions = true

        if (refresh) {
          pagination.value.orderSessions.page = 1
          orderSessions.value = []
        }

        const requestParams = {
          page: pagination.value.orderSessions.page,
          pageSize: 20,
          ...params,
        }

        const response = await getOrderSessions(requestParams)

        if (refresh) {
          orderSessions.value = response.data.list
        } else {
          orderSessions.value.push(...response.data.list)
        }

        pagination.value.orderSessions.hasMore = response.data.page < response.data.page_count

        return response
      } catch (error) {
        console.error('获取订单会话失败:', error)
        throw error
      } finally {
        loading.value.orderSessions = false
      }
    }

    /**
     * 获取客服会话列表
     */
    const fetchServiceSessions = async (params?: ISessionListParams, refresh = false) => {
      try {
        loading.value.serviceSessions = true

        if (refresh) {
          pagination.value.serviceSessions.page = 1
          serviceSessions.value = []
        }

        const requestParams = {
          page: pagination.value.serviceSessions.page,
          pageSize: 20,
          ...params,
        }

        const response: any = await getServiceSessions(requestParams)

        if (refresh) {
          serviceSessions.value = response.data.list
        } else {
          serviceSessions.value.push(...response.data.list)
        }

        pagination.value.serviceSessions.hasMore = response.data.page < response.data.page_count

        return response
      } catch (error) {
        console.error('获取客服会话失败:', error)
        throw error
      } finally {
        loading.value.serviceSessions = false
      }
    }

    /**
     * 搜索消息
     */
    const searchAllMessages = async (keyword: string) => {
      try {
        loading.value.recentMessages = true
        const response = await searchMessages({
          keyword,
          page: 1,
          pageSize: 20,
        })

        recentMessages.value = response.data.list
        return response
      } catch (error) {
        console.error('搜索消息失败:', error)
        throw error
      } finally {
        loading.value.recentMessages = false
      }
    }

    /**
     * 标记系统通知为已读
     */
    const markSystemNotificationRead = async (notificationId: string) => {
      try {
        await markSystemNotificationAsRead(notificationId)

        // 更新本地状态
        const notification = systemNotifications.value.find((n) => n.id === notificationId)
        if (notification) {
          notification.isRead = true
        }

        // 更新未读统计
        if (unreadCount.value.system > 0) {
          unreadCount.value.system--
          unreadCount.value.total--
        }

        // 更新分类未读数
        const systemCategory = messageCategories.value.find((c) => c.type === 'system')
        if (systemCategory && systemCategory.unreadCount > 0) {
          systemCategory.unreadCount--
        }
      } catch (error) {
        console.error('标记系统通知已读失败:', error)
        throw error
      }
    }

    /**
     * 标记订单通知为已读
     */
    const markOrderNotificationRead = async (notificationId: string) => {
      try {
        await markOrderNotificationAsRead(notificationId)

        // 更新本地状态
        const notification = orderNotifications.value.find((n) => n.id === notificationId)
        if (notification) {
          notification.isRead = true
        }

        // 更新未读统计
        if (unreadCount.value.order > 0) {
          unreadCount.value.order--
          unreadCount.value.total--
        }

        // 更新分类未读数
        const orderCategory = messageCategories.value.find((c) => c.type === 'order')
        if (orderCategory && orderCategory.unreadCount > 0) {
          orderCategory.unreadCount--
        }
      } catch (error) {
        console.error('标记订单通知已读失败:', error)
        throw error
      }
    }

    /**
     * 加载更多系统通知
     */
    const loadMoreSystemNotifications = async () => {
      if (!pagination.value.systemNotifications.hasMore || loading.value.systemNotifications) {
        return
      }

      pagination.value.systemNotifications.page++
      await fetchSystemNotifications()
    }

    /**
     * 加载更多订单通知
     */
    const loadMoreOrderNotifications = async () => {
      if (!pagination.value.orderNotifications.hasMore || loading.value.orderNotifications) {
        return
      }

      pagination.value.orderNotifications.page++
      await fetchOrderNotifications()
    }

    /**
     * 加载更多客服消息
     */
    const loadMoreServiceMessages = async () => {
      if (!pagination.value.serviceMessages.hasMore || loading.value.serviceMessages) {
        return
      }

      pagination.value.serviceMessages.page++
      await fetchServiceMessages()
    }

    /**
     * 加载更多聊天会话
     */
    const loadMoreChatSessions = async () => {
      if (!pagination.value.chatSessions.hasMore || loading.value.chatSessions) {
        return
      }

      pagination.value.chatSessions.page++
      await fetchChatSessions()
    }

    /**
     * 加载更多订单会话
     */
    const loadMoreOrderSessions = async () => {
      if (!pagination.value.orderSessions.hasMore || loading.value.orderSessions) {
        return
      }

      pagination.value.orderSessions.page++
      await fetchOrderSessions()
    }

    /**
     * 加载更多客服会话
     */
    const loadMoreServiceSessions = async () => {
      if (!pagination.value.serviceSessions.hasMore || loading.value.serviceSessions) {
        return
      }

      pagination.value.serviceSessions.page++
      await fetchServiceSessions()
    }

    /**
     * 初始化消息数据
     */
    const initMessageData = async () => {
      try {
        await Promise.all([fetchMessageCategories(), fetchUnreadCount()])
      } catch (error) {
        console.error('初始化消息数据失败:', error)
      }
    }

    /**
     * 清空消息数据
     */
    const clearMessageData = () => {
      messageCategories.value = []
      systemNotifications.value = []
      orderNotifications.value = []
      serviceMessages.value = []
      recentMessages.value = []
      chatSessions.value = []
      orderSessions.value = []
      serviceSessions.value = []
      unreadCount.value = {
        chat: 0,
        system: 0,
        order: 0,
        service: 0,
        total: 0,
      }

      // 重置分页信息
      Object.keys(pagination.value).forEach((key) => {
        pagination.value[key as keyof typeof pagination.value] = { page: 1, hasMore: true }
      })
    }

    return {
      // 状态
      messageCategories,
      unreadCount,
      systemNotifications,
      orderNotifications,
      serviceMessages,
      recentMessages,
      chatSessions,
      orderSessions,
      serviceSessions,
      loading,
      pagination,

      // 计算属性
      totalUnreadCount,
      chatUnreadCount,
      systemUnreadCount,
      orderUnreadCount,
      serviceUnreadCount,

      // 方法
      fetchMessageCategories,
      fetchUnreadCount,
      fetchSystemNotifications,
      fetchOrderNotifications,
      fetchServiceMessages,
      fetchChatSessions,
      fetchOrderSessions,
      fetchServiceSessions,
      searchAllMessages,
      markSystemNotificationRead,
      markOrderNotificationRead,
      loadMoreSystemNotifications,
      loadMoreOrderNotifications,
      loadMoreServiceMessages,
      loadMoreChatSessions,
      loadMoreOrderSessions,
      loadMoreServiceSessions,
      initMessageData,
      clearMessageData,
    }
  },
  {
    persist: {
      key: 'message-store',
      storage: {
        getItem: uni.getStorageSync,
        setItem: uni.setStorageSync,
      },
      // 只持久化部分数据
      paths: ['unreadCount'],
    },
  },
)
