/**
 * 订单状态管理模块
 * 管理订单的创建、查询、状态更新等
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import {
  createOrder,
  getOrderList,
  getOrderDetail,
  cancelOrder,
  confirmOrder,
  applyRefund,
  deleteOrder,
  reorder,
  getOrderStatusCount,
} from '@/api/order'
import type {
  IOrder,
  IOrderDetail,
  IOrderList,
  ICreateOrderParams,
  IOrderSearchParams,
} from '@/api/order.typings'
import { toast } from '@/utils/toast'

export const useOrderStore = defineStore(
  'order',
  () => {
    // 订单列表
    const orderList = ref<IOrder[]>([])
    // 订单总数
    const orderTotal = ref(0)
    // 当前页码
    const currentPage = ref(1)
    // 每页数量
    const pageSize = ref(10)
    // 是否正在加载
    const loading = ref(false)
    // 是否有更多数据
    const hasMore = ref(true)
    // 当前订单详情
    const currentOrderDetail = ref<IOrderDetail | null>(null)
    // 订单状态统计
    const orderStatusStats = ref({
      all: 0,
      pending: 0,
      paid: 0,
      shipped: 0,
      delivered: 0,
      completed: 0,
      cancelled: 0,
      refunded: 0,
    })
    // 搜索参数
    const searchParams = ref<IOrderSearchParams>({
      status: undefined,
      keyword: '',
      start_time: '',
      end_time: '',
      page: 1,
      page_size: 10,
    })

    // 计算属性：是否为空列表
    const isEmpty = computed(() => orderList.value.length === 0)

    // 计算属性：是否为第一页
    const isFirstPage = computed(() => currentPage.value === 1)

    // 计算属性：是否可以加载更多
    const canLoadMore = computed(() => hasMore.value && !loading.value)

    /**
     * 获取订单列表
     * @param params 搜索参数
     * @param loadMore 是否为加载更多
     */
    const fetchOrderList = async (params?: IOrderSearchParams, loadMore = false) => {
      try {
        loading.value = true

        // 如果不是加载更多，重置页码
        if (!loadMore) {
          currentPage.value = 1
          orderList.value = []
        }

        // 合并搜索参数，处理新旧参数兼容
        const searchData: IOrderSearchParams = {
          ...searchParams.value,
          ...params,
          page: currentPage.value,
          page_size: pageSize.value,
          // 兼容处理：如果使用了旧的参数名，转换为新的参数名
          start_time: params?.start_time || searchParams.value.start_time,
          end_time: params?.end_time || searchParams.value.end_time,
        }

        // 清理undefined值
        Object.keys(searchData).forEach((key) => {
          if (
            searchData[key as keyof IOrderSearchParams] === undefined ||
            searchData[key as keyof IOrderSearchParams] === ''
          ) {
            delete searchData[key as keyof IOrderSearchParams]
          }
        })

        const { data } = await getOrderList(searchData)

        if (loadMore) {
          // 加载更多：追加到现有列表
          orderList.value.push(...data.list)
        } else {
          // 首次加载或刷新：替换列表
          orderList.value = data.list
        }

        orderTotal.value = data.total
        hasMore.value = data.list.length === pageSize.value

        // 更新搜索参数
        if (params) {
          searchParams.value = { ...searchParams.value, ...params }
        }
      } catch (error) {
        console.error('获取订单列表失败:', error)
        toast.error('获取订单列表失败')
      } finally {
        loading.value = false
      }
    }

    /**
     * 加载更多订单
     */
    const loadMoreOrders = async () => {
      if (!canLoadMore.value) return

      currentPage.value++
      await fetchOrderList(undefined, true)
    }

    /**
     * 刷新订单列表
     */
    const refreshOrderList = async () => {
      await fetchOrderList(searchParams.value, false)
    }

    /**
     * 根据状态获取订单列表
     * @param status 订单状态 (10:待付款, 20:已付款, 30:已发货, 40:已收货, 50:已完成, 60:已取消, 70:退款中, 80:已退款)
     */
    const fetchOrdersByStatus = async (status?: number) => {
      await fetchOrderList({ status })
    }

    /**
     * 搜索订单
     * @param keyword 搜索关键词
     */
    const searchOrders = async (keyword: string) => {
      await fetchOrderList({ keyword })
    }

    /**
     * 获取订单详情
     * @param orderId 订单ID
     */
    const fetchOrderDetail = async (orderId: number) => {
      try {
        loading.value = true
        const { data } = await getOrderDetail(orderId)
        currentOrderDetail.value = data
        return data
      } catch (error) {
        console.error('获取订单详情失败:', error)
        toast.error('获取订单详情失败')
        throw error
      } finally {
        loading.value = false
      }
    }

    /**
     * 创建订单
     * @param params 创建订单参数
     */
    const createNewOrder = async (params: ICreateOrderParams) => {
      try {
        loading.value = true
        const { data } = await createOrder(params)
        toast.success('订单创建成功')
        // 刷新订单列表
        await refreshOrderList()
        return data
      } catch (error) {
        console.error('创建订单失败:', error)
        toast.error('创建订单失败')
        throw error
      } finally {
        loading.value = false
      }
    }

    /**
     * 取消订单
     * @param orderId 订单ID
     * @param reason 取消原因
     */
    const cancelOrderById = async (orderId: number, reason?: string) => {
      try {
        await cancelOrder(orderId, reason)
        toast.success('订单已取消')
        // 更新本地订单状态
        updateOrderStatusLocal(orderId, 60) // 60: 已取消
        // 刷新订单列表
        await refreshOrderList()
        // 更新状态统计
        await fetchOrderStatusStats()
      } catch (error) {
        console.error('取消订单失败:', error)
        toast.error('取消订单失败')
      }
    }

    /**
     * 确认收货
     * @param orderId 订单ID
     */
    const confirmReceiveOrder = async (orderId: number) => {
      try {
        await confirmOrder(orderId)
        toast.success('确认收货成功')
        // 更新本地订单状态
        updateOrderStatusLocal(orderId, 50) // 50: 已完成
        // 刷新订单列表
        await refreshOrderList()
        // 更新状态统计
        await fetchOrderStatusStats()
      } catch (error) {
        console.error('确认收货失败:', error)
        toast.error('确认收货失败')
      }
    }

    /**
     * 申请退款
     * @param orderId 订单ID
     * @param reason 退款原因
     * @param amount 退款金额
     */
    const applyOrderRefund = async (orderId: number, reason: string, amount?: number) => {
      try {
        await applyRefund(orderId, reason, amount)
        toast.success('退款申请已提交')
        // 刷新订单列表
        await refreshOrderList()
        // 更新状态统计
        await fetchOrderStatusStats()
      } catch (error) {
        console.error('申请退款失败:', error)
        toast.error('申请退款失败')
      }
    }

    /**
     * 删除订单
     * @param orderId 订单ID
     */
    const deleteOrderById = async (orderId: number) => {
      try {
        await deleteOrder(orderId)
        toast.success('订单已删除')
        // 从本地列表中移除
        removeOrderFromLocal(orderId)
        // 更新状态统计
        await fetchOrderStatusCount()
      } catch (error) {
        console.error('删除订单失败:', error)
        toast.error('删除订单失败')
      }
    }

    /**
     * 再次购买
     * @param orderId 订单ID
     */
    const buyAgain = async (orderId: number) => {
      try {
        const { data } = await reorder(orderId)
        toast.success('商品已添加到购物车')
        return data
      } catch (error) {
        console.error('再次购买失败:', error)
        toast.error('再次购买失败')
        throw error
      }
    }

    /**
     * 获取订单状态统计
     */
    const fetchOrderStatusStats = async () => {
      try {
        const { data } = await getOrderStatusCount()
        orderStatusStats.value = data
      } catch (error) {
        console.error('获取订单状态统计失败:', error)
      }
    }

    /**
     * 本地更新订单状态
     * @param orderId 订单ID
     * @param status 新状态
     */
    const updateOrderStatusLocal = (orderId: number, status: number) => {
      const order = orderList.value.find((item) => item.id === orderId)
      if (order) {
        order.status = status
      }

      // 同时更新订单详情
      if (currentOrderDetail.value && currentOrderDetail.value.id === orderId) {
        currentOrderDetail.value.status = status
      }
    }

    /**
     * 从本地列表中移除订单
     * @param orderId 订单ID
     */
    const removeOrderFromLocal = (orderId: number) => {
      const index = orderList.value.findIndex((item) => item.id === orderId)
      if (index > -1) {
        orderList.value.splice(index, 1)
        orderTotal.value--
      }
    }

    /**
     * 重置订单列表
     */
    const resetOrderList = () => {
      orderList.value = []
      orderTotal.value = 0
      currentPage.value = 1
      hasMore.value = true
      searchParams.value = {
        status: undefined,
        keyword: '',
        start_time: '',
        end_time: '',
        page: 1,
        page_size: 10,
      }
    }

    /**
     * 清除当前订单详情
     */
    const clearCurrentOrderDetail = () => {
      currentOrderDetail.value = null
    }

    return {
      // 状态
      orderList,
      orderTotal,
      currentPage,
      pageSize,
      loading,
      hasMore,
      currentOrderDetail,
      orderStatusStats,
      searchParams,

      // 计算属性
      isEmpty,
      isFirstPage,
      canLoadMore,

      // 方法
      fetchOrderList,
      loadMoreOrders,
      refreshOrderList,
      fetchOrdersByStatus,
      searchOrders,
      fetchOrderDetail,
      createNewOrder,
      cancelOrderById,
      confirmReceiveOrder,
      applyOrderRefund,
      deleteOrder: deleteOrderById,
      buyAgain,
      fetchOrderStatusStats,
      cancelOrder: cancelOrderById,
      updateOrderStatusLocal,
      removeOrderFromLocal,
      resetOrderList,
      clearCurrentOrderDetail,
    }
  },
  {
    persist: {
      paths: ['orderStatusStats'],
    },
  },
)
