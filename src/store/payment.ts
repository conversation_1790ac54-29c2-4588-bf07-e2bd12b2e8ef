/**
 * 支付相关状态管理
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import {
  getPaymentMethods,
  getUserBalance,
  getBalanceRecords,
  getBankCards,
  setDefaultBankCard,
  getPaymentSecuritySettings,
  updatePaymentSecuritySettings,
} from '@/api/payment'
import type {
  IPaymentMethodConfig,
  IUserBalance,
  IBalanceRecord,
  IBankCard,
  IPaymentSecuritySettings,
  IBalanceRecordParams,
} from '@/api/payment.typings'

export const usePaymentStore = defineStore('payment', () => {
  // 支付方式列表
  const paymentMethods = ref<IPaymentMethodConfig[]>([])
  // 用户余额信息
  const balanceInfo = ref<IUserBalance | null>(null)
  // 余额记录
  const balanceRecords = ref<IBalanceRecord[]>([])
  // 余额记录总数
  const balanceRecordsTotal = ref(0)
  // 银行卡列表
  const bankCards = ref<IBankCard[]>([])
  // 支付安全设置
  const securitySettings = ref<IPaymentSecuritySettings | null>(null)
  // 加载状态
  const loading = ref({
    methods: false,
    balance: false,
    records: false,
    cards: false,
    settings: false,
  })

  // 可用的支付方式
  const availablePaymentMethods = computed(() => {
    return paymentMethods.value.filter((method) => method.enabled)
  })

  // 默认银行卡
  const defaultBankCard = computed(() => {
    return bankCards.value.find((card) => card.isDefault) || null
  })

  /**
   * 获取支付方式列表
   */
  const fetchPaymentMethods = async () => {
    try {
      loading.value.methods = true
      const res = await getPaymentMethods()
      // 后端返回的数据结构是 { data: { methods: [...] } }
      const methods = res.data?.methods || res.methods || []
      paymentMethods.value = methods
      return methods
    } catch (error) {
      console.error('获取支付方式失败:', error)
      // 返回默认支付方式
      const defaultMethods = [
        {
          method: 'wechat',
          name: '微信支付',
          icon: '/static/icons/wechat-pay.png',
          enabled: true,
          description: '微信安全支付',
        },
        {
          method: 'alipay',
          name: '支付宝',
          icon: '/static/icons/alipay.png',
          enabled: true,
          description: '支付宝安全支付',
        },
        {
          method: 'balance',
          name: '余额支付',
          icon: '/static/icons/balance.png',
          enabled: true,
          description: '使用账户余额支付',
        },
      ]
      paymentMethods.value = defaultMethods
      return defaultMethods
    } finally {
      loading.value.methods = false
    }
  }

  /**
   * 获取用户余额
   */
  const fetchUserBalance = async () => {
    try {
      loading.value.balance = true
      const res = await getUserBalance()
      // 后端返回的数据结构可能是 { data: {...} } 或直接是 {...}
      const balance = res.data || res
      balanceInfo.value = balance
      return balance
    } catch (error) {
      console.error('获取用户余额失败:', error)
      // 返回默认余额信息
      const defaultBalance = {
        balance: 0,
        frozenBalance: 0,
        availableBalance: 0,
        currency: 'CNY',
      }
      balanceInfo.value = defaultBalance
      return defaultBalance
    } finally {
      loading.value.balance = false
    }
  }

  /**
   * 获取余额记录
   */
  const fetchBalanceRecords = async (params: IBalanceRecordParams = {}) => {
    try {
      loading.value.records = true
      const res = await getBalanceRecords(params)
      balanceRecords.value = res.data.records
      balanceRecordsTotal.value = res.data.total
      return res.data
    } catch (error) {
      console.error('获取余额记录失败:', error)
      return { records: [], total: 0, page: 1, pageSize: 10 }
    } finally {
      loading.value.records = false
    }
  }

  /**
   * 获取银行卡列表
   */
  const fetchBankCards = async () => {
    try {
      loading.value.cards = true
      const res = await getBankCards()
      // 后端返回的数据结构可能是 { data: [...] } 或直接是 [...]
      const cards = res.data || res
      bankCards.value = Array.isArray(cards) ? cards : []
      return bankCards.value
    } catch (error) {
      console.error('获取银行卡列表失败:', error)
      // 返回空数组
      bankCards.value = []
      return []
    } finally {
      loading.value.cards = false
    }
  }

  /**
   * 设置默认银行卡
   */
  const setDefaultCard = async (cardId: string) => {
    try {
      const res = await setDefaultBankCard(cardId)
      if (res.code === 0) {
        // 更新本地银行卡列表的默认状态
        bankCards.value = bankCards.value.map((card) => ({
          ...card,
          isDefault: card.id === cardId,
        }))
      }
      return res
    } catch (error) {
      console.error('设置默认银行卡失败:', error)
      throw error
    }
  }

  /**
   * 获取支付安全设置
   */
  const fetchSecuritySettings = async () => {
    try {
      loading.value.settings = true
      const res = await getPaymentSecuritySettings()
      securitySettings.value = res.data
      return res.data
    } catch (error) {
      console.error('获取支付安全设置失败:', error)
      return null
    } finally {
      loading.value.settings = false
    }
  }

  /**
   * 更新支付安全设置
   */
  const updateSecuritySettings = async (settings: Partial<IPaymentSecuritySettings>) => {
    try {
      const res = await updatePaymentSecuritySettings(settings)
      if (res.code === 0 && securitySettings.value) {
        // 更新本地设置
        securitySettings.value = {
          ...securitySettings.value,
          ...settings,
        }
      }
      return res
    } catch (error) {
      console.error('更新支付安全设置失败:', error)
      throw error
    }
  }

  /**
   * 清空支付相关状态
   */
  const clearPaymentState = () => {
    paymentMethods.value = []
    balanceInfo.value = null
    balanceRecords.value = []
    balanceRecordsTotal.value = 0
    bankCards.value = []
    securitySettings.value = null
  }

  return {
    // 状态
    paymentMethods,
    balanceInfo,
    balanceRecords,
    balanceRecordsTotal,
    bankCards,
    securitySettings,
    loading,
    // 计算属性
    availablePaymentMethods,
    defaultBankCard,
    // 方法
    fetchPaymentMethods,
    fetchUserBalance,
    fetchBalanceRecords,
    fetchBankCards,
    setDefaultCard,
    fetchSecuritySettings,
    updateSecuritySettings,
    clearPaymentState,
  }
})
