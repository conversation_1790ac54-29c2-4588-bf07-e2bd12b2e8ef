/**
 * 商品状态管理模块
 * 管理商品列表、分类、搜索等状态
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import {
  getProductList,
  getProductDetail,
  getCategoryList,
  searchProducts,
  getHotProducts,
  getRecommendProducts,
  getProductsByCategory,
} from '@/api/product'
import type {
  IProduct,
  IProductDetail,
  IProductSearchParams,
  ICategory,
} from '@/api/product.typings'
import { toast } from '@/utils/toast'

export const useProductStore = defineStore(
  'product',
  () => {
    // 商品列表
    const productList = ref<IProduct[]>([])
    // 商品总数
    const productTotal = ref(0)
    // 当前页码
    const currentPage = ref(1)
    // 每页数量
    const pageSize = ref(10)
    // 是否正在加载
    const loading = ref(false)
    // 是否还有更多数据
    const hasMore = ref(true)

    // 商品分类列表
    const categoryList = ref<ICategory[]>([])

    // 当前商品详情
    const currentProduct = ref<IProductDetail | null>(null)

    // 搜索关键词
    const searchKeyword = ref('')

    // 搜索参数
    const searchParams = ref<IProductSearchParams>({
      page: 1,
      pageSize: 10,
    })

    // 热门商品
    const hotProducts = ref<IProduct[]>([])

    // 推荐商品
    const recommendProducts = ref<IProduct[]>([])

    // 计算属性：是否有商品
    const hasProducts = computed(() => productList.value.length > 0)

    // 计算属性：总页数
    const totalPages = computed(() => Math.ceil(productTotal.value / pageSize.value))

    /**
     * 获取商品列表
     * @param params 搜索参数
     * @param append 是否追加到现有列表
     */
    const fetchProductList = async (params?: IProductSearchParams, append = false) => {
      try {
        loading.value = true
        const searchData = { ...searchParams.value, ...params }
        const { data } = await getProductList(searchData)

        if (append) {
          productList.value.push(...data.list)
        } else {
          productList.value = data.list
        }

        productTotal.value = data.total
        currentPage.value = data.page
        hasMore.value = data.page < data.totalPages

        // 更新搜索参数
        searchParams.value = searchData
      } catch (error) {
        console.error('获取商品列表失败:', error)
        toast.error('获取商品列表失败')
      } finally {
        loading.value = false
      }
    }

    /**
     * 加载更多商品
     */
    const loadMoreProducts = async () => {
      if (loading.value || !hasMore.value) return

      const nextPage = currentPage.value + 1
      await fetchProductList({ ...searchParams.value, page: nextPage }, true)
    }

    /**
     * 搜索商品
     * @param keyword 搜索关键词
     * @param params 其他搜索参数
     */
    const searchProductList = async (keyword: string, params?: Partial<IProductSearchParams>) => {
      try {
        loading.value = true
        searchKeyword.value = keyword
        const searchData = { page: 1, pageSize: pageSize.value, keyword, ...params }
        const { data } = await searchProducts(keyword, searchData)

        productList.value = data.list
        productTotal.value = data.total
        currentPage.value = data.page
        hasMore.value = data.page < data.totalPages
        searchParams.value = searchData
      } catch (error) {
        console.error('搜索商品失败:', error)
        toast.error('搜索商品失败')
      } finally {
        loading.value = false
      }
    }

    /**
     * 获取商品详情
     * @param productId 商品ID
     */
    const fetchProductDetail = async (productId: number) => {
      try {
        loading.value = true
        const { data } = await getProductDetail(productId)
        currentProduct.value = data
        return data
      } catch (error) {
        console.error('获取商品详情失败:', error)
        toast.error('获取商品详情失败')
        return null
      } finally {
        loading.value = false
      }
    }

    /**
     * 获取商品分类列表
     */
    const fetchCategoryList = async () => {
      try {
        const { data } = await getCategoryList()
        categoryList.value = data.list
      } catch (error) {
        console.error('获取分类列表失败:', error)
        toast.error('获取分类列表失败')
      }
    }

    /**
     * 根据分类获取商品
     * @param categoryId 分类ID
     * @param params 其他参数
     */
    const fetchProductsByCategory = async (
      categoryId: number,
      params?: Partial<IProductSearchParams>,
    ) => {
      try {
        loading.value = true
        const searchData = { page: 1, pageSize: pageSize.value, categoryId, ...params }
        const { data } = await getProductsByCategory(categoryId, searchData)

        productList.value = data.list
        productTotal.value = data.total
        currentPage.value = data.page
        hasMore.value = data.page < data.totalPages
        searchParams.value = searchData
      } catch (error) {
        console.error('获取分类商品失败:', error)
        toast.error('获取分类商品失败')
      } finally {
        loading.value = false
      }
    }

    /**
     * 获取热门商品
     */
    const fetchHotProducts = async (limit = 10) => {
      try {
        const { data } = await getHotProducts(limit)
        hotProducts.value = data.list
      } catch (error) {
        console.error('获取热门商品失败:', error)
      }
    }

    /**
     * 获取推荐商品
     */
    const fetchRecommendProducts = async (limit = 10) => {
      try {
        const { data } = await getRecommendProducts(limit)
        recommendProducts.value = data.list
      } catch (error) {
        console.error('获取推荐商品失败:', error)
      }
    }

    /**
     * 重置商品列表
     */
    const resetProductList = () => {
      productList.value = []
      productTotal.value = 0
      currentPage.value = 1
      hasMore.value = true
      searchKeyword.value = ''
      searchParams.value = {
        page: 1,
        pageSize: 10,
      }
    }

    /**
     * 清除当前商品详情
     */
    const clearCurrentProduct = () => {
      currentProduct.value = null
    }

    return {
      // 状态
      productList,
      productTotal,
      currentPage,
      pageSize,
      loading,
      hasMore,
      categoryList,
      currentProduct,
      searchKeyword,
      searchParams,
      hotProducts,
      recommendProducts,

      // 计算属性
      hasProducts,
      totalPages,

      // 方法
      fetchProductList,
      loadMoreProducts,
      searchProductList,
      fetchProductDetail,
      fetchCategoryList,
      fetchProductsByCategory,
      fetchHotProducts,
      fetchRecommendProducts,
      resetProductList,
      clearCurrentProduct,
    }
  },
  {
    persist: {
      paths: ['categoryList', 'hotProducts', 'recommendProducts'],
    },
  },
)
