/**
 * 促销活动状态管理
 */
import { defineStore } from 'pinia'
import {
  getMerchantPromotions,
  getMerchantsPromotionsAndCoupons,
  validatePromotions,
} from '@/api/promotion'
import { PromotionStatus } from '@/api/promotion.typings'
import type {
  IPromotion,
  IPromotionRules,
  IPromotionApplicationResult,
  IPromotionValidationParams,
  IMerchantPromotionCouponInfo,
  IPromotionInfoDTO,
} from '@/api/promotion.typings'

interface PromotionState {
  // 商家促销活动（按商家ID分组）
  merchantPromotions: Record<number, IPromotion[]>

  // 🔧 新增：商家优惠券数据（按商家ID分组）
  merchantCoupons: Record<number, any[]>

  // 适用的促销活动（按商家ID分组）
  applicablePromotions: Record<number, IPromotionApplicationResult[]>

  // 选中的促销活动（按商家ID分组）
  selectedPromotions: Record<number, IPromotion | null>

  // 加载状态
  loading: {
    merchantPromotions: boolean
    validation: boolean
  }
}

export const usePromotionStore = defineStore('promotion', {
  state: (): PromotionState => ({
    merchantPromotions: {},
    merchantCoupons: {}, // 🔧 新增：初始化优惠券存储
    applicablePromotions: {},
    selectedPromotions: {},
    loading: {
      merchantPromotions: false,
      validation: false,
    },
  }),

  getters: {
    // 获取指定商家的促销活动
    getMerchantPromotions: (state) => {
      return (merchantId: number) => state.merchantPromotions[merchantId] || []
    },

    // 🔧 新增：获取指定商家的优惠券数据
    getMerchantCoupons: (state) => {
      return (merchantId: number) => state.merchantCoupons[merchantId] || []
    },

    // 获取指定商家的适用促销活动
    getApplicablePromotions: (state) => {
      return (merchantId: number) => state.applicablePromotions[merchantId] || []
    },

    // 获取指定商家的选中促销活动
    getSelectedPromotion: (state) => {
      return (merchantId: number) => state.selectedPromotions[merchantId] || null
    },

    // 获取指定商家的促销折扣金额
    getPromotionDiscount: (state) => {
      return (merchantId: number) => {
        const selectedPromotion = state.selectedPromotions[merchantId]
        if (!selectedPromotion) return 0

        // 🔧 首先尝试从验证过的促销活动中获取折扣金额
        const applicablePromotions = state.applicablePromotions[merchantId] || []
        const applicablePromotion = applicablePromotions.find(
          (p) => p.promotion.id === selectedPromotion.id,
        )

        if (applicablePromotion?.discount_amount) {
          return applicablePromotion.discount_amount
        }

        // 🔧 如果没有验证数据，从新版API数据中计算折扣金额
        if (
          selectedPromotion.rules &&
          typeof selectedPromotion.rules === 'object' &&
          selectedPromotion.rules.coupon
        ) {
          const amount = selectedPromotion.rules.coupon.amount || 0
          console.log(`🔧 从促销活动规则计算折扣金额: ${amount}`, selectedPromotion.rules)
          return amount
        }

        return 0
      }
    },
  },

  actions: {
    /**
     * 获取商家促销活动（旧版API）
     * @deprecated 请使用 fetchMerchantsPromotionsAndCoupons 替代
     */
    async fetchMerchantPromotions(
      merchantId: number,
      params: {
        food_ids: string
        total_amount: number
      },
    ) {
      this.loading.merchantPromotions = true

      try {
        console.log('🎉 开始加载商家促销活动（旧版API）:', { merchantId, params })

        const response = await getMerchantPromotions(merchantId, params)
        console.log('🎉 API响应:', response)

        if (response.data) {
          const promotions = response.data.promotions.map((promotion) => ({
            ...promotion,
            rules: this.parsePromotionRules(promotion.rules),
          }))

          this.merchantPromotions[merchantId] = promotions

          console.log('✅ 商家促销活动加载成功:', {
            merchantId,
            params,
            count: promotions.length,
            promotions,
          })
        }
      } catch (error) {
        console.error('❌ 加载商家促销活动失败:', error)
        this.merchantPromotions[merchantId] = []
        throw error
      } finally {
        this.loading.merchantPromotions = false
      }
    },

    /**
     * 获取多个商家的促销活动和优惠券信息（新版API）
     */
    async fetchMerchantsPromotionsAndCoupons(merchantIds: number[]) {
      this.loading.merchantPromotions = true

      try {
        console.log('🎉 开始加载多商家促销活动和优惠券（新版API）:', { merchantIds })

        const response = await getMerchantsPromotionsAndCoupons(merchantIds)
        console.log('🎉 新版API响应:', response)

        if (response.data && Array.isArray(response.data)) {
          // 处理每个商家的促销活动数据
          response.data.forEach((merchantInfo: IMerchantPromotionCouponInfo) => {
            const merchantId = merchantInfo.merchant_id

            // 转换促销活动数据格式，使其兼容现有的IPromotion接口
            const promotions: IPromotion[] = merchantInfo.promotions.map(
              (promotionDTO: IPromotionInfoDTO) => ({
                id: promotionDTO.id,
                merchant_id: merchantId,
                name: promotionDTO.name,
                description: promotionDTO.description,
                type: promotionDTO.type as any, // 类型转换
                type_text: promotionDTO.type_name,
                start_time: promotionDTO.start_time,
                end_time: promotionDTO.end_time,
                status: 2, // ACTIVE状态，因为后端只返回活跃的促销活动
                status_text: '进行中',
                rules: this.parsePromotionRules(promotionDTO.rules),
                max_usage_count: 0, // 新API暂不返回此字段
                usage_count: 0, // 新API暂不返回此字段
                created_at: '', // 新API暂不返回此字段
              }),
            )

            this.merchantPromotions[merchantId] = promotions

            // 🔧 新增：存储优惠券数据
            this.merchantCoupons[merchantId] = merchantInfo.coupons || []

            console.log('✅ 商家促销活动数据处理完成:', {
              merchantId,
              merchantName: merchantInfo.merchant_name,
              promotionCount: promotions.length,
              couponCount: merchantInfo.coupons.length,
              hasPromotion: merchantInfo.has_promotion,
              hasCoupon: merchantInfo.has_coupon,
            })
          })

          console.log('✅ 所有商家促销活动加载成功:', {
            merchantIds,
            totalMerchants: response.data.length,
          })
        }
      } catch (error) {
        console.error('❌ 加载多商家促销活动失败:', error)
        // 清空失败的商家数据
        merchantIds.forEach((merchantId) => {
          this.merchantPromotions[merchantId] = []
        })
        throw error
      } finally {
        this.loading.merchantPromotions = false
      }
    },

    /**
     * 验证促销活动适用性
     */
    async validatePromotionsForOrder(params: IPromotionValidationParams) {
      this.loading.validation = true

      try {
        console.log('🎉 开始验证促销活动:', params)

        // 使用新版API获取商家促销活动
        await this.fetchMerchantsPromotionsAndCoupons([params.merchant_id])

        const promotions = this.merchantPromotions[params.merchant_id] || []
        const applicablePromotions: IPromotionApplicationResult[] = []

        // 本地验证每个促销活动
        for (const promotion of promotions) {
          const result = this.validateSinglePromotion(promotion, params)
          applicablePromotions.push(result)
        }

        this.applicablePromotions[params.merchant_id] = applicablePromotions

        // 验证并清理不适用的促销活动选择
        this.validateAndClearInvalidPromotions(params.merchant_id)

        // 如果有可用的促销活动且当前没有选择，自动选择第一个可用的
        const availablePromotions = applicablePromotions.filter((p) => p.applicable)
        if (availablePromotions.length > 0 && !this.selectedPromotions[params.merchant_id]) {
          console.log('🎉 自动选择第一个可用的促销活动:', availablePromotions[0].promotion)
          this.selectedPromotions[params.merchant_id] = availablePromotions[0].promotion
        }

        console.log('✅ 促销活动验证完成:', {
          merchantId: params.merchant_id,
          total: applicablePromotions.length,
          applicable: availablePromotions.length,
          autoSelected: this.selectedPromotions[params.merchant_id],
          results: applicablePromotions,
        })

        return applicablePromotions
      } catch (error) {
        console.error('❌ 验证促销活动失败:', error)
        this.applicablePromotions[params.merchant_id] = []
        throw error
      } finally {
        this.loading.validation = false
      }
    },

    /**
     * 选择促销活动
     */
    selectPromotion(merchantId: number, promotion: IPromotion | null) {
      console.log('🎉 选择促销活动:', { merchantId, promotion })
      this.selectedPromotions[merchantId] = promotion
    },

    /**
     * 清除指定商家的选中促销活动
     */
    clearSelectedPromotion(merchantId: number) {
      console.log('🎉 清除商家促销活动选择:', { merchantId })
      this.selectedPromotions[merchantId] = null
    },

    /**
     * 验证并清理不适用的促销活动选择
     */
    validateAndClearInvalidPromotions(merchantId: number) {
      const selectedPromotion = this.selectedPromotions[merchantId]
      if (!selectedPromotion) return

      const applicablePromotions = this.applicablePromotions[merchantId] || []
      const isStillApplicable = applicablePromotions.some(
        (p) => p.promotion.id === selectedPromotion.id && p.applicable,
      )

      if (!isStillApplicable) {
        console.log('🎉 当前选中的促销活动不再适用，自动清除:', {
          merchantId,
          promotionId: selectedPromotion.id,
          promotionName: selectedPromotion.name,
        })
        this.selectedPromotions[merchantId] = null
      }
    },

    /**
     * 解析促销规则
     */
    parsePromotionRules(rules: string | IPromotionRules): IPromotionRules {
      if (typeof rules === 'string') {
        try {
          return JSON.parse(rules)
        } catch (error) {
          console.error('❌ 解析促销规则失败:', error)
          return {}
        }
      }
      return rules
    },

    /**
     * 验证单个促销活动
     */
    validateSinglePromotion(
      promotion: IPromotion,
      params: IPromotionValidationParams,
    ): IPromotionApplicationResult {
      const rules = this.parsePromotionRules(promotion.rules)

      console.log('🔍 验证单个促销活动:', {
        promotion,
        params,
        rules,
        status: promotion.status,
        expectedStatus: PromotionStatus.ACTIVE,
      })

      // 检查促销活动状态
      if (promotion.status !== PromotionStatus.ACTIVE) {
        console.log('❌ 促销活动状态不匹配:', {
          actualStatus: promotion.status,
          expectedStatus: PromotionStatus.ACTIVE,
          statusText: promotion.status_text,
        })
        return {
          promotion,
          applicable: false,
          reason: `促销活动未激活 (状态: ${promotion.status_text})`,
          discount_amount: 0,
          final_amount: params.total_amount,
        }
      }

      // 检查时间范围
      const now = new Date()
      const startTime = new Date(promotion.start_time)
      const endTime = new Date(promotion.end_time)

      console.log('🕒 时间范围检查:', {
        now: now.toISOString(),
        startTime: startTime.toISOString(),
        endTime: endTime.toISOString(),
        isAfterStart: now >= startTime,
        isBeforeEnd: now <= endTime,
        isValid: now >= startTime && now <= endTime,
      })

      if (now < startTime || now > endTime) {
        console.log('❌ 促销活动不在有效期内')
        return {
          promotion,
          applicable: false,
          reason: '促销活动不在有效期内',
          discount_amount: 0,
          final_amount: params.total_amount,
        }
      }

      // 检查优惠券规则
      if (rules.coupon) {
        const couponRule = rules.coupon

        console.log('💰 优惠券规则检查:', {
          couponRule,
          totalAmount: params.total_amount,
          minOrderAmount: couponRule.min_order_amount,
          isAmountSufficient: params.total_amount >= couponRule.min_order_amount,
        })

        // 检查最小订单金额
        if (params.total_amount < couponRule.min_order_amount) {
          console.log('❌ 订单金额不足')
          return {
            promotion,
            applicable: false,
            reason: `订单金额不足，需满${couponRule.min_order_amount}元`,
            discount_amount: 0,
            final_amount: params.total_amount,
          }
        }

        // 计算折扣金额
        const discountAmount = Math.min(couponRule.amount, params.total_amount)
        const finalAmount = Math.max(0, params.total_amount - discountAmount)

        console.log('✅ 促销活动适用:', {
          discountAmount,
          finalAmount,
          couponAmount: couponRule.amount,
        })

        return {
          promotion,
          applicable: true,
          discount_amount: discountAmount,
          final_amount: finalAmount,
        }
      }

      // 默认不适用
      return {
        promotion,
        applicable: false,
        reason: '促销规则不匹配',
        discount_amount: 0,
        final_amount: params.total_amount,
      }
    },
  },
})
