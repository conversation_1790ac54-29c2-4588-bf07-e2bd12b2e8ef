/**
 * 系统配置 Store
 * 管理系统基本信息、联系方式、维护模式、轮播图、功能导航、外卖全局分类等系统配置数据
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type {
  ISystemInfo,
  IContactInfo,
  IMaintenanceMode,
  IBannerInfo,
  IAppMenuInfo,
  ISystemConfig,
  IConfigResponse,
  IGlobalCategory,
} from '@/api/system.typings'
import * as systemApi from '@/api/system'
import { CACHE_KEYS, CONFIG_CATEGORIES, DEFAULT_VALUES } from '@/constants/system'

/**
 * 系统配置 Store
 * 管理系统基本信息、联系方式、维护模式、轮播图、功能导航等系统配置数据
 */
export const useSystemStore = defineStore(
  'system',
  () => {
    // ==================== 状态定义 ====================

    // 系统基本信息
    const systemInfo = ref<ISystemInfo | null>(null)

    // 联系方式信息
    const contactInfo = ref<IContactInfo | null>(null)

    // 维护模式状态
    const maintenanceMode = ref<IMaintenanceMode | null>(null)

    // 轮播图列表
    const bannerList = ref<IBannerInfo[]>([])

    // 功能导航列表
    const appMenuList = ref<IAppMenuInfo[]>([])

    // 外卖全局分类列表
    const globalCategoryList = ref<IGlobalCategory[]>([])

    // 系统配置缓存
    const configCache = ref<Record<string, any>>({})

    // 加载状态
    const loading = ref({
      systemInfo: false,
      contactInfo: false,
      maintenanceMode: false,
      bannerList: false,
      appMenuList: false,
      globalCategoryList: false,
      configs: false,
    })

    // 错误状态
    const errors = ref<Record<string, string | null>>({})

    // ==================== 计算属性 ====================

    // 是否处于维护模式
    const isMaintenanceMode = computed(() => {
      return maintenanceMode.value?.enabled || false
    })

    // 网站名称
    const siteName = computed(() => {
      return systemInfo.value?.siteName || DEFAULT_VALUES.SITE_NAME
    })

    // 网站Logo
    const siteLogo = computed(() => {
      return systemInfo.value?.siteLogo || ''
    })

    // 版权信息
    const copyright = computed(() => {
      return systemInfo.value?.copyright || ''
    })

    // 有效的轮播图列表（过滤掉禁用的）
    const activeBannerList = computed(() => {
      return bannerList.value.filter((banner) => banner.status !== 0)
    })

    // 有效的功能导航列表（过滤掉禁用的）
    const activeAppMenuList = computed(() => {
      return appMenuList.value.filter((menu) => menu.status !== 0)
    })

    // 有效的外卖全局分类列表（过滤掉禁用的）
    const activeGlobalCategoryList = computed(() => {
      return globalCategoryList.value.filter((category) => category.isActive)
    })

    // 一级外卖分类列表
    const primaryGlobalCategories = computed(() => {
      return activeGlobalCategoryList.value.filter((category) => category.level === 1)
    })

    // ==================== 方法定义 ====================

    /**
     * 获取系统基本信息
     */
    const getSystemInfo = async (force = false) => {
      if (!force && systemInfo.value) {
        return systemInfo.value
      }

      try {
        loading.value.systemInfo = true
        errors.value.systemInfo = null

        const response = await systemApi.getSystemInfo()
        if (response.code === 200) {
          if (!response.data) {
            console.warn('系统信息API返回空数据')
            return null
          }
          systemInfo.value = response.data
          return response.data
        } else {
          throw new Error(response.msg || '获取系统信息失败')
        }
      } catch (error: any) {
        console.error('[SystemStore] 获取系统信息失败:', error)
        errors.value.systemInfo = error.message || '获取系统信息失败'
        throw error
      } finally {
        loading.value.systemInfo = false
      }
    }

    /**
     * 获取联系方式信息
     */
    const getContactInfo = async (force = false) => {
      if (!force && contactInfo.value) {
        return contactInfo.value
      }

      try {
        loading.value.contactInfo = true
        errors.value.contactInfo = null

        const response = await systemApi.getContactInfo()
        if (response.code === 200) {
          contactInfo.value = response.data
          return response.data
        } else {
          throw new Error(response.msg || '获取联系信息失败')
        }
      } catch (error: any) {
        console.error('[SystemStore] 获取联系信息失败:', error)
        errors.value.contactInfo = error.message || '获取联系信息失败'
        throw error
      } finally {
        loading.value.contactInfo = false
      }
    }

    /**
     * 获取维护模式状态
     */
    const getMaintenanceMode = async (force = false) => {
      if (!force && maintenanceMode.value) {
        return maintenanceMode.value
      }

      try {
        loading.value.maintenanceMode = true
        errors.value.maintenanceMode = null

        const response = await systemApi.getMaintenanceMode()
        if (response.code === 200) {
          maintenanceMode.value = response.data
          return response.data
        } else {
          throw new Error(response.msg || '获取维护模式状态失败')
        }
      } catch (error: any) {
        console.error('[SystemStore] 获取维护模式状态失败:', error)
        errors.value.maintenanceMode = error.message || '获取维护模式状态失败'
        throw error
      } finally {
        loading.value.maintenanceMode = false
      }
    }

    /**
     * 获取轮播图列表
     * @param cacheTime 缓存时间（分钟），默认10分钟
     */
    const getBannerList = async (cacheTime: number = DEFAULT_VALUES.CACHE_TIME) => {
      // 缓存键名
      const cacheKey = CACHE_KEYS.SYSTEM_BANNER_LIST

      // 从缓存中获取轮播图数据
      const cachedData = uni.getStorageSync(cacheKey)

      // 如果存在缓存且未过期，直接返回缓存数据
      if (cachedData) {
        try {
          const data = JSON.parse(cachedData)
          // 检查缓存是否过期
          if (data.expireTime > Date.now()) {
            console.log('从缓存获取轮播图数据', data.banners)
            bannerList.value = data.banners
            return {
              code: 0,
              message: '获取成功',
              data: data.banners,
            }
          }
          // 缓存已过期，删除缓存
          uni.removeStorageSync(cacheKey)
        } catch (error) {
          console.error('解析轮播图缓存数据失败:', error)
          uni.removeStorageSync(cacheKey)
        }
      }

      try {
        loading.value.bannerList = true
        errors.value.bannerList = null

        // 从服务器获取轮播图数据
        const response = await systemApi.getConfigDetails({ category: CONFIG_CATEGORIES.BANNER })
        console.log('从服务器获取轮播图数据', response)

        let banners: IBannerInfo[] = []

        // 处理返回的数据结构
        if (response.code === 200 && response.data) {
          try {
            // 获取配置列表
            const configList = response.data.list
            if (configList && configList.length > 0) {
              // 获取第一个配置项的configValue
              const configItem = configList[0]
              const configValueStr = configItem.configValue

              if (configValueStr) {
                // 解析JSON字符串
                const rawBanners = JSON.parse(configValueStr) as Array<any>

                // 转换为前端所需的格式
                banners = rawBanners
                  .map((item) => ({
                    id: item.id,
                    // 清理图片URL中的反引号和多余空格
                    imageUrl: item.image ? item.image.replace(/`/g, '').trim() : '',
                    linkUrl: item.link || '',
                    title: item.title || '',
                    sort: item.sort_order || 0,
                    status: item.status || 0,
                  }))
                  .filter((item) => item.status === 1) // 只显示启用状态的轮播图
              }
            }

            // 计算过期时间
            const expireTime = Date.now() + cacheTime * 60 * 1000
            // 保存到缓存
            uni.setStorageSync(
              cacheKey,
              JSON.stringify({
                banners: banners,
                expireTime: expireTime,
              }),
            )

            bannerList.value = banners

            return {
              code: 0,
              message: '获取成功',
              data: banners,
            }
          } catch (err) {
            console.error('解析轮播图数据出错:', err)
            throw new Error('数据解析错误')
          }
        }

        throw new Error(response.msg || '获取数据失败')
      } catch (error: any) {
        console.error('[SystemStore] 获取轮播图失败:', error)
        errors.value.bannerList = error.message || '获取轮播图失败'
        return {
          code: -1,
          message: error.message || '获取轮播图失败',
          data: [],
        }
      } finally {
        loading.value.bannerList = false
      }
    }

    /**
     * 获取功能导航列表
     * @param cacheTime 缓存时间（分钟），默认10分钟
     */
    const getAppMenuList = async (cacheTime: number = DEFAULT_VALUES.CACHE_TIME) => {
      // 缓存键名
      const cacheKey = CACHE_KEYS.SYSTEM_APPMENU_LIST

      // 从缓存中获取功能导航数据
      const cachedData = uni.getStorageSync(cacheKey)

      // 如果存在缓存且未过期，直接返回缓存数据
      if (cachedData) {
        try {
          const data = JSON.parse(cachedData)
          // 检查缓存是否过期
          if (data.expireTime > Date.now()) {
            console.log('从缓存获取功能导航数据', data.appMenus)
            appMenuList.value = data.appMenus
            return {
              code: 0,
              message: '获取成功',
              data: data.appMenus,
            }
          }
          // 缓存已过期，删除缓存
          uni.removeStorageSync(cacheKey)
        } catch (error) {
          console.error('解析功能导航缓存数据失败:', error)
          uni.removeStorageSync(cacheKey)
        }
      }

      try {
        loading.value.appMenuList = true
        errors.value.appMenuList = null

        // 从服务器获取功能导航数据
        const response = await systemApi.getConfigDetails({ category: CONFIG_CATEGORIES.APPMENU })
        console.log('从服务器获取功能导航数据', response)

        let appMenus: IAppMenuInfo[] = []

        // 处理返回的数据结构
        if (response.code === 200 && response.data) {
          try {
            // 获取配置列表
            const configList = response.data.list
            if (configList && configList.length > 0) {
              // 获取第一个配置项的configValue
              const configItem = configList[0]
              const configValueStr = configItem.configValue

              if (configValueStr) {
                // 解析JSON字符串
                const rawAppMenus = JSON.parse(configValueStr) as Array<any>

                // 转换为前端所需的格式
                appMenus = rawAppMenus
                  .map((item) => ({
                    id: item.id,
                    title: item.title || '',
                    type: item.type || '',
                    iconType: item.iconType || '',
                    icon: item.icon || '',
                    // 清理图标URL中的反引号和多余空格
                    iconUrl: item.iconUrl ? item.iconUrl.replace(/`/g, '').trim() : '',
                    action: item.action || '',
                    description: item.description || '',
                    sort: item.sort_order || 0,
                    status: item.status || 0,
                  }))
                  .filter((item) => item.status === 1) // 只显示启用状态的功能导航
              }
            }

            // 计算过期时间
            const expireTime = Date.now() + cacheTime * 60 * 1000
            // 保存到缓存
            uni.setStorageSync(
              cacheKey,
              JSON.stringify({
                appMenus: appMenus,
                expireTime: expireTime,
              }),
            )

            appMenuList.value = appMenus

            return {
              code: 0,
              message: '获取成功',
              data: appMenus,
            }
          } catch (err) {
            console.error('解析功能导航数据出错:', err)
            throw new Error('数据解析错误')
          }
        }

        throw new Error(response.msg || '获取数据失败')
      } catch (error: any) {
        console.error('[SystemStore] 获取功能导航失败:', error)
        errors.value.appMenuList = error.message || '获取功能导航失败'
        return {
          code: -1,
          message: error.message || '获取功能导航失败',
          data: [],
        }
      } finally {
        loading.value.appMenuList = false
      }
    }

    /**
     * 获取外卖全局分类列表
     * @param cacheTime 缓存时间（分钟），默认30分钟
     */
    const getGlobalCategoryList = async (cacheTime: number = 30) => {
      // 缓存键名
      const cacheKey = CACHE_KEYS.SYSTEM_GLOBAL_CATEGORY_LIST

      // 从缓存中获取外卖全局分类数据
      const cachedData = uni.getStorageSync(cacheKey)

      // 如果存在缓存且未过期，直接返回缓存数据
      // if (cachedData) {
      //   try {
      //     const data = JSON.parse(cachedData)
      //     // 检查缓存是否过期
      //     if (data.expireTime > Date.now()) {
      //       console.log('从缓存获取外卖全局分类数据', data.categories)
      //       globalCategoryList.value = data.categories
      //       return {
      //         code: 0,
      //         message: '获取成功',
      //         data: data.categories,
      //       }
      //     }
      //     // 缓存已过期，删除缓存
      //     uni.removeStorageSync(cacheKey)
      //   } catch (error) {
      //     console.error('解析外卖全局分类缓存数据失败:', error)
      //     uni.removeStorageSync(cacheKey)
      //   }
      // }

      try {
        loading.value.globalCategoryList = true
        errors.value.globalCategoryList = null

        // 从服务器获取外卖全局分类数据
        const response = await systemApi.getGlobalCategories()
        console.log('从服务器获取外卖全局分类数据', response)

        if (response.code === 200 && response.data && response.data.list) {
          // 将后端数据结构映射为前端期望的结构
          const categories = response.data.list.map((item: any) => ({
            id: item.id,
            name: item.name,
            icon: item.icon,
            level: 1, // 后端数据暂时都设为一级分类
            parentId: undefined,
            sortOrder: item.sort_order || 0,
            isActive: item.is_show || false,
            children: [],
          }))

          // 计算过期时间
          const expireTime = Date.now() + cacheTime * 60 * 1000
          // 保存到缓存
          uni.setStorageSync(
            cacheKey,
            JSON.stringify({
              categories: categories,
              expireTime: expireTime,
            }),
          )

          globalCategoryList.value = categories

          return {
            code: 0,
            message: '获取成功',
            data: categories,
          }
        }

        throw new Error(response.msg || '获取数据失败')
      } catch (error: any) {
        console.error('[SystemStore] 获取外卖全局分类失败:', error)
        errors.value.globalCategoryList = error.message || '获取外卖全局分类失败'
        return {
          code: -1,
          message: error.message || '获取外卖全局分类失败',
          data: [],
        }
      } finally {
        loading.value.globalCategoryList = false
      }
    }

    /**
     * 根据分类ID获取子分类列表
     * @param parentId 父分类ID
     */
    const getChildCategories = (parentId: number): IGlobalCategory[] => {
      return activeGlobalCategoryList.value.filter((category) => category.parentId === parentId)
    }

    /**
     * 根据分类ID获取分类信息
     * @param categoryId 分类ID
     */
    const getCategoryById = (categoryId: number): IGlobalCategory | undefined => {
      return globalCategoryList.value.find((category) => category.id === categoryId)
    }

    /**
     * 获取指定分类的配置详情
     * @param category 配置分类
     */
    const getConfigsByCategory = async (category: string) => {
      try {
        loading.value.configs = true
        errors.value.configs = null

        const response = await systemApi.getConfigDetails({ category })
        if (response.code === 200) {
          // 缓存配置数据
          configCache.value[category] = response.data
          return response.data
        } else {
          throw new Error(response.msg || '获取配置失败')
        }
      } catch (error: any) {
        console.error(`[SystemStore] 获取${category}配置失败:`, error)
        errors.value.configs = error.message || '获取配置失败'
        throw error
      } finally {
        loading.value.configs = false
      }
    }

    /**
     * 获取公开配置信息
     * @param key 配置键名
     */
    const getPublicConfig = async (key: string) => {
      try {
        const response = await systemApi.getPublicConfig(key)
        if (response.code === 200) {
          // 缓存单个配置
          configCache.value[key] = response.data
          return response.data
        } else {
          throw new Error(response.msg || '获取配置失败')
        }
      } catch (error: any) {
        console.error(`[SystemStore] 获取${key}配置失败:`, error)
        throw error
      }
    }

    /**
     * 初始化系统数据
     * 在应用启动时调用，获取必要的系统信息
     */
    const initSystemData = async () => {
      try {
        // 并行获取系统基本信息和维护模式状态
        await Promise.allSettled([
          getSystemInfo(),
          getMaintenanceMode(),
          getBannerList(),
          getAppMenuList(),
          getGlobalCategoryList(),
        ])
      } catch (error) {
        console.error('[SystemStore] 初始化系统数据失败:', error)
      }
    }

    /**
     * 清除所有缓存数据
     */
    const clearCache = () => {
      systemInfo.value = null
      contactInfo.value = null
      maintenanceMode.value = null
      bannerList.value = []
      appMenuList.value = []
      globalCategoryList.value = []
      configCache.value = {}

      // 清除本地存储的缓存
      uni.removeStorageSync(CACHE_KEYS.SYSTEM_BANNER_LIST)
      uni.removeStorageSync(CACHE_KEYS.SYSTEM_APPMENU_LIST)
      uni.removeStorageSync(CACHE_KEYS.SYSTEM_GLOBAL_CATEGORY_LIST)
    }

    /**
     * 重置错误状态
     */
    const clearErrors = () => {
      errors.value = {}
    }

    // ==================== 返回 ====================

    return {
      // 状态
      systemInfo,
      contactInfo,
      maintenanceMode,
      bannerList,
      appMenuList,
      globalCategoryList,
      configCache,
      loading,
      errors,

      // 计算属性
      isMaintenanceMode,
      siteName,
      siteLogo,
      copyright,
      activeBannerList,
      activeAppMenuList,
      activeGlobalCategoryList,
      primaryGlobalCategories,

      // 方法
      getSystemInfo,
      getContactInfo,
      getMaintenanceMode,
      getBannerList,
      getAppMenuList,
      getGlobalCategoryList,
      getChildCategories,
      getCategoryById,
      getConfigsByCategory,
      getPublicConfig,
      initSystemData,
      clearCache,
      clearErrors,
    }
  },
  {
    // 持久化配置
    persist: {
      key: 'system-store',
      storage: {
        getItem: uni.getStorageSync,
        setItem: uni.setStorageSync,
      },
      // 只持久化部分数据，避免缓存过期问题
      paths: ['systemInfo', 'contactInfo'],
    },
  },
)
