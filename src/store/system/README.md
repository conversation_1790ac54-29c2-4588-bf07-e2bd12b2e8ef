# System Store 使用说明

## 概述

`useSystemStore` 是一个基于 Pinia 的状态管理模块，用于管理系统配置相关的数据，包括系统基本信息、联系方式、维护模式、轮播图等。该模块只使用不需要认证的公开接口，确保在用户未登录时也能正常获取系统配置信息。

## 功能特性

- ✅ 系统基本信息管理（网站名称、Logo、版权等）
- ✅ 联系方式信息管理
- ✅ 维护模式状态检查
- ✅ 轮播图数据管理（支持缓存）
- ✅ 配置数据缓存机制
- ✅ 数据持久化存储
- ✅ 错误状态管理
- ✅ 加载状态管理

## 快速开始

### 1. 导入 Store

```typescript
import { useSystemStore } from '@/store/system'

// 在组件中使用
const systemStore = useSystemStore()
```

### 2. 获取系统基本信息

```typescript
// 获取系统信息
await systemStore.getSystemInfo()

// 访问系统信息
console.log(systemStore.siteName) // 网站名称
console.log(systemStore.siteLogo) // 网站Logo
console.log(systemStore.copyright) // 版权信息
```

### 3. 获取轮播图列表

```typescript
// 获取轮播图（默认缓存10分钟）
await systemStore.getBannerList()

// 获取轮播图（自定义缓存时间）
await systemStore.getBannerList(30) // 缓存30分钟

// 访问轮播图数据
console.log(systemStore.activeBannerList) // 有效的轮播图列表
```

### 4. 检查维护模式

```typescript
// 获取维护模式状态
await systemStore.getMaintenanceMode()

// 检查是否处于维护模式
if (systemStore.isMaintenanceMode) {
  console.log('系统正在维护中')
  console.log(systemStore.maintenanceMode?.message)
}
```

### 5. 初始化系统数据

```typescript
// 在应用启动时初始化所有必要的系统数据
await systemStore.initSystemData()
```

## API 接口说明

### 状态属性

| 属性              | 类型                       | 说明             |
| ----------------- | -------------------------- | ---------------- |
| `systemInfo`      | `ISystemInfo \| null`      | 系统基本信息     |
| `contactInfo`     | `IContactInfo \| null`     | 联系方式信息     |
| `maintenanceMode` | `IMaintenanceMode \| null` | 维护模式状态     |
| `bannerList`      | `IBannerInfo[]`            | 轮播图列表       |
| `configCache`     | `Record<string, any>`      | 配置缓存         |
| `loading`         | `object`                   | 各接口的加载状态 |
| `errors`          | `object`                   | 各接口的错误信息 |

### 计算属性

| 属性                | 类型            | 说明             |
| ------------------- | --------------- | ---------------- |
| `isMaintenanceMode` | `boolean`       | 是否处于维护模式 |
| `siteName`          | `string`        | 网站名称         |
| `siteLogo`          | `string`        | 网站Logo URL     |
| `copyright`         | `string`        | 版权信息         |
| `activeBannerList`  | `IBannerInfo[]` | 有效的轮播图列表 |

### 方法

| 方法                             | 参数                 | 返回值                      | 说明               |
| -------------------------------- | -------------------- | --------------------------- | ------------------ |
| `getSystemInfo(force?)`          | `force?: boolean`    | `Promise<ISystemInfo>`      | 获取系统基本信息   |
| `getContactInfo(force?)`         | `force?: boolean`    | `Promise<IContactInfo>`     | 获取联系方式信息   |
| `getMaintenanceMode(force?)`     | `force?: boolean`    | `Promise<IMaintenanceMode>` | 获取维护模式状态   |
| `getBannerList(cacheTime?)`      | `cacheTime?: number` | `Promise<any>`              | 获取轮播图列表     |
| `getConfigsByCategory(category)` | `category: string`   | `Promise<any>`              | 获取指定分类的配置 |
| `getPublicConfig(key)`           | `key: string`        | `Promise<any>`              | 获取公开配置       |
| `initSystemData()`               | -                    | `Promise<void>`             | 初始化系统数据     |
| `clearCache()`                   | -                    | `void`                      | 清除所有缓存       |
| `clearErrors()`                  | -                    | `void`                      | 清除错误状态       |

## 使用示例

### 在页面中使用

```vue
<template>
  <view class="page">
    <!-- 显示网站名称 -->
    <text class="site-name">{{ systemStore.siteName }}</text>

    <!-- 显示轮播图 -->
    <swiper class="banner-swiper" v-if="systemStore.activeBannerList.length > 0">
      <swiper-item v-for="banner in systemStore.activeBannerList" :key="banner.id">
        <image :src="banner.imageUrl" mode="aspectFill" />
      </swiper-item>
    </swiper>

    <!-- 维护模式提示 -->
    <view class="maintenance-notice" v-if="systemStore.isMaintenanceMode">
      <text>{{ systemStore.maintenanceMode?.message }}</text>
    </view>
  </view>
</template>

<script setup>
import { onMounted } from 'vue'
import { useSystemStore } from '@/store/system'

const systemStore = useSystemStore()

onMounted(async () => {
  // 初始化系统数据
  await systemStore.initSystemData()
})
</script>
```

### 在应用启动时初始化

```typescript
// main.ts
import { createSSRApp } from 'vue'
import { useSystemStore } from '@/store/system'

export function createApp() {
  const app = createSSRApp(App)

  // 应用启动时初始化系统数据
  const systemStore = useSystemStore()
  systemStore.initSystemData().catch(console.error)

  return { app }
}
```

## 缓存机制

### 轮播图缓存

轮播图数据支持本地缓存，默认缓存时间为10分钟。缓存数据存储在 `uni.storage` 中，键名为 `system_banner_list`。

```typescript
// 使用默认缓存时间（10分钟）
await systemStore.getBannerList()

// 自定义缓存时间（30分钟）
await systemStore.getBannerList(30)

// 强制从服务器获取（忽略缓存）
systemStore.clearCache()
await systemStore.getBannerList()
```

### 数据持久化

部分数据（如系统基本信息、联系方式）会自动持久化到本地存储，应用重启后会自动恢复。

## 错误处理

```typescript
try {
  await systemStore.getSystemInfo()
} catch (error) {
  // 处理错误
  console.error('获取系统信息失败:', error)

  // 检查具体错误信息
  if (systemStore.errors.systemInfo) {
    uni.showToast({
      title: systemStore.errors.systemInfo,
      icon: 'error',
    })
  }
}

// 清除错误状态
systemStore.clearErrors()
```

## 注意事项

1. **网络依赖**：所有数据都需要从服务器获取，请确保网络连接正常
2. **缓存管理**：轮播图等数据有缓存机制，如需最新数据请手动清除缓存
3. **错误处理**：建议在使用时添加适当的错误处理逻辑
4. **性能优化**：避免频繁调用接口，合理利用缓存机制
5. **数据格式**：后端返回的数据格式可能会变化，请注意兼容性处理

## 相关文件

- `@/store/system.ts` - Store 主文件
- `@/api/system.ts` - API 接口定义
- `@/api/system.typings.ts` - 类型定义
- `@/pages/system/demo.vue` - 使用示例页面
