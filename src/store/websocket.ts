/**
 * websocket.ts
 * WebSocket状态管理Store - 简化版
 *
 * 作为WebSocket服务层的代理，提供响应式状态访问
 */

import { defineStore } from 'pinia'
import { computed } from 'vue'
import { webSocketService, WebSocketStatus } from '@/services/websocket'

/**
 * WebSocket Store - 简化版
 * 作为WebSocket服务层的代理，提供响应式状态访问
 */
export const useWebSocketStore = defineStore(
  'websocket',
  () => {
    // 状态代理到服务层
    const isConnected = computed(() => webSocketService.isConnected)
    const connectionStatus = computed(() => webSocketService.connectionStatus)
    const currentStatus = computed(() => webSocketService.currentStatus)
    const isInitialized = computed(() => webSocketService.initialized)

    // 计算属性
    const isConnectedOrConnecting = computed(() => {
      const status = currentStatus.value
      return status === WebSocketStatus.CONNECTED || status === WebSocketStatus.CONNECTING
    })

    const isDisconnected = computed(() => {
      return currentStatus.value === WebSocketStatus.DISCONNECTED
    })

    const isError = computed(() => {
      return currentStatus.value === WebSocketStatus.ERROR
    })

    const isReconnecting = computed(() => {
      return currentStatus.value === WebSocketStatus.RECONNECTING
    })

    /**
     * 初始化WebSocket服务
     */
    const initService = async () => {
      console.log('🚀 初始化WebSocket服务...')
      try {
        await webSocketService.initialize()
      } catch (error) {
        console.error('❌ WebSocket服务初始化失败:', error)
      }
    }

    /**
     * 手动连接WebSocket（用于调试或特殊情况）
     */
    const connect = () => {
      console.log('🔗 手动连接WebSocket')
      webSocketService.connect()
    }

    /**
     * 手动断开WebSocket连接
     */
    const disconnect = () => {
      console.log('🔌 手动断开WebSocket连接')
      webSocketService.disconnect()
    }

    /**
     * 手动重新连接WebSocket
     */
    const reconnect = () => {
      console.log('🔄 手动重新连接WebSocket')
      webSocketService.reconnect()
    }

    /**
     * 获取连接统计信息
     */
    const getConnectionStats = () => {
      return webSocketService.getConnectionStats()
    }

    /**
     * 调试：打印当前状态
     */
    const debugStatus = () => {
      webSocketService.debugStatus()
    }

    // 返回store接口
    return {
      // 状态
      isConnected,
      connectionStatus,
      currentStatus,
      isInitialized,
      isConnectedOrConnecting,
      isDisconnected,
      isError,
      isReconnecting,

      // 方法
      initService,
      connect,
      disconnect,
      reconnect,
      getConnectionStats,
      debugStatus,
    }
  },
  {
    persist: false, // WebSocket状态不需要持久化
  },
)
