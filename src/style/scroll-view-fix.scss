/**
 * uni-app scroll-view 被动事件监听器修复样式
 * 
 * 专门用于修复 scroll-view 组件的被动事件监听器警告
 * 通过 CSS 优化来减少 JavaScript 事件处理的需求
 */

/* 全局 scroll-view 优化 */
uni-scroll-view,
.uni-scroll-view,
scroll-view,
.merchant-scroll {
  /* 启用硬件加速，减少重绘 */
  -webkit-transform: translateZ(0) !important;
  transform: translateZ(0) !important;
  will-change: scroll-position !important;

  /* 优化触摸滚动性能 */
  -webkit-overflow-scrolling: touch !important;
  overflow-scrolling: touch !important;

  /* 减少重绘和重排 */
  contain: layout style paint !important;

  /* 平滑滚动 */
  scroll-behavior: smooth !important;

  /* 防止选择文本 */
  -webkit-user-select: none !important;
  user-select: none !important;

  /* 防止拖拽 */
  -webkit-user-drag: none !important;
  user-drag: none !important;

  /* 防止长按菜单 */
  -webkit-touch-callout: none !important;
  touch-callout: none !important;

  /* 防止高亮 */
  -webkit-tap-highlight-color: transparent !important;
  tap-highlight-color: transparent !important;
}

/* 水平滚动的 scroll-view 特殊优化 */
uni-scroll-view[scroll-x='true'],
uni-scroll-view[scroll-x],
.uni-scroll-view.scroll-x,
scroll-view[scroll-x='true'],
scroll-view[scroll-x],
.merchant-scroll {
  /* 强制水平滚动 */
  overflow-x: auto !important;
  overflow-y: hidden !important;
  white-space: nowrap !important;

  /* 防止垂直滚动 */
  overscroll-behavior-y: none !important;

  /* 优化水平滚动 */
  overscroll-behavior-x: auto !important;

  /* 隐藏滚动条 */
  scrollbar-width: none !important; /* Firefox */
  -ms-overflow-style: none !important; /* IE and Edge */

  &::-webkit-scrollbar {
    display: none !important; /* Chrome, Safari, Opera */
    width: 0 !important;
    height: 0 !important;
  }
}

/* scroll-view 内部元素优化 */
uni-scroll-view > *,
.uni-scroll-view > *,
scroll-view > *,
.merchant-scroll > * {
  /* 防止子元素影响滚动 */
  pointer-events: auto !important;

  /* 优化渲染 */
  -webkit-transform: translateZ(0) !important;
  transform: translateZ(0) !important;
}

/* 水平滚动内容优化 */
.merchant-scroll .merchant-item,
.merchant-scroll > view,
uni-scroll-view[scroll-x] > view,
scroll-view[scroll-x] > view {
  /* 强制内联块显示 */
  display: inline-block !important;
  vertical-align: top !important;
  white-space: normal !important;

  /* 防止换行 */
  flex-shrink: 0 !important;
}

/* 首页特定的商家滚动区域优化 */
.takeout-section .merchant-scroll {
  /* 确保正确的滚动行为 */
  overflow-x: auto !important;
  overflow-y: hidden !important;

  /* 优化性能 */
  -webkit-overflow-scrolling: touch !important;
  overflow-scrolling: touch !important;

  /* 防止意外的垂直滚动 */
  overscroll-behavior: none !important;

  /* 平滑滚动 */
  scroll-behavior: smooth !important;

  /* 隐藏滚动条 */
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;

  &::-webkit-scrollbar {
    display: none !important;
  }
}

/* 商家卡片优化 */
.takeout-section .merchant-scroll .merchant-item {
  /* 确保正确的布局 */
  display: inline-block !important;
  vertical-align: top !important;
  white-space: normal !important;

  /* 防止收缩 */
  flex-shrink: 0 !important;

  /* 优化渲染 */
  -webkit-transform: translateZ(0) !important;
  transform: translateZ(0) !important;
  will-change: auto !important;
}

/* 防止触摸事件冲突 */
.merchant-scroll,
.merchant-scroll * {
  /* 确保触摸事件正确传递 */
  touch-action: pan-x !important;

  /* 防止默认的触摸行为 */
  -webkit-touch-callout: none !important;
  -webkit-user-select: none !important;
  user-select: none !important;
}

/* 针对 H5 平台的特殊优化 */
/* #ifdef H5 */
uni-scroll-view,
.uni-scroll-view,
scroll-view {
  /* 强制使用 CSS 滚动而不是 JavaScript */
  overflow: auto !important;

  /* 禁用 JavaScript 滚动处理 */
  pointer-events: auto !important;

  /* 优化滚动性能 */
  scroll-snap-type: none !important;

  /* 防止滚动链 */
  overscroll-behavior: contain !important;
}

/* H5 平台水平滚动优化 */
uni-scroll-view[scroll-x],
scroll-view[scroll-x],
.merchant-scroll {
  /* 强制水平滚动 */
  overflow-x: auto !important;
  overflow-y: hidden !important;

  /* 防止垂直滚动链 */
  overscroll-behavior-y: none !important;
  overscroll-behavior-x: auto !important;
}
/* #endif */

/* 响应式优化 */
@media (max-width: 768px) {
  .merchant-scroll {
    /* 移动端优化 */
    -webkit-overflow-scrolling: touch !important;
    overflow-scrolling: touch !important;

    /* 防止缩放 */
    touch-action: pan-x !important;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  uni-scroll-view,
  .uni-scroll-view,
  scroll-view,
  .merchant-scroll {
    /* 深色模式下的优化 */
    color-scheme: dark !important;
  }
}

/* 高对比度模式适配 */
@media (prefers-contrast: high) {
  uni-scroll-view,
  .uni-scroll-view,
  scroll-view,
  .merchant-scroll {
    /* 高对比度模式下的优化 */
    forced-color-adjust: none !important;
  }
}

/* 减少动画的用户偏好 */
@media (prefers-reduced-motion: reduce) {
  uni-scroll-view,
  .uni-scroll-view,
  scroll-view,
  .merchant-scroll {
    /* 禁用滚动动画 */
    scroll-behavior: auto !important;
  }
}
