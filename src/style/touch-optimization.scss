/**
 * 触摸事件和滚动性能优化样式
 * 
 * 用于优化移动端触摸体验和滚动性能
 * 主要解决第三方组件库的性能问题
 */

/* 全局滚动优化 - 只对滚动容器应用优化 */
.scroll-view,
.coupon-list,
.promotion-list,
.virtual-list,
.message-list,
.favorites-list,
.loadmore-demo {
  /* 启用硬件加速 */
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  will-change: scroll-position;

  /* 优化滚动性能 */
  -webkit-overflow-scrolling: touch;
  overflow-scrolling: touch;

  /* 减少重绘 */
  contain: layout style paint;
}

/* 滚动容器优化 */
.scroll-view,
.coupon-list,
.promotion-list,
.virtual-list,
.message-list,
.favorites-list,
.loadmore-demo {
  /* 启用硬件加速 */
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  will-change: scroll-position;

  /* 优化滚动性能 */
  -webkit-overflow-scrolling: touch;
  overflow-scrolling: touch;

  /* 减少重绘 */
  contain: layout style paint;
}

/* uni-app scroll-view 特殊优化 */
uni-scroll-view,
.uni-scroll-view,
scroll-view,
.merchant-scroll {
  /* 启用硬件加速 */
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  will-change: scroll-position;

  /* 优化触摸滚动 */
  -webkit-overflow-scrolling: touch;
  overflow-scrolling: touch;

  /* 减少重绘和重排 */
  contain: layout style paint;

  /* 平滑滚动 */
  scroll-behavior: smooth;
}

/* 水平滚动的 scroll-view 特殊优化 */
uni-scroll-view[scroll-x],
.uni-scroll-view.scroll-x,
scroll-view[scroll-x],
.merchant-scroll {
  overflow-x: auto !important;
  overflow-y: hidden !important;
  white-space: nowrap !important;

  /* 隐藏滚动条但保持滚动功能 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */

  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }
}

/* TabBar 固定定位优化 */
.wd-tabbar,
.custom-tabbar,
.tabbar-container {
  /* 确保固定定位正常工作 */
  position: fixed !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  z-index: 1000 !important;

  /* 不应用全局的 transform，避免影响 fixed 定位 */
  -webkit-transform: none !important;
  transform: none !important;

  /* 优化渲染性能 */
  will-change: auto;
  contain: layout style;
}

/* 弹窗和加载组件优化 */
.wd-popup,
.wd-loadmore,
.optimized-popup-content,
.optimized-loadmore {
  /* 启用硬件加速 */
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  will-change: transform, opacity;

  /* 优化动画性能 */
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;

  /* 减少重绘 */
  contain: layout style paint;
}

/* 弹窗内容区域优化 */
.coupon-modal,
.promotion-modal {
  /* 启用硬件加速 */
  -webkit-transform: translateZ(0);
  transform: translateZ(0);

  /* 优化滚动 */
  -webkit-overflow-scrolling: touch;
  overflow-scrolling: touch;

  /* 减少重绘和重排 */
  contain: layout style paint;
}

/* 列表项优化 */
.coupon-item,
.promotion-item,
.virtual-list-item,
.favorite-item,
.demo-item {
  /* 启用硬件加速 */
  -webkit-transform: translateZ(0);
  transform: translateZ(0);

  /* 减少重绘 */
  contain: layout style paint;

  /* 优化点击响应 */
  -webkit-tap-highlight-color: transparent;
  tap-highlight-color: transparent;
}

/* 图标和按钮优化 */
.wd-icon,
.coupon-center-btn,
.confirm-btn {
  /* 启用硬件加速 */
  -webkit-transform: translateZ(0);
  transform: translateZ(0);

  /* 优化点击响应 */
  -webkit-tap-highlight-color: transparent;
  tap-highlight-color: transparent;

  /* 减少重绘 */
  contain: layout style paint;
}

/* 输入框和表单元素优化 */
input,
textarea,
.form-control {
  /* 优化触摸体验 */
  -webkit-tap-highlight-color: transparent;
  tap-highlight-color: transparent;

  /* 启用硬件加速 */
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
}

/* 动画优化 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

/* 弹窗动画优化 */
.wd-popup.wd-popup--bottom {
  animation: slideUp 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.optimized-popup-content {
  animation: fadeIn 0.3s ease-out;
}

/* 响应式优化 */
@media (max-width: 768px) {
  /* 移动端特定优化 */
  .scroll-view,
  .coupon-list,
  .promotion-list {
    /* 更激进的硬件加速 */
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);

    /* 优化滚动条 */
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  /* 优化触摸区域 */
  .coupon-item,
  .promotion-item {
    min-height: 44px; /* 符合移动端最小触摸区域标准 */

    /* 优化触摸反馈 */
    &:active {
      background-color: rgba(0, 0, 0, 0.05);
      transition: background-color 0.1s ease;
    }
  }
}

/* 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .wd-icon,
  .coupon-center-btn,
  .confirm-btn {
    /* 优化高分辨率屏幕的渲染 */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

/* 深色模式优化 */
@media (prefers-color-scheme: dark) {
  .coupon-item:active,
  .promotion-item:active {
    background-color: rgba(255, 255, 255, 0.1);
  }
}

/* 减少动画的用户偏好 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 页面底部间距，避免被 TabBar 遮挡 */
.page-container,
.uni-page-body,
body {
  padding-bottom: env(safe-area-inset-bottom, 0px) !important;
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom, 0px)) !important;
}

/* 有 TabBar 的页面需要额外的底部间距 */
.has-tabbar {
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom, 0px)) !important;
}

/* 确保滚动容器有正确的底部间距 */
.scroll-view,
.page-scroll {
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom, 0px)) !important;
}

/* 性能监控类 */
.performance-optimized {
  /* 标记已优化的元素 */
  --performance-optimized: true;

  /* 启用所有性能优化 */
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  will-change: auto;
  contain: layout style paint;
  -webkit-overflow-scrolling: touch;
  overflow-scrolling: touch;
}
