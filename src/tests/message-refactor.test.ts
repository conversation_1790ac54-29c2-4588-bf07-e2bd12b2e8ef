/**
 * 消息页面重构测试
 * 验证重构后的消息系统是否正常工作
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { createPinia, setActivePinia } from 'pinia'
import { useMessageStore } from '@/store/message'
import * as messageApi from '@/api/message'

// Mock API 调用
vi.mock('@/api/message', () => ({
  getMessageCategories: vi.fn(),
  getSystemNotifications: vi.fn(),
  getOrderNotifications: vi.fn(),
  getServiceMessages: vi.fn(),
  getUnreadCount: vi.fn(),
  markSystemNotificationAsRead: vi.fn(),
  markOrderNotificationAsRead: vi.fn(),
  searchMessages: vi.fn(),
}))

describe('消息系统重构测试', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
  })

  describe('消息 Store 测试', () => {
    it('应该正确初始化消息分类', async () => {
      const mockCategories = [
        {
          type: 'chat',
          title: '聊天消息',
          icon: 'chat',
          color: '#4D8EFF',
          unreadCount: 5,
          path: '/pages/chat/sessions/index'
        },
        {
          type: 'system',
          title: '系统通知',
          icon: 'notification',
          color: '#FF9500',
          unreadCount: 3,
          path: '/pages/message/system'
        }
      ]

      vi.mocked(messageApi.getMessageCategories).mockResolvedValue({
        data: mockCategories,
        code: 200,
        message: 'success'
      })

      const store = useMessageStore()
      await store.fetchMessageCategories()

      expect(store.messageCategories).toEqual(mockCategories)
      expect(messageApi.getMessageCategories).toHaveBeenCalledTimes(1)
    })

    it('应该正确处理字段名差异', async () => {
      const mockCategoriesWithSnakeCase = [
        {
          type: 'system',
          title: '系统通知',
          icon: 'notification',
          color: '#FF9500',
          unread_count: 10, // API 返回的是 snake_case
          path: '/pages/message/system'
        }
      ]

      vi.mocked(messageApi.getMessageCategories).mockResolvedValue({
        data: mockCategoriesWithSnakeCase,
        code: 200,
        message: 'success'
      })

      const store = useMessageStore()
      await store.fetchMessageCategories()

      // 验证字段名转换
      expect(store.messageCategories[0].unreadCount).toBe(10)
      expect(store.messageCategories[0]).not.toHaveProperty('unread_count')
    })

    it('应该正确获取系统通知', async () => {
      const mockNotifications = {
        list: [
          {
            id: '1',
            title: '系统维护通知',
            content: '系统将于今晚进行维护',
            type: 'system',
            isRead: false,
            createdAt: '2024-01-01T00:00:00Z'
          }
        ],
        total: 1,
        hasMore: false
      }

      vi.mocked(messageApi.getSystemNotifications).mockResolvedValue({
        data: mockNotifications,
        code: 200,
        message: 'success'
      })

      const store = useMessageStore()
      await store.fetchSystemNotifications()

      expect(store.systemNotifications).toEqual(mockNotifications.list)
      expect(store.pagination.systemNotifications.hasMore).toBe(false)
    })

    it('应该正确处理分页加载', async () => {
      const store = useMessageStore()
      
      // 第一页数据
      const firstPageData = {
        list: [{ id: '1', title: '消息1' }],
        total: 2,
        hasMore: true
      }

      vi.mocked(messageApi.getSystemNotifications).mockResolvedValueOnce({
        data: firstPageData,
        code: 200,
        message: 'success'
      })

      await store.fetchSystemNotifications()
      expect(store.systemNotifications).toHaveLength(1)
      expect(store.pagination.systemNotifications.hasMore).toBe(true)

      // 第二页数据
      const secondPageData = {
        list: [{ id: '2', title: '消息2' }],
        total: 2,
        hasMore: false
      }

      vi.mocked(messageApi.getSystemNotifications).mockResolvedValueOnce({
        data: secondPageData,
        code: 200,
        message: 'success'
      })

      await store.loadMoreSystemNotifications()
      expect(store.systemNotifications).toHaveLength(2)
      expect(store.pagination.systemNotifications.hasMore).toBe(false)
    })
  })

  describe('API 接口测试', () => {
    it('应该使用正确的接口路径调用系统通知', async () => {
      const store = useMessageStore()
      await store.fetchSystemNotifications()

      expect(messageApi.getSystemNotifications).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'system'
        })
      )
    })

    it('应该使用正确的接口路径调用订单通知', async () => {
      const store = useMessageStore()
      await store.fetchOrderNotifications()

      expect(messageApi.getOrderNotifications).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'order'
        })
      )
    })

    it('应该使用正确的接口路径调用客服消息', async () => {
      const store = useMessageStore()
      await store.fetchServiceMessages()

      expect(messageApi.getServiceMessages).toHaveBeenCalledWith(
        expect.objectContaining({
          category: 'service'
        })
      )
    })
  })

  describe('错误处理测试', () => {
    it('应该正确处理API错误', async () => {
      vi.mocked(messageApi.getMessageCategories).mockRejectedValue(
        new Error('网络错误')
      )

      const store = useMessageStore()
      
      await expect(store.fetchMessageCategories()).rejects.toThrow('网络错误')
      expect(store.loading.categories).toBe(false)
    })
  })
})
