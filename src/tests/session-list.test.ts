/**
 * 会话列表功能测试
 * @description 测试会话列表的获取和显示功能
 * <AUTHOR> Assistant
 * @date 2025-01-27
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { useMessageStore } from '@/store/message'
import * as messageApi from '@/api/message'
import type { ISessionListResponse, ISessionItem } from '@/api/message.typings'

// Mock API
vi.mock('@/api/message')

describe('会话列表功能测试', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('聊天会话列表', () => {
    it('应该正确获取聊天会话列表', async () => {
      const mockResponse: ISessionListResponse = {
        code: 200,
        message: 'success',
        data: {
          category: 'chat',
          list: [
            {
              id: 1,
              type: 'user_to_user',
              creator_id: 1,
              creator_type: 'user',
              receiver_id: 2,
              receiver_type: 'user',
              last_message_id: 10,
              unread_count: 3,
              status: 0,
              created_at: '2025-01-27T10:00:00Z',
              updated_at: '2025-01-27T12:00:00Z',
              last_message: {
                id: 10,
                session_id: 1,
                sender_id: 2,
                sender_type: 'user',
                content: '你好，最近怎么样？',
                type: 'text',
                resource_id: '',
                status: 1,
                created_at: '2025-01-27T12:00:00Z',
                sender_name: '张三',
                sender_avatar: 'http://example.com/avatar1.jpg'
              },
              target_name: '张三',
              target_avatar: 'http://example.com/avatar1.jpg'
            }
          ],
          page: 1,
          page_count: 1,
          page_size: 20,
          total: 1
        }
      }

      vi.mocked(messageApi.getChatSessions).mockResolvedValue(mockResponse)

      const store = useMessageStore()
      await store.fetchChatSessions(undefined, true)

      expect(store.chatSessions).toHaveLength(1)
      expect(store.chatSessions[0].target_name).toBe('张三')
      expect(store.chatSessions[0].unread_count).toBe(3)
      expect(messageApi.getChatSessions).toHaveBeenCalledWith({
        page: 1,
        pageSize: 20
      })
    })

    it('应该正确处理分页加载', async () => {
      const store = useMessageStore()
      
      // 第一页数据
      const firstPageResponse: ISessionListResponse = {
        code: 200,
        message: 'success',
        data: {
          category: 'chat',
          list: [
            {
              id: 1,
              type: 'user_to_user',
              creator_id: 1,
              creator_type: 'user',
              receiver_id: 2,
              receiver_type: 'user',
              last_message_id: 10,
              unread_count: 1,
              status: 0,
              created_at: '2025-01-27T10:00:00Z',
              updated_at: '2025-01-27T12:00:00Z',
              last_message: {
                id: 10,
                session_id: 1,
                sender_id: 2,
                sender_type: 'user',
                content: '第一条消息',
                type: 'text',
                resource_id: '',
                status: 1,
                created_at: '2025-01-27T12:00:00Z',
                sender_name: '用户1',
                sender_avatar: ''
              },
              target_name: '用户1',
              target_avatar: ''
            }
          ],
          page: 1,
          page_count: 2,
          page_size: 20,
          total: 2
        }
      }

      // 第二页数据
      const secondPageResponse: ISessionListResponse = {
        code: 200,
        message: 'success',
        data: {
          category: 'chat',
          list: [
            {
              id: 2,
              type: 'user_to_user',
              creator_id: 1,
              creator_type: 'user',
              receiver_id: 3,
              receiver_type: 'user',
              last_message_id: 20,
              unread_count: 0,
              status: 0,
              created_at: '2025-01-27T09:00:00Z',
              updated_at: '2025-01-27T11:00:00Z',
              last_message: {
                id: 20,
                session_id: 2,
                sender_id: 1,
                sender_type: 'user',
                content: '第二条消息',
                type: 'text',
                resource_id: '',
                status: 1,
                created_at: '2025-01-27T11:00:00Z',
                sender_name: '我',
                sender_avatar: ''
              },
              target_name: '用户2',
              target_avatar: ''
            }
          ],
          page: 2,
          page_count: 2,
          page_size: 20,
          total: 2
        }
      }

      vi.mocked(messageApi.getChatSessions)
        .mockResolvedValueOnce(firstPageResponse)
        .mockResolvedValueOnce(secondPageResponse)

      // 加载第一页
      await store.fetchChatSessions(undefined, true)
      expect(store.chatSessions).toHaveLength(1)

      // 加载更多（第二页）
      await store.loadMoreChatSessions()
      expect(store.chatSessions).toHaveLength(2)
      expect(store.chatSessions[1].target_name).toBe('用户2')
    })
  })

  describe('订单会话列表', () => {
    it('应该正确获取订单会话列表', async () => {
      const mockResponse: ISessionListResponse = {
        code: 200,
        message: 'success',
        data: {
          category: 'order',
          list: [
            {
              id: 3,
              type: 'user_to_merchant',
              creator_id: 1,
              creator_type: 'user',
              receiver_id: 100,
              receiver_type: 'merchant',
              last_message_id: 30,
              unread_count: 2,
              status: 0,
              created_at: '2025-01-27T08:00:00Z',
              updated_at: '2025-01-27T10:30:00Z',
              last_message: {
                id: 30,
                session_id: 3,
                sender_id: 100,
                sender_type: 'merchant',
                content: '您的订单已发货',
                type: 'text',
                resource_id: '',
                status: 1,
                created_at: '2025-01-27T10:30:00Z',
                sender_name: '商家客服',
                sender_avatar: 'http://example.com/merchant.jpg'
              },
              target_name: '某某商店',
              target_avatar: 'http://example.com/merchant.jpg'
            }
          ],
          page: 1,
          page_count: 1,
          page_size: 20,
          total: 1
        }
      }

      vi.mocked(messageApi.getOrderSessions).mockResolvedValue(mockResponse)

      const store = useMessageStore()
      await store.fetchOrderSessions(undefined, true)

      expect(store.orderSessions).toHaveLength(1)
      expect(store.orderSessions[0].target_name).toBe('某某商店')
      expect(store.orderSessions[0].unread_count).toBe(2)
      expect(store.orderSessions[0].last_message?.content).toBe('您的订单已发货')
    })
  })

  describe('客服会话列表', () => {
    it('应该正确获取客服会话列表', async () => {
      const mockResponse: ISessionListResponse = {
        code: 200,
        message: 'success',
        data: {
          category: 'service',
          list: [
            {
              id: 13,
              type: 'user_to_merchant',
              creator_id: 3,
              creator_type: 'user',
              receiver_id: 200,
              receiver_type: 'service',
              last_message_id: 187,
              unread_count: 8,
              status: 0,
              created_at: '2025-01-27T07:00:00Z',
              updated_at: '2025-01-27T09:45:00Z',
              last_message: {
                id: 187,
                session_id: 13,
                sender_id: 3,
                sender_type: 'user',
                content: '不知道',
                type: 'text',
                resource_id: '',
                status: 1,
                created_at: '2025-01-27T09:45:00Z',
                sender_name: 'songda',
                sender_avatar: 'http://omallimg.qwyx.shop/...'
              },
              target_name: 'songda',
              target_avatar: 'http://omallimg.qwyx.shop/...'
            }
          ],
          page: 1,
          page_count: 1,
          page_size: 20,
          total: 1
        }
      }

      vi.mocked(messageApi.getServiceSessions).mockResolvedValue(mockResponse)

      const store = useMessageStore()
      await store.fetchServiceSessions(undefined, true)

      expect(store.serviceSessions).toHaveLength(1)
      expect(store.serviceSessions[0].target_name).toBe('songda')
      expect(store.serviceSessions[0].unread_count).toBe(8)
      expect(store.serviceSessions[0].last_message?.content).toBe('不知道')
    })
  })

  describe('错误处理', () => {
    it('应该正确处理API错误', async () => {
      const error = new Error('网络错误')
      vi.mocked(messageApi.getChatSessions).mockRejectedValue(error)

      const store = useMessageStore()
      
      await expect(store.fetchChatSessions()).rejects.toThrow('网络错误')
      expect(store.loading.chatSessions).toBe(false)
    })
  })
})
