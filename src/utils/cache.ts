/**
 * 缓存管理工具
 *
 * 提供本地存储缓存功能，支持过期时间设置
 */

interface CacheItem<T> {
  data: T
  timestamp: number
  expireTime?: number
}

class CacheManager {
  private prefix: string

  constructor(prefix: string = 'omall_cache_') {
    this.prefix = prefix
  }

  /**
   * 设置缓存
   * @param key 缓存键
   * @param data 缓存数据
   * @param expireTime 过期时间（毫秒），不传则永不过期
   */
  set<T>(key: string, data: T, expireTime?: number): void {
    try {
      const cacheItem: CacheItem<T> = {
        data,
        timestamp: Date.now(),
        expireTime: expireTime ? Date.now() + expireTime : undefined,
      }

      uni.setStorageSync(this.prefix + key, JSON.stringify(cacheItem))
    } catch (error) {
      console.error('设置缓存失败:', error)
    }
  }

  /**
   * 获取缓存
   * @param key 缓存键
   * @returns 缓存数据，如果不存在或已过期则返回null
   */
  get<T>(key: string): T | null {
    try {
      const cacheStr = uni.getStorageSync(this.prefix + key)
      if (!cacheStr) {
        return null
      }

      const cacheItem: CacheItem<T> = JSON.parse(cacheStr)

      // 检查是否过期
      if (cacheItem.expireTime && Date.now() > cacheItem.expireTime) {
        this.remove(key)
        return null
      }

      return cacheItem.data
    } catch (error) {
      console.error('获取缓存失败:', error)
      return null
    }
  }

  /**
   * 删除缓存
   * @param key 缓存键
   */
  remove(key: string): void {
    try {
      uni.removeStorageSync(this.prefix + key)
    } catch (error) {
      console.error('删除缓存失败:', error)
    }
  }

  /**
   * 清空所有缓存
   */
  clear(): void {
    try {
      const info = uni.getStorageInfoSync()
      info.keys.forEach((key) => {
        if (key.startsWith(this.prefix)) {
          uni.removeStorageSync(key)
        }
      })
    } catch (error) {
      console.error('清空缓存失败:', error)
    }
  }

  /**
   * 检查缓存是否存在且未过期
   * @param key 缓存键
   * @returns 是否存在有效缓存
   */
  has(key: string): boolean {
    return this.get(key) !== null
  }

  /**
   * 获取缓存的时间戳
   * @param key 缓存键
   * @returns 缓存时间戳，如果不存在则返回null
   */
  getTimestamp(key: string): number | null {
    try {
      const cacheStr = uni.getStorageSync(this.prefix + key)
      if (!cacheStr) {
        return null
      }

      const cacheItem: CacheItem<any> = JSON.parse(cacheStr)
      return cacheItem.timestamp
    } catch (error) {
      console.error('获取缓存时间戳失败:', error)
      return null
    }
  }
}

// 创建默认的缓存管理器实例
export const cacheManager = new CacheManager()

// 会话列表专用缓存管理器
export const sessionCacheManager = new CacheManager('omall_session_')

// 导出缓存管理器类
export { CacheManager }

// 缓存键常量
export const CACHE_KEYS = {
  SESSION_LIST: 'session_list',
  USER_INFO: 'user_info',
  CHAT_MESSAGES: 'chat_messages_',
  FRIEND_LIST: 'friend_list',
  GROUP_LIST: 'group_list',
}
