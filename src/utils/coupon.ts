/**
 * 优惠券相关工具函数
 */

import type { ICoupon, IUserCoupon } from '@/api/coupon.typings'
import { CouponType, CouponStatus } from '@/api/coupon.typings'

/**
 * 格式化优惠券金额显示
 */
export const formatCouponAmount = (coupon: ICoupon | IUserCoupon) => {
  const actualCoupon = 'coupon' in coupon ? coupon.coupon : coupon
  const { type, amount } = actualCoupon

  switch (type) {
    case CouponType.DISCOUNT:
      return `¥${amount}`
    case CouponType.PERCENTAGE:
      return `${amount * 10}折`
    case CouponType.FREE_DELIVERY:
      return '免配送费'
    case CouponType.CASHBACK:
      return `¥${amount}`
    default:
      return `¥${amount}`
  }
}

/**
 * 获取优惠券类型文本
 */
export const getCouponTypeText = (type: CouponType) => {
  switch (type) {
    case CouponType.DISCOUNT:
      return '满减券'
    case CouponType.PERCENTAGE:
      return '折扣券'
    case CouponType.FREE_DELIVERY:
      return '免配送费券'
    case CouponType.GIFT:
      return '赠品券'
    case CouponType.CASHBACK:
      return '返现券'
    default:
      return '优惠券'
  }
}

/**
 * 获取优惠券状态文本
 */
export const getCouponStatusText = (status: CouponStatus) => {
  switch (status) {
    case CouponStatus.UNUSED:
      return '未使用'
    case CouponStatus.USED:
      return '已使用'
    case CouponStatus.EXPIRED:
      return '已过期'
    case CouponStatus.INVALID:
      return '已失效'
    default:
      return '未知状态'
  }
}

/**
 * 获取优惠券描述文本
 */
export const getCouponDescription = (coupon: ICoupon | IUserCoupon) => {
  const actualCoupon = 'coupon' in coupon ? coupon.coupon : coupon
  const { type, amount, min_order_amount } = actualCoupon

  switch (type) {
    case CouponType.DISCOUNT:
      return `满¥${min_order_amount}减¥${amount}`
    case CouponType.PERCENTAGE:
      return `满¥${min_order_amount}享${amount * 10}折`
    case CouponType.FREE_DELIVERY:
      return `满¥${min_order_amount}免配送费`
    case CouponType.CASHBACK:
      return `满¥${min_order_amount}返现¥${amount}`
    default:
      return actualCoupon.description || `满¥${min_order_amount}可用`
  }
}

/**
 * 计算优惠券折扣金额
 */
export const calculateCouponDiscount = (
  coupon: ICoupon | IUserCoupon,
  orderAmount: number,
): number => {
  const actualCoupon = 'coupon' in coupon ? coupon.coupon : coupon
  const { type, amount, min_order_amount, max_discount_amount } = actualCoupon

  // 检查是否满足最低订单金额
  if (orderAmount < min_order_amount) {
    return 0
  }

  switch (type) {
    case CouponType.DISCOUNT:
      return Math.min(amount, orderAmount)

    case CouponType.PERCENTAGE:
      const discountAmount = orderAmount * (1 - amount)
      return max_discount_amount ? Math.min(discountAmount, max_discount_amount) : discountAmount

    case CouponType.FREE_DELIVERY:
      // 免配送费券的折扣金额需要根据实际配送费计算
      return 0 // 这里返回0，实际折扣在配送费计算中处理

    case CouponType.CASHBACK:
      return amount

    default:
      return 0
  }
}

/**
 * 检查优惠券是否可用
 */
export const isCouponAvailable = (
  coupon: ICoupon | IUserCoupon,
  orderAmount: number,
  merchantId?: number,
  productIds?: number[],
): { available: boolean; reason?: string } => {
  const actualCoupon = 'coupon' in coupon ? coupon.coupon : coupon
  const userCoupon = 'coupon' in coupon ? (coupon as IUserCoupon) : null

  // 检查用户优惠券状态
  if (userCoupon) {
    if (userCoupon.status !== CouponStatus.UNUSED) {
      return {
        available: false,
        reason: getCouponStatusText(userCoupon.status),
      }
    }

    // 检查是否过期（检查实际过期时间）
    const now = new Date()
    const expireTime =
      userCoupon.expire_time || userCoupon.coupon?.end_time || actualCoupon.end_time
    if (expireTime) {
      const expireDate = new Date(expireTime)
      if (now > expireDate) {
        return {
          available: false,
          reason: '优惠券已过期',
        }
      }
    }
  }

  // 检查最低订单金额
  if (orderAmount < actualCoupon.min_order_amount) {
    return {
      available: false,
      reason: `订单金额需满¥${actualCoupon.min_order_amount}`,
    }
  }

  // 检查商家限制
  if (actualCoupon.merchant_id && merchantId && actualCoupon.merchant_id !== merchantId) {
    return {
      available: false,
      reason: '该优惠券不适用于当前商家',
    }
  }

  // 检查商品限制
  if (actualCoupon.product_ids && actualCoupon.product_ids.length > 0 && productIds) {
    const hasValidProduct = productIds.some((id) => actualCoupon.product_ids!.includes(id))
    if (!hasValidProduct) {
      return {
        available: false,
        reason: '该优惠券不适用于当前商品',
      }
    }
  }

  return { available: true }
}

/**
 * 格式化过期时间
 */
export const formatExpireTime = (expireTime: string) => {
  if (!expireTime) {
    return '无有效期信息'
  }

  const date = new Date(expireTime)
  if (isNaN(date.getTime())) {
    return '无效日期'
  }

  const now = new Date()
  const diffDays = Math.ceil((date.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))

  if (diffDays < 0) {
    return '已过期'
  } else if (diffDays === 0) {
    return '今日过期'
  } else if (diffDays <= 3) {
    return `${diffDays}天后过期`
  } else {
    return `有效期至${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
  }
}

/**
 * 获取优惠券颜色主题
 */
export const getCouponTheme = (type: CouponType) => {
  switch (type) {
    case CouponType.DISCOUNT:
      return {
        primary: '#ff5500',
        secondary: '#ff7700',
        background: 'linear-gradient(135deg, #ff5500, #ff7700)',
      }
    case CouponType.PERCENTAGE:
      return {
        primary: '#00c851',
        secondary: '#00a843',
        background: 'linear-gradient(135deg, #00c851, #00a843)',
      }
    case CouponType.FREE_DELIVERY:
      return {
        primary: '#007bff',
        secondary: '#0056b3',
        background: 'linear-gradient(135deg, #007bff, #0056b3)',
      }
    case CouponType.GIFT:
      return {
        primary: '#e91e63',
        secondary: '#c2185b',
        background: 'linear-gradient(135deg, #e91e63, #c2185b)',
      }
    case CouponType.CASHBACK:
      return {
        primary: '#9c27b0',
        secondary: '#7b1fa2',
        background: 'linear-gradient(135deg, #9c27b0, #7b1fa2)',
      }
    default:
      return {
        primary: '#6c757d',
        secondary: '#545b62',
        background: 'linear-gradient(135deg, #6c757d, #545b62)',
      }
  }
}

/**
 * 排序优惠券（按优惠金额降序）
 */
export const sortCouponsByDiscount = (coupons: (ICoupon | IUserCoupon)[], orderAmount: number) => {
  return coupons.sort((a, b) => {
    const discountA = calculateCouponDiscount(a, orderAmount)
    const discountB = calculateCouponDiscount(b, orderAmount)
    return discountB - discountA
  })
}

/**
 * 获取最优优惠券
 */
export const getBestCoupon = (
  coupons: (ICoupon | IUserCoupon)[],
  orderAmount: number,
  merchantId?: number,
  productIds?: number[],
) => {
  const availableCoupons = coupons.filter((coupon) => {
    const { available } = isCouponAvailable(coupon, orderAmount, merchantId, productIds)
    return available
  })

  if (availableCoupons.length === 0) {
    return null
  }

  const sortedCoupons = sortCouponsByDiscount(availableCoupons, orderAmount)
  return sortedCoupons[0]
}
