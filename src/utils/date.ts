/**
 * 日期工具函数
 * 提供统一的日期格式化功能
 */

/**
 * 格式化日期
 * @param date 日期字符串或Date对象
 * @param format 格式化模板，默认为 'YYYY-MM-DD'
 * @returns 格式化后的日期字符串
 */
export const formatDate = (date: string | Date, format: string = 'YYYY-MM-DD'): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date

  if (isNaN(dateObj.getTime())) {
    return ''
  }

  const year = dateObj.getFullYear()
  const month = String(dateObj.getMonth() + 1).padStart(2, '0')
  const day = String(dateObj.getDate()).padStart(2, '0')
  const hours = String(dateObj.getHours()).padStart(2, '0')
  const minutes = String(dateObj.getMinutes()).padStart(2, '0')
  const seconds = String(dateObj.getSeconds()).padStart(2, '0')

  return format
    .replace('YYYY', String(year))
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
}

/**
 * 获取相对时间描述
 * @param date 日期字符串或Date对象
 * @returns 相对时间描述，如"刚刚"、"5分钟前"等
 */
export const getRelativeTime = (date: string | Date): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  const now = new Date()
  const diff = now.getTime() - dateObj.getTime()

  if (diff < 60000) {
    // 1分钟内
    return '刚刚'
  } else if (diff < 3600000) {
    // 1小时内
    return `${Math.floor(diff / 60000)}分钟前`
  } else if (diff < 86400000) {
    // 1天内
    return `${Math.floor(diff / 3600000)}小时前`
  } else if (diff < 2592000000) {
    // 30天内
    return `${Math.floor(diff / 86400000)}天前`
  } else {
    return formatDate(dateObj, 'YYYY-MM-DD')
  }
}

/**
 * 判断是否为今天
 * @param date 日期字符串或Date对象
 * @returns 是否为今天
 */
export const isToday = (date: string | Date): boolean => {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  const today = new Date()

  return (
    dateObj.getFullYear() === today.getFullYear() &&
    dateObj.getMonth() === today.getMonth() &&
    dateObj.getDate() === today.getDate()
  )
}

/**
 * 判断是否为昨天
 * @param date 日期字符串或Date对象
 * @returns 是否为昨天
 */
export const isYesterday = (date: string | Date): boolean => {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  const yesterday = new Date()
  yesterday.setDate(yesterday.getDate() - 1)

  return (
    dateObj.getFullYear() === yesterday.getFullYear() &&
    dateObj.getMonth() === yesterday.getMonth() &&
    dateObj.getDate() === yesterday.getDate()
  )
}

/**
 * 格式化时间
 * @param date 日期字符串或Date对象或时间戳
 * @param format 格式化模板，默认为 'HH:mm'
 * @returns 格式化后的时间字符串
 */
export const formatTime = (
  date: string | Date | number | undefined | null,
  format: string = 'HH:mm',
): string => {
  // 检查输入是否为空
  if (date === undefined || date === null) {
    return ''
  }

  let dateObj: Date

  if (typeof date === 'number') {
    dateObj = new Date(date)
  } else if (typeof date === 'string') {
    // 检查字符串是否为空
    if (date.trim() === '') {
      return ''
    }
    dateObj = new Date(date)
  } else {
    dateObj = date
  }

  // 检查dateObj是否为有效的Date对象
  if (!dateObj || isNaN(dateObj.getTime())) {
    return ''
  }

  const hours = String(dateObj.getHours()).padStart(2, '0')
  const minutes = String(dateObj.getMinutes()).padStart(2, '0')
  const seconds = String(dateObj.getSeconds()).padStart(2, '0')

  return format.replace('HH', hours).replace('mm', minutes).replace('ss', seconds)
}

/**
 * 格式化相对时间
 * @param date 日期字符串或Date对象或时间戳
 * @returns 相对时间描述
 */
export const formatRelativeTime = (date: string | Date | number): string => {
  return getRelativeTime(date)
}
