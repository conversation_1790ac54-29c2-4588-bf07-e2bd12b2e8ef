/**
 * 防抖工具函数
 *
 * 防抖是指在事件被触发n秒后再执行回调，如果在这n秒内又被触发，则重新计时
 * 常用于搜索框输入、按钮点击等场景
 */

/**
 * 防抖函数
 * @param func 要执行的函数
 * @param delay 延迟时间（毫秒）
 * @param immediate 是否立即执行
 * @returns 防抖后的函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number,
  immediate: boolean = false,
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout | null = null
  let isInvoked = false

  return function (this: any, ...args: Parameters<T>) {
    const callNow = immediate && !isInvoked

    // 清除之前的定时器
    if (timeoutId) {
      clearTimeout(timeoutId)
    }

    if (callNow) {
      // 立即执行
      func.apply(this, args)
      isInvoked = true
    }

    // 设置新的定时器
    timeoutId = setTimeout(() => {
      if (!immediate) {
        func.apply(this, args)
      }
      isInvoked = false
      timeoutId = null
    }, delay)
  }
}

/**
 * 节流函数
 * @param func 要执行的函数
 * @param delay 延迟时间（毫秒）
 * @returns 节流后的函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  delay: number,
): (...args: Parameters<T>) => void {
  let lastExecTime = 0
  let timeoutId: NodeJS.Timeout | null = null

  return function (this: any, ...args: Parameters<T>) {
    const currentTime = Date.now()

    if (currentTime - lastExecTime > delay) {
      // 立即执行
      func.apply(this, args)
      lastExecTime = currentTime
    } else {
      // 延迟执行
      if (timeoutId) {
        clearTimeout(timeoutId)
      }
      timeoutId = setTimeout(
        () => {
          func.apply(this, args)
          lastExecTime = Date.now()
          timeoutId = null
        },
        delay - (currentTime - lastExecTime),
      )
    }
  }
}
