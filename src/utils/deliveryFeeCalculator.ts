/**
 * 配送费计算工具
 *
 * 统一的配送费计算逻辑，支持：
 * - 基于距离的配送费计算
 * - 满额免配送费
 * - 满额配送费折扣
 * - 详细的计算过程日志
 */

import { calculateDistance } from './distance'
import { getDeliveryConfig, type IDeliveryConfig } from '@/api/delivery'

export interface DeliveryFeeCalculationParams {
  merchantId: number
  merchantName?: string
  merchantLat?: number
  merchantLng?: number
  userLat?: number
  userLng?: number
  orderAmount?: number
  // 新增：计算场景标识
  scenario?: 'merchant_detail' | 'cart' | 'checkout'
  // 新增：地址信息（用于调试）
  addressInfo?: {
    id?: number
    isDefault?: boolean
    address?: string
    source?: 'default' | 'first_available' | 'manual'
  }
}

export interface DeliveryFeeResult {
  deliveryFee: number
  originalFee: number
  distance: number
  freeDelivery: boolean
  discounted: boolean
  debugInfo: string[]
}

/**
 * 计算配送费
 */
export async function calculateDeliveryFee(
  params: DeliveryFeeCalculationParams,
): Promise<DeliveryFeeResult> {
  const debugInfo: string[] = []
  const startTime = Date.now()

  try {
    debugInfo.push(`🚚 开始计算配送费 - 商家ID: ${params.merchantId}`)
    debugInfo.push(`📱 计算场景: ${params.scenario || 'merchant_detail'}`)
    debugInfo.push(`🏪 商家名称: ${params.merchantName || '未知'}`)
    debugInfo.push(`⏰ 计算时间: ${new Date().toLocaleString()}`)

    // 获取配送费配置
    const config = await getDeliveryConfig()
    debugInfo.push(`⚙️ 配送费配置获取成功`)
    debugInfo.push(`📍 基础配送费: ¥${config.deliveryBaseFee}`)
    debugInfo.push(`📏 距离费用: ¥${config.deliveryKmFee}/km`)
    debugInfo.push(
      `🆓 免费门槛: ¥${config.deliveryFreeAmount} (${config.deliveryFreeEnabled ? '启用' : '禁用'})`,
    )
    debugInfo.push(
      `💸 折扣门槛: ¥${config.deliveryDiscountAmount} (${config.deliveryDiscountEnabled ? '启用' : '禁用'})`,
    )
    debugInfo.push(`📊 折扣比例: ${(config.deliveryDiscountRate * 100).toFixed(0)}%`)

    // 记录地址信息
    if (params.addressInfo) {
      debugInfo.push(`📍 使用地址信息:`)
      debugInfo.push(`   - 地址ID: ${params.addressInfo.id || '未知'}`)
      debugInfo.push(`   - 是否默认: ${params.addressInfo.isDefault ? '是' : '否'}`)
      debugInfo.push(`   - 地址来源: ${params.addressInfo.source || '未知'}`)
      debugInfo.push(`   - 详细地址: ${params.addressInfo.address || '未知'}`)
    }

    // 计算距离
    let distance = 0
    if (params.merchantLat && params.merchantLng && params.userLat && params.userLng) {
      distance = calculateDistance(
        params.userLat,
        params.userLng,
        params.merchantLat,
        params.merchantLng,
      )
      debugInfo.push(`📏 配送距离: ${distance.toFixed(2)}km`)
      debugInfo.push(`🏠 用户坐标: (${params.userLat.toFixed(6)}, ${params.userLng.toFixed(6)})`)
      debugInfo.push(
        `🏪 商家坐标: (${params.merchantLat.toFixed(6)}, ${params.merchantLng.toFixed(6)})`,
      )
    } else {
      debugInfo.push(`⚠️ 坐标信息不完整，无法计算距离`)
      debugInfo.push(
        `   - 用户坐标: ${params.userLat ? `(${params.userLat}, ${params.userLng})` : '缺失'}`,
      )
      debugInfo.push(
        `   - 商家坐标: ${params.merchantLat ? `(${params.merchantLat}, ${params.merchantLng})` : '缺失'}`,
      )
    }

    // 计算基础配送费
    let originalFee = config.deliveryBaseFee
    let deliveryFee = originalFee

    // 基于距离计算配送费
    if (config.deliveryKmFee > 0 && distance > 0) {
      const freeDistance = 3 // 免费配送距离3km
      if (distance > freeDistance) {
        const extraDistance = distance - freeDistance
        const extraDistanceCeil = Math.ceil(extraDistance)
        const extraFee = extraDistanceCeil * config.deliveryKmFee
        originalFee = config.deliveryBaseFee + extraFee
        deliveryFee = originalFee

        debugInfo.push(
          `📐 距离计算: 超出${freeDistance}km，额外${extraDistance.toFixed(2)}km，向上取整${extraDistanceCeil}km`,
        )
        debugInfo.push(
          `💰 距离费用: ¥${config.deliveryBaseFee} + ${extraDistanceCeil}km × ¥${config.deliveryKmFee}/km = ¥${originalFee.toFixed(2)}`,
        )
      } else {
        debugInfo.push(`📐 距离${distance}km在免费范围内(${freeDistance}km)`)
      }
    }

    // 检查订单金额优惠
    const orderAmount = params.orderAmount || 0
    const scenario = params.scenario || 'merchant_detail'
    let freeDelivery = false
    let discounted = false

    // 根据场景决定是否应用订单金额优惠
    if (scenario === 'merchant_detail') {
      // 商家详情页：不应用订单金额优惠，显示基础配送费
      debugInfo.push(`📱 计算场景: 商家详情页，不应用订单金额优惠`)
      debugInfo.push(`💰 订单金额: 暂无（商家详情页）`)
    } else if (orderAmount > 0) {
      // 购物车/结算页：应用订单金额优惠
      debugInfo.push(`📱 计算场景: ${scenario === 'cart' ? '购物车' : '结算页'}`)
      debugInfo.push(`💰 订单金额: ¥${orderAmount.toFixed(2)}`)

      // 满额免配送费
      if (config.deliveryFreeEnabled && orderAmount >= config.deliveryFreeAmount) {
        deliveryFee = 0
        freeDelivery = true
        debugInfo.push(`🎉 满¥${config.deliveryFreeAmount}免配送费，配送费: ¥0.00`)
      }
      // 满额配送费折扣
      else if (config.deliveryDiscountEnabled && orderAmount >= config.deliveryDiscountAmount) {
        deliveryFee = originalFee * config.deliveryDiscountRate
        discounted = true
        debugInfo.push(
          `🎊 满¥${config.deliveryDiscountAmount}配送费${(config.deliveryDiscountRate * 100).toFixed(0)}%折扣`,
        )
        debugInfo.push(
          `💸 折扣后配送费: ¥${originalFee.toFixed(2)} × ${config.deliveryDiscountRate} = ¥${deliveryFee.toFixed(2)}`,
        )
      }
    } else {
      debugInfo.push(`📱 计算场景: ${scenario === 'cart' ? '购物车' : '结算页'}`)
      debugInfo.push(`💰 订单金额: 未提供，跳过金额优惠计算`)
    }

    const endTime = Date.now()
    const calculationTime = endTime - startTime

    debugInfo.push(`✅ 最终配送费: ¥${deliveryFee.toFixed(2)}`)
    debugInfo.push(`⏱️ 计算耗时: ${calculationTime}ms`)
    debugInfo.push(`📋 计算摘要:`)
    debugInfo.push(`   - 基础费用: ¥${config.deliveryBaseFee}`)
    debugInfo.push(`   - 距离费用: ¥${(originalFee - config.deliveryBaseFee).toFixed(2)}`)
    debugInfo.push(`   - 优惠金额: ¥${(originalFee - deliveryFee).toFixed(2)}`)
    debugInfo.push(
      `   - 节省比例: ${originalFee > 0 ? (((originalFee - deliveryFee) / originalFee) * 100).toFixed(1) : 0}%`,
    )

    return {
      deliveryFee: Math.round(deliveryFee * 100) / 100, // 保留2位小数
      originalFee: Math.round(originalFee * 100) / 100,
      distance: Math.round(distance * 100) / 100,
      freeDelivery,
      discounted,
      debugInfo,
    }
  } catch (error) {
    debugInfo.push(`❌ 配送费计算失败: ${error}`)
    console.error('配送费计算失败:', error)

    // 返回默认配送费
    return {
      deliveryFee: 5.0,
      originalFee: 5.0,
      distance: 0,
      freeDelivery: false,
      discounted: false,
      debugInfo,
    }
  }
}

/**
 * 格式化配送费显示文本
 */
export function formatDeliveryFeeText(result: DeliveryFeeResult): string {
  if (result.freeDelivery) {
    return '免配送费'
  }

  if (result.discounted) {
    return `配送费¥${result.deliveryFee.toFixed(2)}(已优惠)`
  }

  return `配送费¥${result.deliveryFee.toFixed(2)}`
}

/**
 * 获取配送费提示信息
 */
export function getDeliveryFeeTip(
  result: DeliveryFeeResult,
  config: IDeliveryConfig,
  orderAmount: number = 0,
): string {
  if (result.freeDelivery) {
    return '已享受免配送费优惠'
  }

  if (result.discounted) {
    return `已享受配送费${(config.deliveryDiscountRate * 100).toFixed(0)}%折扣`
  }

  // 提示用户可以享受的优惠
  if (config.deliveryFreeEnabled && orderAmount > 0) {
    const remaining = config.deliveryFreeAmount - orderAmount
    if (remaining > 0) {
      return `再购¥${remaining.toFixed(2)}即可免配送费`
    }
  }

  if (config.deliveryDiscountEnabled && orderAmount > 0) {
    const remaining = config.deliveryDiscountAmount - orderAmount
    if (remaining > 0) {
      return `再购¥${remaining.toFixed(2)}即可享受配送费${(config.deliveryDiscountRate * 100).toFixed(0)}%折扣`
    }
  }

  return ''
}

/**
 * 打印配送费计算调试信息
 */
export function logDeliveryFeeDebug(result: DeliveryFeeResult, merchantName?: string) {
  console.group(`🚚 配送费计算调试信息${merchantName ? ` - ${merchantName}` : ''}`)
  result.debugInfo.forEach((info) => console.log(info))
  console.groupEnd()
}
