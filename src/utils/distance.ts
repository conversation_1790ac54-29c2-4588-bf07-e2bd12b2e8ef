/**
 * 距离计算工具函数
 */

/**
 * 计算两点之间的距离（使用Haversine公式）
 * @param lat1 第一个点的纬度
 * @param lng1 第一个点的经度
 * @param lat2 第二个点的纬度
 * @param lng2 第二个点的经度
 * @returns 距离（公里）
 */
export function calculateDistance(lat1: number, lng1: number, lat2: number, lng2: number): number {
  // 地球半径（公里）
  const R = 6371

  // 将角度转换为弧度
  const dLat = toRadians(lat2 - lat1)
  const dLng = toRadians(lng2 - lng1)

  // Haversine公式
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(toRadians(lat1)) * Math.cos(toRadians(lat2)) * Math.sin(dLng / 2) * Math.sin(dLng / 2)

  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))

  // 距离（公里）
  const distance = R * c

  return Math.round(distance * 100) / 100 // 保留2位小数
}

/**
 * 将角度转换为弧度
 * @param degrees 角度
 * @returns 弧度
 */
function toRadians(degrees: number): number {
  return degrees * (Math.PI / 180)
}

/**
 * 格式化距离显示
 * @param distance 距离（公里）
 * @returns 格式化的距离字符串
 */
export function formatDistance(distance: number): string {
  if (distance < 1) {
    return `${Math.round(distance * 1000)}m`
  } else {
    return `${distance.toFixed(1)}km`
  }
}

/**
 * 检查距离是否在配送范围内
 * @param distance 距离（公里）
 * @param maxDistance 最大配送距离（公里）
 * @returns 是否在配送范围内
 */
export function isWithinDeliveryRange(distance: number, maxDistance: number): boolean {
  return distance <= maxDistance
}

/**
 * 根据距离计算配送费
 * @param distance 距离（公里）
 * @param baseFee 基础配送费
 * @param freeDistance 免费配送距离（公里）
 * @param extraFeePerKm 超出免费距离后每公里的额外费用
 * @returns 配送费
 */
export function calculateDistanceBasedDeliveryFee(
  distance: number,
  baseFee: number = 5,
  freeDistance: number = 3,
  extraFeePerKm: number = 2,
): number {
  if (distance <= freeDistance) {
    return baseFee
  } else {
    const extraDistance = distance - freeDistance
    const extraFee = Math.ceil(extraDistance) * extraFeePerKm
    return baseFee + extraFee
  }
}

/**
 * 获取配送距离提示文本
 * @param distance 距离（公里）
 * @param maxDistance 最大配送距离（公里）
 * @returns 提示文本
 */
export function getDeliveryDistanceTip(distance: number, maxDistance: number): string {
  if (distance > maxDistance) {
    return `超出配送范围，最大配送距离${maxDistance}km`
  } else if (distance > maxDistance * 0.8) {
    return `距离较远，配送时间可能较长`
  } else {
    return `配送距离${formatDistance(distance)}`
  }
}
