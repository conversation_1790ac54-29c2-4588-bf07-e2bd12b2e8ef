/**
 * 距离格式化工具
 *
 * 提供统一的距离显示格式化功能
 */

/**
 * 格式化距离显示
 * @param distance 距离（单位：km）
 * @returns 格式化后的距离字符串
 */
export function formatDistance(distance: number): string {
  if (distance <= 0) {
    return '距离未知'
  }

  if (distance < 0.1) {
    // 小于100m显示"附近"
    return '附近'
  } else if (distance < 1) {
    // 小于1km显示米数
    const meters = Math.round(distance * 1000)
    return `${meters}m`
  } else if (distance < 10) {
    // 1-10km显示一位小数
    return `${distance.toFixed(1)}km`
  } else if (distance < 100) {
    // 10-100km显示整数
    return `${distance.toFixed(0)}km`
  } else {
    // 大于100km显示"较远"
    return '较远'
  }
}

/**
 * 格式化距离显示（详细版本，用于调试）
 * @param distance 距离（单位：km）
 * @returns 格式化后的距离字符串
 */
export function formatDistanceDetailed(distance: number): string {
  if (distance <= 0) {
    return '距离未知'
  }

  if (distance < 1) {
    // 小于1km显示米数
    const meters = Math.round(distance * 1000)
    return `${meters}m (${distance.toFixed(3)}km)`
  } else {
    // 大于1km显示精确到小数点后2位
    return `${distance.toFixed(2)}km`
  }
}

/**
 * 获取距离状态
 * @param distance 距离（单位：km）
 * @returns 距离状态
 */
export function getDistanceStatus(
  distance: number,
): 'nearby' | 'close' | 'medium' | 'far' | 'very_far' | 'unknown' {
  if (distance <= 0) {
    return 'unknown'
  } else if (distance < 0.5) {
    return 'nearby'
  } else if (distance < 2) {
    return 'close'
  } else if (distance < 5) {
    return 'medium'
  } else if (distance < 10) {
    return 'far'
  } else {
    return 'very_far'
  }
}

/**
 * 获取距离状态对应的颜色
 * @param distance 距离（单位：km）
 * @returns CSS颜色值
 */
export function getDistanceColor(distance: number): string {
  const status = getDistanceStatus(distance)

  switch (status) {
    case 'nearby':
      return '#52c41a' // 绿色 - 很近
    case 'close':
      return '#1890ff' // 蓝色 - 较近
    case 'medium':
      return '#faad14' // 橙色 - 中等
    case 'far':
      return '#fa8c16' // 深橙色 - 较远
    case 'very_far':
      return '#f5222d' // 红色 - 很远
    case 'unknown':
    default:
      return '#666666' // 灰色 - 未知
  }
}

/**
 * 获取距离描述文本
 * @param distance 距离（单位：km）
 * @returns 距离描述
 */
export function getDistanceDescription(distance: number): string {
  const status = getDistanceStatus(distance)

  switch (status) {
    case 'nearby':
      return '就在附近'
    case 'close':
      return '距离较近'
    case 'medium':
      return '距离适中'
    case 'far':
      return '距离较远'
    case 'very_far':
      return '距离很远'
    case 'unknown':
    default:
      return '距离未知'
  }
}

/**
 * 估算配送时间（基于距离）
 * @param distance 距离（单位：km）
 * @param baseTime 基础配送时间（分钟）
 * @returns 估算的配送时间（分钟）
 */
export function estimateDeliveryTime(distance: number, baseTime: number = 30): number {
  if (distance <= 0) {
    return baseTime
  }

  // 每公里增加2-3分钟配送时间
  const extraTime = Math.ceil(distance * 2.5)
  return baseTime + extraTime
}

/**
 * 格式化配送时间显示
 * @param minutes 配送时间（分钟）
 * @returns 格式化后的时间字符串
 */
export function formatDeliveryTime(minutes: number): string {
  if (minutes <= 0) {
    return '时间未知'
  }

  if (minutes < 60) {
    return `${minutes}分钟`
  } else {
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    if (remainingMinutes === 0) {
      return `${hours}小时`
    } else {
      return `${hours}小时${remainingMinutes}分钟`
    }
  }
}
