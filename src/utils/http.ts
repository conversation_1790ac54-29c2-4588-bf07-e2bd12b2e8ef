/**
 * HTTP 客户端适配器
 *
 * 该文件是为了兼容现有代码而创建的适配器，将所有 http 请求转发到 request.ts
 * 这样可以确保所有 API 调用都使用统一的 token 刷新机制
 */

import { request, get, post, put, del } from '@/utils/request'
import { CustomRequestOptions } from '@/interceptors/request'

// 定义响应数据接口，与原 http.ts 保持一致
export interface IResData<T> {
  code: number
  message: string
  data: T
}

/**
 * HTTP 客户端函数调用方式
 * 支持 http<T>({...}) 的调用方式
 */
function httpFunction<T>(options: CustomRequestOptions): Promise<T> {
  return request<T>(options)
}

/**
 * HTTP 客户端
 *
 * 这是一个适配器，将所有请求转发到 request.ts
 * 为了保持兼容性，保留了原有的 API 接口
 */
export const http = Object.assign(httpFunction, {
  /**
   * GET 请求
   * @param url 请求地址
   * @param params 请求参数
   * @returns Promise
   */
  get: <T>(url: string, params?: any) => {
    return get<IResData<T>>(url, params)
  },

  /**
   * POST 请求
   * @param url 请求地址
   * @param data 请求数据
   * @returns Promise
   */
  post: <T>(url: string, data?: any) => {
    return post<IResData<T>>(url, data)
  },

  /**
   * PUT 请求
   * @param url 请求地址
   * @param data 请求数据
   * @returns Promise
   */
  put: <T>(url: string, data?: any) => {
    return put<IResData<T>>(url, data)
  },

  /**
   * DELETE 请求
   * @param url 请求地址
   * @param data 请求数据
   * @returns Promise
   */
  delete: <T>(url: string, data?: any) => {
    return del<IResData<T>>(url, data)
  },
})
