# 🔧 登录页面无限循环问题修复

## 🔍 问题分析

### 问题现象

登录页面出现不停刷新的情况，控制台报错：

```
[SystemStore] 获取外卖全局分类失败: Error: token已过期且刷新失败
检测到token过期，尝试刷新...
没有refreshToken，无法刷新token
```

### 问题根源

1. **API 认证配置错误**: 登录页面调用 `initSystemData()` 时，尝试获取外卖全局分类数据
2. **全局分类 API 需要认证**: `/api/v1/merchant/merchant-categories` 不在 `noTokenUrls` 列表中
3. **用户未登录**: 登录页面时用户没有 token 和 refreshToken
4. **无限循环**: token 刷新失败 → 跳转到登录页 → 重新加载 → 再次调用 API → 循环

### 循环流程

```
登录页面加载
    ↓
调用 initSystemData()
    ↓
获取外卖全局分类 (需要认证)
    ↓
检测到 token 过期
    ↓
尝试刷新 token (没有 refreshToken)
    ↓
刷新失败，跳转到登录页
    ↓
登录页面重新加载 (循环开始)
```

## 🛠️ 修复方案

### 1. 将公开 API 添加到免认证列表

**问题**: 全局分类、商家列表等应该是公开数据，不需要用户登录就能访问

**解决方案**: 在 `noTokenUrls` 列表中添加相关 API 路径

```typescript
const noTokenUrls = [
  '/api/v1/user/login',
  '/api/v1/user/refresh-token',
  '/api/v1/user/resetpassword',
  '/api/v1/user/wx-login',
  // 系统配置相关的公开接口
  '/api/v1/system/info/',
  '/api/v1/system/maintenance',
  '/api/v1/system/configs/details',
  '/api/v1/system/notices',
  '/api/v1/system/addresses/options',
  '/api/v1/system/chat/completion',
  '/api/v1/system/ui/generate',
  // 商家和分类相关的公开接口 ✅ 新增
  '/api/v1/merchant/merchant-categories',
  '/api/takeout/categories/global',
  '/api/takeout/merchants',
]
```

### 2. 防止登录页面重复跳转

**问题**: 当 token 刷新失败时，会跳转到登录页面，但如果已经在登录页面，会造成无限循环

**解决方案**: 在跳转前检查当前页面，避免重复跳转

```typescript
// 检查当前页面，避免在登录页面时重复跳转
const pages = getCurrentPages()
const currentPage = pages[pages.length - 1]
const currentRoute = currentPage ? currentPage.route : ''

if (!currentRoute.includes('login')) {
  uni.showToast({
    icon: 'none',
    title: '登录已过期，请重新登录',
    duration: 1500,
  })
  setTimeout(() => {
    uni.reLaunch({ url: '/pages/login/index' })
  }, 1000)
}
```

## ✅ 修复内容

### 1. 更新免认证 API 列表

- ✅ 添加 `/api/v1/merchant/merchant-categories` - 外卖全局分类
- ✅ 添加 `/api/takeout/categories/global` - 全局分类
- ✅ 添加 `/api/takeout/merchants` - 商家列表

### 2. 优化跳转逻辑

- ✅ 在 `handle401AndRetry` 函数中添加页面检查
- ✅ 在主要的 token 刷新失败处理中添加页面检查
- ✅ 避免在登录页面时重复跳转

### 3. 改善错误处理

- ✅ 保持错误日志记录
- ✅ 优化用户提示逻辑
- ✅ 防止无限循环

## 🔄 修复后的流程

### 正常流程

```
登录页面加载
    ↓
调用 initSystemData()
    ↓
获取外卖全局分类 (免认证)
    ↓
成功获取数据 ✅
    ↓
页面正常显示
```

### 异常流程 (其他页面)

```
其他页面 API 调用
    ↓
检测到 token 过期
    ↓
尝试刷新 token
    ↓
刷新失败
    ↓
检查当前页面 (不是登录页)
    ↓
跳转到登录页 ✅
```

### 异常流程 (登录页面)

```
登录页面 API 调用
    ↓
检测到 token 过期
    ↓
尝试刷新 token
    ↓
刷新失败
    ↓
检查当前页面 (是登录页)
    ↓
不跳转，避免循环 ✅
```

## 📊 修复效果

### 解决的问题

- ✅ **无限循环**: 登录页面不再无限刷新
- ✅ **API 认证**: 公开数据不再需要认证
- ✅ **用户体验**: 登录页面正常加载系统配置
- ✅ **错误处理**: 避免重复跳转和错误提示

### 性能优化

- ✅ **减少无效请求**: 公开 API 不再触发 token 检查
- ✅ **避免重复跳转**: 防止页面无限循环
- ✅ **改善加载速度**: 登录页面数据加载更快

## 🎯 测试建议

### 1. 登录页面测试

- 直接访问登录页面，确保不会无限刷新
- 检查系统配置数据是否正常加载
- 验证全局分类数据是否正常获取

### 2. Token 过期测试

- 在其他页面模拟 token 过期
- 验证是否正确跳转到登录页面
- 确保不会出现重复跳转

### 3. 公开 API 测试

- 验证商家列表、分类等公开数据无需登录即可访问
- 确保这些 API 不会触发 token 检查

## 🔮 后续优化建议

### 1. API 分类优化

- 明确区分公开 API 和需要认证的 API
- 建立 API 文档，标明认证要求
- 定期审查 API 的认证配置

### 2. 错误处理优化

- 添加更详细的错误分类
- 提供更友好的用户提示
- 考虑添加重试机制

### 3. 性能监控

- 监控 API 调用成功率
- 记录 token 刷新频率
- 跟踪用户登录状态变化

---

通过这次修复，登录页面的无限循环问题已经彻底解决，用户现在可以正常访问登录页面并进行登录操作。
