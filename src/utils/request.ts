import { CustomRequestOptions } from '@/interceptors/request'
import { useUserStore } from '@/store/user'

// 用于防止并发刷新token的标志
let isRefreshing = false
// 存储等待刷新token完成的请求队列
let refreshQueue: Array<{
  resolve: (value: any) => void
  reject: (reason?: any) => void
  options: CustomRequestOptions
}> = []

/**
 * 不需要令牌的API路径列表
 * 这些API请求将不会自动添加Authorization头
 */
const noTokenUrls = [
  '/api/v1/user/login',
  '/api/v1/user/refresh-token',
  '/api/v1/user/resetpassword',
  '/api/v1/user/wx-login',
  // 系统配置相关的公开接口
  '/api/v1/system/info/',
  '/api/v1/system/maintenance',
  '/api/v1/system/configs/details',
  '/api/v1/system/notices',
  '/api/v1/system/addresses/options',
  '/api/v1/system/chat/completion',
  '/api/v1/system/ui/generate',
  // 商家和分类相关的公开接口
  '/api/v1/merchant/merchant-categories',
  '/api/takeout/categories/global',
  '/api/takeout/merchants',
]

/**
 * 检查token是否过期
 * @returns 是否过期
 */
const isTokenExpired = (): boolean => {
  const userStore = useUserStore()
  const tokenExpiration = userStore.userInfo?.tokenExpiration || 0

  if (!tokenExpiration || !userStore.userInfo?.token) {
    return true
  }

  // 提前30秒判断为过期，避免请求过程中token过期
  const now = Date.now()
  return tokenExpiration - now <= 30000
}

/**
 * 刷新token
 * @returns Promise<boolean> 刷新是否成功
 */
const refreshTokenIfNeeded = async (): Promise<boolean> => {
  const userStore = useUserStore()

  // 如果没有refreshToken，无法刷新
  if (!userStore.userInfo?.refreshToken) {
    console.log('没有refreshToken，无法刷新token')
    return false
  }

  try {
    console.log('开始刷新token...')
    await userStore.refreshUserToken()
    console.log('token刷新成功')
    return true
  } catch (error) {
    console.error('token刷新失败:', error)
    return false
  }
}

/**
 * 检查URL是否在不需要令牌的列表中
 * @param url 请求URL
 * @returns 是否不需要令牌
 */
const isNoTokenUrl = (url: string): boolean => {
  return noTokenUrls.some((noTokenUrl) => url.includes(noTokenUrl))
}

/**
 * 处理401错误，尝试刷新token后重试请求
 * @param options 请求参数
 * @returns Promise<T>
 */
const handle401AndRetry = async <T>(options: CustomRequestOptions): Promise<T> => {
  // 如果是刷新token的请求，不要重试，避免无限循环
  if (options.url && options.url.includes('/api/v1/user/refresh-token')) {
    throw new Error('刷新token失败')
  }

  // 如果正在刷新token，将请求加入队列
  if (isRefreshing) {
    return new Promise<T>((resolve, reject) => {
      refreshQueue.push({
        resolve,
        reject,
        options,
      })
    })
  }

  // 开始刷新token
  isRefreshing = true

  try {
    const refreshSuccess = await refreshTokenIfNeeded()

    if (refreshSuccess) {
      // 刷新成功，处理队列中的请求
      const queue = refreshQueue.slice()
      refreshQueue = []
      isRefreshing = false

      // 重新执行队列中的请求
      queue.forEach(({ resolve, reject, options: queueOptions }) => {
        baseRequest<any>(queueOptions).then(resolve).catch(reject)
      })

      // 重新执行当前请求
      return baseRequest<T>(options)
    } else {
      // 刷新失败，清空队列并跳转到登录页
      const queue = refreshQueue.slice()
      refreshQueue = []
      isRefreshing = false

      // 拒绝队列中的所有请求
      queue.forEach(({ reject }) => {
        reject(new Error('token刷新失败，请重新登录'))
      })

      // 清理用户信息并跳转到登录页
      const userStore = useUserStore()
      userStore.logout()

      // 检查当前页面，避免在登录页面时重复跳转
      const pages = getCurrentPages()
      const currentPage = pages[pages.length - 1]
      const currentRoute = currentPage ? currentPage.route : ''

      if (!currentRoute.includes('login')) {
        uni.showToast({
          icon: 'none',
          title: '登录已过期，请重新登录',
          duration: 1500,
        })
        setTimeout(() => {
          uni.reLaunch({ url: '/pages/login/index' })
        }, 1000)
      }

      throw new Error('token刷新失败，请重新登录')
    }
  } catch (error) {
    // 刷新过程中出错
    const queue = refreshQueue.slice()
    refreshQueue = []
    isRefreshing = false

    // 拒绝队列中的所有请求
    queue.forEach(({ reject }) => {
      reject(error)
    })

    throw error
  }
}

/**
 * 请求方法: 主要是对 uni.request 的封装，去适配 openapi-ts-request 的 request 方法
 * @param options 请求参数
 * @returns 返回 Promise 对象
 */
const baseRequest = <T>(options: CustomRequestOptions) => {
  // 1. 返回 Promise 对象
  return new Promise<T>(async (resolve, reject) => {
    // 2. 检查是否需要token且token是否过期
    const needsToken = !isNoTokenUrl(options.url || '')
    if (needsToken && isTokenExpired()) {
      console.log('检测到token过期，尝试刷新...')
      try {
        const refreshSuccess = await refreshTokenIfNeeded()
        if (!refreshSuccess) {
          // 刷新失败，跳转到登录页
          const userStore = useUserStore()
          userStore.logout()

          // 检查当前页面，避免在登录页面时重复跳转
          const pages = getCurrentPages()
          const currentPage = pages[pages.length - 1]
          const currentRoute = currentPage ? currentPage.route : ''

          if (!currentRoute.includes('login')) {
            uni.showToast({
              icon: 'none',
              title: '登录已过期，请重新登录',
              duration: 1500,
            })
            setTimeout(() => {
              uni.reLaunch({ url: '/pages/login/index' })
            }, 1000)
          }
          reject(new Error('token已过期且刷新失败'))
          return
        }
      } catch (error) {
        console.error('刷新token失败:', error)
        reject(error)
        return
      }
    }

    uni.request({
      ...options,
      dataType: 'json',
      // #ifndef MP-WEIXIN
      responseType: 'json',
      // #endif
      // 响应成功
      success(res) {
        console.log(res)
        // 状态码 2xx，参考 axios 的设计
        if (res.statusCode >= 200 && res.statusCode < 300) {
          // 2.1 提取核心数据 res.data
          console.log(res.data)
          // 确保res.data是对象且包含code属性
          if (res.data && typeof res.data === 'object' && 'code' in res.data) {
            if (res.data.code == 401) {
              // 401错误 -> 尝试刷新token后重试
              console.log('[请求错误] 401 未授权，尝试刷新token后重试:', options.url)
              handle401AndRetry<T>(options).then(resolve).catch(reject)
              return
            }
          } else {
            if (typeof res === 'object' && 'code' in res) {
              if (res.code == 401) {
                // 401错误 -> 尝试刷新token后重试
                console.log('[请求错误] 401 未授权，尝试刷新token后重试:', options.url)
                handle401AndRetry<T>(options).then(resolve).catch(reject)
                return
              }
            }
          }
          resolve(res.data as T)
        } else if (res.statusCode === 401) {
          // 401错误 -> 尝试刷新token后重试
          console.log('[请求错误] HTTP 401 未授权，尝试刷新token后重试:', options.url)
          handle401AndRetry<T>(options).then(resolve).catch(reject)
        } else {
          // 其他错误 -> 根据后端错误信息轻提示
          !options.hideErrorToast &&
            uni.showToast({
              icon: 'none',
              title: (res.data as T & { message?: string })?.message || '请求错误',
            })
          reject(res)
        }
      },
      // 响应失败
      fail(err) {
        uni.showToast({
          icon: 'none',
          title: '网络错误，换个网络试试',
        })
        reject(err)
      },
    })
  })
}

/**
 * 请求函数: 统一的请求接口，支持传入URL和配置对象
 * @param url 请求URL或配置对象
 * @param options 请求选项
 * @returns Promise对象
 */
function request<T = unknown>(
  url: string | CustomRequestOptions,
  options?: Partial<CustomRequestOptions>,
) {
  let requestOptions: CustomRequestOptions

  // 如果url是字符串，将其处理成配置对象
  if (typeof url === 'string') {
    requestOptions = {
      url,
      ...options,
    } as CustomRequestOptions
  } else {
    // url参数是对象形式
    requestOptions = url as CustomRequestOptions
  }

  // 如果URL在不需要token的列表中，删除Authorization头
  if (requestOptions.url && isNoTokenUrl(requestOptions.url)) {
    if (!requestOptions.header) requestOptions.header = {}
    delete requestOptions.header.Authorization
  }

  // 使用baseRequest执行请求
  return baseRequest<T>(requestOptions)
}

/**
 * GET请求
 * @param url 请求URL
 * @param params 查询参数
 * @param options 其他选项
 * @returns Promise对象
 */
const get = <T>(url: string, params?: any, options?: Partial<CustomRequestOptions>) => {
  return request<T>({
    url,
    method: 'GET',
    query: params, // 使用query参数，这将在interceptor中被处理成查询字符串
    ...options,
  })
}

/**
 * POST请求
 * @param url 请求URL
 * @param data 请求数据
 * @param options 其他选项
 * @returns Promise对象
 */
const post = <T>(url: string, data?: any, options?: Partial<CustomRequestOptions>) => {
  return request<T>({
    url,
    method: 'POST',
    data,
    ...options,
  })
}

/**
 * PUT请求
 * @param url 请求URL
 * @param data 请求数据
 * @param options 其他选项
 * @returns Promise对象
 */
const put = <T>(url: string, data?: any, options?: Partial<CustomRequestOptions>) => {
  return request<T>({
    url,
    method: 'PUT',
    data,
    ...options,
  })
}

/**
 * DELETE请求
 * @param url 请求URL
 * @param data 请求数据
 * @param options 其他选项
 * @returns Promise对象
 */
const del = <T>(url: string, data?: any, options?: Partial<CustomRequestOptions>) => {
  return request<T>({
    url,
    method: 'DELETE',
    data,
    ...options,
  })
}

// 创建类型，使 http 对象既可以作为函数调用，又有方法属性
type RequestFunc = typeof request
interface HttpRequest extends RequestFunc {
  request: RequestFunc
  get: typeof get
  post: typeof post
  put: typeof put
  del: typeof del
  delete: typeof del
}

// 创建 http 对象并添加方法
// 注意: 这里在类型层面上做了强制转换，使 http 对象既可以作为函数调用，又可以访问其方法
// 在运行时，这将被处理为一个函数对象
// @ts-ignore - 我们知道这里的类型转换是安全的
const http = request as HttpRequest

// 添加方法
http.request = request
http.get = get
http.post = post
http.put = put
http.del = del
http.delete = del

// 导出
// 同时导出单独的方法和 http 对象
export { request, get, post, put, del, del as delete }
export default http
