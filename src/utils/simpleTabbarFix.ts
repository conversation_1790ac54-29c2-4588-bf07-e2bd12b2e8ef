/**
 * 简单直接的 TabBar 修复方案
 *
 * 避免复杂的全局样式修改，直接针对问题进行修复
 */

/**
 * 简单直接的 TabBar 修复
 */
export function simpleTabBarFix() {
  if (typeof document === 'undefined') return

  console.log('🔧 开始简单 TabBar 修复...')

  // 移除之前可能添加的问题样式
  const problematicStyles = ['aggressive-tabbar-fix', 'tabbar-fix-css', 'precise-tabbar-fix']

  problematicStyles.forEach((id) => {
    const style = document.getElementById(id)
    if (style) {
      style.remove()
      console.log(`🗑️ 已移除问题样式: ${id}`)
    }
  })

  // 创建简单的修复样式
  const style = document.createElement('style')
  style.id = 'simple-tabbar-fix'
  style.textContent = `
    /* 简单直接的 TabBar 修复 */
    .custom-tabbar {
      position: fixed !important;
      bottom: 0 !important;
      left: 0 !important;
      right: 0 !important;
      width: 100% !important;
      z-index: 1000 !important;
    }

    /* 确保内部的 wd-tabbar 不被重新定位 */
    .custom-tabbar .wd-tabbar {
      position: static !important;
      bottom: auto !important;
      left: auto !important;
      right: auto !important;
    }

    /* 固定顶部标题栏 */
    .fixed-header {
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      right: 0 !important;
      z-index: 1001 !important;
      background-color: #ffffff !important;
    }

    /* TabBar布局组件的内容区域 */
    .tabbar-layout .content-area {
      min-height: 100vh !important;
    }

    /* 页面底部留出空间 */
    body {
      padding-bottom: 100px !important;
    }

    /* 有固定标题栏的页面，内容区域需要顶部间距 */
    .tabbar-layout .page-content {
      padding-top: 0 !important;
    }
  `

  document.head.appendChild(style)

  // 直接修复 DOM 元素
  const customTabbar = document.querySelector('.custom-tabbar') as HTMLElement
  if (customTabbar) {
    customTabbar.style.setProperty('position', 'fixed', 'important')
    customTabbar.style.setProperty('bottom', '0', 'important')
    customTabbar.style.setProperty('left', '0', 'important')
    customTabbar.style.setProperty('right', '0', 'important')
    customTabbar.style.setProperty('width', '100%', 'important')
    customTabbar.style.setProperty('z-index', '1000', 'important')

    console.log('✅ 已修复 .custom-tabbar 元素')

    // 检查修复效果
    setTimeout(() => {
      const rect = customTabbar.getBoundingClientRect()
      const computedStyle = window.getComputedStyle(customTabbar)

      console.log('📊 修复后状态:', {
        position: computedStyle.position,
        bottom: computedStyle.bottom,
        rectBottom: rect.bottom,
        windowHeight: window.innerHeight,
        isAtBottom: Math.abs(rect.bottom - window.innerHeight) < 5,
      })
    }, 100)
  } else {
    console.warn('⚠️ 未找到 .custom-tabbar 元素')
  }

  console.log('✅ 简单 TabBar 修复完成')
}

/**
 * 监听页面变化并自动修复
 */
export function autoFixTabBar() {
  if (typeof document === 'undefined') return

  console.log('🔄 启动 TabBar 自动修复监听...')

  // 立即执行一次修复
  simpleTabBarFix()

  // 监听 DOM 变化
  const observer = new MutationObserver((mutations) => {
    let needsFix = false

    mutations.forEach((mutation) => {
      mutation.addedNodes.forEach((node) => {
        if (node.nodeType === Node.ELEMENT_NODE) {
          const element = node as Element
          if (element.matches('.custom-tabbar') || element.querySelector('.custom-tabbar')) {
            needsFix = true
          }
        }
      })
    })

    if (needsFix) {
      console.log('🔄 检测到 TabBar 变化，重新修复...')
      setTimeout(simpleTabBarFix, 100)
    }
  })

  observer.observe(document.body, {
    childList: true,
    subtree: true,
  })

  // 定期检查和修复
  setInterval(() => {
    const customTabbar = document.querySelector('.custom-tabbar') as HTMLElement
    if (customTabbar) {
      const computedStyle = window.getComputedStyle(customTabbar)
      if (computedStyle.position !== 'fixed') {
        console.log('🔄 检测到 TabBar 定位异常，重新修复...')
        simpleTabBarFix()
      }
    }
  }, 2000)

  console.log('✅ TabBar 自动修复监听已启动')
}

/**
 * 检查 TabBar 状态
 */
export function checkTabBarStatus() {
  if (typeof document === 'undefined') return false

  const customTabbar = document.querySelector('.custom-tabbar') as HTMLElement
  if (!customTabbar) {
    console.warn('⚠️ 未找到 .custom-tabbar 元素')
    return false
  }

  const rect = customTabbar.getBoundingClientRect()
  const computedStyle = window.getComputedStyle(customTabbar)

  const status = {
    found: true,
    position: computedStyle.position,
    bottom: computedStyle.bottom,
    rectBottom: rect.bottom,
    windowHeight: window.innerHeight,
    isFixed: computedStyle.position === 'fixed',
    isAtBottom: Math.abs(rect.bottom - window.innerHeight) < 5,
    isWorking: computedStyle.position === 'fixed' && Math.abs(rect.bottom - window.innerHeight) < 5,
  }

  console.log('📊 TabBar 状态检查:', status)

  return status.isWorking
}

/**
 * 清理所有 TabBar 修复样式
 */
export function cleanupTabBarFixes() {
  if (typeof document === 'undefined') return

  console.log('🧹 清理所有 TabBar 修复样式...')

  const styleIds = [
    'aggressive-tabbar-fix',
    'tabbar-fix-css',
    'precise-tabbar-fix',
    'simple-tabbar-fix',
  ]

  styleIds.forEach((id) => {
    const style = document.getElementById(id)
    if (style) {
      style.remove()
      console.log(`🗑️ 已移除样式: ${id}`)
    }
  })

  // 清理内联样式
  const elements = document.querySelectorAll('.custom-tabbar, .wd-tabbar, .tabbar-container')
  elements.forEach((element) => {
    const htmlElement = element as HTMLElement
    htmlElement.style.removeProperty('position')
    htmlElement.style.removeProperty('bottom')
    htmlElement.style.removeProperty('left')
    htmlElement.style.removeProperty('right')
    htmlElement.style.removeProperty('width')
    htmlElement.style.removeProperty('z-index')
    htmlElement.style.removeProperty('transform')
    htmlElement.style.removeProperty('-webkit-transform')
  })

  console.log('✅ TabBar 修复样式清理完成')
}
