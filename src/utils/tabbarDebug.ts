/**
 * TabBar 调试工具
 *
 * 用于深入调试 TabBar 定位问题
 */

/**
 * 全面调试 TabBar 状态
 */
export function debugTabBarState() {
  if (typeof document === 'undefined') {
    console.log('🚫 Document 未定义')
    return
  }

  console.log('🔍 开始全面调试 TabBar 状态...')

  // 1. 查找所有可能的 TabBar 元素
  const selectors = [
    '.wd-tabbar',
    '.custom-tabbar',
    '.tabbar-container',
    '.fg-tabbar',
    '[class*="tabbar"]',
    'wd-tabbar',
  ]

  let foundElements = []

  selectors.forEach((selector) => {
    const elements = document.querySelectorAll(selector)
    elements.forEach((element) => {
      foundElements.push({ selector, element })
    })
  })

  console.log(`📊 找到 ${foundElements.length} 个 TabBar 相关元素`)

  // 2. 详细分析每个元素
  foundElements.forEach(({ selector, element }, index) => {
    console.log(`\n📋 分析元素 ${index + 1}: ${selector}`)

    const htmlElement = element as HTMLElement
    const computedStyle = window.getComputedStyle(htmlElement)
    const rect = htmlElement.getBoundingClientRect()

    // 基本信息
    console.log('基本信息:', {
      tagName: htmlElement.tagName,
      className: htmlElement.className,
      id: htmlElement.id,
      innerHTML: htmlElement.innerHTML.substring(0, 100) + '...',
    })

    // 定位信息
    console.log('定位信息:', {
      position: computedStyle.position,
      top: computedStyle.top,
      bottom: computedStyle.bottom,
      left: computedStyle.left,
      right: computedStyle.right,
      zIndex: computedStyle.zIndex,
    })

    // 变换信息
    console.log('变换信息:', {
      transform: computedStyle.transform,
      webkitTransform: computedStyle.webkitTransform,
      willChange: computedStyle.willChange,
    })

    // 尺寸信息
    console.log('尺寸信息:', {
      width: computedStyle.width,
      height: computedStyle.height,
      display: computedStyle.display,
      visibility: computedStyle.visibility,
    })

    // 边界框信息
    console.log('边界框信息:', {
      top: rect.top,
      bottom: rect.bottom,
      left: rect.left,
      right: rect.right,
      width: rect.width,
      height: rect.height,
    })

    // 内联样式
    console.log('内联样式:', {
      position: htmlElement.style.position,
      bottom: htmlElement.style.bottom,
      transform: htmlElement.style.transform,
      zIndex: htmlElement.style.zIndex,
    })

    // 父元素链分析
    console.log('父元素链分析:')
    let parent = htmlElement.parentElement
    let level = 0
    while (parent && level < 5) {
      const parentStyle = window.getComputedStyle(parent)
      console.log(`  父元素 ${level + 1}:`, {
        tagName: parent.tagName,
        className: parent.className,
        position: parentStyle.position,
        transform: parentStyle.transform,
        zIndex: parentStyle.zIndex,
        overflow: parentStyle.overflow,
      })
      parent = parent.parentElement
      level++
    }
  })

  // 3. 检查全局样式
  console.log('\n🎨 检查全局样式...')

  // 检查我们的优化样式是否生效
  const testElement = document.createElement('div')
  testElement.className = 'test-global-styles'
  testElement.style.position = 'absolute'
  testElement.style.top = '-9999px'
  document.body.appendChild(testElement)

  const testStyle = window.getComputedStyle(testElement)
  console.log('全局样式测试:', {
    transform: testStyle.transform,
    webkitTransform: testStyle.webkitTransform,
    overflowScrolling: testStyle.webkitOverflowScrolling,
  })

  document.body.removeChild(testElement)

  // 4. 检查CSS规则
  console.log('\n📜 检查相关CSS规则...')

  const relevantRules = []
  for (let i = 0; i < document.styleSheets.length; i++) {
    try {
      const styleSheet = document.styleSheets[i]
      if (styleSheet.cssRules) {
        for (let j = 0; j < styleSheet.cssRules.length; j++) {
          const rule = styleSheet.cssRules[j] as CSSStyleRule
          if (
            rule.selectorText &&
            (rule.selectorText.includes('tabbar') ||
              rule.selectorText.includes('*') ||
              rule.selectorText.includes('fixed'))
          ) {
            relevantRules.push({
              selector: rule.selectorText,
              position: rule.style.position,
              bottom: rule.style.bottom,
              transform: rule.style.transform,
              zIndex: rule.style.zIndex,
              cssText: rule.cssText,
            })
          }
        }
      }
    } catch (e) {
      console.log(`样式表 ${i} 无法访问:`, e.message)
    }
  }

  console.log('相关CSS规则:', relevantRules)

  // 5. 检查viewport和body
  console.log('\n📱 检查viewport和body...')

  const bodyStyle = window.getComputedStyle(document.body)
  console.log('Body样式:', {
    position: bodyStyle.position,
    transform: bodyStyle.transform,
    overflow: bodyStyle.overflow,
    height: bodyStyle.height,
    paddingBottom: bodyStyle.paddingBottom,
  })

  const htmlStyle = window.getComputedStyle(document.documentElement)
  console.log('HTML样式:', {
    position: htmlStyle.position,
    transform: htmlStyle.transform,
    overflow: htmlStyle.overflow,
    height: htmlStyle.height,
  })

  console.log('Viewport信息:', {
    innerWidth: window.innerWidth,
    innerHeight: window.innerHeight,
    scrollY: window.scrollY,
    devicePixelRatio: window.devicePixelRatio,
  })

  console.log('✅ TabBar 状态调试完成')
}

/**
 * 实时监控 TabBar 位置变化
 */
export function monitorTabBarPosition() {
  if (typeof document === 'undefined') return

  console.log('🔍 开始实时监控 TabBar 位置...')

  const monitor = () => {
    const tabbar = document.querySelector(
      '.wd-tabbar, .custom-tabbar, .tabbar-container',
    ) as HTMLElement
    if (tabbar) {
      const rect = tabbar.getBoundingClientRect()
      const computedStyle = window.getComputedStyle(tabbar)

      console.log('TabBar 实时位置:', {
        timestamp: new Date().toLocaleTimeString(),
        position: computedStyle.position,
        bottom: computedStyle.bottom,
        rectBottom: rect.bottom,
        windowHeight: window.innerHeight,
        isAtBottom: Math.abs(rect.bottom - window.innerHeight) < 5,
        scrollY: window.scrollY,
      })
    }
  }

  // 每秒监控一次
  const intervalId = setInterval(monitor, 1000)

  // 监控滚动事件
  window.addEventListener('scroll', () => {
    monitor()
  })

  // 5秒后停止监控
  setTimeout(() => {
    clearInterval(intervalId)
    console.log('⏹️ TabBar 位置监控已停止')
  }, 5000)
}

/**
 * 精准修复 TabBar（替代激进方案）
 */
export function preciseTabBarFix() {
  if (typeof document === 'undefined') return

  console.log('🎯 开始精准修复 TabBar...')

  // 1. 移除之前的激进修复样式
  const aggressiveStyle = document.getElementById('aggressive-tabbar-fix')
  if (aggressiveStyle) {
    aggressiveStyle.remove()
    console.log('🗑️ 已移除激进修复样式')
  }

  // 2. 应用精准修复样式
  const preciseStyleId = 'precise-tabbar-fix'
  let existingStyle = document.getElementById(preciseStyleId)
  if (existingStyle) {
    existingStyle.remove()
  }

  const style = document.createElement('style')
  style.id = preciseStyleId
  style.textContent = `
    /* 精准修复：只针对 TabBar 及其父容器 */
    .wd-tabbar {
      position: fixed !important;
      bottom: 0 !important;
      left: 0 !important;
      right: 0 !important;
      width: 100% !important;
      z-index: 9999 !important;
      transform: none !important;
      -webkit-transform: none !important;
    }

    /* 修复 TabBar 的父容器 */
    .custom-tabbar,
    .tabbar-container {
      position: fixed !important;
      bottom: 0 !important;
      left: 0 !important;
      right: 0 !important;
      width: 100% !important;
      z-index: 9999 !important;
      transform: none !important;
      -webkit-transform: none !important;
    }

    /* 确保 TabBar 父元素不影响定位 */
    .custom-tabbar .wd-tabbar,
    .tabbar-container .wd-tabbar {
      position: relative !important;
      bottom: auto !important;
      left: auto !important;
      right: auto !important;
    }

    /* 页面底部间距 */
    body {
      padding-bottom: calc(100px + env(safe-area-inset-bottom, 0px)) !important;
    }
  `

  document.head.appendChild(style)

  // 3. 直接操作 DOM - 只修复 TabBar 容器
  const tabbarContainers = document.querySelectorAll('.custom-tabbar, .tabbar-container')
  tabbarContainers.forEach((container) => {
    const htmlElement = container as HTMLElement

    // 只修复容器的定位
    htmlElement.style.setProperty('position', 'fixed', 'important')
    htmlElement.style.setProperty('bottom', '0', 'important')
    htmlElement.style.setProperty('left', '0', 'important')
    htmlElement.style.setProperty('right', '0', 'important')
    htmlElement.style.setProperty('width', '100%', 'important')
    htmlElement.style.setProperty('z-index', '9999', 'important')
    htmlElement.style.setProperty('transform', 'none', 'important')
    htmlElement.style.setProperty('-webkit-transform', 'none', 'important')

    console.log('🎯 已修复容器:', htmlElement.className)
  })

  // 4. 检查并修复可能影响的父元素
  const allTabBars = document.querySelectorAll('.wd-tabbar')
  allTabBars.forEach((tabbar) => {
    let parent = tabbar.parentElement
    let level = 0
    while (parent && level < 2) {
      const parentStyle = window.getComputedStyle(parent)
      if (
        parentStyle.transform !== 'none' &&
        parent.classList.contains('custom-tabbar', 'tabbar-container')
      ) {
        parent.style.setProperty('transform', 'none', 'important')
        parent.style.setProperty('-webkit-transform', 'none', 'important')
        console.log('🎯 已修复父元素 transform:', parent.className)
      }
      parent = parent.parentElement
      level++
    }
  })

  console.log('🎯 精准修复完成')

  // 5. 验证修复效果
  setTimeout(() => {
    const tabbar = document.querySelector('.custom-tabbar, .tabbar-container') as HTMLElement
    if (tabbar) {
      const rect = tabbar.getBoundingClientRect()
      const isFixed = Math.abs(rect.bottom - window.innerHeight) < 10
      console.log('🎯 修复验证:', {
        isFixed,
        rectBottom: rect.bottom,
        windowHeight: window.innerHeight,
        position: window.getComputedStyle(tabbar).position,
      })
    }
  }, 500)
}
