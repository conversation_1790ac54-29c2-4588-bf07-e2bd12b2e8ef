/**
 * TabBar 固定定位修复工具
 *
 * 用于修复 TouchMove 优化后导致的 TabBar 定位问题
 * 确保 TabBar 始终固定在屏幕底部
 */

/**
 * 修复 TabBar 固定定位
 */
export function fixTabBarPosition() {
  // 等待 DOM 加载完成
  if (typeof document === 'undefined') {
    console.log('🚫 Document 未定义，跳过 TabBar 修复')
    return
  }

  console.log('🔧 开始 TabBar 固定定位修复...')

  // 检查是否已经修复过
  if ((window as any).__tabbarFixApplied) {
    console.log('⚠️ TabBar 修复已应用过，跳过重复修复')
    return
  }

  // 查找 TabBar 元素
  const tabbarSelectors = [
    '.wd-tabbar',
    '.custom-tabbar',
    '.tabbar-container',
    '.fg-tabbar',
    '[class*="tabbar"]',
  ]

  let tabbarFixed = false

  // 修复现有的 TabBar 元素
  function fixExistingTabBars() {
    console.log('🔍 开始查找 TabBar 元素...')

    tabbarSelectors.forEach((selector) => {
      const elements = document.querySelectorAll(selector)
      console.log(`🔍 选择器 "${selector}" 找到 ${elements.length} 个元素`)

      elements.forEach((element, index) => {
        const htmlElement = element as HTMLElement

        console.log(`📋 处理第 ${index + 1} 个 TabBar 元素:`, {
          selector,
          className: htmlElement.className,
          id: htmlElement.id,
          tagName: htmlElement.tagName,
          currentPosition: window.getComputedStyle(htmlElement).position,
          currentBottom: window.getComputedStyle(htmlElement).bottom,
          currentTransform: window.getComputedStyle(htmlElement).transform,
        })

        // 记录修复前的样式
        const beforeStyles = {
          position: htmlElement.style.position || window.getComputedStyle(htmlElement).position,
          bottom: htmlElement.style.bottom || window.getComputedStyle(htmlElement).bottom,
          transform: htmlElement.style.transform || window.getComputedStyle(htmlElement).transform,
          zIndex: htmlElement.style.zIndex || window.getComputedStyle(htmlElement).zIndex,
        }

        console.log('📋 修复前样式:', beforeStyles)

        // 强制设置固定定位样式
        htmlElement.style.setProperty('position', 'fixed', 'important')
        htmlElement.style.setProperty('bottom', '0', 'important')
        htmlElement.style.setProperty('left', '0', 'important')
        htmlElement.style.setProperty('right', '0', 'important')
        htmlElement.style.setProperty('z-index', '1000', 'important')
        htmlElement.style.setProperty('width', '100%', 'important')

        // 移除可能影响定位的 transform
        htmlElement.style.setProperty('transform', 'none', 'important')
        htmlElement.style.setProperty('-webkit-transform', 'none', 'important')

        // 记录修复后的样式
        const afterStyles = {
          position: window.getComputedStyle(htmlElement).position,
          bottom: window.getComputedStyle(htmlElement).bottom,
          transform: window.getComputedStyle(htmlElement).transform,
          zIndex: window.getComputedStyle(htmlElement).zIndex,
        }

        console.log('📋 修复后样式:', afterStyles)

        // 检查父元素是否有影响定位的样式
        let parent = htmlElement.parentElement
        let level = 0
        while (parent && level < 5) {
          const parentStyles = window.getComputedStyle(parent)
          if (
            parentStyles.transform !== 'none' ||
            parentStyles.position === 'relative' ||
            parentStyles.position === 'absolute'
          ) {
            console.warn(`⚠️ 父元素 ${level + 1} 级可能影响定位:`, {
              tagName: parent.tagName,
              className: parent.className,
              position: parentStyles.position,
              transform: parentStyles.transform,
              zIndex: parentStyles.zIndex,
            })
          }
          parent = parent.parentElement
          level++
        }

        tabbarFixed = true
      })
    })

    if (tabbarFixed) {
      console.log('✅ TabBar 固定定位修复完成')
    } else {
      console.warn('⚠️ 未找到任何 TabBar 元素')
    }
  }

  // 立即执行修复
  fixExistingTabBars()

  // 使用 MutationObserver 监听新添加的 TabBar
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      mutation.addedNodes.forEach((node) => {
        if (node.nodeType === Node.ELEMENT_NODE) {
          const element = node as Element

          // 检查是否是 TabBar 元素
          const isTabBar = tabbarSelectors.some(
            (selector) => element.matches(selector) || element.querySelector(selector),
          )

          if (isTabBar) {
            // 延迟修复，确保元素已完全渲染
            setTimeout(() => {
              fixExistingTabBars()
            }, 100)
          }
        }
      })
    })
  })

  // 开始观察 DOM 变化
  observer.observe(document.body, {
    childList: true,
    subtree: true,
  })

  // 标记修复已应用
  ;(window as any).__tabbarFixApplied = true

  console.log('🔧 TabBar 固定定位修复已启动')
}

/**
 * 添加页面底部间距，避免内容被 TabBar 遮挡
 */
export function addTabBarPadding() {
  if (typeof document === 'undefined') return

  // 检查是否已经添加过间距
  if ((window as any).__tabbarPaddingApplied) {
    return
  }

  // 创建样式元素
  const style = document.createElement('style')
  style.id = 'tabbar-padding-fix'
  style.textContent = `
    /* TabBar 底部间距修复 */
    body,
    .uni-page-body,
    .page-container {
      padding-bottom: calc(120rpx + env(safe-area-inset-bottom, 0px)) !important;
    }
    
    /* 滚动容器底部间距 */
    .scroll-view:not(.wd-tabbar):not(.custom-tabbar),
    .page-scroll:not(.wd-tabbar):not(.custom-tabbar) {
      padding-bottom: calc(120rpx + env(safe-area-inset-bottom, 0px)) !important;
    }
    
    /* 确保 TabBar 不受全局样式影响 */
    .wd-tabbar,
    .custom-tabbar,
    .tabbar-container {
      position: fixed !important;
      bottom: 0 !important;
      left: 0 !important;
      right: 0 !important;
      z-index: 1000 !important;
      transform: none !important;
      -webkit-transform: none !important;
    }
  `

  // 添加到 head
  document.head.appendChild(style)

  // 标记间距已应用
  ;(window as any).__tabbarPaddingApplied = true

  console.log('✅ TabBar 底部间距已添加')
}

/**
 * 检查 TabBar 是否正确固定
 */
export function checkTabBarPosition(): boolean {
  if (typeof document === 'undefined') {
    console.log('🚫 Document 未定义，无法检查 TabBar 定位')
    return false
  }

  console.log('🔍 开始检查 TabBar 定位状态...')

  const tabbarSelectors = [
    '.wd-tabbar',
    '.custom-tabbar',
    '.tabbar-container',
    '.fg-tabbar',
    '[class*="tabbar"]',
  ]

  let isFixed = false
  let foundElements = 0

  tabbarSelectors.forEach((selector) => {
    const elements = document.querySelectorAll(selector)
    elements.forEach((element, index) => {
      foundElements++
      const htmlElement = element as HTMLElement
      const computedStyle = window.getComputedStyle(htmlElement)

      const positionInfo = {
        selector,
        index,
        className: htmlElement.className,
        position: computedStyle.position,
        bottom: computedStyle.bottom,
        left: computedStyle.left,
        right: computedStyle.right,
        zIndex: computedStyle.zIndex,
        transform: computedStyle.transform,
        width: computedStyle.width,
        height: computedStyle.height,
        display: computedStyle.display,
        visibility: computedStyle.visibility,
      }

      console.log(`📋 TabBar 元素 ${foundElements} 定位信息:`, positionInfo)

      // 检查是否正确固定
      if (
        computedStyle.position === 'fixed' &&
        (computedStyle.bottom === '0px' || computedStyle.bottom === '0')
      ) {
        isFixed = true
        console.log(`✅ TabBar 元素 ${foundElements} 定位正确`)
      } else {
        console.warn(`⚠️ TabBar 元素 ${foundElements} 定位异常:`, {
          expected: { position: 'fixed', bottom: '0px' },
          actual: { position: computedStyle.position, bottom: computedStyle.bottom },
        })

        // 检查是否被其他样式覆盖
        const inlineStyles = {
          position: htmlElement.style.position,
          bottom: htmlElement.style.bottom,
          transform: htmlElement.style.transform,
        }
        console.log(`📋 内联样式:`, inlineStyles)

        // 检查所有应用的CSS规则
        const rules = []
        for (let i = 0; i < document.styleSheets.length; i++) {
          try {
            const styleSheet = document.styleSheets[i]
            if (styleSheet.cssRules) {
              for (let j = 0; j < styleSheet.cssRules.length; j++) {
                const rule = styleSheet.cssRules[j] as CSSStyleRule
                if (rule.selectorText && htmlElement.matches(rule.selectorText)) {
                  rules.push({
                    selector: rule.selectorText,
                    position: rule.style.position,
                    bottom: rule.style.bottom,
                    transform: rule.style.transform,
                  })
                }
              }
            }
          } catch (e) {
            // 跨域样式表无法访问，忽略
          }
        }
        console.log(`📋 匹配的CSS规则:`, rules)
      }
    })
  })

  console.log(
    `📊 检查完成: 找到 ${foundElements} 个 TabBar 元素，${isFixed ? '有' : '无'}正确固定的元素`,
  )

  return isFixed
}

/**
 * 强制重置 TabBar 定位
 */
export function forceResetTabBarPosition() {
  if (typeof document === 'undefined') return

  const tabbarSelectors = ['.wd-tabbar', '.custom-tabbar', '.tabbar-container']

  tabbarSelectors.forEach((selector) => {
    const elements = document.querySelectorAll(selector)
    elements.forEach((element) => {
      const htmlElement = element as HTMLElement

      // 临时移除元素
      const parent = htmlElement.parentNode
      const nextSibling = htmlElement.nextSibling

      if (parent) {
        parent.removeChild(htmlElement)

        // 重新设置样式
        htmlElement.style.cssText = `
          position: fixed !important;
          bottom: 0 !important;
          left: 0 !important;
          right: 0 !important;
          z-index: 1000 !important;
          transform: none !important;
          -webkit-transform: none !important;
        `

        // 重新插入元素
        if (nextSibling) {
          parent.insertBefore(htmlElement, nextSibling)
        } else {
          parent.appendChild(htmlElement)
        }
      }
    })
  })

  console.log('🔄 TabBar 定位已强制重置')
}

/**
 * 创建全局CSS样式来强制修复TabBar
 */
export function injectTabBarFixCSS() {
  if (typeof document === 'undefined') return

  // 检查是否已经注入过
  if (document.getElementById('tabbar-fix-css')) {
    console.log('⚠️ TabBar 修复 CSS 已注入，跳过重复注入')
    return
  }

  const style = document.createElement('style')
  style.id = 'tabbar-fix-css'
  style.textContent = `
    /* 强制修复 TabBar 定位 */
    .wd-tabbar,
    .custom-tabbar,
    .tabbar-container,
    .fg-tabbar,
    [class*="tabbar"] {
      position: fixed !important;
      bottom: 0 !important;
      left: 0 !important;
      right: 0 !important;
      width: 100% !important;
      z-index: 9999 !important;
      transform: none !important;
      -webkit-transform: none !important;
      will-change: auto !important;
    }

    /* 移除可能影响定位的父元素样式 */
    .wd-tabbar *,
    .custom-tabbar *,
    .tabbar-container *,
    .fg-tabbar * {
      transform: none !important;
      -webkit-transform: none !important;
    }

    /* 确保页面有底部间距 */
    body,
    .uni-page-body,
    .page-container {
      padding-bottom: calc(120rpx + env(safe-area-inset-bottom, 0px)) !important;
      margin-bottom: 0 !important;
    }

    /* 滚动容器底部间距 */
    .scroll-view:not(.wd-tabbar):not(.custom-tabbar):not(.tabbar-container) {
      padding-bottom: calc(120rpx + env(safe-area-inset-bottom, 0px)) !important;
    }
  `

  document.head.appendChild(style)
  console.log('✅ TabBar 修复 CSS 已注入')
}

/**
 * 使用 MutationObserver 持续监控和修复 TabBar
 */
export function startTabBarMonitoring() {
  if (typeof document === 'undefined') return

  console.log('🔍 开始 TabBar 持续监控...')

  const observer = new MutationObserver((mutations) => {
    let needsFix = false

    mutations.forEach((mutation) => {
      // 检查新添加的节点
      mutation.addedNodes.forEach((node) => {
        if (node.nodeType === Node.ELEMENT_NODE) {
          const element = node as Element
          if (
            element.matches(
              '.wd-tabbar, .custom-tabbar, .tabbar-container, .fg-tabbar, [class*="tabbar"]',
            ) ||
            element.querySelector(
              '.wd-tabbar, .custom-tabbar, .tabbar-container, .fg-tabbar, [class*="tabbar"]',
            )
          ) {
            needsFix = true
          }
        }
      })

      // 检查属性变化
      if (mutation.type === 'attributes' && mutation.target) {
        const target = mutation.target as Element
        if (
          target.matches(
            '.wd-tabbar, .custom-tabbar, .tabbar-container, .fg-tabbar, [class*="tabbar"]',
          )
        ) {
          needsFix = true
        }
      }
    })

    if (needsFix) {
      console.log('🔧 检测到 TabBar 变化，重新应用修复...')
      setTimeout(() => {
        fixTabBarPosition()
        checkTabBarPosition()
      }, 100)
    }
  })

  observer.observe(document.body, {
    childList: true,
    subtree: true,
    attributes: true,
    attributeFilter: ['class', 'style'],
  })

  console.log('✅ TabBar 持续监控已启动')
}

/**
 * 一键应用所有 TabBar 修复
 */
export function applyAllTabBarFixes() {
  console.log('🚀 开始应用所有 TabBar 修复...')

  // 1. 注入强制修复CSS
  injectTabBarFixCSS()

  // 2. 修复 TabBar 定位
  fixTabBarPosition()

  // 3. 添加底部间距
  addTabBarPadding()

  // 4. 启动持续监控
  startTabBarMonitoring()

  // 5. 多次检查和修复
  const checkIntervals = [500, 1000, 2000, 5000]
  checkIntervals.forEach((delay) => {
    setTimeout(() => {
      console.log(`🔍 第 ${delay}ms 检查 TabBar 定位...`)
      const isFixed = checkTabBarPosition()
      if (!isFixed) {
        console.warn(`⚠️ 第 ${delay}ms 检查失败，重新应用修复`)
        fixTabBarPosition()
        forceResetTabBarPosition()
      }
    }, delay)
  })

  console.log('🚀 所有 TabBar 修复已应用')
}
