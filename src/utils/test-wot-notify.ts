/**
 * WOT UI Notify 测试工具
 * 用于测试和调试WOT UI通知功能
 */

/**
 * 测试WOT UI通知功能
 */
export async function testWotNotify() {
  console.log('🧪 开始测试通知功能...')

  try {
    // 方法1: 使用uni.showToast (主要方案)
    console.log('📝 测试方法1: uni.showToast')
    uni.showToast({
      title: '测试通知 - uni.showToast',
      icon: 'none',
      duration: 3000,
    })
    console.log('✅ 方法1成功: uni.showToast')
    return true
  } catch (error) {
    console.error('❌ 方法1失败:', error)
  }

  try {
    // 方法2: 使用uni.$u.toast (如果可用)
    console.log('📝 测试方法2: uni.$u.toast')
    if (typeof uni !== 'undefined' && (uni as any).$u && (uni as any).$u.toast) {
      ;(uni as any).$u.toast({
        type: 'primary',
        message: '测试通知 - uni.$u.toast',
        duration: 3000,
      })
      console.log('✅ 方法2成功: uni.$u.toast')
      return true
    } else {
      console.warn('⚠️ uni.$u.toast不可用')
    }
  } catch (error) {
    console.error('❌ 方法2失败:', error)
  }

  try {
    // 方法3: 使用系统弹窗
    console.log('📝 测试方法3: uni.showModal')
    uni.showModal({
      title: '测试通知',
      content: '这是一个测试通知 - uni.showModal',
      showCancel: false,
      confirmText: '确定',
    })
    console.log('✅ 方法3成功: uni.showModal')
    return true
  } catch (error) {
    console.error('❌ 方法3失败:', error)
  }

  // 最后的降级方案
  console.log('📝 使用最后降级方案: console.log')
  console.log('📢 测试通知: 所有通知方法都失败了')

  return false
}

/**
 * 测试所有通知类型
 */
export async function testAllNotifyTypes() {
  console.log('🧪 测试所有通知类型...')

  const types = [
    { type: 'success', message: '成功通知测试', icon: 'success' as const },
    { type: 'warning', message: '警告通知测试', icon: 'none' as const },
    { type: 'error', message: '错误通知测试', icon: 'error' as const },
    { type: 'info', message: '信息通知测试', icon: 'none' as const },
  ]

  try {
    for (let i = 0; i < types.length; i++) {
      const { message, icon } = types[i]

      setTimeout(() => {
        uni.showToast({
          title: message,
          icon: icon,
          duration: 2000,
        })
        console.log(`✅ 显示通知:`, message)
      }, i * 1000)
    }

    return true
  } catch (error) {
    console.error('❌ 测试所有通知类型失败:', error)

    // 降级方案 - 使用console.log
    for (let i = 0; i < types.length; i++) {
      const { message } = types[i]
      setTimeout(() => {
        console.log(`📢 通知: ${message}`)
      }, i * 1000)
    }

    return false
  }
}

/**
 * 检查WOT UI组件是否正确加载
 */
export function checkWotUIComponents() {
  console.log('🔍 检查WOT UI组件加载状态...')

  const results = {
    notifyComponent: false,
    toastComponent: false,
    messageBoxComponent: false,
    configProvider: false,
  }

  try {
    // 检查页面中是否有WOT UI组件
    if (typeof document !== 'undefined') {
      const notifyElements = document.querySelectorAll('wd-notify')
      const toastElements = document.querySelectorAll('wd-toast')
      const messageBoxElements = document.querySelectorAll('wd-message-box')
      const configProviderElements = document.querySelectorAll('wd-config-provider')

      results.notifyComponent = notifyElements.length > 0
      results.toastComponent = toastElements.length > 0
      results.messageBoxComponent = messageBoxElements.length > 0
      results.configProvider = configProviderElements.length > 0

      console.log('📊 WOT UI组件检查结果:', results)
      console.log(
        `🔔 wd-notify组件: ${results.notifyComponent ? '✅ 已找到' : '❌ 未找到'} (${notifyElements.length}个)`,
      )
      console.log(
        `🍞 wd-toast组件: ${results.toastComponent ? '✅ 已找到' : '❌ 未找到'} (${toastElements.length}个)`,
      )
      console.log(
        `📦 wd-message-box组件: ${results.messageBoxComponent ? '✅ 已找到' : '❌ 未找到'} (${messageBoxElements.length}个)`,
      )
      console.log(
        `⚙️ wd-config-provider组件: ${results.configProvider ? '✅ 已找到' : '❌ 未找到'} (${configProviderElements.length}个)`,
      )
    }
  } catch (error) {
    console.error('❌ 检查WOT UI组件失败:', error)
  }

  return results
}

/**
 * 测试WebSocket通知服务
 */
export async function testWebSocketNotification() {
  console.log('🧪 测试WebSocket通知服务...')

  try {
    // 动态导入通知服务
    const { NotificationService } = await import('@/services/websocket/notificationService')
    const notificationService = NotificationService.getInstance()

    // 测试聊天通知
    console.log('📝 测试聊天通知...')
    notificationService.showChatNotification({
      sender_name: '测试用户',
      content: '这是一条测试消息',
      session_id: 'test-session-123',
    })

    // 测试普通通知
    setTimeout(() => {
      console.log('📝 测试普通通知...')
      notificationService.showNotification({
        message: '这是一条普通通知',
        priority: 1,
      })
    }, 2000)

    // 测试重要通知
    setTimeout(() => {
      console.log('📝 测试重要通知...')
      notificationService.showNotification({
        message: '这是一条重要通知',
        priority: 2,
      })
    }, 4000)

    console.log('✅ WebSocket通知服务测试完成')
    return true
  } catch (error) {
    console.error('❌ WebSocket通知服务测试失败:', error)
    return false
  }
}

/**
 * 全面测试通知功能
 */
export async function runFullNotifyTest() {
  console.log('🚀 开始全面测试通知功能...')

  // 1. 检查组件加载状态
  const componentStatus = checkWotUIComponents()

  // 2. 测试基本通知功能
  const basicTestResult = await testWotNotify()

  // 3. 测试WebSocket通知服务
  const webSocketTestResult = await testWebSocketNotification()

  // 4. 测试所有通知类型
  setTimeout(async () => {
    const allTypesResult = await testAllNotifyTypes()

    // 5. 输出测试总结
    console.log('📋 测试总结:')
    console.log(`🔔 wd-notify组件: ${componentStatus.notifyComponent ? '✅' : '❌'}`)
    console.log(`🧪 基本通知功能: ${basicTestResult ? '✅' : '❌'}`)
    console.log(`🔗 WebSocket通知服务: ${webSocketTestResult ? '✅' : '❌'}`)
    console.log(`🎨 所有通知类型: ${allTypesResult ? '✅' : '❌'}`)

    if (basicTestResult && webSocketTestResult && allTypesResult) {
      console.log('🎉 通知功能测试全部通过！')
    } else {
      console.log('⚠️ 通知功能存在问题，请检查配置')
    }
  }, 6000)
}

// 在开发环境中暴露到全局
if (import.meta.env.DEV && typeof window !== 'undefined') {
  ;(window as any).testWotNotify = testWotNotify
  ;(window as any).testAllNotifyTypes = testAllNotifyTypes
  ;(window as any).checkWotUIComponents = checkWotUIComponents
  ;(window as any).testWebSocketNotification = testWebSocketNotification
  ;(window as any).runFullNotifyTest = runFullNotifyTest

  console.log('🔧 通知测试工具已加载到全局:')
  console.log('  - testWotNotify(): 测试基本通知功能')
  console.log('  - testAllNotifyTypes(): 测试所有通知类型')
  console.log('  - checkWotUIComponents(): 检查组件加载状态')
  console.log('  - testWebSocketNotification(): 测试WebSocket通知服务')
  console.log('  - runFullNotifyTest(): 运行全面测试')
}
