# 🔧 Token 自动刷新功能修复总结

## 🔍 问题分析

### 原始问题

用户反馈：页面长时间隐藏后重新刷新时，控制台出现以下错误：

```
cart.ts:131 购物车列表: null
cart.ts:192 获取购物车列表失败: TypeError: Cannot read properties of null (reading 'list')
coupon.ts:218 ❌ 加载优惠券列表失败: TypeError: Cannot destructure property 'list' of 'response.data' as it is null.
```

### 根本原因

1. **Token 过期未处理**: 页面长时间隐藏后，access token 已过期，但系统未自动刷新
2. **401 错误未正确处理**: API 返回 `{"code":401,"message":"未授权","data":null}` 时，响应拦截器未按预期工作
3. **HTTP 客户端不统一**: 项目中存在两个 HTTP 客户端，只有一个实现了 token 刷新功能
4. **数据解构缺乏防护**: Store 中直接解构 API 返回的 `data` 字段，未检查 null 值

## 🛠️ 修复方案

### 1. 统一 HTTP 客户端的 Token 刷新功能

**问题**: `@/utils/http.ts` 和 `@/utils/request.ts` 两个 HTTP 客户端，购物车等 API 使用的是没有 token 刷新功能的 `http.ts`

**解决方案**: 将完整的 token 刷新功能集成到 `@/utils/http.ts` 中

**修复内容**:

- ✅ 添加 token 过期检查逻辑
- ✅ 添加 401 错误自动重试机制
- ✅ 添加并发请求队列处理
- ✅ 添加刷新失败的错误处理

### 2. 实现主动 Token 过期检查

**功能**: 在发送 API 请求前检查 token 是否即将过期

```typescript
const isTokenExpired = (): boolean => {
  const userStore = useUserStore()
  const tokenExpiration = userStore.userInfo?.tokenExpiration || 0

  if (!tokenExpiration || !userStore.userInfo?.token) {
    return true
  }

  // 提前30秒判断为过期，避免请求过程中token过期
  const now = Date.now()
  return tokenExpiration - now <= 30000
}
```

### 3. 实现被动 401 错误处理

**功能**: 当 API 返回 401 错误时，自动尝试刷新 token 后重试

```typescript
// 检查响应数据中的 401 错误
if (res.data && typeof res.data === 'object' && 'code' in res.data) {
  if (res.data.code == 401) {
    // 401错误 -> 尝试刷新token后重试
    handle401AndRetry<T>(options).then(resolve).catch(reject)
    return
  }
}
```

### 4. 实现并发请求安全处理

**功能**: 防止多个并发请求同时触发 token 刷新

```typescript
// 用于防止并发刷新token的标志
let isRefreshing = false
// 存储等待刷新token完成的请求队列
let refreshQueue: Array<{
  resolve: (value: any) => void
  reject: (reason?: any) => void
  options: CustomRequestOptions
}> = []
```

### 5. 修复 Store 中的数据解构错误

**购物车 Store 修复**:

```typescript
// 修复前
const { data } = await getCartList()
console.log('购物车列表:', data)

// 修复后
const response = await getCartList()
console.log('购物车列表:', response)

// 检查响应数据是否有效
if (!response || !response.data) {
  console.warn('购物车API返回空数据，可能是token过期或其他错误')
  cartItems.value = []
  return
}
```

**优惠券 Store 修复**:

```typescript
// 修复前
const { list: coupons, total } = response.data

// 修复后
// 检查响应数据是否有效
if (!response || !response.data) {
  console.warn('优惠券API返回空数据，可能是token过期或其他错误')
  if (refresh) {
    this.myCoupons = []
  }
  return
}

const { list: coupons = [], total = 0 } = response.data || {}
```

## 🔄 工作流程

### 主动检查流程

1. 发送 API 请求前检查 token 是否过期
2. 如果过期，使用 refresh token 刷新 access token
3. 刷新成功后发送原始请求
4. 刷新失败则跳转到登录页

### 被动处理流程

1. API 返回 401 错误
2. 检查是否正在刷新（避免并发）
3. 如果未在刷新，开始刷新流程
4. 如果正在刷新，将请求加入队列
5. 刷新完成后处理队列中的所有请求

### 错误处理流程

1. 刷新 token 失败
2. 清空请求队列
3. 清除用户信息
4. 显示友好提示
5. 跳转到登录页

## ✅ 修复验证

### 解决的问题

- ✅ 页面长时间隐藏后的 token 过期问题
- ✅ API 返回 401 时的自动重试机制
- ✅ 购物车数据加载时的 null 解构错误
- ✅ 优惠券数据加载时的 null 解构错误
- ✅ 并发请求时的重复刷新问题

### 改善的用户体验

- ✅ 用户无感知的 token 自动刷新
- ✅ 减少因 token 过期导致的登录跳转
- ✅ 更稳定的数据加载体验
- ✅ 友好的错误提示和处理

## 📋 测试建议

1. **Token 过期测试**: 手动修改 token 过期时间，验证自动刷新
2. **401 错误测试**: 使用无效 token，验证自动重试机制
3. **并发请求测试**: 同时发起多个请求，验证队列处理
4. **刷新失败测试**: 使用无效 refresh token，验证错误处理

## 🔮 后续优化建议

1. **监控和日志**: 添加 token 刷新的监控和统计
2. **性能优化**: 考虑 token 刷新的频率限制
3. **用户提示**: 在 token 即将过期时给用户友好提示
4. **离线处理**: 考虑网络断开时的 token 处理策略

---

通过以上修复，系统现在能够正确处理 token 过期和 401 错误，为用户提供更流畅的使用体验。
