# Token 自动刷新功能实现说明

## 功能概述

在 `request.ts` 文件中实现了 token 过期检查和自动刷新功能，确保用户在使用应用时不会因为 token 过期而被强制退出登录。

## 实现的功能

### 1. Token 过期检查

- 在发送需要认证的 API 请求前，自动检查 token 是否即将过期（提前30秒判断）
- 如果 token 过期，会自动使用 refresh token 刷新 token 后再发送请求

### 2. 401 错误处理

- 当 API 返回 401 未授权错误时，自动尝试使用 refresh token 刷新 token
- 刷新成功后重新发送原始请求
- 刷新失败则清除用户信息并跳转到登录页

### 3. 并发请求处理

- 使用请求队列机制防止多个并发请求同时触发 token 刷新
- 当正在刷新 token 时，后续的请求会被加入队列等待
- token 刷新完成后，队列中的所有请求会使用新的 token 重新发送

## 核心函数说明

### `isTokenExpired()`

检查当前 token 是否过期或即将过期（提前30秒）

### `refreshTokenIfNeeded()`

使用 refresh token 刷新 access token

### `handle401AndRetry()`

处理 401 错误，尝试刷新 token 后重试请求，包含并发控制逻辑

### `baseRequest()`

主要的请求函数，集成了 token 过期检查和 401 错误处理

## 使用场景

1. **主动检查**: 在发送请求前检查 token 是否过期
2. **被动处理**: 当服务器返回 401 错误时自动处理
3. **并发安全**: 多个请求同时进行时避免重复刷新 token

## 配置说明

### 不需要 token 的 API 列表

```typescript
const noTokenUrls = [
  '/api/v1/user/login',
  '/api/v1/user/refresh-token',
  '/api/v1/user/resetpassword',
  '/api/v1/user/wx-login',
  // 系统配置相关的公开接口
  '/api/v1/system/info/',
  '/api/v1/system/maintenance',
  '/api/v1/system/configs/details',
  '/api/v1/system/notices',
  '/api/v1/system/addresses/options',
  '/api/v1/system/chat/completion',
  '/api/v1/system/ui/generate',
]
```

这些 API 不会进行 token 检查和自动刷新处理。

## 错误处理

1. **Token 刷新失败**: 清除用户信息，显示提示，跳转到登录页
2. **网络错误**: 显示网络错误提示
3. **其他错误**: 根据后端返回的错误信息显示提示

## 注意事项

1. Token 过期时间基于 `userInfo.tokenExpiration` 字段
2. Refresh token 存储在 `userInfo.refreshToken` 字段
3. 刷新 token 的 API 路径为 `/api/v1/user/refresh-token`
4. 登录页面路径为 `/pages/login/index`

## 与用户 Store 的集成

该功能与用户 Store (`@/store/user`) 紧密集成：

- 使用 `userStore.userInfo` 获取 token 信息
- 调用 `userStore.refreshUserToken()` 刷新 token
- 调用 `userStore.logout()` 清除用户信息

## 测试建议

1. 测试 token 即将过期时的自动刷新
2. 测试 API 返回 401 时的自动重试
3. 测试并发请求时的队列处理
4. 测试 refresh token 失效时的登录跳转
