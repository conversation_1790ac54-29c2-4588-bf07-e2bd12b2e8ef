# Token 自动刷新功能测试指南

## 测试目的

验证我们实现的 token 自动刷新功能是否能正确处理以下场景：

1. Token 过期前的主动刷新
2. API 返回 401 时的被动刷新
3. 并发请求时的队列处理
4. 刷新失败时的错误处理

## 测试场景

### 1. 模拟 Token 过期测试

**步骤：**

1. 登录应用获取 token
2. 手动修改 `userStore.userInfo.tokenExpiration` 为一个过去的时间
3. 发起任何需要认证的 API 请求（如获取购物车）
4. 观察控制台日志

**预期结果：**

- 控制台显示 "检测到token过期，尝试刷新..."
- 自动调用刷新 token API
- 刷新成功后重新发送原始请求
- 购物车数据正常加载

### 2. 模拟 401 错误测试

**步骤：**

1. 登录应用
2. 在后端手动使当前 token 失效（或修改 token 值）
3. 发起需要认证的 API 请求
4. 观察控制台日志和应用行为

**预期结果：**

- API 返回 401 错误
- 控制台显示 "401 未授权，尝试刷新token后重试"
- 自动尝试刷新 token
- 如果刷新成功，重新发送请求；如果失败，跳转到登录页

### 3. 并发请求测试

**步骤：**

1. 登录应用
2. 手动修改 token 过期时间为过去时间
3. 同时发起多个需要认证的请求（购物车、优惠券、地址等）
4. 观察控制台日志

**预期结果：**

- 只有一个刷新 token 请求被发送
- 其他请求被加入队列等待
- 刷新完成后，所有队列中的请求使用新 token 重新发送
- 所有数据正常加载

### 4. 刷新失败测试

**步骤：**

1. 登录应用
2. 手动删除或修改 `refreshToken` 为无效值
3. 手动修改 token 过期时间为过去时间
4. 发起需要认证的 API 请求

**预期结果：**

- 尝试刷新 token 但失败
- 控制台显示刷新失败的错误信息
- 清除用户信息
- 显示 "登录已过期，请重新登录" 提示
- 自动跳转到登录页

## 测试代码示例

### 在浏览器控制台中执行以下代码进行测试：

```javascript
// 1. 获取用户 store
const userStore = window.$nuxt.$pinia._s.get('user')

// 2. 模拟 token 过期
userStore.userInfo.tokenExpiration = Date.now() - 1000 // 1秒前过期

// 3. 发起测试请求
fetch('/api/v1/user/takeout/cart/list', {
  headers: {
    Authorization: `Bearer ${userStore.userInfo.token}`,
  },
})
  .then((res) => res.json())
  .then(console.log)

// 4. 观察控制台输出
```

### 模拟并发请求测试：

```javascript
// 同时发起多个请求
Promise.all([
  fetch('/api/v1/user/takeout/cart/list'),
  fetch('/api/v1/user/coupons/my-list'),
  fetch('/api/v1/user/addresses'),
]).then((results) => {
  console.log('所有请求完成:', results)
})
```

## 验证要点

### 1. 日志输出检查

- ✅ "检测到token过期，尝试刷新..."
- ✅ "开始刷新token..."
- ✅ "token刷新成功"
- ✅ "401 未授权，尝试刷新token后重试"

### 2. 网络请求检查

- ✅ 刷新 token 的 POST 请求到 `/api/v1/user/refresh-token`
- ✅ 原始请求使用新的 token 重新发送
- ✅ 并发场景下只有一个刷新请求

### 3. 用户体验检查

- ✅ 用户无感知的 token 刷新
- ✅ 数据正常加载，无需手动刷新页面
- ✅ 刷新失败时友好的错误提示

### 4. 错误处理检查

- ✅ 刷新失败时正确清理用户状态
- ✅ 适当的错误提示和页面跳转
- ✅ 避免无限循环刷新

## 常见问题排查

### 1. Token 刷新不触发

- 检查 `isTokenExpired()` 函数逻辑
- 确认 `tokenExpiration` 字段正确设置
- 验证 `isNoTokenUrl()` 函数是否正确识别需要 token 的 API

### 2. 401 错误未处理

- 检查响应数据结构，确认 401 错误在 `res.data.code` 还是 `res.statusCode`
- 验证 `handle401AndRetry` 函数是否被正确调用

### 3. 并发请求问题

- 检查 `isRefreshing` 标志是否正确设置和清理
- 验证请求队列 `refreshQueue` 的处理逻辑

### 4. 刷新循环问题

- 确认刷新 token API 在 `noTokenUrls` 列表中
- 检查刷新失败时是否正确清理状态

## 修复验证

经过修复后，应该能够解决以下问题：

- ✅ 页面长时间隐藏后重新激活时的 token 过期问题
- ✅ API 返回 401 时的自动重试机制
- ✅ 购物车、优惠券等数据加载时的 "Cannot read properties of null" 错误
- ✅ 用户无需手动重新登录的流畅体验
