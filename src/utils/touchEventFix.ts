/**
 * 触摸事件优化工具
 *
 * 用于修复第三方组件库中非被动事件监听器导致的性能警告
 * 主要解决 wot-design-uni 等组件库的 touchmove 事件监听器问题
 */

/**
 * 重写 addEventListener 方法，自动为所有触摸事件添加 passive 选项
 */
export function fixTouchEventListeners() {
  // 检查是否已经应用过修复，避免重复应用
  if ((window as any).__touchEventFixApplied) {
    console.log('⚠️ 触摸事件修复已应用，跳过重复应用')
    return
  }

  // 保存原始的 addEventListener 方法
  const originalAddEventListener = EventTarget.prototype.addEventListener

  // 需要优化的触摸事件类型
  const touchEvents = ['touchstart', 'touchmove', 'touchend', 'touchcancel']

  // 需要优化的滚动事件类型
  const scrollEvents = ['wheel', 'mousewheel', 'scroll']

  // 日志计数器，避免过度打印
  const logCounts: Record<string, number> = {}
  const MAX_LOGS_PER_EVENT = 3

  // 重写 addEventListener 方法
  EventTarget.prototype.addEventListener = function (
    type: string,
    listener: EventListenerOrEventListenerObject,
    options?: boolean | AddEventListenerOptions,
  ) {
    // 如果是触摸事件或滚动事件且没有明确设置 passive 选项
    if (touchEvents.includes(type) || scrollEvents.includes(type)) {
      // 如果 options 是布尔值或未定义，转换为对象格式
      if (typeof options === 'boolean' || options === undefined) {
        options = {
          capture: typeof options === 'boolean' ? options : false,
          passive: true, // 默认设置为被动监听器
        }
      } else if (typeof options === 'object' && options.passive === undefined) {
        // 如果是对象但没有设置 passive，则设置为 true
        options.passive = true
      }

      // 限制日志输出，避免无限循环打印
      if (process.env.NODE_ENV === 'development') {
        logCounts[type] = (logCounts[type] || 0) + 1
        if (logCounts[type] <= MAX_LOGS_PER_EVENT) {
          console.log(
            `🔧 自动优化 ${type} 事件监听器为被动模式 (${logCounts[type]}/${MAX_LOGS_PER_EVENT})`,
          )
        } else if (logCounts[type] === MAX_LOGS_PER_EVENT + 1) {
          console.log(`🔧 ${type} 事件优化日志已达到最大次数，后续将静默处理`)
        }
      }
    }

    // 调用原始方法
    return originalAddEventListener.call(this, type, listener, options)
  }

  // 标记修复已应用
  ;(window as any).__touchEventFixApplied = true
  console.log('✅ 触摸和滚动事件监听器优化已应用')
}

/**
 * 为特定元素的触摸事件设置被动监听器
 */
export function addPassiveTouchListener(
  element: Element,
  eventType: 'touchstart' | 'touchmove' | 'touchend' | 'touchcancel',
  handler: (event: TouchEvent) => void,
) {
  element.addEventListener(eventType, handler, { passive: true })
}

/**
 * 为特定元素添加所有触摸事件的被动监听器
 */
export function addAllPassiveTouchListeners(
  element: Element,
  handlers: {
    touchstart?: (event: TouchEvent) => void
    touchmove?: (event: TouchEvent) => void
    touchend?: (event: TouchEvent) => void
    touchcancel?: (event: TouchEvent) => void
  },
) {
  Object.entries(handlers).forEach(([eventType, handler]) => {
    if (handler) {
      element.addEventListener(eventType, handler, { passive: true })
    }
  })
}

/**
 * 检查浏览器是否支持被动事件监听器
 */
export function supportsPassiveEvents(): boolean {
  let supportsPassive = false

  try {
    const opts = Object.defineProperty({}, 'passive', {
      get() {
        supportsPassive = true
        return false
      },
    })

    window.addEventListener('testPassive', () => {}, opts)
    window.removeEventListener('testPassive', () => {}, opts)
  } catch (e) {
    // 浏览器不支持被动事件监听器
  }

  return supportsPassive
}

/**
 * 优化滚动性能的工具函数
 */
export function optimizeScrollPerformance() {
  // 检查是否支持被动事件监听器
  if (!supportsPassiveEvents()) {
    console.warn('浏览器不支持被动事件监听器，无法优化滚动性能')
    return
  }

  // 修复全局的触摸和滚动事件监听器
  fixTouchEventListeners()

  // 为常见的滚动容器添加优化
  const scrollContainers = document.querySelectorAll(
    '.scroll-view, .coupon-list, .promotion-list, .favorites-list',
  )

  scrollContainers.forEach((container) => {
    // 为所有触摸事件添加被动监听器
    const touchEvents = ['touchstart', 'touchmove', 'touchend', 'touchcancel']

    touchEvents.forEach((eventType) => {
      container.addEventListener(
        eventType,
        (e) => {
          // 允许默认的触摸和滚动行为
        },
        { passive: true },
      )
    })
  })

  console.log('✅ 触摸和滚动事件优化已应用')
}

/**
 * 防抖函数，用于优化频繁触发的事件
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null

  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      timeout = null
      func(...args)
    }

    if (timeout) {
      clearTimeout(timeout)
    }
    timeout = setTimeout(later, wait)
  }
}

/**
 * 节流函数，用于限制事件触发频率
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number,
): (...args: Parameters<T>) => void {
  let inThrottle: boolean = false

  return function executedFunction(...args: Parameters<T>) {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => (inThrottle = false), limit)
    }
  }
}

/**
 * 专门针对 wot-design-uni 组件的触摸事件优化
 */
export function fixWotDesignTouchEvents() {
  // 等待DOM加载完成后执行
  if (typeof document === 'undefined') return

  // 检查是否已经应用过修复，避免重复应用
  if ((window as any).__wotDesignFixApplied) {
    console.log('⚠️ wot-design-uni 触摸事件优化已应用，跳过重复应用')
    return
  }

  // 标记修复已应用
  ;(window as any).__wotDesignFixApplied = true

  // 优化已存在的元素（一次性）
  setTimeout(() => {
    optimizeExistingWotDesignElements()
  }, 100)

  console.log('✅ wot-design-uni 触摸事件优化已启动')
}

/**
 * 优化单个 wot-design-uni 元素
 */
function optimizeWotDesignElement(element: Element) {
  // 检查元素是否已经优化过
  if ((element as any).__touchOptimized) {
    return
  }

  // wot-design-uni 组件的类名模式
  const wotComponents = [
    'wd-search',
    'wd-button',
    'wd-tabs',
    'wd-tab',
    'wd-loadmore',
    'wd-checkbox',
    'wd-popup',
    'wd-loading',
    'wd-icon',
    'wd-swipe',
  ]

  // 检查是否是 wot-design-uni 组件
  const isWotComponent = wotComponents.some(
    (className) =>
      element.classList.contains(className) || element.tagName.toLowerCase() === className,
  )

  if (isWotComponent) {
    // 只为该元素添加被动触摸事件监听器，不递归处理子元素
    addPassiveTouchEventsToElement(element)
  }
}

/**
 * 优化现有的 wot-design-uni 元素
 */
function optimizeExistingWotDesignElements() {
  const wotSelectors = [
    '.wd-search',
    '.wd-button',
    '.wd-tabs',
    '.wd-tab',
    '.wd-loadmore',
    '.wd-checkbox',
    '.wd-popup',
    '.wd-loading',
    '.wd-icon',
    '.wd-swipe',
  ]

  let optimizedCount = 0

  wotSelectors.forEach((selector) => {
    try {
      const elements = document.querySelectorAll(selector)
      elements.forEach((element) => {
        if (!(element as any).__touchOptimized) {
          addPassiveTouchEventsToElement(element)
          optimizedCount++
        }
      })
    } catch (e) {
      // 忽略查询失败的情况
    }
  })

  if (optimizedCount > 0) {
    console.log(`✅ 已优化 ${optimizedCount} 个 wot-design-uni 组件`)
  }
}

/**
 * 为元素添加被动触摸事件监听器
 */
function addPassiveTouchEventsToElement(element: Element) {
  // 检查元素是否已经优化过，避免重复处理
  if ((element as any).__touchOptimized) {
    return
  }

  const touchEvents = ['touchstart', 'touchmove', 'touchend', 'touchcancel']

  touchEvents.forEach((eventType) => {
    // 简单地为元素添加被动监听器，不进行复杂的克隆操作
    try {
      element.addEventListener(
        eventType,
        (e) => {
          // 允许默认行为，不阻塞滚动
        },
        { passive: true },
      )
    } catch (e) {
      // 忽略添加失败的情况
    }
  })

  // 标记元素已优化
  ;(element as any).__touchOptimized = true
}
