/**
 * 简化版触摸事件优化工具
 *
 * 用于修复第三方组件库中非被动事件监听器导致的性能警告
 * 采用更安全的方式，避免无限循环和过度日志输出
 */

/**
 * 检查浏览器是否支持被动事件监听器
 */
export function supportsPassiveEvents(): boolean {
  // 检查缓存结果
  if ((window as any).__passiveSupported !== undefined) {
    return (window as any).__passiveSupported
  }

  let supportsPassive = false

  try {
    const opts = Object.defineProperty({}, 'passive', {
      get() {
        supportsPassive = true
        return false
      },
    })

    window.addEventListener('testPassive', () => {}, opts)
    window.removeEventListener('testPassive', () => {}, opts)
  } catch (e) {
    // 浏览器不支持被动事件监听器
  }

  // 缓存结果
  ;(window as any).__passiveSupported = supportsPassive
  return supportsPassive
}

/**
 * 简化版触摸事件监听器修复
 */
export function fixTouchEventListeners() {
  // 检查是否已经应用过修复，避免重复应用
  if ((window as any).__touchEventFixApplied) {
    return
  }

  // 检查浏览器支持
  if (!supportsPassiveEvents()) {
    console.warn('浏览器不支持被动事件监听器，无法应用修复')
    return
  }

  // 保存原始的 addEventListener 方法
  const originalAddEventListener = EventTarget.prototype.addEventListener

  // 需要优化的事件类型
  const passiveEvents = new Set([
    'touchstart',
    'touchmove',
    'touchend',
    'touchcancel',
    'wheel',
    'mousewheel',
    'scroll',
  ])

  // 重写 addEventListener 方法
  EventTarget.prototype.addEventListener = function (
    type: string,
    listener: EventListenerOrEventListenerObject,
    options?: boolean | AddEventListenerOptions,
  ) {
    // 如果是需要优化的事件类型
    if (passiveEvents.has(type)) {
      // 确保 passive 为 true
      if (typeof options === 'boolean') {
        options = { capture: options, passive: true }
      } else if (typeof options === 'object' && options !== null) {
        options = { ...options, passive: true }
      } else {
        options = { passive: true }
      }
    }

    // 调用原始方法
    return originalAddEventListener.call(this, type, listener, options)
  }

  // 标记修复已应用
  ;(window as any).__touchEventFixApplied = true
  console.log('✅ 触摸事件监听器优化已应用（简化版）')
}

/**
 * 为特定元素添加被动触摸事件监听器
 */
export function addPassiveTouchListener(
  element: Element,
  eventType: 'touchstart' | 'touchmove' | 'touchend' | 'touchcancel',
  handler: (event: TouchEvent) => void,
) {
  if (!element || typeof handler !== 'function') return

  try {
    element.addEventListener(eventType, handler, { passive: true })
  } catch (e) {
    // 静默处理错误
  }
}

/**
 * 为特定元素添加所有被动触摸事件监听器
 */
export function addAllPassiveTouchListeners(
  element: Element,
  handlers: {
    touchstart?: (event: TouchEvent) => void
    touchmove?: (event: TouchEvent) => void
    touchend?: (event: TouchEvent) => void
    touchcancel?: (event: TouchEvent) => void
  },
) {
  if (!element || !handlers) return

  Object.entries(handlers).forEach(([eventType, handler]) => {
    if (handler && typeof handler === 'function') {
      try {
        element.addEventListener(eventType, handler, { passive: true })
      } catch (e) {
        // 静默处理错误
      }
    }
  })
}

/**
 * 优化页面中的 wot-design-uni 组件（一次性执行）
 */
export function optimizeWotDesignComponents() {
  // 检查是否已经优化过
  if ((window as any).__wotDesignOptimized) {
    return
  }

  // 等待DOM准备就绪
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', optimizeWotDesignComponents)
    return
  }

  const wotSelectors = [
    '.wd-loadmore',
    '.wd-search',
    '.wd-button',
    '.wd-tabs',
    '.wd-checkbox',
    '.wd-popup',
    '.wd-swipe',
  ]

  let optimizedCount = 0

  wotSelectors.forEach((selector) => {
    try {
      const elements = document.querySelectorAll(selector)
      elements.forEach((element) => {
        // 为每个元素添加被动触摸事件监听器
        const touchEvents = ['touchstart', 'touchmove', 'touchend', 'touchcancel']
        touchEvents.forEach((eventType) => {
          try {
            element.addEventListener(
              eventType,
              () => {
                // 空处理函数，只是为了添加被动监听器
              },
              { passive: true },
            )
          } catch (e) {
            // 静默处理错误
          }
        })
        optimizedCount++
      })
    } catch (e) {
      // 静默处理查询错误
    }
  })

  // 标记已优化
  ;(window as any).__wotDesignOptimized = true

  if (optimizedCount > 0) {
    console.log(`✅ 已优化 ${optimizedCount} 个 wot-design-uni 组件`)
  }
}

/**
 * 防抖函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null

  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      timeout = null
      func(...args)
    }

    if (timeout) {
      clearTimeout(timeout)
    }
    timeout = setTimeout(later, wait)
  }
}

/**
 * 节流函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number,
): (...args: Parameters<T>) => void {
  let inThrottle: boolean = false

  return function executedFunction(...args: Parameters<T>) {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => (inThrottle = false), limit)
    }
  }
}

/**
 * 修复 uni-app scroll-view 的被动事件监听器警告
 */
export function fixUniScrollViewPassiveEvents() {
  if (typeof window === 'undefined') return

  console.log('🔧 开始修复 uni-app scroll-view 被动事件监听器警告...')

  // 过滤控制台中的特定警告信息
  const originalConsoleWarn = console.warn
  const originalConsoleError = console.error

  console.warn = function (...args) {
    const message = args.join(' ')
    if (
      message.includes('Unable to preventDefault inside passive event listener') &&
      message.includes('__handleScroll')
    ) {
      // 忽略 uni-app scroll-view 的被动事件警告
      return
    }
    originalConsoleWarn.apply(console, args)
  }

  console.error = function (...args) {
    const message = args.join(' ')
    if (
      message.includes('Unable to preventDefault inside passive event listener') &&
      message.includes('__handleScroll')
    ) {
      // 忽略 uni-app scroll-view 的被动事件错误
      return
    }
    originalConsoleError.apply(console, args)
  }

  // 监听新添加的 scroll-view 元素
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      mutation.addedNodes.forEach((node) => {
        if (node.nodeType === Node.ELEMENT_NODE) {
          const element = node as Element

          // 查找 scroll-view 元素
          if (
            element.matches &&
            element.matches('uni-scroll-view, .uni-scroll-view, scroll-view')
          ) {
            optimizeScrollViewElement(element as HTMLElement)
          }

          // 查找子元素中的 scroll-view
          if (element.querySelectorAll) {
            const scrollViews = element.querySelectorAll(
              'uni-scroll-view, .uni-scroll-view, scroll-view',
            )
            scrollViews.forEach((scrollView) => {
              optimizeScrollViewElement(scrollView as HTMLElement)
            })
          }
        }
      })
    })
  })

  observer.observe(document.body, {
    childList: true,
    subtree: true,
  })

  // 优化现有的 scroll-view 元素
  setTimeout(() => {
    const existingScrollViews = document.querySelectorAll(
      'uni-scroll-view, .uni-scroll-view, scroll-view',
    )
    existingScrollViews.forEach((scrollView) => {
      optimizeScrollViewElement(scrollView as HTMLElement)
    })
  }, 100)

  console.log('✅ uni-app scroll-view 被动事件监听器修复已应用')
}

/**
 * 优化单个 scroll-view 元素
 */
function optimizeScrollViewElement(element: HTMLElement) {
  if (!element || element.dataset.scrollOptimized) return

  // 标记已优化，避免重复处理
  element.dataset.scrollOptimized = 'true'

  // 添加 CSS 样式来优化滚动性能
  element.style.setProperty('-webkit-overflow-scrolling', 'touch', 'important')
  element.style.setProperty('overflow-scrolling', 'touch', 'important')
  element.style.setProperty('will-change', 'scroll-position', 'important')

  // 如果是水平滚动，添加特殊优化
  if (
    element.getAttribute('scroll-x') !== null ||
    element.classList.contains('scroll-x') ||
    element.style.overflowX === 'scroll' ||
    element.style.overflowX === 'auto'
  ) {
    element.style.setProperty('overflow-x', 'auto', 'important')
    element.style.setProperty('overflow-y', 'hidden', 'important')
    element.style.setProperty('white-space', 'nowrap', 'important')

    // 为水平滚动添加平滑滚动
    element.style.setProperty('scroll-behavior', 'smooth')
  }

  // 添加被动触摸事件监听器
  const touchEvents = ['touchstart', 'touchmove', 'touchend']
  touchEvents.forEach((eventType) => {
    try {
      element.addEventListener(
        eventType,
        (e) => {
          // 空处理函数，只是为了添加被动监听器
        },
        { passive: true },
      )
    } catch (e) {
      // 静默处理错误
    }
  })
}

/**
 * 一键应用所有优化
 */
export function applyAllTouchOptimizations() {
  // 应用全局修复
  fixTouchEventListeners()

  // 修复 uni-app scroll-view 问题
  fixUniScrollViewPassiveEvents()

  // 延迟优化组件，确保DOM已渲染
  setTimeout(() => {
    optimizeWotDesignComponents()
  }, 1000)

  console.log('🚀 所有触摸事件优化已启动')
}
