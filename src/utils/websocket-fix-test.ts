/**
 * WebSocket修复验证测试
 * 用于验证require错误是否已修复
 */

import { webSocketService } from '@/services/websocket'

/**
 * 测试WebSocket服务初始化
 */
export async function testWebSocketInitialization() {
  console.log('🧪 开始测试WebSocket服务初始化...')

  try {
    // 1. 检查服务实例
    console.log('1. 检查服务实例:', webSocketService ? '✅ 存在' : '❌ 不存在')

    // 2. 检查初始化状态
    const isInitialized = webSocketService.initialized
    console.log('2. 初始化状态:', isInitialized ? '✅ 已初始化' : '⚠️ 未初始化')

    // 3. 如果未初始化，尝试初始化
    if (!isInitialized) {
      console.log('3. 开始初始化服务...')
      await webSocketService.initialize()
      console.log('3. 初始化完成:', webSocketService.initialized ? '✅ 成功' : '❌ 失败')
    } else {
      console.log('3. 服务已初始化，跳过')
    }

    // 4. 检查连接状态
    console.log('4. 连接状态:', webSocketService.currentStatus)
    console.log('4. 是否已连接:', webSocketService.isConnected ? '✅ 已连接' : '⚠️ 未连接')

    // 5. 测试token提供函数（这是之前出错的地方）
    console.log('5. 测试token提供函数...')
    try {
      // 尝试手动连接来触发token提供函数
      webSocketService.connect()
      console.log('5. token提供函数测试: ✅ 无错误')
    } catch (error) {
      console.error('5. token提供函数测试: ❌ 出现错误:', error)
      return false
    }

    console.log('🎉 WebSocket服务测试完成，所有检查通过！')
    return true
  } catch (error) {
    console.error('❌ WebSocket服务测试失败:', error)
    return false
  }
}

/**
 * 测试设备ID获取
 */
export function testDeviceIdRetrieval() {
  console.log('🧪 测试设备ID获取...')

  try {
    // 检查存储的设备ID
    const deviceId = uni.getStorageSync('device_id')
    const currentDeviceId = uni.getStorageSync('current_device_id')

    console.log('- device_id:', deviceId || '未设置')
    console.log('- current_device_id:', currentDeviceId || '未设置')

    // 检查设备信息生成工具
    try {
      const { generateDeviceInfo } = require('@/utils/device')
      const deviceInfo = generateDeviceInfo()
      console.log('- 生成的设备信息:', deviceInfo)
      console.log('✅ 设备ID获取测试通过')
      return true
    } catch (error) {
      console.error('❌ 设备信息生成工具错误:', error)
      return false
    }
  } catch (error) {
    console.error('❌ 设备ID获取测试失败:', error)
    return false
  }
}

/**
 * 测试URL构建
 */
export function testURLConstruction() {
  console.log('🧪 测试WebSocket URL构建...')

  try {
    const baseUrl = import.meta.env.VITE_WEBSOCKET_URL || 'ws://localhost:8181/api/v1/chat/ws'
    const testToken = 'test_token_123'
    const testDeviceId = 'test_device_456'

    // 构建URL
    const params = new URLSearchParams()
    params.append('token', testToken)
    params.append('device_id', testDeviceId)

    const fullUrl = `${baseUrl}?${params.toString()}`

    console.log('- 基础URL:', baseUrl)
    console.log('- 完整URL:', fullUrl)
    console.log('- 包含token:', fullUrl.includes('token=') ? '✅' : '❌')
    console.log('- 包含device_id:', fullUrl.includes('device_id=') ? '✅' : '❌')

    const isValid = fullUrl.includes('token=') && fullUrl.includes('device_id=')
    console.log(isValid ? '✅ URL构建测试通过' : '❌ URL构建测试失败')

    return isValid
  } catch (error) {
    console.error('❌ URL构建测试失败:', error)
    return false
  }
}

/**
 * 测试消息过滤逻辑
 */
export function testMessageFiltering() {
  console.log('🧪 测试消息过滤逻辑...')

  // 模拟各种消息类型
  const testMessages = [
    // 通用消息 - 应该处理
    { event: 'text_message', type: 'message', data: { content: '测试文本' } },
    { event: 'media_message', type: 'message', data: { type: 'image' } },
    { event: 'system_message', type: 'notification', data: { title: '系统通知' } },

    // 用户消息 - 应该处理
    { event: 'user_order_payment_success', type: 'notification', data: {} },
    { event: 'user_coupon_received', type: 'notification', data: {} },
    { event: 'user_balance_change', type: 'notification', data: {} },

    // 跑腿员消息 - 应该处理
    { event: 'runner_task_assigned', type: 'notification', data: {} },
    { event: 'runner_earnings', type: 'notification', data: {} },

    // 商家消息 - 应该忽略
    { event: 'merchant_new_order', type: 'notification', data: {} },
    { event: 'merchant_refund_request', type: 'notification', data: {} },

    // 管理员消息 - 应该忽略
    { event: 'system_maintenance', type: 'notification', data: {} },
    { event: 'system_error_alert', type: 'notification', data: {} },
    { event: 'daily_report', type: 'notification', data: {} },

    // 边界情况
    { event: '', type: 'message', data: {} },
    { event: 'unknown_event', type: 'notification', data: {} },
  ]

  console.log('📋 消息过滤测试结果:')
  let processedCount = 0
  let ignoredCount = 0

  testMessages.forEach((msg) => {
    const shouldProcess = shouldProcessMessage(msg)
    const status = shouldProcess ? '✅ 处理' : '🚫 忽略'
    console.log(`${status} - ${msg.event || '(空事件)'} (${msg.type})`)

    if (shouldProcess) {
      processedCount++
    } else {
      ignoredCount++
    }
  })

  console.log(`\n📊 统计: 处理 ${processedCount} 条，忽略 ${ignoredCount} 条`)

  // 验证关键消息是否正确处理
  const textMessageProcessed = shouldProcessMessage({ event: 'text_message', type: 'message' })
  const merchantMessageIgnored = !shouldProcessMessage({
    event: 'merchant_new_order',
    type: 'notification',
  })
  const userMessageProcessed = shouldProcessMessage({
    event: 'user_order_payment_success',
    type: 'notification',
  })

  const isCorrect = textMessageProcessed && merchantMessageIgnored && userMessageProcessed
  console.log(isCorrect ? '✅ 消息过滤逻辑测试通过' : '❌ 消息过滤逻辑测试失败')

  return isCorrect
}

/**
 * 模拟messageHandler中的消息过滤逻辑
 */
function shouldProcessMessage(message: any): boolean {
  const event = message.event || ''

  // 通用消息类型 - 用户端需要处理
  const commonMessageEvents = [
    'text_message', // 文本消息
    'media_message', // 媒体消息
    'system_message', // 系统消息
  ]

  // 用户相关事件前缀
  const userEventPrefixes = [
    'user_', // 用户通知
    'runner_', // 跑腿员通知（跑腿员也是用户）
  ]

  // 商家和管理员事件前缀 - 用户端应该忽略
  const ignoredEventPrefixes = [
    'merchant_', // 商家消息
    'system_maintenance', // 系统维护
    'system_announcement', // 系统公告
    'system_error_alert', // 系统错误告警
    'performance_alert', // 性能告警
    'security_alert', // 安全告警
    'order_exception', // 订单异常
    'payment_exception', // 支付异常
    'refund_exception', // 退款异常
    'user_report', // 用户举报（管理员处理）
    'user_ban', // 用户封禁（管理员处理）
    'merchant_audit', // 商家审核
    'merchant_violation', // 商家违规
    'daily_report', // 日报
    'threshold_alert', // 阈值告警
  ]

  // 检查是否为需要忽略的事件
  for (const prefix of ignoredEventPrefixes) {
    if (event.startsWith(prefix) || event === prefix) {
      return false
    }
  }

  // 检查是否为通用消息
  if (commonMessageEvents.includes(event)) {
    return true
  }

  // 检查是否为用户相关事件
  for (const prefix of userEventPrefixes) {
    if (event.startsWith(prefix)) {
      return true
    }
  }

  // 默认情况下返回false
  return false
}

/**
 * 运行所有测试
 */
export async function runAllTests() {
  console.log('🚀 开始运行WebSocket修复验证测试...')
  console.log('='.repeat(50))

  const results = {
    initialization: false,
    deviceId: false,
    urlConstruction: false,
    messageFiltering: false,
  }

  // 1. 测试初始化
  console.log('\n📋 测试1: WebSocket服务初始化')
  results.initialization = await testWebSocketInitialization()

  // 2. 测试设备ID
  console.log('\n📋 测试2: 设备ID获取')
  results.deviceId = testDeviceIdRetrieval()

  // 3. 测试URL构建
  console.log('\n📋 测试3: URL构建')
  results.urlConstruction = testURLConstruction()

  // 4. 测试消息过滤
  console.log('\n📋 测试4: 消息过滤逻辑')
  results.messageFiltering = testMessageFiltering()

  // 总结
  console.log('\n📊 测试结果总结:')
  console.log('- 服务初始化:', results.initialization ? '✅ 通过' : '❌ 失败')
  console.log('- 设备ID获取:', results.deviceId ? '✅ 通过' : '❌ 失败')
  console.log('- URL构建:', results.urlConstruction ? '✅ 通过' : '❌ 失败')
  console.log('- 消息过滤:', results.messageFiltering ? '✅ 通过' : '❌ 失败')

  const allPassed = Object.values(results).every((result) => result)
  console.log('- 整体结果:', allPassed ? '✅ 所有测试通过' : '❌ 部分测试失败')

  console.log('='.repeat(50))
  console.log(allPassed ? '🎉 修复验证成功！' : '⚠️ 仍有问题需要解决')

  return {
    ...results,
    overall: allPassed,
  }
}

// 在开发环境中自动挂载到全局对象
if (typeof window !== 'undefined' && import.meta.env.DEV) {
  // @ts-ignore
  window.wsFixTest = {
    testInit: testWebSocketInitialization,
    testDevice: testDeviceIdRetrieval,
    testURL: testURLConstruction,
    testMessageFiltering: testMessageFiltering,
    runAll: runAllTests,
  }

  console.log('🔧 WebSocket修复测试工具已加载，可在控制台使用 wsFixTest 对象')
  console.log('📋 可用方法: testInit, testDevice, testURL, testMessageFiltering, runAll')
}
