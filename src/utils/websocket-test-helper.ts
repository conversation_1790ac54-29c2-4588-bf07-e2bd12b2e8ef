/**
 * WebSocket测试辅助工具
 * 用于测试新的WebSocket服务功能
 */

import { webSocketService } from '@/services/websocket'
import { useUserStore } from '@/store/user'

/**
 * WebSocket服务测试套件
 */
export class WebSocketTestSuite {
  private userStore = useUserStore()
  private testResults: Array<{ name: string; success: boolean; message: string }> = []

  /**
   * 运行所有测试
   */
  async runAllTests(): Promise<{ passed: number; failed: number; results: any[] }> {
    console.log('🧪 开始运行WebSocket服务测试套件...')
    this.testResults = []

    // 基础功能测试
    await this.testServiceInitialization()
    await this.testConnectionManagement()
    await this.testMessageHandling()
    await this.testNotificationService()
    await this.testUserStateIntegration()

    const passed = this.testResults.filter((r) => r.success).length
    const failed = this.testResults.filter((r) => !r.success).length

    console.log(`✅ 测试完成: ${passed} 通过, ${failed} 失败`)

    return {
      passed,
      failed,
      results: this.testResults,
    }
  }

  /**
   * 测试服务初始化
   */
  private async testServiceInitialization() {
    try {
      // 检查服务是否已初始化
      const isInitialized = webSocketService.initialized

      if (!isInitialized) {
        await webSocketService.initialize()
      }

      this.addTestResult('服务初始化', true, '服务初始化成功')
    } catch (error) {
      this.addTestResult('服务初始化', false, `初始化失败: ${error}`)
    }
  }

  /**
   * 测试连接管理
   */
  private async testConnectionManagement() {
    try {
      // 测试连接状态获取
      const status = webSocketService.currentStatus
      const isConnected = webSocketService.isConnected

      // 测试连接统计
      const stats = webSocketService.getConnectionStats()

      if (stats && typeof stats === 'object') {
        this.addTestResult('连接管理', true, '连接状态和统计获取正常')
      } else {
        this.addTestResult('连接管理', false, '连接统计获取失败')
      }
    } catch (error) {
      this.addTestResult('连接管理', false, `连接管理测试失败: ${error}`)
    }
  }

  /**
   * 测试消息处理
   */
  private async testMessageHandling() {
    try {
      // 模拟用户消息
      const testMessage = {
        type: 'notification',
        event: 'user_test_message',
        data: {
          message: '测试消息',
          priority: 1,
          title: '测试通知',
        },
      }

      // 检查消息处理器是否存在
      if (webSocketService['messageHandler']) {
        // 尝试处理消息（不会实际发送通知，只是测试处理逻辑）
        webSocketService['messageHandler'].handleMessage(testMessage)
        this.addTestResult('消息处理', true, '消息处理器工作正常')
      } else {
        this.addTestResult('消息处理', false, '消息处理器不存在')
      }
    } catch (error) {
      this.addTestResult('消息处理', false, `消息处理测试失败: ${error}`)
    }
  }

  /**
   * 测试通知服务
   */
  private async testNotificationService() {
    try {
      // 检查通知服务是否存在
      if (webSocketService['notificationService']) {
        this.addTestResult('通知服务', true, '通知服务已加载')
      } else {
        this.addTestResult('通知服务', false, '通知服务未找到')
      }
    } catch (error) {
      this.addTestResult('通知服务', false, `通知服务测试失败: ${error}`)
    }
  }

  /**
   * 测试用户状态集成
   */
  private async testUserStateIntegration() {
    try {
      // 检查用户登录状态
      const isLoggedIn = this.userStore.isLoggedIn
      const hasToken = !!this.userStore.userInfo?.token

      // 检查服务是否正确响应用户状态
      if (isLoggedIn && hasToken) {
        // 用户已登录，服务应该尝试连接
        this.addTestResult('用户状态集成', true, '用户已登录，服务状态正常')
      } else {
        // 用户未登录，服务应该保持断开状态
        this.addTestResult('用户状态集成', true, '用户未登录，服务状态正常')
      }
    } catch (error) {
      this.addTestResult('用户状态集成', false, `用户状态集成测试失败: ${error}`)
    }
  }

  /**
   * 添加测试结果
   */
  private addTestResult(name: string, success: boolean, message: string) {
    this.testResults.push({ name, success, message })
    const status = success ? '✅' : '❌'
    console.log(`${status} ${name}: ${message}`)
  }

  /**
   * 获取测试结果
   */
  getTestResults() {
    return this.testResults
  }
}

/**
 * 快速测试WebSocket服务
 */
export async function quickTestWebSocketService() {
  const testSuite = new WebSocketTestSuite()
  return await testSuite.runAllTests()
}

/**
 * 模拟各种WebSocket消息
 */
export const mockMessages = {
  // 订单支付成功
  orderPaymentSuccess: {
    type: 'notification',
    event: 'user_order_payment_success',
    data: {
      message: '订单支付成功',
      priority: 2,
      order_id: 'test_order_123',
      amount: 99.99,
      title: '支付成功通知',
    },
  },

  // 新聊天消息
  newChatMessage: {
    type: 'message',
    event: 'user_new_message',
    data: {
      session_id: 'test_session_123',
      sender_name: '客服小助手',
      content: '您好，有什么可以帮助您的吗？',
      timestamp: Date.now(),
      message: '您有新的聊天消息',
    },
  },

  // 订单状态更新
  orderStatusUpdate: {
    type: 'notification',
    event: 'user_order_status_update',
    data: {
      message: '您的订单已发货',
      priority: 1,
      order_id: 'test_order_456',
      status: 'shipped',
      title: '订单状态更新',
    },
  },

  // 系统维护通知
  systemMaintenance: {
    type: 'notification',
    event: 'user_system_maintenance',
    data: {
      message: '系统将于今晚23:00-01:00进行维护',
      priority: 3,
      title: '系统维护通知',
      maintenance_time: '23:00-01:00',
    },
  },

  // 优惠券到期提醒
  couponExpiry: {
    type: 'notification',
    event: 'user_coupon_expiry_reminder',
    data: {
      message: '您有优惠券即将过期',
      priority: 2,
      coupon_id: 'test_coupon_789',
      expiry_date: '2024-12-31',
      title: '优惠券过期提醒',
    },
  },

  // 积分变动通知
  pointsUpdate: {
    type: 'notification',
    event: 'user_points_update',
    data: {
      message: '您获得了100积分',
      priority: 1,
      points_change: 100,
      current_points: 1500,
      title: '积分变动通知',
    },
  },
}

/**
 * 发送模拟消息
 */
export function sendMockMessage(messageKey: keyof typeof mockMessages) {
  try {
    const message = mockMessages[messageKey]
    if (webSocketService && webSocketService['messageHandler']) {
      webSocketService['messageHandler'].handleMessage(message)
      console.log(`📨 已发送模拟消息: ${messageKey}`)
      return true
    } else {
      console.error('❌ WebSocket服务未初始化')
      return false
    }
  } catch (error) {
    console.error(`❌ 发送模拟消息失败: ${error}`)
    return false
  }
}

/**
 * 批量发送模拟消息
 */
export function sendAllMockMessages() {
  const messageKeys = Object.keys(mockMessages) as Array<keyof typeof mockMessages>
  let successCount = 0

  messageKeys.forEach((key, index) => {
    setTimeout(() => {
      if (sendMockMessage(key)) {
        successCount++
      }

      if (index === messageKeys.length - 1) {
        console.log(`📊 批量发送完成: ${successCount}/${messageKeys.length} 成功`)
      }
    }, index * 1000) // 每秒发送一条消息
  })
}
