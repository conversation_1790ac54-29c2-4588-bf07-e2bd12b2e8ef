/**
 * WebSocket多端连接测试工具
 * 用于验证WebSocket连接是否正确传递device_id参数
 * 
 * <AUTHOR>
 * @email <EMAIL>
 */

import { WebSocketService } from '@/service/websocket'
import { generateDeviceInfo } from '@/utils/device'

/**
 * 测试WebSocket多端连接
 * @param token 用户token
 * @param deviceId 可选的设备ID，不传则自动生成
 */
export async function testMultiDeviceWebSocketConnection(token: string, deviceId?: string) {
  console.log('🧪 开始测试WebSocket多端连接...')
  
  // 获取或生成设备ID
  const testDeviceId = deviceId || generateDeviceInfo().device_id
  console.log('📱 测试设备ID:', testDeviceId)
  
  // 创建WebSocket服务实例
  const wsService = new WebSocketService()
  
  try {
    // 测试连接
    console.log('🔗 尝试建立WebSocket连接...')
    await wsService.connect(token, testDeviceId)
    
    console.log('✅ WebSocket连接成功！')
    console.log('📊 连接状态:', wsService.status.value)
    console.log('🔌 是否已连接:', wsService.isConnected.value)
    
    // 监听连接事件
    wsService.addEventListener('connection', (data) => {
      console.log('🎉 连接事件:', data)
    })
    
    // 监听消息事件
    wsService.addEventListener('message', (message) => {
      console.log('📨 收到消息:', message)
    })
    
    // 发送测试消息
    const testMessage = {
      type: 'heartbeat',
      data: {
        device_id: testDeviceId,
        timestamp: Date.now()
      }
    }
    
    console.log('📤 发送测试消息:', testMessage)
    const sendResult = wsService.sendMessage(testMessage)
    console.log('📤 发送结果:', sendResult)
    
    // 等待一段时间观察连接状态
    setTimeout(() => {
      console.log('⏰ 5秒后连接状态检查:')
      console.log('📊 连接状态:', wsService.status.value)
      console.log('🔌 是否已连接:', wsService.isConnected.value)
      
      // 断开连接
      wsService.disconnect()
      console.log('🔌 已断开WebSocket连接')
    }, 5000)
    
    return {
      success: true,
      deviceId: testDeviceId,
      service: wsService
    }
    
  } catch (error) {
    console.error('❌ WebSocket连接测试失败:', error)
    return {
      success: false,
      error,
      deviceId: testDeviceId
    }
  }
}

/**
 * 验证WebSocket URL是否包含正确的参数
 * @param token 用户token
 * @param deviceId 设备ID
 */
export function validateWebSocketURL(token: string, deviceId: string) {
  console.log('🔍 验证WebSocket URL参数...')
  
  // 模拟URL构建过程
  const protocol = 'ws:'
  const host = 'localhost:8080' // 示例host
  const wsUrl = `${protocol}//${host}/api/v1/chat/ws`
  
  const params = new URLSearchParams()
  params.append('token', token)
  if (deviceId) {
    params.append('device_id', deviceId)
  }
  const fullUrl = `${wsUrl}?${params.toString()}`
  
  console.log('🌐 构建的WebSocket URL:', fullUrl)
  
  // 验证URL是否包含必要参数
  const url = new URL(fullUrl)
  const hasToken = url.searchParams.has('token')
  const hasDeviceId = url.searchParams.has('device_id')
  const tokenValue = url.searchParams.get('token')
  const deviceIdValue = url.searchParams.get('device_id')
  
  console.log('✅ URL验证结果:')
  console.log('  - 包含token参数:', hasToken, '值:', tokenValue?.substring(0, 10) + '...')
  console.log('  - 包含device_id参数:', hasDeviceId, '值:', deviceIdValue)
  
  return {
    url: fullUrl,
    hasToken,
    hasDeviceId,
    tokenValue,
    deviceIdValue,
    isValid: hasToken && hasDeviceId
  }
}

/**
 * 模拟多设备同时连接测试
 * @param token 用户token
 * @param deviceCount 模拟设备数量
 */
export async function testMultipleDeviceConnections(token: string, deviceCount: number = 2) {
  console.log(`🔄 开始测试${deviceCount}个设备同时连接...`)
  
  const connections: Array<{
    deviceId: string
    service: WebSocketService
    connected: boolean
  }> = []
  
  // 创建多个设备连接
  for (let i = 0; i < deviceCount; i++) {
    const deviceInfo = generateDeviceInfo()
    const deviceId = `${deviceInfo.device_id}_test_${i + 1}`
    const wsService = new WebSocketService()
    
    try {
      console.log(`📱 设备${i + 1} (${deviceId}) 尝试连接...`)
      await wsService.connect(token, deviceId)
      
      connections.push({
        deviceId,
        service: wsService,
        connected: true
      })
      
      console.log(`✅ 设备${i + 1}连接成功`)
      
    } catch (error) {
      console.error(`❌ 设备${i + 1}连接失败:`, error)
      connections.push({
        deviceId,
        service: wsService,
        connected: false
      })
    }
  }
  
  console.log('📊 多设备连接测试结果:')
  connections.forEach((conn, index) => {
    console.log(`  设备${index + 1}: ${conn.connected ? '✅ 已连接' : '❌ 连接失败'} (${conn.deviceId})`)
  })
  
  // 清理连接
  setTimeout(() => {
    connections.forEach((conn, index) => {
      if (conn.connected) {
        conn.service.disconnect()
        console.log(`🔌 设备${index + 1}已断开连接`)
      }
    })
  }, 10000)
  
  return connections
}
