# 购物车结算功能修复与订单确认页面重构指南

## 🔧 问题修复

### 原始问题

购物车页面点击结算后，控制台报错：

```
uni-h5.es.js:2712 Uncaught (in promise) {errMsg: 'navigateTo:fail page `/pages/order/confirm?data=%7…A1%7D%5D%2C%22fromCart%22%3Atrue%7D` is not found'}
```

### 修复内容

1. **修复跳转路径**：从 `/pages/order/confirm` 改为 `/pages/takeout/order-confirm`
2. **同步store数据**：在跳转前将购物车选中商品同步到takeoutStore
3. **重构订单确认页面**：移除模拟数据，使用真实的store和API

## 🔄 订单确认页面重构

### 主要改进

1. **使用真实的store数据**

   - 集成 `useAddressStore` 获取用户地址
   - 使用 `takeoutStore.selectedCartItems` 获取选中商品
   - 修复商家信息获取

2. **修复数据类型问题**

   - 统一使用正确的TypeScript接口
   - 修复 `CouponInfo` 接口使用
   - 修复 `IAddress` 接口字段名

3. **优化价格计算**

   - 修复商品总价计算逻辑
   - 重构优惠券折扣计算
   - 支持满减券和折扣券两种类型

4. **改进订单提交**
   - 使用正确的 `CreateTakeoutOrderRequest` 接口
   - 修复订单数据构建逻辑
   - 正确处理订单创建响应

### 具体修改内容

#### 1. 导入和类型定义

```typescript
// 新增导入
import { useAddressStore } from '@/store/address'
import type { IAddress } from '@/api/address.typings'

// 修复类型定义
type IUserAddress = IAddress
```

#### 2. 地址管理

```typescript
// 使用真实的地址store
const addressStore = useAddressStore()
const selectedAddress = computed(
  () =>
    addressStore.defaultAddress ||
    (addressStore.addressList.length > 0 ? addressStore.addressList[0] : null),
)

// 加载地址列表
const loadUserAddresses = async () => {
  await addressStore.fetchAddressList()
}
```

#### 3. 优惠券处理

```typescript
// 使用正确的CouponInfo接口
const selectedCoupon = ref<CouponInfo | null>(null)

// 修复优惠券数据结构
selectedCoupon.value = {
  couponID: 1,
  couponName: '满30减5',
  couponType: 1, // 1表示满减券
  couponValue: 5, // 减免5元
}

// 重构折扣计算
const discountAmount = computed(() => {
  if (!selectedCoupon.value) return 0

  if (selectedCoupon.value.couponType === 1) {
    // 满减券
    return Math.min(selectedCoupon.value.couponValue, itemsTotalPrice.value)
  } else if (selectedCoupon.value.couponType === 2) {
    // 折扣券
    const discountRate = selectedCoupon.value.couponValue / 100
    return itemsTotalPrice.value * (1 - discountRate)
  }
  return 0
})
```

#### 4. 订单提交

```typescript
// 使用正确的订单创建接口
const orderData = {
  takeoutAddressID: selectedAddress.value!.id,
  deliveryTime: undefined,
  remark: remark.value || undefined,
  paymentMethod: selectedPaymentMethod.value.type,
  couponID: selectedCoupon.value?.couponID,
  cartItemIDs: orderItems.value.map((item) => item.cart_item_id),
}

// 正确处理响应
const response = await takeoutStore.createOrder(orderData)
uni.redirectTo({
  url: `/pages/takeout/order-detail?id=${response.orderID}`,
})
```

## 🧪 测试步骤

### 基础功能测试

1. 启动项目：`npm run dev:h5`
2. 登录用户账号
3. 添加商品到购物车
4. 在购物车页面选择商品
5. 点击结算按钮
6. 验证跳转到订单确认页面

### 订单确认页面测试

1. **地址显示**：验证是否显示用户的收货地址
2. **商品列表**：验证是否显示购物车中选中的商品
3. **价格计算**：验证商品总价、配送费、优惠券折扣计算是否正确
4. **订单提交**：验证提交订单功能是否正常

### 边界情况测试

1. 无收货地址时的处理
2. 无选中商品时的处理
3. 网络错误时的错误处理
4. 优惠券过期或不可用的处理

## 📋 修改文件清单

### 主要修改

- ✅ `H5/o-mall-user/src/pages/cart/index.vue` - 修复跳转路径和数据同步
- ✅ `H5/o-mall-user/src/pages/takeout/order-confirm.vue` - 完整重构，移除模拟数据

### 新增文件

- ✅ `H5/o-mall-user/test-cart-checkout.md` - 测试指南文档

## 🎯 预期结果

1. **购物车结算**：点击结算后能正确跳转到订单确认页面
2. **数据同步**：订单确认页面显示购物车中选中的商品
3. **真实数据**：页面使用真实的用户地址、商品信息和价格计算
4. **错误处理**：适当的错误提示和异常处理
5. **类型安全**：所有TypeScript类型错误已修复

## 🔍 技术要点

1. **数据流**：购物车 → takeoutStore → 订单确认页面
2. **状态管理**：使用Pinia store进行状态管理
3. **类型安全**：严格的TypeScript类型检查
4. **错误处理**：完善的异常处理和用户提示
5. **响应式设计**：使用Vue 3 Composition API
