/**
 * 购物车选择功能修复验证脚本
 * 在浏览器控制台中运行此脚本来验证修复效果
 */

// 测试API参数格式
function testSelectAPIFormat() {
  console.log('=== 测试选择API参数格式 ===')

  // 模拟正确的API调用
  const testData = {
    cart_item_ids: [174, 181],
    selected: true,
  }

  console.log('✅ 正确的API参数格式:', testData)

  // 检查参数类型
  console.log(
    'cart_item_ids 类型:',
    typeof testData.cart_item_ids,
    Array.isArray(testData.cart_item_ids),
  )
  console.log('selected 类型:', typeof testData.selected, testData.selected)

  return testData
}

// 测试组件事件参数处理
function testEventParameterHandling() {
  console.log('=== 测试组件事件参数处理 ===')

  // 模拟不同格式的参数
  const testCases = [
    { input: true, expected: true, description: '布尔值 true' },
    { input: false, expected: false, description: '布尔值 false' },
    { input: { value: true }, expected: true, description: '对象格式 {value: true}' },
    { input: { value: false }, expected: false, description: '对象格式 {value: false}' },
    { input: 1, expected: true, description: '数字 1' },
    { input: 0, expected: false, description: '数字 0' },
    { input: 'true', expected: true, description: '字符串 "true"' },
    { input: '', expected: false, description: '空字符串' },
  ]

  // 模拟修复后的处理函数
  function handleSelectedParameter(selected) {
    let isSelected
    if (typeof selected === 'object' && selected !== null && 'value' in selected) {
      isSelected = selected.value
    } else {
      isSelected = Boolean(selected)
    }
    return isSelected
  }

  testCases.forEach((testCase, index) => {
    const result = handleSelectedParameter(testCase.input)
    const passed = result === testCase.expected

    console.log(`测试 ${index + 1}: ${testCase.description}`)
    console.log(`  输入:`, testCase.input)
    console.log(`  期望:`, testCase.expected)
    console.log(`  结果:`, result)
    console.log(`  状态: ${passed ? '✅ 通过' : '❌ 失败'}`)
    console.log('')
  })
}

// 测试实际的购物车选择功能
async function testCartSelectionFunction() {
  console.log('=== 测试购物车选择功能 ===')

  // 检查是否在购物车页面
  const isCartPage =
    window.location.pathname.includes('/cart') || document.querySelector('.cart-content')

  if (!isCartPage) {
    console.warn('⚠️ 请在购物车页面运行此测试')
    return
  }

  // 获取购物车store
  const cartStore = window.$pinia?.state?.value?.cart
  if (!cartStore) {
    console.error('❌ 无法获取购物车store')
    return
  }

  console.log('📦 当前购物车商品数量:', cartStore.cartItems?.length || 0)

  if (!cartStore.cartItems || cartStore.cartItems.length === 0) {
    console.warn('⚠️ 购物车为空，请先添加商品')
    return
  }

  // 测试选择第一个商品
  const firstItem = cartStore.cartItems[0]
  console.log('🧪 测试商品:', {
    id: firstItem.id,
    name: firstItem.productTitle,
    currentSelected: firstItem.selected,
  })

  // 获取token
  const token = localStorage.getItem('token') || sessionStorage.getItem('token')
  if (!token) {
    console.error('❌ 未找到认证token')
    return
  }

  try {
    // 测试API调用
    const newSelectedState = !firstItem.selected
    console.log(`🌐 调用API切换选中状态: ${firstItem.selected} → ${newSelectedState}`)

    const response = await fetch('/api/v1/user/takeout/cart/select', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({
        cart_item_ids: [firstItem.id],
        selected: newSelectedState,
      }),
    })

    const result = await response.json()

    if (response.ok) {
      console.log('✅ API调用成功:', result)

      // 等待一段时间后检查状态
      setTimeout(() => {
        console.log('🔄 检查状态更新...')
        // 这里可以检查购物车状态是否更新
        window.location.reload() // 简单的刷新来验证状态持久性
      }, 1000)
    } else {
      console.error('❌ API调用失败:', result)
    }
  } catch (error) {
    console.error('❌ API调用异常:', error)
  }
}

// 运行所有测试
function runAllTests() {
  console.log('🚀 开始运行购物车选择功能修复验证')
  console.log('')

  testSelectAPIFormat()
  console.log('')

  testEventParameterHandling()
  console.log('')

  // 如果在购物车页面，运行实际测试
  if (window.location.pathname.includes('/cart')) {
    testCartSelectionFunction()
  } else {
    console.log('💡 要测试实际功能，请在购物车页面运行 testCartSelectionFunction()')
  }

  console.log('')
  console.log('🎯 测试完成！')
}

// 导出函数到全局
if (typeof window !== 'undefined') {
  window.testSelectAPIFormat = testSelectAPIFormat
  window.testEventParameterHandling = testEventParameterHandling
  window.testCartSelectionFunction = testCartSelectionFunction
  window.runAllTests = runAllTests

  console.log('🔧 购物车选择功能测试工具已加载')
  console.log('💡 使用方法:')
  console.log('   runAllTests() - 运行所有测试')
  console.log('   testSelectAPIFormat() - 测试API参数格式')
  console.log('   testEventParameterHandling() - 测试事件参数处理')
  console.log('   testCartSelectionFunction() - 测试实际选择功能')

  // 自动运行测试
  setTimeout(runAllTests, 1000)
}
