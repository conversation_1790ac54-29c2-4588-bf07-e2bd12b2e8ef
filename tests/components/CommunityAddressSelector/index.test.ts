/**
 * 社区地址选择器组件单元测试
 * @file CommunityAddressSelector组件测试
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { nextTick } from 'vue'
import CommunityAddressSelector from '@/components/CommunityAddressSelector/index.vue'
import { getAddressOptions } from '@/api/system'

// 模拟API返回数据
const mockAddressData = {
  code: 200,
  data: [
    {
      id: 1,
      name: '测试小区',
      level: 1,
      parentId: 0,
      children: [{ id: 11, name: '1号楼', level: 2, parentId: 1 }],
    },
    {
      id: 2,
      name: '另一个小区',
      level: 1,
      parentId: 0,
    },
  ],
  message: 'success',
}

// 模拟uni的存储函数
vi.mock('uni', () => ({
  getStorageSync: vi.fn(),
  setStorageSync: vi.fn(),
}))

// 模拟API调用
vi.mock('@/api/system', () => ({
  getAddressOptions: vi.fn(),
}))

describe('CommunityAddressSelector', () => {
  beforeEach(() => {
    // 重置模拟函数
    vi.resetAllMocks()

    // 模拟API返回值
    vi.mocked(getAddressOptions).mockResolvedValue(mockAddressData)

    // 模拟本地存储为空
    vi.mocked(uni.getStorageSync).mockReturnValue(null)
  })

  it('组件应正确渲染', async () => {
    const wrapper = mount(CommunityAddressSelector)

    // 确保组件已加载
    expect(wrapper.exists()).toBe(true)

    // 确保API被调用
    expect(getAddressOptions).toHaveBeenCalled()

    // 等待异步操作完成
    await nextTick()

    // 确保组件显示了占位符文本
    expect(wrapper.text()).toContain('请选择')
  })

  it('选择地址时应触发address-selected事件', async () => {
    const wrapper = mount(CommunityAddressSelector)

    // 等待数据加载
    await nextTick()

    // 模拟点击操作打开选择器
    await wrapper.find('.community-selector').trigger('click')

    // 触发选择完成事件（模拟内部wd-cascade-picker的确认操作）
    await wrapper.vm.handleConfirm([1, 11]) // 选择测试小区的1号楼

    // 检查事件是否触发且带有正确的参数
    expect(wrapper.emitted('address-selected')).toBeTruthy()
    expect(wrapper.emitted('address-selected')![0][0]).toMatchObject({
      fullPath: '测试小区/1号楼',
      selectedKeys: [1, 11],
      communityId: 1,
      buildingId: 11,
    })
  })

  it('应正确清除选择', async () => {
    const wrapper = mount(CommunityAddressSelector)

    // 等待数据加载
    await nextTick()

    // 先选择一个值
    await wrapper.vm.handleConfirm([1, 11])

    // 调用清除方法
    await wrapper.vm.clearSelection()

    // 检查事件是否触发且带有空值
    const events = wrapper.emitted('address-selected')
    expect(events).toBeTruthy()
    expect(events![events!.length - 1][0]).toMatchObject({
      fullPath: '',
      selectedKeys: [],
      communityId: null,
      buildingId: null,
      unitId: null,
    })
  })

  it('应处理懒加载模式', async () => {
    const wrapper = mount(CommunityAddressSelector, {
      props: {
        lazyLoad: true,
      },
    })

    // 等待初始数据加载
    await nextTick()

    // 模拟懒加载调用
    await wrapper.vm.loadData(1) // 加载id为1的小区的子节点

    // 验证存储调用
    expect(uni.setStorageSync).toHaveBeenCalled()
  })

  it('应使用initialValue正确初始化选中项', async () => {
    const initialValue = [1, 11] // 测试小区的1号楼

    const wrapper = mount(CommunityAddressSelector, {
      props: {
        initialValue,
      },
    })

    // 等待初始化完成
    await nextTick()
    await nextTick() // 可能需要多个tick等待异步操作完成

    // 确认显示了正确的文本
    expect(wrapper.vm.selectedText).toContain('测试小区')
  })
})
