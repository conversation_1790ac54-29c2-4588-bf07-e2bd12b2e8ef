/**
 * 地址编辑页面集成测试
 * @file 测试地址编辑页面与社区地址选择器的集成
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { nextTick } from 'vue'
import AddressEditPage from '@/pages/address/edit.vue'
import CommunityAddressSelector from '@/components/CommunityAddressSelector/index.vue'
import { createPinia, setActivePinia } from 'pinia'

// 模拟路由和路由参数
vi.mock('vue-router', () => ({
  useRoute: vi.fn(() => ({
    query: {},
    params: { id: '' }, // 默认为新建地址模式
  })),
}))

// 模拟API调用
vi.mock('@/api/address', () => ({
  getAddressDetail: vi.fn().mockResolvedValue({
    code: 200,
    data: {
      id: 1,
      receiverName: '测试用户',
      receiverPhone: '13800138000',
      province: '广东省',
      city: '深圳市',
      district: '南山区',
      address: '科技园测试路1号',
      communityId: 1,
      buildingId: 11,
      unitId: null,
      isDefault: true,
    },
    message: 'success',
  }),
  createAddress: vi.fn().mockResolvedValue({ code: 200, message: 'success' }),
  updateAddress: vi.fn().mockResolvedValue({ code: 200, message: 'success' }),
}))

describe('地址编辑页面', () => {
  beforeEach(() => {
    // 设置pinia
    const pinia = createPinia()
    setActivePinia(pinia)

    // 重置所有模拟
    vi.resetAllMocks()
  })

  it('应正确渲染地址编辑页面', async () => {
    const wrapper = mount(AddressEditPage)

    // 确保页面已渲染
    expect(wrapper.exists()).toBe(true)

    // 检查表单元素是否存在
    expect(wrapper.find('input[placeholder="请输入收货人姓名"]').exists()).toBe(true)
    expect(wrapper.find('input[placeholder="请输入收货人手机号"]').exists()).toBe(true)
  })

  it('应正确集成社区地址选择器', async () => {
    const wrapper = mount(AddressEditPage, {
      global: {
        stubs: {
          // 保留CommunityAddressSelector作为实际组件，其他可以存根
          'wd-input': true,
          'wd-cell': true,
          'wd-cell-group': true,
          CommunityAddressSelector: false,
        },
      },
    })

    // 确认社区地址选择器存在
    expect(wrapper.findComponent(CommunityAddressSelector).exists()).toBe(true)

    // 模拟社区地址选择事件
    await wrapper.findComponent(CommunityAddressSelector).vm.$emit('address-selected', {
      fullPath: '测试小区/1号楼',
      selectedKeys: [1, 11],
      communityId: 1,
      buildingId: 11,
      unitId: null,
      longitude: 114.123,
      latitude: 22.456,
    })

    // 等待异步操作完成
    await nextTick()

    // 检查表单数据是否正确更新
    expect(wrapper.vm.formData.communityId).toBe(1)
    expect(wrapper.vm.formData.buildingId).toBe(11)
  })

  it('应在表单提交时包含社区地址信息', async () => {
    const wrapper = mount(AddressEditPage)

    // 填写表单数据 - 使用setData方法正确设置表单数据
    await wrapper.setData({
      formData: {
        ...wrapper.vm.formData,
        receiverName: '测试用户',
        receiverPhone: '13800138000',
        address: '详细地址',
      },
    })

    // 模拟社区地址选择
    await wrapper.vm.handleCommunityAddressSelected({
      fullPath: '测试小区/1号楼',
      selectedKeys: [1, 11],
      communityId: 1,
      buildingId: 11,
      unitId: null,
      longitude: 114.123,
      latitude: 22.456,
    })

    // 模拟区域选择
    await wrapper.vm.handleRegionSelected({
      province: '广东省',
      city: '深圳市',
      district: '南山区',
    })

    // 调用保存方法
    await wrapper.vm.saveAddress()

    // 验证API调用参数包含社区地址信息
    const apiCalls = vi.mocked(require('@/api/address').createAddress).mock.calls
    expect(apiCalls.length).toBe(1)
    expect(apiCalls[0][0]).toMatchObject({
      communityId: 1,
      buildingId: 11,
    })
  })
})
