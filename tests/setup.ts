/**
 * 测试环境全局设置文件
 * 用于配置全局的测试环境、模拟对象等
 */

import { vi } from 'vitest'

// 模拟uni-app的全局对象
global.uni = {
  getStorageSync: vi.fn(),
  setStorageSync: vi.fn(),
  showToast: vi.fn(),
  navigateTo: vi.fn(),
  navigateBack: vi.fn(),
  switchTab: vi.fn(),
  request: vi.fn(),
  // 根据需要添加其他方法
}

// 如果需要，可以模拟window对象
Object.defineProperty(global, 'window', {
  value: {
    // 添加必要的window属性和方法
  },
})

// 模拟Vue的nextTick函数
vi.mock('vue', async (importOriginal) => {
  const actual = await importOriginal()
  return {
    ...actual,
    nextTick: vi.fn().mockImplementation(() => Promise.resolve()),
  }
})

// 模拟API调用
vi.mock('@/api/system', () => ({
  getAddressOptions: vi.fn().mockResolvedValue({
    code: 200,
    data: [
      {
        id: 1,
        name: '测试小区',
        level: 1,
        parentId: 0,
        children: [{ id: 11, name: '1号楼', level: 2, parentId: 1 }],
      },
    ],
    message: 'success',
  }),
}))
