# 消息系统后端开发需求书

## 1. 项目概述

### 1.1 项目背景
本项目为O-Mall商城H5端的消息系统，需要开发完整的消息管理后端API，支持多种消息类型的统一管理，包括聊天消息、系统通知、订单消息和客服消息。

### 1.2 技术栈要求
- 后端框架：建议使用Go/Java/Node.js
- 数据库：MySQL/PostgreSQL
- 缓存：Redis
- 消息队列：RabbitMQ/Kafka（可选）
- WebSocket：支持实时消息推送

### 1.3 系统架构
```
前端H5应用 <-> API网关 <-> 消息服务 <-> 数据库
                              ↓
                         WebSocket服务
                              ↓
                           消息队列
```

## 2. 数据库设计

### 2.1 消息分类表 (message_categories)
```sql
CREATE TABLE message_categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    type VARCHAR(50) NOT NULL COMMENT '分类类型：chat,system,order,service',
    title VARCHAR(100) NOT NULL COMMENT '分类标题',
    icon VARCHAR(100) COMMENT '图标名称',
    color VARCHAR(20) COMMENT '图标颜色',
    sort_order INT DEFAULT 0 COMMENT '排序',
    status TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 2.2 会话表 (conversations)
```sql
CREATE TABLE conversations (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    type ENUM('chat','system','order','service','customer_service','merchant','delivery') NOT NULL COMMENT '会话类型',
    title VARCHAR(200) NOT NULL COMMENT '会话标题',
    avatar VARCHAR(500) COMMENT '会话头像',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    target_id BIGINT COMMENT '目标用户ID（商家、客服等）',
    target_type ENUM('user','merchant','admin','delivery','system') COMMENT '目标用户类型',
    last_message_id BIGINT COMMENT '最后一条消息ID',
    unread_count INT DEFAULT 0 COMMENT '未读消息数',
    is_pinned TINYINT DEFAULT 0 COMMENT '是否置顶',
    is_muted TINYINT DEFAULT 0 COMMENT '是否静音',
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-删除',
    extra_data JSON COMMENT '扩展数据（订单ID、商品ID等）',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_type (type),
    INDEX idx_updated_at (updated_at)
);
```

### 2.3 消息表 (messages)
```sql
CREATE TABLE messages (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    conversation_id BIGINT NOT NULL COMMENT '会话ID',
    sender_id BIGINT NOT NULL COMMENT '发送者ID',
    sender_type ENUM('user','merchant','admin','delivery','system') NOT NULL COMMENT '发送者类型',
    receiver_id BIGINT COMMENT '接收者ID',
    receiver_type ENUM('user','merchant','admin','delivery','system') COMMENT '接收者类型',
    message_type ENUM('text','image','voice','video','file','location','system','order','goods','order_notification') NOT NULL COMMENT '消息类型',
    content JSON NOT NULL COMMENT '消息内容',
    status ENUM('sending','sent','delivered','read','failed') DEFAULT 'sent' COMMENT '消息状态',
    is_read TINYINT DEFAULT 0 COMMENT '是否已读',
    is_recalled TINYINT DEFAULT 0 COMMENT '是否撤回',
    reply_to_id BIGINT COMMENT '回复的消息ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_conversation_id (conversation_id),
    INDEX idx_sender_id (sender_id),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (conversation_id) REFERENCES conversations(id) ON DELETE CASCADE
);
```

### 2.4 系统通知表 (system_notifications)
```sql
CREATE TABLE system_notifications (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT COMMENT '用户ID，NULL表示全局通知',
    type VARCHAR(50) NOT NULL COMMENT '通知类型',
    title VARCHAR(200) NOT NULL COMMENT '通知标题',
    content TEXT NOT NULL COMMENT '通知内容',
    action_type VARCHAR(50) COMMENT '操作类型',
    action_url VARCHAR(500) COMMENT '操作链接',
    is_read TINYINT DEFAULT 0 COMMENT '是否已读',
    priority INT DEFAULT 0 COMMENT '优先级',
    expire_time TIMESTAMP COMMENT '过期时间',
    extra_data JSON COMMENT '扩展数据',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_type (type),
    INDEX idx_created_at (created_at)
);
```

### 2.5 订单通知表 (order_notifications)
```sql
CREATE TABLE order_notifications (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    order_id BIGINT NOT NULL COMMENT '订单ID',
    order_no VARCHAR(100) NOT NULL COMMENT '订单号',
    type ENUM('payment_success','payment_failed','order_shipped','order_delivered','order_cancelled','refund_applied','refund_approved','refund_rejected','refund_success','refund_failed') NOT NULL COMMENT '通知类型',
    title VARCHAR(200) NOT NULL COMMENT '通知标题',
    content TEXT NOT NULL COMMENT '通知内容',
    amount DECIMAL(10,2) COMMENT '金额',
    refund_amount DECIMAL(10,2) COMMENT '退款金额',
    action_type VARCHAR(50) COMMENT '操作类型',
    action_url VARCHAR(500) COMMENT '操作链接',
    is_read TINYINT DEFAULT 0 COMMENT '是否已读',
    extra_data JSON COMMENT '扩展数据',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_order_id (order_id),
    INDEX idx_type (type),
    INDEX idx_created_at (created_at)
);
```

## 3. API接口设计

### 3.1 消息分类接口

#### 3.1.1 获取消息分类列表
```
GET /api/v1/chat/message/categories
```

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "type": "chat",
      "title": "聊天消息",
      "icon": "chat",
      "color": "#4D8EFF",
      "unreadCount": 3,
      "path": "/pages/chat/sessions/index"
    },
    {
      "type": "system",
      "title": "系统通知",
      "icon": "notification",
      "color": "#FF9500",
      "unreadCount": 2,
      "path": "/pages/message/system"
    },
    {
      "type": "order",
      "title": "订单消息",
      "icon": "goods",
      "color": "#34C759",
      "unreadCount": 1,
      "path": "/pages/message/order"
    },
    {
      "type": "service",
      "title": "客服消息",
      "icon": "service",
      "color": "#FF3B30",
      "unreadCount": 0,
      "path": "/pages/message/service"
    }
  ]
}
```

### 3.2 消息列表接口

#### 3.2.1 获取消息列表
```
GET /api/v1/messages
```

**请求参数：**
```json
{
  "page": 1,
  "pageSize": 20,
  "type": "chat", // 可选：消息类型筛选
  "keyword": "搜索关键词" // 可选：搜索关键词
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "id": "1",
        "type": "chat",
        "title": "张三",
        "avatar": "https://example.com/avatar.jpg",
        "lastMessage": "好的，我知道了",
        "lastTime": 1640995200000,
        "unreadCount": 2,
        "isPinned": true,
        "isMuted": false
      }
    ],
    "total": 100,
    "page": 1,
    "pageSize": 20,
    "hasMore": true
  }
}
```

### 3.3 会话管理接口

#### 3.3.1 获取会话列表
```
GET /api/v1/chat/sessions
```

**请求参数：**
```json
{
  "page": 1,
  "page_size": 20,
  "type": "customer_service" // 可选：会话类型
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "conversations": [
      {
        "id": "1",
        "type": "customer_service",
        "title": "客服小助手",
        "avatar": "https://example.com/avatar.jpg",
        "participants": [
          {
            "id": "1",
            "nickname": "客服小助手",
            "avatar": "https://example.com/avatar.jpg",
            "userType": "service",
            "isOnline": true
          }
        ],
        "lastMessage": {
          "id": "1",
          "content": {
            "text": "有什么可以帮助您的吗？"
          },
          "createdAt": "2024-01-01T12:00:00Z"
        },
        "unreadCount": 0,
        "isTop": false,
        "isMuted": false,
        "createdAt": "2024-01-01T10:00:00Z",
        "updatedAt": "2024-01-01T12:00:00Z"
      }
    ],
    "total": 10,
    "page": 1,
    "pageSize": 20
  }
}
```

#### 3.3.2 创建会话
```
POST /api/v1/chat/sessions
```

**请求参数：**
```json
{
  "receiver_id": 123,
  "receiver_type": "merchant" // user, merchant, admin, delivery
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "1",
    "type": "merchant",
    "title": "商家名称",
    "avatar": "https://example.com/avatar.jpg",
    "participants": [],
    "unreadCount": 0,
    "isTop": false,
    "isMuted": false,
    "createdAt": "2024-01-01T12:00:00Z",
    "updatedAt": "2024-01-01T12:00:00Z"
  }
}
```

### 3.4 消息管理接口

#### 3.4.1 获取会话消息列表
```
GET /api/v1/chat/sessions/{sessionId}/messages
```

**请求参数：**
```json
{
  "page": 1,
  "page_size": 20,
  "before_message_id": "123", // 可选：获取指定消息之前的消息
  "after_message_id": "456" // 可选：获取指定消息之后的消息
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "messages": [
      {
        "id": "1",
        "conversationId": "1",
        "senderId": "123",
        "senderInfo": {
          "id": "123",
          "nickname": "张三",
          "avatar": "https://example.com/avatar.jpg",
          "userType": "user",
          "isOnline": true
        },
        "receiverId": "456",
        "type": "text",
        "content": {
          "text": "你好"
        },
        "status": "read",
        "isRead": true,
        "createdAt": "2024-01-01T12:00:00Z",
        "updatedAt": "2024-01-01T12:00:00Z"
      }
    ],
    "total": 50,
    "page": 1,
    "pageSize": 20,
    "hasMore": true
  }
}
```

#### 3.4.2 发送文本消息
```
POST /api/v1/chat/sessions/{sessionId}/messages/text
```

**请求参数：**
```json
{
  "content": "你好，这是一条文本消息"
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "1",
    "conversationId": "1",
    "senderId": "123",
    "senderInfo": {
      "id": "123",
      "nickname": "张三",
      "avatar": "https://example.com/avatar.jpg",
      "userType": "user",
      "isOnline": true
    },
    "receiverId": "456",
    "type": "text",
    "content": {
      "text": "你好，这是一条文本消息"
    },
    "status": "sent",
    "isRead": false,
    "createdAt": "2024-01-01T12:00:00Z",
    "updatedAt": "2024-01-01T12:00:00Z"
  }
}
```

#### 3.4.3 标记消息已读
```
POST /api/v1/chat/sessions/{sessionId}/read
```

**请求参数：**
```json
{
  "message_ids": ["1", "2", "3"] // 可选：指定消息ID，不传则标记所有未读消息
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "readCount": 3
  }
}
```

### 3.5 系统通知接口

#### 3.5.1 获取系统通知列表
```
GET /api/v1/notifications/system
```

**请求参数：**
```json
{
  "page": 1,
  "pageSize": 20,
  "type": "system", // 可选：通知类型
  "isRead": false // 可选：是否已读
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "id": "1",
        "type": "system",
        "title": "系统维护通知",
        "content": "系统将于今晚22:00-24:00进行维护，期间可能无法正常使用",
        "actionType": "link",
        "actionUrl": "/pages/notice/detail?id=1",
        "isRead": false,
        "priority": 1,
        "createdAt": "2024-01-01T12:00:00Z"
      }
    ],
    "total": 20,
    "page": 1,
    "pageSize": 20,
    "hasMore": true
  }
}
```

#### 3.5.2 标记系统通知已读
```
POST /api/v1/notifications/system/{notificationId}/read
```

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

### 3.6 订单通知接口

#### 3.6.1 获取订单通知列表
```
GET /api/v1/notifications/order
```

**请求参数：**
```json
{
  "page": 1,
  "pageSize": 20,
  "type": "payment_success", // 可选：通知类型
  "orderId": 123, // 可选：订单ID
  "isRead": false // 可选：是否已读
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "id": "1",
        "orderId": 123,
        "orderNo": "OM202401010001",
        "type": "payment_success",
        "title": "支付成功",
        "content": "您的订单已支付成功，商家正在准备发货",
        "amount": 99.90,
        "actionType": "order_detail",
        "actionUrl": "/pages/order/detail?id=123",
        "isRead": false,
        "createdAt": "2024-01-01T12:00:00Z"
      }
    ],
    "total": 15,
    "page": 1,
    "pageSize": 20,
    "hasMore": true
  }
}
```

#### 3.6.2 标记订单通知已读
```
POST /api/v1/notifications/order/{notificationId}/read
```

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

### 3.7 未读消息统计接口

#### 3.7.1 获取未读消息统计
```
GET /api/v1/messages/unread-count
```

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 10,
    "categories": {
      "chat": 3,
      "system": 2,
      "order": 4,
      "service": 1
    },
    "conversations": {
      "1": 2,
      "2": 1,
      "3": 0
    }
  }
}
```

### 3.8 文件上传接口

#### 3.8.1 上传聊天文件
```
POST /api/v1/chat/upload
```

**请求参数：**
```
Content-Type: multipart/form-data

file: [文件]
type: image // image, voice, video, file
```

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "url": "https://example.com/uploads/chat/image.jpg",
    "fileName": "image.jpg",
    "fileSize": 1024000,
    "width": 800,
    "height": 600
  }
}
```

## 4. WebSocket实时消息推送

### 4.1 连接建立
```
ws://domain.com/ws/chat?token={jwt_token}
```

### 4.2 消息格式

#### 4.2.1 连接消息
```json
{
  "type": "connect",
  "data": {
    "userId": "123",
    "status": "online"
  },
  "timestamp": 1640995200000
}
```

#### 4.2.2 新消息推送
```json
{
  "type": "message",
  "data": {
    "id": "1",
    "conversationId": "1",
    "senderId": "123",
    "senderInfo": {
      "id": "123",
      "nickname": "张三",
      "avatar": "https://example.com/avatar.jpg",
      "userType": "user"
    },
    "type": "text",
    "content": {
      "text": "你好"
    },
    "createdAt": "2024-01-01T12:00:00Z"
  },
  "timestamp": 1640995200000
}
```

#### 4.2.3 订单通知推送
```json
{
  "type": "notification",
  "data": {
    "type": "order_payment_success",
    "title": "支付成功",
    "content": "您的订单已支付成功",
    "orderId": 123,
    "orderNo": "OM202401010001",
    "userId": 456,
    "amount": 99.90,
    "actionType": "order_detail",
    "actionUrl": "/pages/order/detail?id=123",
    "persistent": true,
    "priority": 1
  },
  "timestamp": 1640995200000
}
```

#### 4.2.4 已读状态推送
```json
{
  "type": "read",
  "data": {
    "conversationId": "1",
    "messageIds": ["1", "2", "3"],
    "userId": "123"
  },
  "timestamp": 1640995200000
}
```

#### 4.2.5 在线状态推送
```json
{
  "type": "online",
  "data": {
    "userId": "123",
    "isOnline": true,
    "lastActiveTime": "2024-01-01T12:00:00Z"
  },
  "timestamp": 1640995200000
}
```

## 5. 业务逻辑要求

### 5.1 消息分类统计
- 实时统计各分类的未读消息数量
- 支持按分类筛选消息列表
- 支持搜索功能（标题、内容关键词搜索）

### 5.2 会话管理
- 支持多种会话类型（用户聊天、客服、商家、配送员、系统）
- 支持会话置顶、静音功能
- 自动创建会话（首次发送消息时）
- 会话列表按最后消息时间排序

### 5.3 消息状态管理
- 消息状态：发送中、已发送、已送达、已读、发送失败
- 支持消息撤回（限时）
- 支持消息删除
- 已读状态实时同步

### 5.4 系统通知
- 支持全局通知和个人通知
- 支持通知优先级
- 支持通知过期时间
- 支持操作按钮（跳转链接）

### 5.5 订单通知
- 订单状态变更自动发送通知
- 支持多种订单事件（支付、发货、收货、退款等）
- 通知内容包含订单基本信息
- 支持跳转到订单详情

### 5.6 文件上传
- 支持图片、语音、视频、文件上传
- 文件大小限制和格式验证
- 自动生成缩略图（图片、视频）
- 文件存储和CDN加速

## 6. 性能要求

### 6.1 响应时间
- API响应时间 < 200ms
- WebSocket消息推送延迟 < 100ms
- 文件上传响应时间 < 2s

### 6.2 并发处理
- 支持10000+并发WebSocket连接
- 支持1000+QPS的API请求
- 消息队列处理能力 > 10000条/秒

### 6.3 数据存储
- 消息数据保留1年
- 文件存储支持CDN加速
- 数据库读写分离
- Redis缓存热点数据

## 7. 安全要求

### 7.1 身份认证
- JWT Token认证
- Token过期自动刷新
- 用户权限验证

### 7.2 数据安全
- 敏感信息加密存储
- API接口防刷限流
- 文件上传安全检查
- SQL注入防护

### 7.3 消息安全
- 消息内容过滤（敏感词）
- 垃圾消息检测
- 用户举报功能

## 8. 监控和日志

### 8.1 系统监控
- API接口监控
- WebSocket连接监控
- 数据库性能监控
- 服务器资源监控

### 8.2 业务监控
- 消息发送成功率
- 用户活跃度统计
- 异常消息告警

### 8.3 日志记录
- 操作日志记录
- 错误日志记录
- 性能日志记录
- 审计日志记录

## 9. 部署要求

### 9.1 环境要求
- 生产环境：Linux服务器
- 数据库：MySQL 8.0+
- 缓存：Redis 6.0+
- 负载均衡：Nginx

### 9.2 扩展性
- 支持水平扩展
- 微服务架构
- 容器化部署
- 自动伸缩

### 9.3 高可用
- 数据库主从复制
- Redis集群
- 服务多实例部署
- 故障自动切换

## 10. 测试要求

### 10.1 单元测试
- 代码覆盖率 > 80%
- 核心业务逻辑测试
- 边界条件测试

### 10.2 集成测试
- API接口测试
- WebSocket功能测试
- 数据库操作测试

### 10.3 性能测试
- 压力测试
- 并发测试
- 稳定性测试

## 11. 交付物

### 11.1 代码交付
- 完整的后端代码
- 数据库脚本
- 配置文件
- 部署脚本

### 11.2 文档交付
- API接口文档
- 数据库设计文档
- 部署文档
- 运维文档

### 11.3 测试交付
- 测试用例
- 测试报告
- 性能测试报告

## 12. 开发计划

### 12.1 第一阶段（2周）
- 数据库设计和创建
- 基础API接口开发
- 用户认证和权限

### 12.2 第二阶段（2周）
- 会话管理功能
- 消息发送和接收
- WebSocket实时推送

### 12.3 第三阶段（1周）
- 系统通知功能
- 订单通知功能
- 文件上传功能

### 12.4 第四阶段（1周）
- 性能优化
- 安全加固
- 测试和部署

## 13. 验收标准

### 13.1 功能验收
- 所有API接口正常工作
- WebSocket实时推送正常
- 前端页面功能完整

### 13.2 性能验收
- 满足性能要求指标
- 通过压力测试
- 系统稳定运行

### 13.3 安全验收
- 通过安全测试
- 无重大安全漏洞
- 符合安全规范

---

**联系方式：**
- 项目负责人：张二豪
- 邮箱：<EMAIL>
- 创建时间：2024年1月
- 文档版本：v1.0